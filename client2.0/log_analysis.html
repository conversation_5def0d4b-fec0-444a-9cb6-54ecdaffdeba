<link href="lib/third/bootstrap-table/bootstrap-table.min.css"/>
<div class="main-content">
  <div class="main-content-inner">
    <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs">
      <div class="page-content">
        <div class="page-header">
          <h1>日志分析</h1>
        </div><!-- /.page-header -->
        <div class="row wa-mb10">
          <div class="col-xs-12">
            <form class="form-horizontal" role="form" id="log_analysis_frm">
              <div class="form-group">
                <label class="col-sm-2 control-label no-padding-right">
                  <b>用户/主体名:</b>
                </label>
                <div class="col-sm-2">
                  <input type="text" class="form-control" id="username" name="username" maxlength="20"
                         placeholder="长度不超过20" >
                </div>
              </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label no-padding-right">
                        <b>开始时间:</b>
                    </label>
                    <div class="col-sm-4">
                        <input type="text" id="newTimeRange" class="form-control" style="width: 58%">
                        <i class="glyphicon glyphicon-calendar fa fa-calendar"
                           style="position: absolute;right: 45%;top: 10px;"></i>
                    </div>
                    <label class="col-sm-2 control-label no-padding-right">
                        <b>结束时间:</b>
                    </label>
                    <div class="col-sm-4">
                        <input type="text" id="endTimeRange" class="form-control" style="width: 58%">
                        <i class="glyphicon glyphicon-calendar fa fa-calendar"
                           style="position: absolute;right: 45%;top: 10px;"></i>
                    </div>
                </div>
              <div class="col-sm-12 text-right">
                <a href="javascript:;" type="button" class="btn btn-sm btn-primary" id="query"><i
                    class="ace-icon fa fa-refresh"></i> 查询</a>
                <a href="javascript:;" type="button" class="btn btn-sm btn-primary" id="log_export"> 导出报表</a>
              </div>
            </form>
          </div>
        </div>
          <div class="row wa-mb10">
              <div id="images" style="width:1000px;height:289px;float:right;text-align:center;display: none">图片预览区</div>
          <div class="col-xs-12" id="runLog">
            <div class="col-xs-6" style="margin-top: 8%">
              <div id="level_percent" style="height: 285px;top: -49px"></div>
            </div><!-- /.col -->
              <div class="col-xs-6" style="border:0px dashed #000;margin-top: -2%">
                  <!-- <div style="position: absolute;width: 30%;margin-left: 4%;margin-top: 8%;z-index: 999" >-->
                  <select class="form-control" id="analysis_type" name="analysis_type" style="border-radius: 5px;width: 30%;margin-top: 8%;margin-left: 8%;">
                      <option value="LX">日志分类统计</option>
                      <option value="LY">日志来源</option>
                      <option value="JB">日志级别</option>
                      <option value="MK">日志模块</option>
                  </select>
                  <!--                </div>-->
                  <div id="module_statis" style="height: 285px;top: -5px;"></div>
              </div><!-- /.col -->
          </div>
        </div>
        <div class="row">
          <div class="col-xs-12" id="excepLog">
            <!--<div id="excepLog" class="tab-pane active">-->
            <div class="panel panel-default table-responsive" id="log-excep-tb">

            </div>
            <div class="pagination-wrap" id="log-excep-pager">
            </div>
            <div class="hidden" id="log-excep-tb-nodata">
              <p>当前没有日志分析记录</p>
            </div>
          </div>
        </div>
      </div><!-- /.page-content -->
    </div>
  </div><!-- /.main-content -->
  <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
    <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
  </a>
  <!-- 验证用户身份-->
  <div class="modal" id="validateUserDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <form class="form-horizontal" id="validateUserForm" method="post" action="" data-toggle="validator" role="form">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header" style="color: white;background: deepskyblue">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            <h4 class="modal-title">验证用户身份</h4>
          </div>
          <div class="modal-body" style="padding: 10px 50px;">
            <p>请输入登录密码</p>
            <input type="hidden" id="opraType"/>
            <input type="password" id="password" class="form-control" placeholder="密码" required/>
            <div class="help-block with-errors"></div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
            <button type="submit" class="btn btn-primary" id="validateuser_btn">确定
            </button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </form>
  </div><!-- /.modal -->
  <!-- 导入导出选择对话框 -->
  <div class="modal fade" id="confirmPortDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
       aria-hidden="true" data-backdrop="false">
    <form class="form-horizontal" id="confirmPortForm" method="post" action="" data-toggle="validator" role="form">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
              <span class="sr-only">Close</span>
            </button>
            <h4 class="modal-title" wa-name="import_tt">请选择文件路径</h4>
          </div>
          <div class="modal-body">
            <div class="form-group">
              <label class="col-sm-2 control-label text-right" for="select_folder">请选择:</label>
<!--              <div class="col-sm-9">-->
<!--                <label class="ace-file-input">-->
<!--                  <input type="file" id="select_folder" webkitdirectory directory required>-->
<!--                </label>-->
<!--                &lt;!&ndash;<div class="help-block with-errors"></div>&ndash;&gt;-->
<!--              </div>-->
              <!--导出用2-->
              <div class="col-sm-9">
                    <div class="input-group">
                        <input type="text" id="export_file_path" class="form-control" readonly required>
                        <span class="input-group-btn">
                                        <button type="button" id="export_selectPath_btn" class="btn btn-default">选择路径</button>
                                    </span>
                    </div>
                    <!--<div class="help-block with-errors"></div>-->
                </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
            <button type="button" class="btn btn-primary" id="port_btn">确定</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.form-horizontal-->
    </form><!-- /.modal-dialog -->
  </div><!-- /.modal -->
  <script src="lib/third/bootstrap-table/bootstrap-table.js"></script>
  <script src="lib/third/bootstrap-table/bootstrap-table-pagejump.js"></script>
  <script src="lib/third/bootstrap-table/locale/bootstrap-table-zh-CN.min.js"></script>
    <!--<script src="lib/third/Export2Excel.js"></script>-->
    <script src="lib/third/html2canvas.js"></script>
    <script src="lib/third/canvas2image.js"></script>
    <script src="lib/third/base64.js"></script>
  <script>
    delete require.cache[require.resolve('./script/view/log_analysis.js')];
    require('./script/view/log_analysis.js');
    $('#log_analysis_frm').validator();
  </script>
</div>
