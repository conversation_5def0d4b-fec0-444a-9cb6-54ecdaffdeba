"use strict";
// 🔁 开启热重载功能（仅开发时生效）
// require('electron-reload')(__dirname, {
//     electron: require(`${__dirname}/node_modules/electron`)
// });
// Modules to control application life and create native browser window
const {app, BrowserWindow, Menu, ipcMain, dialog} = require('electron');
const fs = require('fs');
const path = require('path');
// Keep a global reference of the window object, if you don't, the window will
// be closed automatically when the JavaScript object is garbage collected.
let mainWindow;
const config = require('./config');
const temp_config = require('../temp_config');
const util = require('./lib/util');
const crypto = require('crypto');
const tcp_client = require('./lib/tcp_client');
const {exec, spawnSync} = require('child_process');
const ui_util = require('./script/view/ui_util');
// spawnSync("java", ['-jar', 'ClientBridge.jar', "aaa", +"bbb"]);
if (!config.enable_ssl) {
    exec('java -jar ClientBridge.jar ' + config.safe_port, {cwd: __dirname + '/safe_service'}, (err, stdout, stderr) => {
        if (err) {
            console.log(err);
            return;
        }
        console.log(`stdout: ${stdout}`);
        console.log(`stderr: ${stderr}`);
    });
}
// 文件夹选择

ipcMain.handle('select-folder', async () => {
    const result = await dialog.showOpenDialog({
        properties: ['openDirectory']
    });
    if (result.canceled || result.filePaths.length === 0) {
        return null; // 用户取消
    }
    return result.filePaths[0]; // 返回文件夹路径
});

// tcp_client.send(Buffer.from("dssdsd"));
// for (let i in crypto.getCiphers()) {
//     console.log(crypto.getCiphers()[i])
// }
// console.log('crypto.getCiphers()',crypto.getCiphers().length, process.version)
// let buf = Buffer.alloc(8)
// buf[0] = 99;
// buf[7] = 77;
// let encrypted = util.aesEncrypt(Buffer.from("hello"));
// let decrypted = util.aesDecrypt("33635b3b8680f92f5d69401114ebf665")
// console.log(decrypted);


// let buf = Buffer.alloc(12);
// for (let i = 0; i < buf.length; i++) {
//     buf[i] = 180 + i;
// }
// const enc = util.sm2Encrypt(Buffer.from("hello"));
// console.log('enc', enc, util.encodeBase64(buf))
// const dec = util.sm2Decrypt("68c060c9c9143fbc937784f5a78f3bb8818e728684fb05b05edf049f2c3edd87dfa294f74b2751d8c4667cd986e1bfda040b101916348e971dbd30ae1d7a3dd532e8ca2c373e3965481b384c45738f0f87c5df70732ce115ff5c756b9c4fa5ba62a1b9e61e530acf3ae4ba4c7086794c");
// console.log('dec', dec, dec.toString("base64"), dec.length)

function createWindow() {
    console.log('main thread log');
    // Create the browser window.
    mainWindow = new BrowserWindow({
        width: 1680,
        height: 1050,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false, // 关闭隔离
        }});

    // and load the index.html of the app.
    mainWindow.loadURL(`file://${__dirname}/login.html`);
    // mainWindow.loadURL(`file://${__dirname}/param_BackupAndRestore.html`);

    // 去掉默认菜单
    Menu.setApplicationMenu(null);

    if (temp_config.enable_debug) {
        // Open the DevTools.
        mainWindow.webContents.openDevTools();
    }

    // Emitted when the window is closed.
    mainWindow.on('closed', function () {
        // Dereference the window object, usually you would store windows
        // in an array if your app supports multi windows, this is the time
        // when you should delete the corresponding element.
        mainWindow = null
    });
}

// app.commandLine.appendSwitch("--disable-http-cache");

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.on('ready', createWindow);

// Quit when all windows are closed.
app.on('window-all-closed', function () {
    // On OS X it is common for applications and their menu bar
    // to stay active until the user quits explicitly with Cmd + Q
    if (process.platform !== 'darwin') {
        app.quit()
    }
});

app.on('activate', function () {
    // On OS X it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (mainWindow === null) {
        createWindow()
    }
});

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
// 接收来自渲染进程的消息
ipcMain.on('loadPage', (event, arg) => {
    if (mainWindow) {
        mainWindow.loadURL(`file://${__dirname}/login.html`);
        // mainWindow.loadURL(`file://${__dirname}/param_BackupAndRestore.html`);
    }
});
ipcMain.on('asynchronous-message', (event, arg) => {
    console.log('asynchronous-message', arg); // prints "ping"
    // event.sender.send('asynchronous-reply', 'pong')
    if (arg === 'close') {
        console.log('关闭进程', mainWindow);
        if (mainWindow != null) {
            ui_util.messageBox(mainWindow, '通信异常', '与装置通信断开，请确认网络是否通畅。\n\n点击确定关闭人机客户端。');
            mainWindow.close();
        }
    } else if (arg === 'lock-timeout') {
        console.log('客户端超时，点击确定重启客户端', mainWindow);
        if (mainWindow != null) {
            ui_util.messageBox(mainWindow, '超时', '人机客户端长时间无操作，点击确定关闭人机客户端');
            mainWindow.close();
        }
    }
});

ipcMain.on('synchronous-message', (event, arg) => {
    console.log('synchronous-message', arg); // prints "ping"
    event.returnValue = 'pong';
});

ipcMain.on('capture_img', (event, rect, path) => {
    mainWindow.webContents.capturePage(rect, (image) => {
        const buff = image.toPNG();
        util.saveToFile(buff, path, false);
    })
});

ipcMain.on('capture_pdf', (event, pdfPath) => {
    const fs = require('fs');
    const shell = require('electron').shell;

    const win = BrowserWindow.fromWebContents(event.sender);
    win.webContents.printToPDF({}, (error, data) => {
        if (error) {
            return console.log(error.message);
        }

        fs.writeFile(pdfPath, data, err => {
            if (err) return console.log(err.message);
            shell.openExternal('file://' + pdfPath);
            event.sender.send('wrote-pdf', pdfPath);
        });
    })
});
