<!--&lt;!&ndash; element-plus &ndash;&gt;-->

<!--<script src="vue/admin/controller/port_param.js"></script>-->

<div class="main-content">
    <div class="main-content-inner">
        <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs" style="height: calc(100% - 100px);">
            <div class="page-content">
                <div class="page-header">
                    <h1>参数配置
                        <small>
                            <i class="ace-icon fa fa-angle-double-right"></i>
                            网口参数
                        </small>
                    </h1>
                </div>
                <div class="row" style="height: 100%">
                    <div id="app" style="height: 100%">
                        <div ref="box" style="height: 100%">
                            <div class="mainTitle">网口配置</div>
                            <el-form ref="formRef" :model="formData" :rules="formRules" style="width: 100%;">
                                <el-row style="padding: 5px">
                                    <el-table stripe  :height="tableHeight" :data="formData.netInter">
                                        <el-table-column label="网卡名" prop="interface">
                                            <template #default="scope">
                                                <el-form-item
                                                        style="width: 100%"
                                                        :prop="'netInter' + `[${scope.$index}]` + '.interface'"
                                                        :rules="formRules.interface"
                                                >
                                                    <template  v-if="true">
                                                        <span v-else>{{ scope.row.interface }}</span>
                                                        <!-- <el-input v-model="scope.row.interface" readonly/> -->
                                                    </template>
                                                </el-form-item>
                                            </template>
                                        </el-table-column>
                                        <el-table-column label="网口IP" prop="netIp">
                                            <template #default="scope">
                                                <el-form-item
                                                        style="width: 100%"
                                                        :prop="'netInter' + `[${scope.$index}]` + '.netIp'"
                                                        :rules="formRules.netIp"
                                                >
                                                    <template  v-if="!scope.row.area">
                                                        <el-input v-model="scope.row.netIp"/>
                                                    </template>
                                                    <span v-else></span>
                                                </el-form-item>
                                            </template>
                                        </el-table-column>
                                        <el-table-column label="子网掩码" prop="netmask">
                                            <template #default="scope">
                                                <el-form-item
                                                        style="width: 100%"
                                                        :prop="'netInter' + `[${scope.$index}]` + '.netmask'"
                                                        :rules="formRules.netmask"
                                                >
                                                    <template  v-if="!scope.row.area">
                                                        <el-input v-model="scope.row.netmask"/>
                                                    </template>
                                                    <span v-else></span>
                                                </el-form-item>
                                            </template>
                                        </el-table-column>
                                        <el-table-column label="网卡mtu值" prop="netMtu">
                                            <template #default="scope">
                                                <el-form-item
                                                        style="width: 100%"
                                                        :prop="'netInter' + `[${scope.$index}]` + '.netMtu'"
                                                        :rules="formRules.netMtu"
                                                >
                                                    <template  v-if="!scope.row.area">
                                                        <el-input v-model="scope.row.netMtu" type="number"/>
                                                    </template>
                                                    <span v-else></span>
                                                </el-form-item>
                                            </template>
                                        </el-table-column>
                                        <el-table-column label="采集区域" prop="area">
                                            <template #default="scope">
                                                <el-form-item
                                                        style="width: 100%"
                                                        :prop="'netInter' + `[${scope.$index}]` + '.area'"
                                                        :rules="formRules.area">
                                                    <el-select v-if="true" v-model="scope.row.area">
                                                        <el-option v-for="item in areaEnum" :key="item.code" :label="item.name" :value="item.code" />
                                                    </el-select>
                                                    <span v-else>{{ scope.row.area ? areaMap[scope.row.area] : '--' }}</span>
                                                </el-form-item>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </el-row>
                                <div class="mainTitle">采集规则</div>
                                <el-row>
                                    <el-table stripe :height="tableHeight" :data="formData.flowInter">
                                        <el-table-column label="流量采集口名" prop="interface">
                                            <template #default="scope">
                                                <el-form-item
                                                        style="width: 100%"
                                                        :prop="'flowInter' + `[${scope.$index}]` + '.interface'"
                                                        :rules="formRules.interface">
                                                    <!-- <span>{{ scope.row.interface ? areaMap[scope.row.interface] : '--' }}</span> -->
                                                    <span>{{ scope.row.interface }}</span>
                                                </el-form-item>
                                            </template>
                                        </el-table-column>
                                        <el-table-column label="采集区域" prop="area">
                                            <template #default="scope">
                                                <el-form-item
                                                        style="width: 100%"
                                                        :prop="'flowInter' + `[${scope.$index}]` + '.area'"
                                                        :rules="formRules.area">
                                                    <el-select v-if="true" v-model="scope.row.area">
                                                        <el-option v-for="item in areaEnum" :key="item.code" :label="item.name" :value="item.code" />
                                                    </el-select>
                                                    <span v-else>{{ scope.row.area ? areaMap[scope.row.area] : '--' }}</span>
                                                </el-form-item>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </el-row>
                                <div style="display: flex;align-items: center;justify-content: center;margin-top: 30px;">
                                    <!-- <el-button v-if="!isEdit" type="primary" @click="() => editFn('ON')">编辑</el-button> -->
                                    <!-- <el-button v-if="isEdit" type="warning" plain @click="() => editFn('OFF')">取消</el-button> -->
                                    <!--                                <el-button type="primary" @click="() => importExportStep1Handler('IMPORT')">导入</el-button>-->
                                    <el-button v-if="true" type="success" @click="() => editFn('SAVE')">保存</el-button>
                                    <!--                                <el-button type="primary" @click="() => importExportStep1Handler('EXPORT')">导出</el-button>-->
                                </div>
                            </el-form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /.main-content -->
    <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
        <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
    </a>
    <!-- 验证用户身份-->
   <div class="modal" id="validateUserDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
        aria-hidden="true">
       <form class="form-horizontal" id="validateUserForm" method="post" action="" data-toggle="validator" role="form">
           <div class="modal-dialog">
               <div class="modal-content">
                   <div class="modal-header" style="color: white;background: deepskyblue">
                       <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                       <h4 class="modal-title">验证用户身份</h4>
                   </div>
                   <div class="modal-body" style="padding: 10px 50px;">
                       <p>请输入登录密码</p>
                       <input type="hidden" id="opraType"/>
                       <input type="password" id="password" class="form-control" placeholder="密码" required/>
                       <div class="help-block with-errors"></div>
                   </div>
                   <div class="modal-footer">
                       <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                       <button type="submit" class="btn btn-primary" id="validateuser_btn">确定
                       </button>
                   </div>
               </div>
           </div>
       </form>
   </div>
    <!-- /.modal -->
    <!-- 导入导出选择对话框 -->
   <div class="modal fade" id="confirmPortDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
        aria-hidden="true">
       <form class="form-horizontal" id="confirmPortForm" method="post" action="" data-toggle="validator" role="form">
           <div class="modal-dialog" role="document">
               <div class="modal-content">
                   <div class="modal-header">
                       <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                           <span aria-hidden="true">&times;</span>
                           <span class="sr-only">Close</span>
                       </button>
                       <h4 class="modal-title" wa-name="import_tt">请选择</h4>
                   </div>
                   <div class="modal-body">
                       <div class="form-group">
                           <label class="col-sm-2 control-label text-right" for="select_file">请选择:</label>
                           <div class="col-sm-9">
                               <label class="ace-file-input">
                                   <input type="file" id="select_file" required/>
                               </label>
                           </div>
                       </div>
                       <div class="form-group hidden">
                           <label class="col-sm-2 control-label text-right" for="select_folder">请选择:</label>
                           <div class="col-sm-9">
                               <label class="ace-file-input">
                                   <input type="file" id="select_folder" webkitdirectory directory required/>
                               </label>
                           </div>
                       </div>
                   </div>
                   <div class="modal-footer">
                       <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                       <button type="button" class="btn btn-primary" id="port_btn">确定</button>
                   </div>
               </div>
           </div>
       </form>
   </div>
    <!-- /.modal -->
<!--    <script src="vue/admin/controller/port_param.js?t=<?=new Date().getTime()?>"></script>-->

</div>
<!-- 统一高度用 -->
<style>
    #app {
        display: flex;
        flex-direction: column;
        height: 100%;
        background-color: #f9f9f9;
    }
    .main-content {
        height: 100%;

    }
    .main-content-inner, .page-content, .row {
        height: 100%;
    }

    html, body {
        height: 100%;
        margin: 0;
        padding: 0;
    }

    #app {
        display: flex;
        flex-direction: column;
        height: 100%;
        background-color: #f9f9f9;
    }
</style>
<style>
    /* 去掉el-input内置的边框 */
    .el-input__inner {
        border: none !important;
    }
    .el-table__header th {
        border-bottom: 3px solid #e1e1e1 !important;
    }
    /* 表格最底部的线（蓝色，加粗） */
    .el-table::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        border-bottom: 2px solid #e1e1e1 !important;
    }
    /* .cell-select .el-select__wrapper {
        height: 26px;
        min-height: 26px;
        margin-left: -11px;
    } */

.el-table .el-table__row {
  height: 50px;
}
.mainTitle {
    font-size: 18px;
    color: #393939;
    width: 100px;
}
</style>
<link rel="stylesheet" href="lib/elementPlus/index.css"/>
<script src="lib/third/vue3/vue.global.js"></script>
<script src="lib/elementPlus/index.full.min.js"></script>
<script src="lib/elementPlus/zh-cn.js"></script>

<script>
    console.log(require.cache)
    delete require.cache[require.resolve('./vue/paramConf/MSG_NetworkInterfaceConfig')];
    require('./vue/paramConf/MSG_NetworkInterfaceConfig');
    // $('#handlerForm').validator();
</script>
