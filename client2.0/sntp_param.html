<!--&lt;!&ndash; element-plus &ndash;&gt;-->

<!--<script src="vue/admin/controller/port_param.js"></script>-->

<div class="main-content">
    <div class="main-content-inner">
        <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs">
            <div class="page-content">
                <div class="page-header">
                    <h1>参数配置
                        <small>
                            <i class="ace-icon fa fa-angle-double-right"></i>
                            SNTP参数
                        </small>
                    </h1>
                </div>
                <!-- /.page-header -->
                <div class="row">
                    <div id="app">
                        <div style="padding: 5px;width: 100%;display: flex;align-items: center;justify-content: center;">
                            <el-form ref="formRef" :model="sntpForm" :rules="formRules" label-width="140px">
                                <el-row>
                                    <el-form-item label="主时钟主网IP地址" prop="mainClockMainNet">
                                        <el-input v-model="sntpForm.mainClockMainNet" :disabled="!true" class="formItemWidth" placeholder="请输入目的网段"></el-input>
                                    </el-form-item>
                                </el-row>
                                <el-row>
                                    <el-form-item label="主时钟备网IP地址" prop="mainClockSubNet">
                                        <el-input v-model="sntpForm.mainClockSubNet" class="formItemWidth" :disabled="!true" placeholder="请输入目的网段"></el-input>
                                    </el-form-item>
                                </el-row>
                                <el-row>
                                    <el-form-item label="备时钟主网IP地址" prop="subClockMainNet">
                                        <el-input v-model="sntpForm.subClockMainNet" class="formItemWidth" :disabled="!true" placeholder="请输入目的网段"></el-input>
                                    </el-form-item>
                                </el-row>
                                <el-row>
                                    <el-form-item label="备时钟备网IP地址" prop="subClockSubNet">
                                        <el-input v-model="sntpForm.subClockSubNet" class="formItemWidth" :disabled="!true" placeholder="请输入目的网段"></el-input>
                                    </el-form-item>
                                </el-row>
                                <el-row>
                                    <el-form-item label="端口号" prop="ports">
                                        <el-input v-model="sntpForm.ports" placeholder="请输入目的网段" disabled class="formItemWidth"></el-input>
                                    </el-form-item>
                                </el-row>
                                <el-row>
                                    <el-form-item label="对时周期(秒)" prop="synPeriod">
                                        <el-input v-model="sntpForm.synPeriod" class="formItemWidth" :disabled="!true" placeholder="请输入1-65535整数，默认30s"></el-input>
                                    </el-form-item>
                                </el-row>
                                <el-row>
                                    <el-form-item label="是否采用广播" prop="broadcast">
                                        <el-select v-model="sntpForm.broadcast" class="formItemWidth" :disabled="!true">
                                            <el-option v-for="item in broadcastEnum" :key="item" :label="item.name" :value="item.code" />
                                        </el-select>
                                    </el-form-item>
                                </el-row>
                            </el-form>
                        </div>
                        <div style="display: flex;align-items: center;justify-content: center;margin-top: 30px;">
                            <!-- <el-button v-if="!isEdit" type="primary" @click="() => editFn('ON')">编辑</el-button> -->
                            <!-- <el-button v-if="isEdit" type="warning" plain @click="() => editFn('OFF')">取消</el-button> -->
<!--                            <el-button type="primary" @click="() => importExportStep1Handler('IMPORT')">导入</el-button>-->
                            <el-button v-if="true" type="success" @click="() => editFn('SAVE')">保存</el-button>
<!--                            <el-button type="primary" @click="() => importExportStep1Handler('EXPORT')">导出</el-button>-->
                        </div>
                    </div>
                    <!-- /.col -->
                </div>
                <!-- /.row -->
            </div>
            <!-- /.page-content -->
        </div>
    </div>
    <!-- /.main-content -->
    <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
        <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
    </a>
    <!-- 验证用户身份-->
   <div class="modal" id="validateUserDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
        aria-hidden="true">
       <form class="form-horizontal" id="validateUserForm" method="post" action="" data-toggle="validator" role="form">
           <div class="modal-dialog">
               <div class="modal-content">
                   <div class="modal-header" style="color: white;background: deepskyblue">
                       <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                       <h4 class="modal-title">验证用户身份</h4>
                   </div>
                   <div class="modal-body" style="padding: 10px 50px;">
                       <p>请输入登录密码</p>
                       <input type="hidden" id="opraType"/>
                       <input type="password" id="password" class="form-control" placeholder="密码" required/>
                       <div class="help-block with-errors"></div>
                   </div>
                   <div class="modal-footer">
                       <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                       <button type="submit" class="btn btn-primary" id="validateuser_btn">确定
                       </button>
                   </div>
               </div>
           </div>
       </form>
   </div>
    <!-- /.modal -->
    <!-- 导入导出选择对话框 -->
   <div class="modal fade" id="confirmPortDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
        aria-hidden="true">
       <form class="form-horizontal" id="confirmPortForm" method="post" action="" data-toggle="validator" role="form">
           <div class="modal-dialog" role="document">
               <div class="modal-content">
                   <div class="modal-header">
                       <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                           <span aria-hidden="true">&times;</span>
                           <span class="sr-only">Close</span>
                       </button>
                       <h4 class="modal-title" wa-name="import_tt">请选择</h4>
                   </div>
                   <div class="modal-body">
                       <div class="form-group">
                           <label class="col-sm-2 control-label text-right" for="select_file">请选择:</label>
                           <div class="col-sm-9">
                               <label class="ace-file-input">
                                   <input type="file" id="select_file" required/>
                               </label>
                           </div>
                       </div>
                       <div class="form-group hidden">
                           <label class="col-sm-2 control-label text-right" for="select_folder">请选择:</label>
                           <div class="col-sm-9">
                               <label class="ace-file-input">
                                   <input type="file" id="select_folder" webkitdirectory directory required/>
                               </label>
                           </div>
                       </div>
                   </div>
                   <div class="modal-footer">
                       <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                       <button type="button" class="btn btn-primary" id="port_btn">确定</button>
                   </div>
               </div>
           </div>
       </form>
   </div>
    <!-- /.modal -->
<!--    <script src="vue/admin/controller/port_param.js?t=<?=new Date().getTime()?>"></script>-->

</div>
<style>
    /* 去掉el-input内置的边框 */
    .el-input__inner {
        border: none !important;
    }
    .el-table__header th {
        border-bottom: 3px solid #e1e1e1 !important;
    }
    /* 表格最底部的线（蓝色，加粗） */
    .el-table::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        border-bottom: 2px solid #e1e1e1 !important;
    }
    .cell-input {
        height: 26px;
        margin-left: -10px;
    }

        .cell-select .el-select__wrapper {
            height: 26px;
            min-height: 26px;
            margin-left: -11px;
        }

.el-table .el-table__row {
  height: 50px;
}
.formItemWidth {
    width: 250px !important;
}
</style>
<link rel="stylesheet" href="lib/elementPlus/index.css"/>
<script src="lib/third/vue3/vue.global.js"></script>
<script src="lib/elementPlus/index.full.min.js"></script>
<script src="lib/elementPlus/zh-cn.js"></script>

<script>
    console.log(require.cache)
    delete require.cache[require.resolve('./vue/paramConf/MSG_SNTPConfig')];
    require('./vue/paramConf/MSG_SNTPConfig');
    // $('#handlerForm').validator();
</script>
