<script type="text/javascript">
  try {
    ace.settings.loadState('sidebar')
  } catch (e) {
  }
</script>
<ul class="l-b-nav" wa-name="operator_list">
  <li class data-id data-code="operational_monitor" data-tt="运行监测" data-nav-id="operational_monitor" data-nav-name="运行监测">
    <div class="l-b-navbox-1">
      <img src="assets/images/l-b-images/selected_bar.png">
    </div>
    <div class="list l-b-navbox-2" align="center">
      <img src="assets/images/l-b-images/status.png"><br>运行监测
      <input name="menuId" type="hidden" value>
      <input name="menuUrl" type="hidden" value>
    </div>
  </li>
  <li class data-id data-code="device_mgr" data-tt="设备管理" data-nav-id="device_mgr" data-nav-name="设备管理">
    <div class="l-b-navbox-1">
      <img src="assets/images/l-b-images/selected_bar.png">
    </div>
    <div class="list l-b-navbox-2" align="center">
      <img src="assets/images/l-b-images/asset.png"><br>设备管理
      <input name="menuId" type="hidden" value>
      <input name="menuUrl" type="hidden" value>
    </div>
  </li>
  <li class data-id data-code="data_acquire" data-tt="数据采集" data-nav-id="data_acquire" data-nav-name="数据采集">
    <div class="l-b-navbox-1">
      <img src="assets/images/l-b-images/selected_bar.png">
    </div>
    <div class="list l-b-navbox-2" align="center">
      <img src="assets/images/l-b-images/monitor.png"><br>数据采集
      <input name="menuId" type="hidden" value>
      <input name="menuUrl" type="hidden" value>
    </div>
  </li>
  <li class data-id data-code="data_analysis" data-tt="数据分析" data-nav-id="data_analysis" data-nav-name="数据分析">
    <div class="l-b-navbox-1">
      <img src="assets/images/l-b-images/selected_bar.png">
    </div>
    <div class="list l-b-navbox-2" align="center">
      <img src="assets/images/l-b-images/analyse.png"><br>数据分析
      <input name="menuId" type="hidden" value>
      <input name="menuUrl" type="hidden" value>
    </div>
  </li>
  <li class data-id data-code="baseline_check" data-tt="基线核查" data-nav-id="baseline_check" data-nav-name="基线核查">
    <div class="l-b-navbox-1">
      <img src="assets/images/l-b-images/selected_bar.png">
    </div>
    <div class="list l-b-navbox-2" align="center">
      <img src="assets/images/l-b-images/inspect.png"><br>基线核查
      <input name="menuId" type="hidden" value>
      <input name="menuUrl" type="hidden" value>
    </div>
  </li>
  <li class data-id data-code="rule_config" data-tt="规则配置" data-nav-id="rule_config" data-nav-name="规则配置">
    <div class="l-b-navbox-1">
      <img src="assets/images/l-b-images/selected_bar.png">
    </div>
    <div class="list l-b-navbox-2" align="center">
      <img src="assets/images/l-b-images/rule.png"><br>规则配置
      <input name="menuId" type="hidden" value>
      <input name="menuUrl" type="hidden" value>
    </div>
  </li>
  <li class data-id data-code="monitorobj_param_mgr" data-tt="监控对象参数管理" data-nav-id="monitorobj_param_mgr" data-nav-name="监控对象参数管理">
    <div class="l-b-navbox-1">
      <img src="assets/images/l-b-images/selected_bar.png">
    </div>
    <div class="list l-b-navbox-2" align="center">
      <img src="assets/images/l-b-images/log_query.png"><br>监控对象参数管理
      <input name="menuId" type="hidden" value>
      <input name="menuUrl" type="hidden" value>
    </div>
  </li>
  <li class data-id data-code="version_match" data-tt="版本管理"
      data-nav-id="version_match,version_verify,version_update"
      data-nav-name="版本匹配,版本校验,版本配置更新">
    <div class="l-b-navbox-1">
      <img src="assets/images/l-b-images/selected_bar.png">
    </div>
    <div class="list l-b-navbox-2" align="center">
      <img src="assets/images/l-b-images/log_query.png"><br>版本管理
      <input name="menuId" type="hidden" value>
      <input name="menuUrl" type="hidden" value>
    </div>
  </li>

  <!--<li class data-id data-code="X-scan" data-tt="漏洞扫描" data-nav-id="X-scan" data-nav-name="漏洞扫描">-->
    <!--<div class="l-b-navbox-1">-->
      <!--<img src="assets/images/l-b-images/selected_bar.png">-->
    <!--</div>-->
    <!--<div class="list l-b-navbox-2" align="center">-->
      <!--<img src="assets/images/l-b-images/inspect.png"><br>漏洞扫描-->
      <!--<input name="menuId" type="hidden" value>-->
      <!--<input name="menuUrl" type="hidden" value>-->
    <!--</div>-->
  <!--</li>-->
  <!--<li class data-id data-code="feature_data_update" data-tt="特征数据更新" data-nav-id="feature_data_update" data-nav-name="特征数据更新">-->
    <!--<div class="l-b-navbox-1">-->
      <!--<img src="assets/images/l-b-images/selected_bar.png">-->
    <!--</div>-->
    <!--<div class="list l-b-navbox-2" align="center">-->
      <!--<img src="assets/images/l-b-images/log_query.png"><br>特征数据更新-->
      <!--<input name="menuId" type="hidden" value>-->
      <!--<input name="menuUrl" type="hidden" value>-->
    <!--</div>-->
  <!--</li>-->
  <li class data-id data-code="device_ctrl" data-tt="设备控制" data-nav-id="device_ctrl" data-nav-name="设备控制">
    <div class="l-b-navbox-1">
      <img src="assets/images/l-b-images/selected_bar.png">
    </div>
    <div class="list l-b-navbox-2" align="center">
      <img src="assets/images/l-b-images/asset.png"><br>设备控制
      <input name="menuId" type="hidden" value>
      <input name="menuUrl" type="hidden" value>
    </div>
  </li>
</ul><!-- /.nav-list -->