"use strict";
const net = require('net');
const config = require('../config');

function send_safe_msg(safe_type, buffer, on_send) {
  // console.log('send_safe_msg', safe_type, buffer)
  let socket = new net.Socket();
  socket.connect(config.safe_port, '127.0.0.1', function () {
    let sendBuffer = Buffer.alloc(1 + 4 + buffer.length);
    let offset = 0;
    sendBuffer.writeInt8(safe_type, offset);
    offset++;
    sendBuffer.writeInt32BE(buffer.length, offset);
    offset += 4;
    for (let i = 0; i < buffer.length; i++) {
      sendBuffer.writeUInt8(buffer[i], offset);
      offset++;
    }
    socket.write(sendBuffer, function (err) {
      // console.log('send sm4 msg to java', sendBuffer, 'err', err);
    });
  });
  let recv_buffer = null;
  let body_size = 0;
  let write_cursor = 0;
  socket.on('data', function (data) {
    // console.log('got java response', data);
    let offset = 0;
    if (recv_buffer == null) {
      body_size = data.readUInt32BE(offset);
      recv_buffer = Buffer.alloc(body_size);
      offset += 4;
    }
    for (let i = offset; i < data.length; i++) {
      recv_buffer.writeUInt8(data.readUInt8(offset), write_cursor);
      offset++;
      write_cursor++;
    }
    if (body_size > 0 && write_cursor === body_size) {
      on_send(recv_buffer);
      recv_buffer = null;
      body_size = 0;
      write_cursor = 0;

      socket.destroy();
      console.log('socket.destroy');
    }
  });
  socket.on('error', function (data) {
    console.log('safe close', data);
  });
}

module.exports = {
  send_safe_msg,
  PACKET_TYPE_SET_PARAM: 0,
  PACKET_TYPE_ENCRYPT_SM4: 1,
  PACKET_TYPE_DECRYPT_SM4: 2,
  PACKET_TYPE_ENCRYPT_SM2: 3,
  PACKET_TYPE_DECRYPT_SM2: 4,
};