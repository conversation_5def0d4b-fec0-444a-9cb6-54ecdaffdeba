"use strict";
const util = require('./util');
const PublicDefine_pb = require('../script/pb/PublicDefine_pb');
const safe = require('./safe');

let DPacket = {
  type: -1,
  sub_type: -1,
  return_type: -1,
  return_value: -1,
  content_size: -1,
  content: null,

  content_cursor: 0,  // 内容游标，自定义字段，用于指示当前内容写入的索引，从0开始

  isComplete() {
    return this.content_size === this.content_cursor && this.content != null;
  },
  fillContent(data) {
    for (; this.content_cursor < data.length && this.content_cursor < this.content_size; this.content_cursor++) {
      this.content.writeUInt8(data[this.content_cursor]);
    }
  },
  clear() {
    this.type = -1;
    this.sub_type = -1;
    this.return_type = -1;
    this.return_value = -1;
    this.content_size = -1;
    this.content = null;
    this.content_cursor = 0;
  }
};

let EncryptMsg = {
  encrypt_type: -1,   // 加密类型
  content_size: 0,    // 加密报文长度
  content: null,      // 加密报文

  content_cursor: 0,  // 内容游标，自定义字段，用于指示当前内容写入的索引，从0开始

  isComplete() {
    return this.content_size === this.content_cursor && this.content != null;
  },

  clear() {
    this.encrypt_type = -1;
    this.content_size = 0;
    this.content = null;
    this.content_cursor = 0;
  }
};

let unpackDecryptMsg = function (message_handler) {
  let content = null;
  if (EncryptMsg.encrypt_type === PublicDefine_pb.PlatformType.PLATFORM_TYPE_ENCRYPTED_MESSAGE_SM2) {
    // console.log("EncryptMsg.content.toString()", EncryptMsg.content.toString())
    const encryptData = EncryptMsg.content.toString().substr(2);
    // console.log("EncryptMsg.content", EncryptMsg.content, EncryptMsg.content.toString(), encryptData)
    // content = util.sm2Decrypt(encryptData);
    // console.log("content", content, EncryptMsg)
    safe.send_safe_msg(safe.PACKET_TYPE_DECRYPT_SM2, Buffer.from(EncryptMsg.content), function (data) {
      genDP(data, message_handler);
    });
    EncryptMsg.clear();
    return;
  } else if (EncryptMsg.encrypt_type === PublicDefine_pb.PlatformType.PLATFORM_TYPE_ENCRYPTED_MESSAGE_SM4) {
    // console.log("EncryptMsg.content", EncryptMsg.content)
    safe.send_safe_msg(safe.PACKET_TYPE_DECRYPT_SM4, Buffer.from(EncryptMsg.content), function (data) {
      genDP(data, message_handler);
    });
    EncryptMsg.clear();
    return;

  } else if (EncryptMsg.encrypt_type === PublicDefine_pb.PlatformType.PLATFORM_TYPE_ENCRYPTED_MESSAGE_AES) {
    // console.log("EncryptMsg.content", EncryptMsg.content)
    content = util.commuDecrypt(EncryptMsg.content.toString());
  } else {
    content = EncryptMsg.content;
  }

  genDP(content, message_handler);
};

function genDP(content, message_handler) {
  if (content == null) {
    console.error('content == null');
    return  false;
  }
  if (content.length < 8) {
    console.error('content.length is too short', content);
    return  false;
  }
  EncryptMsg.clear();

  DPacket.clear();
  let offset = 0;
  // console.log('content in 93 = ', content)
  DPacket.type = content.readUInt8(offset);
  offset++;
  DPacket.sub_type = content.readUInt8(offset);
  offset++;
  DPacket.return_type = content.readUInt8(offset);
  offset++;
  DPacket.return_value = content.readUInt8(offset);
  offset++;
  DPacket.content_size = content.readUInt32BE(offset);
  offset += 4;
  DPacket.content = Buffer.alloc(DPacket.content_size);
  console.log("DPacket.content_size", DPacket.type, DPacket.sub_type, DPacket.content_size, DPacket, content.length)
  for (let i = 0; i < DPacket.content_size; i++) {
    DPacket.content.writeUInt8(content.readUInt8(offset), i);
    offset++;
  }
  // console.log("DPacket", DPacket)

  if (DPacket.type === util.PLATFORM_TYPE_GUI_MESSAGE) {
    if (DPacket.content.length < util.PACKET_USER_DEFINE_PACKET_ID_BYTE) {
      console.error('content is too short', DPacket.content);
      return
    }

    const msgId = (DPacket.content[0] << 8) + DPacket.content[1];
    const pb = DPacket.content.slice(util.PACKET_USER_DEFINE_PACKET_ID_BYTE);
    message_handler(msgId, pb);
  } else {
    message_handler(DPacket);
  }
  return true;
}

function decodeEncryptMsg(data, message_handler) {
  const buf_size = data.length;   // 本次收到的数据包长度
  let offset = 0;
  while (offset < buf_size) {
    if (EncryptMsg.encrypt_type === -1) {
      EncryptMsg.encrypt_type = data.readUInt8(offset);
      offset++;
    } else if (EncryptMsg.content_size === 0) {
      EncryptMsg.content_size = data.readInt32BE(offset);
      offset += 4;
      EncryptMsg.content = Buffer.alloc(EncryptMsg.content_size);
      console.log('EncryptMsg.content_size', EncryptMsg.content_size)
    } else {
      while (offset < buf_size) {
        if (!EncryptMsg.isComplete()) {
          if (EncryptMsg.content == null) {
            console.error("EncryptMsg.content == null");
          }
          EncryptMsg.content.writeUInt8(data[offset], EncryptMsg.content_cursor);
          offset++;
          EncryptMsg.content_cursor++
        } else {
          unpackDecryptMsg(message_handler);
          break;
        }
      }
    }
  }
  if (EncryptMsg.isComplete()) {
    unpackDecryptMsg(message_handler);
    return true;
  }
  return false;
}

function onMsg(data, message_handler) {
  const buf_size = data.length;   // 本次收到的数据包长度
  let offset = 0;

  // 解析出完整的DPacket报文
  while (offset < buf_size) {
    if (DPacket.type === -1) {
      DPacket.type = data.readUInt8(offset);
      offset++;
    }
    if (DPacket.sub_type === -1) {
      DPacket.sub_type = data.readUInt8(offset);
      offset++;
    }
    if (DPacket.return_type === -1) {
      DPacket.return_type = data.readUInt8(offset);
      offset++;
    }
    if (DPacket.return_value === -1) {
      DPacket.return_value = data.readUInt8(offset);
      offset++;
    }
    if (DPacket.content_size === -1) {
      DPacket.content_size = data.readUInt32BE(offset);
      offset += 4;

      DPacket.content = Buffer.alloc(DPacket.content_size);
    }
    if (!DPacket.isComplete()) {
      while (DPacket.content_cursor < DPacket.content_size && offset < buf_size) {
        DPacket.content.writeUInt8(data[offset], DPacket.content_cursor);
        offset++;
        DPacket.content_cursor++
      }
    }
    if (DPacket.isComplete()) {
      const content = DPacket.content;
      if (DPacket.type === util.PLATFORM_TYPE_GUI_MESSAGE) {
        if (content.length < util.PACKET_USER_DEFINE_PACKET_ID_BYTE) {
          console.error('content is too short', content);
          return true;
        }

        const msgId = (content[0] << 8) + content[1];
        const pb = content.slice(util.PACKET_USER_DEFINE_PACKET_ID_BYTE);
        message_handler(msgId, pb);
      } else {
        message_handler(DPacket);
      }
      DPacket.clear();
      // console.log('解析完成', DPacket);
      return true;
    } else {
      // console.log('解析...', DPacket);
    }
    return false;
  }
}

module.exports = {
  decodeEncryptMsg,
  onMsg
};
