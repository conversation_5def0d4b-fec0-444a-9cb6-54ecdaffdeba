/**
 * 端到端分包测试 - 模拟完整的编码-传输-解码-重组流程
 */

const { PacketUtils } = require('./packet-utils');

/**
 * 模拟网络传输的端到端测试
 */
function testEndToEndPacketFlow() {
    console.log('=== 端到端分包测试 ===');
    
    try {
        // 1. 创建大数据包 (150KB)
        const originalData = Buffer.alloc(150 * 1024);
        for (let i = 0; i < originalData.length; i++) {
            originalData[i] = i % 256; // 填充有意义的数据
        }
        
        const packetInfo = {
            dataContent: originalData,
            functionType: 0x01,
            packetType: 0x05,
            sendSeq: 1,
            receiveSeq: 0,
            bodySequence: 12345
        };
        
        console.log(`原始数据大小: ${originalData.length} 字节`);
        
        // 2. 编码（分包）
        const encodedSegments = PacketUtils.encode(packetInfo);
        console.log(`编码结果: ${Array.isArray(encodedSegments) ? encodedSegments.length + ' 个分包' : '单包'}`);
        
        if (!Array.isArray(encodedSegments)) {
            console.log('✗ 期望分包但得到单包');
            return false;
        }
        
        // 3. 模拟网络传输和接收
        const reassembler = new PacketUtils.PacketReassembler();
        const sessionId = 'test_session_e2e';
        let completePacket = null;
        
        console.log('开始模拟网络传输...');
        
        for (let i = 0; i < encodedSegments.length; i++) {
            const segmentBuffer = encodedSegments[i];
            console.log(`处理分包 ${i + 1}/${encodedSegments.length}, 大小: ${segmentBuffer.length} 字节`);
            
            // 4. 解码单个分包
            const decodedSegment = PacketUtils.decode(segmentBuffer);
            
            if (!decodedSegment) {
                console.log(`✗ 分包 ${i + 1} 解码失败`);
                return false;
            }
            
            console.log(`  解码成功: offset=${decodedSegment.body.offset}, dataLength=${decodedSegment.body.dataContent.length}, totalLength=${decodedSegment.body.totalContentLength}`);

            // 添加详细调试信息
            console.log(`  详细信息: bodyLength=${decodedSegment.bodyLength}, flags=${decodedSegment.flags}, sequence=${decodedSegment.body.sequence}`);
            
            // 5. 分包重组
            completePacket = reassembler.processPacket(sessionId, decodedSegment);
            
            if (completePacket) {
                console.log(`✓ 在第 ${i + 1} 个分包后重组完成`);
                break;
            } else {
                console.log(`  等待更多分包...`);
            }
        }
        
        // 6. 验证重组结果
        if (!completePacket) {
            console.log('✗ 分包重组失败');
            return false;
        }
        
        const reassembledData = completePacket.body.dataContent;
        console.log(`重组数据大小: ${reassembledData.length} 字节`);
        
        // 7. 验证数据完整性
        if (reassembledData.length !== originalData.length) {
            console.log(`✗ 数据长度不匹配: 期望 ${originalData.length}, 实际 ${reassembledData.length}`);
            return false;
        }
        
        // 逐字节比较
        for (let i = 0; i < originalData.length; i++) {
            if (originalData[i] !== reassembledData[i]) {
                console.log(`✗ 数据内容不匹配，位置 ${i}: 期望 ${originalData[i]}, 实际 ${reassembledData[i]}`);
                return false;
            }
        }
        
        console.log('✓ 数据完整性验证通过');
        console.log('✓ 端到端测试成功');
        
        return true;
        
    } catch (error) {
        console.error('✗ 端到端测试失败:', error.message);
        console.error(error.stack);
        return false;
    }
}

/**
 * 测试不同大小的数据包
 */
function testVariousDataSizes() {
    console.log('\n=== 测试不同大小的数据包 ===');
    
    const testSizes = [
        1024,      // 1KB - 单包
        32 * 1024, // 32KB - 单包
        64 * 1024, // 64KB - 单包边界
        65 * 1024, // 65KB - 分包
        128 * 1024, // 128KB - 分包
        256 * 1024  // 256KB - 多分包
    ];
    
    let passedTests = 0;
    
    for (const size of testSizes) {
        console.log(`\n测试 ${size / 1024}KB 数据包:`);
        
        try {
            // 创建测试数据
            const testData = Buffer.alloc(size);
            for (let i = 0; i < testData.length; i++) {
                testData[i] = (i * 7) % 256; // 使用不同的模式
            }
            
            const packetInfo = {
                dataContent: testData,
                functionType: 0x02,
                packetType: 0x06,
                sendSeq: 1,
                receiveSeq: 0,
                bodySequence: 54321
            };
            
            // 编码
            const encoded = PacketUtils.encode(packetInfo);
            const isSegmented = Array.isArray(encoded);
            
            console.log(`  编码: ${isSegmented ? encoded.length + ' 个分包' : '单包'}`);
            
            // 解码和重组
            const reassembler = new PacketUtils.PacketReassembler();
            const sessionId = `test_${size}`;
            let result = null;
            
            if (isSegmented) {
                for (const segment of encoded) {
                    const decoded = PacketUtils.decode(segment);
                    result = reassembler.processPacket(sessionId, decoded);
                    if (result) break;
                }
            } else {
                const decoded = PacketUtils.decode(encoded);
                result = reassembler.processPacket(sessionId, decoded);
            }
            
            // 验证
            if (result && result.body.dataContent.equals(testData)) {
                console.log(`  ✓ ${size / 1024}KB 测试通过`);
                passedTests++;
            } else {
                console.log(`  ✗ ${size / 1024}KB 测试失败`);
            }
            
        } catch (error) {
            console.log(`  ✗ ${size / 1024}KB 测试异常:`, error.message);
        }
    }
    
    console.log(`\n不同大小测试结果: ${passedTests}/${testSizes.length} 通过`);
    return passedTests === testSizes.length;
}

/**
 * 运行所有端到端测试
 */
function runEndToEndTests() {
    console.log('开始端到端分包测试...\n');
    
    const tests = [
        { name: '端到端分包流程', fn: testEndToEndPacketFlow },
        { name: '不同大小数据包', fn: testVariousDataSizes }
    ];
    
    let passedTests = 0;
    let totalTests = tests.length;
    
    for (const test of tests) {
        try {
            if (test.fn()) {
                passedTests++;
            }
        } catch (error) {
            console.error(`✗ 测试 "${test.name}" 抛出异常:`, error.message);
        }
    }
    
    console.log('\n=== 端到端测试结果 ===');
    console.log(`通过: ${passedTests}/${totalTests}`);
    console.log(`成功率: ${(passedTests / totalTests * 100).toFixed(1)}%`);
    
    if (passedTests === totalTests) {
        console.log('🎉 所有端到端测试通过！');
    } else {
        console.log('❌ 部分端到端测试失败，请检查实现');
    }
    
    return passedTests === totalTests;
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
    runEndToEndTests();
}

module.exports = {
    testEndToEndPacketFlow,
    testVariousDataSizes,
    runEndToEndTests
};
