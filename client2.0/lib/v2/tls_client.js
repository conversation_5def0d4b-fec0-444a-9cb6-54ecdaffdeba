const tls = require('tls');
const fs = require('fs');
const config = require('./server-config');
const { PacketUtils } = require('./packet-utils');
const PlatformProxyServer_pb = require("../../script/pb/PlatformProxyServer_pb");
const util = require('../util');
const {ipc<PERSON>enderer} = require("electron");

class PacketClient {
    constructor() {
        this.client = null;
        this.isConnected = false;
        this.receiveBuffer = Buffer.alloc(0); // 接收缓冲区，处理粘包/半包
        this.messageCallbacks = {}; // 存储不同功能类型或报文类型的回调函数
        this.packetReassembler = new PacketUtils.PacketReassembler(); // 分包重组器
        this.sessionId = Math.random().toString(36).substr(2, 9); // 生成会话ID

        this._initTlsOptions();
    }

    _initTlsOptions() {
        // TLS 选项配置
        this.tlsOptions = {
            // 如果需要验证服务端证书，需要提供CA证书
            ca: fs.readFileSync(config.certs.caCert),
            // TLS 版本强制为 1.3
            minVersion: 'TLSv1.3',
            maxVersion: 'TLSv1.3',
            // 禁用不安全的重协商
            rejectUnauthorized: true, // 生产环境应设置为true
            // 仅使用标准 TLS 1.3 密码套件，不再考虑国密套件
            ciphers: 'TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_128_GCM_SHA256',
            checkServerIdentity: (hostname, cert) => {
                //console.log('Server certificate details:', cert.subject);
                return undefined; // undefined 表示验证成功
            }
        };
    }

    /**
     * 连接到服务端
     * @returns {Promise<void>}
     * @param hostname 服务器地址
     * @param port  服务器端口
     */
    connect(hostname, port) {
        return new Promise((resolve, reject) => {
            this.client = tls.connect(port, hostname, this.tlsOptions, () => {
                if (this.client.authorized) {
                    console.log('TLS connection authorized.');
                    //console.log('TLS protocol version:', this.client.getProtocol());
                    //console.log('TLS cipher suite:', this.client.getCipher());
                    this.isConnected = true;

                    this.client.setNoDelay(true);
                    resolve();
                } else {
                    const error = this.client.authorizationError;
                    console.error('TLS connection authorization failed:', error);
                    this.isConnected = false;
                    this.client.destroy();
                    reject(error);
                }
            });

            this.client.on('data', this._handleReceivedData.bind(this));
            this.client.on('end', () => {
                console.log('Disconnected from server.');
                this.isConnected = false;
            });
            this.client.on('error', (err) => {
                console.error('Client error:', err);
                this.isConnected = false;
                reject(err);
            });
            this.client.on('close', () => {
                console.log('Connection closed.');
                this.isConnected = false;
                ipcRenderer.send('asynchronous-message', 'close');
            });
        });
    }

    /**
     * 发送报文
     * @param {object} packetInfo 报文信息对象
     * @param {Buffer} packetInfo.dataContent 报文内容
     * @param {number} packetInfo.functionType 功能类型
     * @param {number} packetInfo.packetType 报文类型
     * @param {number} [packetInfo.sendSeq=0] 发送序号
     * @param {number} [packetInfo.receiveSeq=0] 接收序号
     * @returns {Promise<void>}
     */
    async sendPacket(packetInfo) {
        if (!this.isConnected) {
            throw new Error('Client is not connected.');
        }

        const fullPacketInfo = {
            version: 0x0100, // 默认版本号 0x0100
            sendSeq: packetInfo.sendSeq || 0,
            receiveSeq: packetInfo.receiveSeq || 0,
            ...packetInfo,
        };

        try {
            const encodedResult = PacketUtils.encode(fullPacketInfo);

            // 检查是否为分包结果
            if (Array.isArray(encodedResult)) {
                // 分包发送
                for (let i = 0; i < encodedResult.length; i++) {
                    const segmentPacket = encodedResult[i];
                    await new Promise((resolve, reject) => {
                        this.client.write(segmentPacket, (err) => {
                            if (err) {
                                console.error(`Failed to send segment ${i + 1}/${encodedResult.length}:`, err);
                                reject(err);
                            } else {
                                console.log(`Sent segment ${i + 1}/${encodedResult.length}, size: ${segmentPacket.length}`);
                                resolve();
                            }
                        });
                    });
                }
                console.log(`Successfully sent ${encodedResult.length} segments`);
            } else {
                // 单包发送
                this.client.write(encodedResult, function (err) {
                    console.log('send msg', encodedResult.length, err);
                });
            }
        } catch (error) {
            console.error('Failed to encode or send packet:', error);
            throw error;
        }
    }

    async sendProtoMsg(messageId, proto) {
        // 提取最高3位（15-13位）并移动到新字节的高3位位置
        const high3Bits = (messageId >>> 13) & 0x07;

        // 构造第一个字节：高3位 + 固定低5位（11111）
        const functionType = (high3Bits << 5) | PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_GUI;

        // 第二个字节：直接取低8位
        const packetType = messageId & 0xFF;

        await this.sendPacket({
            dataContent: Buffer.from(proto.serializeBinary()), // JSON.stringify(proto.toObject())
            functionType: functionType,
            packetType: packetType,
            sendSeq: 0,
            receiveSeq: 0,
        });
    }

    async sendJsonMsg(functionType, packetType, json) {
        // const json = JSON.stringify(proto.toObject());
        await this.sendPacket({
            dataContent: Buffer.from(json),
            functionType: functionType,
            packetType: packetType,
            sendSeq: 0,
            receiveSeq: 0,
        });
    }

    /**
     * 发送平台类消息
     * @param functionType 功能类型
     * @param packetType 报文类型
     * @param proto 定义的平台结构，PlatformServer.proto中定义的
     * @returns {Promise<void>}
     */
    async sendPlatformMsg(functionType, packetType, proto) {
        await this.sendPacket({
            dataContent: Buffer.from(proto.serializeBinary()),
            functionType: functionType,
            packetType: packetType,
            sendSeq: 0,
            receiveSeq: 0,
        });
    }
    /**
     * 处理接收到的数据，解决粘包/半包问题
     * @param {Buffer} data
     * @private
     */
    _handleReceivedData(data) {
        this.receiveBuffer = Buffer.concat([this.receiveBuffer, data]);

        let decodedPacket = null;

        while (true) {
            try {
                decodedPacket = PacketUtils.decode(this.receiveBuffer);
            } catch (error) {
                console.error('Error decoding packet:', error);
                // 错误的报文，可能需要清除缓冲区或关闭连接
                this.receiveBuffer = Buffer.alloc(0);
                break;
            }

            if (decodedPacket) {
                // 将剩余数据移到缓冲区开头
                this.receiveBuffer = this.receiveBuffer.slice(decodedPacket.bytesConsumed);

                // 处理分包重组 - 对应后端的分包处理逻辑
                const completePacket = this.packetReassembler.processPacket(this.sessionId, decodedPacket);

                if (completePacket) {
                    // 分包重组完成或单包消息，分发完整消息
                    this._dispatchMessage(completePacket);
                }
                // 如果重组未完成，等待更多分包

            } else {
                // 报文不完整，等待更多数据
                break;
            }
        }
    }

    /**
     * 注册消息处理回调
     * @param {string} key 消息类型键 (例如: `${functionType}_${packetType}`)
     * @param {function(dataContent: Buffer, packetInfo: object)} callback 处理函数
     */
    onMessage(key, callback) {
        this.messageCallbacks[key] = (buffer, packbuffer) => {
            console.log('返回数据', buffer, packbuffer)
            callback(buffer, packbuffer)
        };
    }

    /**
     * 分发消息到注册的回调
     * @param {object} decodedPacket 解码后的报文对象
     * @private
     */
    _dispatchMessage(decodedPacket) {
        const { functionType, packetType, dataContent } = decodedPacket.body;
        let msgId = 0;
        if (functionType === PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_GUI) {
            // 提取高3位并恢复位置
            const high3Bits = (functionType & (~PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_GUI)) >>> 5;  // 0xE0 = 11100000

            // 组合恢复原始msgId值
            msgId = (high3Bits << 13) | // 高3位
                (packetType & 0xFF);  // 低8位
        } else {
            msgId = util.genServerMsgKey(functionType, packetType);
        }

        if (this.messageCallbacks[msgId]) {
            console.log(`recv msgId: ${msgId}`);
            this.messageCallbacks[msgId](decodedPacket.body.dataContent);//dataContent, decodedPacket
        } else {
            console.warn(`No handler registered for message type: ${msgId}. Data:`, dataContent.toString('hex'));
        }
    }

    /**
     * 关闭连接
     */
    disconnect() {
        if (this.client) {
            this.client.end();
            this.client = null;
        }
    }
}

console.log("init PacketClient")
const tcpInstance = new PacketClient();
module.exports = tcpInstance;
//module.exports = PacketClient;
// module.exports = {
//     tcpClient
// };
