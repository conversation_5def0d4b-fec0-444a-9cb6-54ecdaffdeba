/**
 * 测试 onMessage 只触发一次的行为
 */

const { PacketUtils } = require('./packet-utils');

/**
 * 测试 onMessage 触发次数
 */
function testOnMessageTriggerCount() {
    console.log('=== 测试 onMessage 触发次数 ===');
    
    try {
        // 创建大数据包，确保会分包
        const largeData = Buffer.alloc(200 * 1024); // 200KB
        for (let i = 0; i < largeData.length; i++) {
            largeData[i] = i % 256;
        }
        
        const packetInfo = {
            dataContent: largeData,
            functionType: 0x01,
            packetType: 0x05,
            sendSeq: 1,
            receiveSeq: 0,
            bodySequence: 99999
        };
        
        console.log(`原始数据大小: ${largeData.length} 字节`);
        
        // 1. 编码（分包）
        const encodedSegments = PacketUtils.encode(packetInfo);
        console.log(`编码结果: ${encodedSegments.length} 个分包`);
        
        // 2. 模拟接收和重组，统计触发次数
        const reassembler = new PacketUtils.PacketReassembler();
        const sessionId = 'test_onmessage';
        let onMessageTriggerCount = 0;
        let receivedData = null;
        
        // 模拟 onMessage 回调
        function mockOnMessage(dataContent) {
            onMessageTriggerCount++;
            receivedData = dataContent;
            console.log(`onMessage 第 ${onMessageTriggerCount} 次触发，数据长度: ${dataContent.length}`);
        }
        
        // 3. 逐个处理分包
        for (let i = 0; i < encodedSegments.length; i++) {
            console.log(`\n处理分包 ${i + 1}/${encodedSegments.length}:`);
            
            // 解码分包
            const decodedSegment = PacketUtils.decode(encodedSegments[i]);
            console.log(`  解码成功: offset=${decodedSegment.body.offset}, length=${decodedSegment.body.dataContent.length}`);
            
            // 分包重组
            const completePacket = reassembler.processPacket(sessionId, decodedSegment);
            
            if (completePacket) {
                console.log(`  重组完成！`);
                // 模拟 _dispatchMessage 调用 onMessage
                mockOnMessage(completePacket.body.dataContent);
            } else {
                console.log(`  等待更多分包...`);
            }
        }
        
        // 4. 验证结果
        console.log(`\n=== 测试结果 ===`);
        console.log(`onMessage 触发次数: ${onMessageTriggerCount}`);
        console.log(`期望触发次数: 1`);
        
        if (onMessageTriggerCount === 1) {
            console.log('✓ onMessage 正确地只触发了一次');
            
            // 验证数据完整性
            if (receivedData && receivedData.equals(largeData)) {
                console.log('✓ 接收到的数据完整正确');
                return true;
            } else {
                console.log('✗ 接收到的数据不完整或不正确');
                return false;
            }
        } else {
            console.log(`✗ onMessage 触发次数错误，期望 1 次，实际 ${onMessageTriggerCount} 次`);
            return false;
        }
        
    } catch (error) {
        console.error('✗ 测试失败:', error.message);
        return false;
    }
}

/**
 * 测试单包情况的 onMessage 触发
 */
function testSinglePacketOnMessage() {
    console.log('\n=== 测试单包 onMessage 触发 ===');
    
    try {
        // 创建小数据包，确保是单包
        const smallData = Buffer.from('Hello, this is a single packet message!');
        
        const packetInfo = {
            dataContent: smallData,
            functionType: 0x02,
            packetType: 0x06,
            sendSeq: 1,
            receiveSeq: 0,
            bodySequence: 88888
        };
        
        console.log(`原始数据大小: ${smallData.length} 字节`);
        
        // 1. 编码
        const encoded = PacketUtils.encode(packetInfo);
        console.log(`编码结果: ${Array.isArray(encoded) ? encoded.length + ' 个分包' : '单包'}`);
        
        // 2. 解码和处理
        const reassembler = new PacketUtils.PacketReassembler();
        const sessionId = 'test_single';
        let onMessageTriggerCount = 0;
        let receivedData = null;
        
        function mockOnMessage(dataContent) {
            onMessageTriggerCount++;
            receivedData = dataContent;
            console.log(`onMessage 触发，数据长度: ${dataContent.length}`);
        }
        
        // 解码
        const decodedPacket = PacketUtils.decode(encoded);
        console.log(`解码成功: offset=${decodedPacket.body.offset}, length=${decodedPacket.body.dataContent.length}, total=${decodedPacket.body.totalContentLength}`);
        
        // 处理（应该直接返回完整包）
        const completePacket = reassembler.processPacket(sessionId, decodedPacket);
        
        if (completePacket) {
            mockOnMessage(completePacket.body.dataContent);
        }
        
        // 3. 验证结果
        console.log(`\n=== 单包测试结果 ===`);
        console.log(`onMessage 触发次数: ${onMessageTriggerCount}`);
        
        if (onMessageTriggerCount === 1 && receivedData && receivedData.equals(smallData)) {
            console.log('✓ 单包 onMessage 正确触发一次，数据完整');
            return true;
        } else {
            console.log('✗ 单包测试失败');
            return false;
        }
        
    } catch (error) {
        console.error('✗ 单包测试失败:', error.message);
        return false;
    }
}

/**
 * 运行所有测试
 */
function runOnMessageTests() {
    console.log('开始测试 onMessage 触发行为...\n');
    
    const tests = [
        { name: '分包 onMessage 触发', fn: testOnMessageTriggerCount },
        { name: '单包 onMessage 触发', fn: testSinglePacketOnMessage }
    ];
    
    let passedTests = 0;
    
    for (const test of tests) {
        try {
            if (test.fn()) {
                passedTests++;
            }
        } catch (error) {
            console.error(`✗ 测试 "${test.name}" 抛出异常:`, error.message);
        }
    }
    
    console.log('\n=== 最终测试结果 ===');
    console.log(`通过: ${passedTests}/${tests.length}`);
    
    if (passedTests === tests.length) {
        console.log('🎉 所有 onMessage 测试通过！');
        console.log('✓ onMessage 确保只在收到完整数据后触发一次');
    } else {
        console.log('❌ 部分测试失败');
    }
    
    return passedTests === tests.length;
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
    runOnMessageTests();
}

module.exports = {
    testOnMessageTriggerCount,
    testSinglePacketOnMessage,
    runOnMessageTests
};
