/**
 * 新版分包功能测试 - 严格按照后端逻辑实现
 */

const { PacketUtils, PlatformPacket } = require('./packet-utils');

/**
 * 测试PlatformPacket基本功能
 */
function testPlatformPacket() {
    console.log('=== 测试PlatformPacket基本功能 ===');
    
    try {
        const packet = new PlatformPacket();
        
        // 设置基本属性
        packet.sendSeq = 1;
        packet.receiveSeq = 0;
        packet.functionType = 0x01;
        packet.packetType = 0x02;
        packet.packetSeq = 12345;
        
        // 设置数据内容
        const testData = Buffer.from('Hello, World! This is a test message.');
        packet.setDataContent(testData);
        
        console.log('✓ PlatformPacket创建成功');
        console.log(`  数据长度: ${packet.dataTotalLength}`);
        console.log(`  报文体长度: ${packet.bodyLength}`);
        console.log(`  完整报文长度: ${packet.length()}`);
        
        // 测试转换为字节数组
        const packetBytes = packet.packetToBytes();
        console.log(`✓ 转换为字节数组成功，长度: ${packetBytes.length}`);
        
        return true;
    } catch (error) {
        console.error('✗ PlatformPacket测试失败:', error.message);
        return false;
    }
}

/**
 * 测试分包编码功能
 */
function testPacketSegmentation() {
    console.log('\n=== 测试分包编码功能 ===');
    
    try {
        // 创建大数据包 (100KB)
        const largeData = Buffer.alloc(100 * 1024, 'A');
        
        const packetInfo = {
            dataContent: largeData,
            sendSeq: 1,
            receiveSeq: 0,
            functionType: 0x01,
            packetType: 0x02,
            bodySequence: 12345
        };
        
        console.log(`原始数据大小: ${largeData.length} 字节`);
        console.log(`MAX_DATA_CONTENT_PER_SEGMENT: ${require('./packet-utils').MAX_DATA_CONTENT_PER_SEGMENT} 字节`);

        const result = PacketUtils.encode(packetInfo);

        if (Array.isArray(result)) {
            console.log(`✓ 数据被分成 ${result.length} 个包`);

            let totalSize = 0;
            result.forEach((segment, index) => {
                totalSize += segment.length;
                console.log(`  分包 ${index + 1}: ${segment.length} 字节`);
            });

            console.log(`✓ 总大小: ${totalSize} 字节`);
            return true;
        } else {
            console.log('✗ 期望分包但返回单包');
            return false;
        }
    } catch (error) {
        console.error('✗ 分包编码测试失败:', error.message);
        return false;
    }
}

/**
 * 测试单包编码功能
 */
function testSinglePacketEncoding() {
    console.log('\n=== 测试单包编码功能 ===');
    
    try {
        // 创建小数据包 (1KB)
        const smallData = Buffer.alloc(1024, 'B');
        
        const packetInfo = {
            dataContent: smallData,
            sendSeq: 1,
            receiveSeq: 0,
            functionType: 0x01,
            packetType: 0x03,
            bodySequence: 12346
        };
        
        const result = PacketUtils.encode(packetInfo);
        
        if (Buffer.isBuffer(result)) {
            console.log(`✓ 单包编码成功，大小: ${result.length} 字节`);
            return true;
        } else {
            console.log('✗ 期望单包但返回分包数组');
            return false;
        }
    } catch (error) {
        console.error('✗ 单包编码测试失败:', error.message);
        return false;
    }
}

/**
 * 测试解码功能
 */
function testPacketDecoding() {
    console.log('\n=== 测试解码功能 ===');
    
    try {
        // 创建测试数据
        const testData = Buffer.from('Hello, World! This is a test message for decoding.');
        
        const packetInfo = {
            dataContent: testData,
            sendSeq: 1,
            receiveSeq: 0,
            functionType: 0x01,
            packetType: 0x04,
            bodySequence: 12347
        };
        
        // 编码
        const encodedPacket = PacketUtils.encode(packetInfo);
        console.log(`✓ 编码成功，大小: ${encodedPacket.length} 字节`);
        
        // 解码
        const decodedPacket = PacketUtils.decode(encodedPacket);
        
        if (decodedPacket && decodedPacket.body) {
            const receivedData = decodedPacket.body.dataContent;
            
            if (receivedData.equals(testData)) {
                console.log('✓ 解码成功，数据完整');
                console.log(`✓ 功能类型: ${decodedPacket.body.functionType}`);
                console.log(`✓ 报文类型: ${decodedPacket.body.packetType}`);
                console.log(`✓ 数据长度: ${receivedData.length}`);
                return true;
            } else {
                console.log('✗ 解码数据不匹配');
                return false;
            }
        } else {
            console.log('✗ 解码失败，返回null或无效数据');
            return false;
        }
    } catch (error) {
        console.error('✗ 解码测试失败:', error.message);
        return false;
    }
}

/**
 * 测试分包重组器
 */
function testPacketReassembler() {
    console.log('\n=== 测试分包重组器 ===');
    
    try {
        const reassembler = new PacketUtils.PacketReassembler();
        const sessionId = 'test_session_001';
        
        // 创建模拟的分包数据
        const totalMessage = 'This is a very long message that will be split into multiple segments for testing the packet reassembler functionality.';
        const part1 = 'This is a very long message ';
        const part2 = 'that will be split into ';
        const part3 = 'multiple segments for testing ';
        const part4 = 'the packet reassembler functionality.';
        
        const segments = [
            {
                body: {
                    functionType: 0x01,
                    packetType: 0x05,
                    sequence: 12348,
                    totalContentLength: totalMessage.length,
                    offset: 0,
                    dataContent: Buffer.from(part1)
                },
                timestamp: Date.now()
            },
            {
                body: {
                    functionType: 0x01,
                    packetType: 0x05,
                    sequence: 12348,
                    totalContentLength: totalMessage.length,
                    offset: part1.length,
                    dataContent: Buffer.from(part2)
                },
                timestamp: Date.now()
            },
            {
                body: {
                    functionType: 0x01,
                    packetType: 0x05,
                    sequence: 12348,
                    totalContentLength: totalMessage.length,
                    offset: part1.length + part2.length,
                    dataContent: Buffer.from(part3)
                },
                timestamp: Date.now()
            },
            {
                body: {
                    functionType: 0x01,
                    packetType: 0x05,
                    sequence: 12348,
                    totalContentLength: totalMessage.length,
                    offset: part1.length + part2.length + part3.length,
                    dataContent: Buffer.from(part4)
                },
                timestamp: Date.now()
            }
        ];
        
        let completePacket = null;
        
        // 逐个处理分包
        for (let i = 0; i < segments.length; i++) {
            console.log(`处理分包 ${i + 1}: "${segments[i].body.dataContent.toString()}"`);
            completePacket = reassembler.processPacket(sessionId, segments[i]);
            
            if (completePacket) {
                console.log(`✓ 在第 ${i + 1} 个分包后重组完成`);
                break;
            } else {
                console.log(`  等待更多分包...`);
            }
        }
        
        if (completePacket) {
            const reassembledData = completePacket.body.dataContent.toString();
            
            if (reassembledData === totalMessage) {
                console.log('✓ 分包重组成功');
                console.log(`✓ 重组数据长度: ${reassembledData.length}`);
                return true;
            } else {
                console.log('✗ 重组数据不匹配');
                console.log(`期望: "${totalMessage}"`);
                console.log(`实际: "${reassembledData}"`);
                return false;
            }
        } else {
            console.log('✗ 分包重组失败');
            return false;
        }
    } catch (error) {
        console.error('✗ 分包重组器测试失败:', error.message);
        return false;
    }
}

/**
 * 运行所有测试
 */
function runAllTests() {
    console.log('开始新版分包功能测试...\n');
    
    const tests = [
        { name: 'PlatformPacket基本功能', fn: testPlatformPacket },
        { name: '分包编码', fn: testPacketSegmentation },
        { name: '单包编码', fn: testSinglePacketEncoding },
        { name: '解码功能', fn: testPacketDecoding },
        { name: '分包重组器', fn: testPacketReassembler }
    ];
    
    let passedTests = 0;
    let totalTests = tests.length;
    
    for (const test of tests) {
        try {
            if (test.fn()) {
                passedTests++;
            }
        } catch (error) {
            console.error(`✗ 测试 "${test.name}" 抛出异常:`, error.message);
        }
    }
    
    console.log('\n=== 测试结果 ===');
    console.log(`通过: ${passedTests}/${totalTests}`);
    console.log(`成功率: ${(passedTests / totalTests * 100).toFixed(1)}%`);
    
    if (passedTests === totalTests) {
        console.log('🎉 所有测试通过！');
    } else {
        console.log('❌ 部分测试失败，请检查实现');
    }
    
    return passedTests === totalTests;
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
    runAllTests();
}

module.exports = {
    testPlatformPacket,
    testPacketSegmentation,
    testSinglePacketEncoding,
    testPacketDecoding,
    testPacketReassembler,
    runAllTests
};
