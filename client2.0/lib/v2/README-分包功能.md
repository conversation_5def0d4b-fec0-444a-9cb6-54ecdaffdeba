# 前端分包功能实现

## 概述

本实现严格按照后端Java代码逻辑，实现了完整的分包编码、解码和重组功能。

## 核心文件

### 1. packet-utils.js
主要的分包工具类，包含：
- `PlatformPacket` 类：对应后端的PlatformPacket类
- `PacketUtils` 类：编解码工具类
- `PacketReassembler` 类：分包重组器

### 2. tls_client.js
TLS客户端，集成了分包功能：
- 自动处理分包发送
- 自动处理分包接收和重组

## 主要特性

### 1. 报文格式
严格按照后端定义的报文格式：
```
报文头 (10字节固定 + 可选IV):
- 启动字符: 0x70 (1字节)
- 版本号: 0x01, 0x00 (2字节)
- 标志位: flags (1字节)
- 报文体长度: bodyLength (2字节)
- 发送序号: sendSeq (2字节)
- 接收序号: receiveSeq (2字节)
- IV值: 16字节 (可选，加密时)

报文体 (18字节固定 + 数据内容):
- 数据总长度: dataTotalLength (4字节)
- 数据偏移量: dataOffset (4字节)
- 报文序号: packetSeq (8字节)
- 功能类型: functionType (1字节)
- 报文类型: packetType (1字节)
- 数据内容: dataContent (N字节)
- 填充值: padding (可选)

报文尾 (可选):
- MAC值: 32字节 (可选)
- 签名值: 64字节 (可选)
```

### 2. 标志位支持
- bit7 (0x80): 对称加密开关
- bit6 (0x40): 签名开关
- bit5 (0x20): MAC开关
- bit4 (0x10): 压缩开关

### 3. 分包逻辑
- 最大分包大小: 64KB (MAX_DATA_CONTENT_PER_SEGMENT)
- 自动分包: 数据超过64KB时自动分包
- 分包重组: 自动重组接收到的分包

## 使用示例

### 1. 基本使用
```javascript
const { PacketUtils } = require('./packet-utils');

// 发送小数据包 (自动单包)
const smallPacketInfo = {
    dataContent: Buffer.from('Hello, World!'),
    functionType: 0x01,
    packetType: 0x02,
    sendSeq: 1,
    receiveSeq: 0
};

const encodedPacket = PacketUtils.encode(smallPacketInfo);
// encodedPacket 是 Buffer

// 发送大数据包 (自动分包)
const largeData = Buffer.alloc(100 * 1024, 'A'); // 100KB
const largePacketInfo = {
    dataContent: largeData,
    functionType: 0x01,
    packetType: 0x03,
    sendSeq: 1,
    receiveSeq: 0
};

const encodedSegments = PacketUtils.encode(largePacketInfo);
// encodedSegments 是 Buffer[] 数组
```

### 2. 使用TLS客户端
```javascript
const tcpClient = require('./tls_client');

// 连接服务器
await tcpClient.connect('localhost', 8443);

// 发送普通数据包
await tcpClient.sendPacket({
    dataContent: Buffer.from('Normal message'),
    functionType: 0x01,
    packetType: 0x01
});

// 发送大数据包 (自动分包)
const largeData = Buffer.alloc(200 * 1024, 'B'); // 200KB
await tcpClient.sendPacket({
    dataContent: largeData,
    functionType: 0x02,
    packetType: 0x01
});

// 注册消息处理器
tcpClient.onMessage('1_1', (dataContent) => {
    console.log('收到消息:', dataContent.toString());
});
```

### 3. 解码数据包
```javascript
// 解码单个数据包
const decodedPacket = PacketUtils.decode(receivedBuffer);
if (decodedPacket) {
    console.log('功能类型:', decodedPacket.body.functionType);
    console.log('报文类型:', decodedPacket.body.packetType);
    console.log('数据内容:', decodedPacket.body.dataContent);
}
```

### 4. 分包重组
```javascript
const reassembler = new PacketUtils.PacketReassembler();

// 处理接收到的分包
const completePacket = reassembler.processPacket(sessionId, decodedPacket);
if (completePacket) {
    // 分包重组完成
    console.log('完整数据:', completePacket.body.dataContent);
}
```

## 配置参数

### 常量定义
```javascript
const HEADER_FIXED_SIZE = 10;           // 报文头固定大小
const BODY_FIXED_SIZE = 18;             // 报文体固定大小
const MAX_DATA_CONTENT_PER_SEGMENT = 65536; // 最大分包大小 64KB
const MAX_PACKET_SIZE = 16777216;       // 最大报文大小 16MB
const IV_SIZE = 16;                     // IV值大小
const MAC_SIZE = 32;                    // MAC值大小
const SIGN_SIZE = 64;                   // 签名值大小
```

### 标志位常量
```javascript
const ENCRYPTION_FLAG = 0x80;   // 加密标志
const SIGNATURE_FLAG = 0x40;    // 签名标志
const MAC_FLAG = 0x20;          // MAC标志
const COMPRESSION_FLAG = 0x10;  // 压缩标志
```

## 测试

运行测试文件验证功能：
```bash
cd client2.0/lib/v2
node packet-test-v2.js
```

测试包括：
1. PlatformPacket基本功能测试
2. 分包编码测试
3. 单包编码测试
4. 解码功能测试
5. 分包重组器测试

## 注意事项

1. **数据大小限制**: 单个分包最大64KB，总数据最大16MB
2. **报文体长度**: 必须在16位整数范围内 (0-65535)
3. **分包重组**: 自动处理，无需手动干预
4. **错误处理**: 包含完整的错误检查和处理
5. **内存管理**: 自动清理超时的分包缓存

## 与后端的对应关系

| 前端 | 后端 | 说明 |
|------|------|------|
| PlatformPacket | PlatformPacket | 报文对象 |
| PacketUtils.encode() | PacketEncoder.encode() | 编码方法 |
| PacketUtils.decode() | PacketDecoder.decodeSinglePacket() | 解码方法 |
| PacketReassembler | PacketDecoder.doDecode() | 分包重组 |
| copyForSegment() | copyForSegment() | 分包属性拷贝 |
| packetToBytes() | packetToBytes() | 报文转字节数组 |

## 扩展功能

### 1. 加密支持 (TODO)
- SM4对称加密
- PKCS7填充

### 2. 签名支持 (TODO)
- SM2数字签名

### 3. MAC支持 (TODO)
- HMAC-SM3

### 4. 压缩支持 (TODO)
- XZ格式压缩

这些功能的接口已预留，可根据需要实现。
