// packet-utils.js

// 报文固定长度
const PACKET_HEADER_START_BYTE_LEN = 1;
const PACKET_HEADER_VERSION_LEN = 2;
const PACKET_HEADER_FLAG_LEN = 1;
const PACKET_HEADER_CURRENT_LENGTH_LEN = 2;
const PACKET_HEADER_SEND_SEQ_LEN = 2;
const PACKET_HEADER_RECEIVE_SEQ_LEN = 2;
// IV值、MAC值、签名值相关的长度常量将不再使用

const PACKET_BODY_TOTAL_CONTENT_LENGTH_LEN = 4;
const PACKET_BODY_OFFSET_LEN = 4;
const PACKET_BODY_SEQUENCE_LEN = 8;
const PACKET_BODY_FUNCTION_TYPE_LEN = 1;
const PACKET_BODY_PACKET_TYPE_LEN = 1;

// 标志位定义 (所有位都将设置为0，但保留常量以供理解)
const FLAG_ENCRYPT_BIT = 0x80; // bit7: 对称加密开关 (将关闭)
const FLAG_SIGN_BIT = 0x40;    // bit6: 签名开关 (将关闭)
const FLAG_MAC_BIT = 0x20;     // bit5: MAC开关 (将关闭)
const FLAG_COMPRESS_BIT = 0x10; // bit4: 压缩开关 (将关闭)

class PacketUtils {
    /**
     * 编码报文
     * @param {object} packetInfo 报文信息对象
     * @param {Buffer} packetInfo.dataContent 报文内容（原始数据）
     * @param {number} packetInfo.version 版本号
     * @param {number} packetInfo.sendSeq 发送序号
     * @param {number} packetInfo.receiveSeq 接收序号
     * @param {number} packetInfo.functionType 功能类型
     * @param {number} packetInfo.packetType 报文类型
     * @returns {Buffer} 编码后的报文Buffer
     */
    static encode(packetInfo) {
        const {
            dataContent,
            version,
            sendSeq,
            receiveSeq,
            functionType,
            packetType,
        } = packetInfo;

        // 1. 构建报文体
        const bodyBufferParts = [];

        // 数据内容总长度
        const totalContentLengthBuffer = Buffer.alloc(PACKET_BODY_TOTAL_CONTENT_LENGTH_LEN);
        totalContentLengthBuffer.writeUInt32BE(dataContent.length, 0);
        bodyBufferParts.push(totalContentLengthBuffer);

        // 报文偏移量 (通常为0)
        const offsetBuffer = Buffer.alloc(PACKET_BODY_OFFSET_LEN);
        offsetBuffer.writeUInt32BE(0, 0); // 偏移量为0
        bodyBufferParts.push(offsetBuffer);

        // 报文序号
        const bodySeqBuffer = Buffer.alloc(PACKET_BODY_SEQUENCE_LEN);
        bodySeqBuffer.writeBigUInt64BE(BigInt(packetInfo.bodySequence || 0), 0);
        bodyBufferParts.push(bodySeqBuffer);

        // 功能类型
        bodyBufferParts.push(Buffer.from([functionType]));

        // 报文类型
        bodyBufferParts.push(Buffer.from([packetType]));

        // 数据内容
        bodyBufferParts.push(dataContent);

        const fullBodyBuffer = Buffer.concat(bodyBufferParts);

        // 2. 构建报文头
        const headerBufferParts = [];

        // 启动字符
        headerBufferParts.push(Buffer.from([0x70]));

        // 版本号
        const versionBuffer = Buffer.alloc(PACKET_HEADER_VERSION_LEN);
        versionBuffer.writeUInt16BE(version, 0);
        headerBufferParts.push(versionBuffer);

        // 标志位 (所有标志位都关闭)
        const flags = 0x00;
        headerBufferParts.push(Buffer.from([flags]));

        // 当前报文长度 (报文体长度)
        const currentPacketLengthBuffer = Buffer.alloc(PACKET_HEADER_CURRENT_LENGTH_LEN);
        currentPacketLengthBuffer.writeUInt16BE(fullBodyBuffer.length, 0);
        headerBufferParts.push(currentPacketLengthBuffer);

        // 发送序号
        const sendSeqBuffer = Buffer.alloc(PACKET_HEADER_SEND_SEQ_LEN);
        sendSeqBuffer.writeUInt16BE(sendSeq, 0);
        headerBufferParts.push(sendSeqBuffer);

        // 接收序号
        const receiveSeqBuffer = Buffer.alloc(PACKET_HEADER_RECEIVE_SEQ_LEN);
        receiveSeqBuffer.writeUInt16BE(receiveSeq, 0);
        headerBufferParts.push(receiveSeqBuffer);

        const headerBuffer = Buffer.concat(headerBufferParts);

        // 3. 最终组装
        return Buffer.concat([headerBuffer, fullBodyBuffer]);
    }

    /**
     * 解码报文
     * @param {Buffer} buffer 接收到的完整报文Buffer
     * @returns {object} 解析后的报文信息对象，或null表示数据不完整/错误
     */
    static decode(buffer) {
        // 最小报文头长度
        const minHeaderLength = PACKET_HEADER_START_BYTE_LEN + PACKET_HEADER_VERSION_LEN + PACKET_HEADER_FLAG_LEN +
            PACKET_HEADER_CURRENT_LENGTH_LEN + PACKET_HEADER_SEND_SEQ_LEN + PACKET_HEADER_RECEIVE_SEQ_LEN;

        if (buffer.length < minHeaderLength) {
            return null; // 报文头不完整
        }

        let offset = 0;
        const decodedPacket = {};

        // 返回已消耗的字节数，以便上层管理缓冲区
        decodedPacket.bytesConsumed = 0;

        // 启动字符
        decodedPacket.startByte = buffer.readUInt8(offset);
        decodedPacket.bytesConsumed += 1;
        if (decodedPacket.startByte !== 0x70) {
            console.error('Invalid start byte:', decodedPacket.startByte);
            return null;
        }
        offset += PACKET_HEADER_START_BYTE_LEN;

        // 版本号
        decodedPacket.version = buffer.readUInt16BE(offset);
        decodedPacket.bytesConsumed += 2;
        offset += PACKET_HEADER_VERSION_LEN;

        // 标志位 (现在都是关闭的，但仍读取以保持结构)
        const flags = buffer.readUInt8(offset);
        decodedPacket.bytesConsumed += 1;
        decodedPacket.encrypt = (flags & FLAG_ENCRYPT_BIT) !== 0; // 应该为 false
        decodedPacket.sign = (flags & FLAG_SIGN_BIT) !== 0;       // 应该为 false
        decodedPacket.mac = (flags & FLAG_MAC_BIT) !== 0;         // 应该为 false
        decodedPacket.compress = (flags & FLAG_COMPRESS_BIT) !== 0; // 应该为 false
        offset += PACKET_HEADER_FLAG_LEN;

        // 当前报文长度 (报文体长度)
        decodedPacket.bodyLength = buffer.readUInt16BE(offset);
        decodedPacket.bytesConsumed += 2;
        offset += PACKET_HEADER_CURRENT_LENGTH_LEN;

        // 发送序号
        decodedPacket.sendSeq = buffer.readUInt16BE(offset);
        decodedPacket.bytesConsumed += 2;
        offset += PACKET_HEADER_SEND_SEQ_LEN;

        // 接收序号
        decodedPacket.receiveSeq = buffer.readUInt16BE(offset);
        decodedPacket.bytesConsumed += 2;
        offset += PACKET_HEADER_RECEIVE_SEQ_LEN;

        // skip IV

        // 校验整个报文的长度是否足够
        const expectedTotalLength = offset + decodedPacket.bodyLength;
        if (buffer.length < expectedTotalLength) {
            // 数据不完整，等待更多数据
            console.log(`Received partial packet. Expected: ${expectedTotalLength}, Actual: ${buffer.length}`);
            return null;
        }

        // 提取报文体
        const fullBodyBuffer = buffer.slice(offset, buffer.length);
        // offset += decodedPacket.bodyLength; // 这一行在处理完当前报文后才更新，在packet-client中处理

        // 解析报文体
        let bodyOffset = 0;
        decodedPacket.body = {};

        decodedPacket.body.totalContentLength = fullBodyBuffer.readUInt32BE(bodyOffset);
        bodyOffset += PACKET_BODY_TOTAL_CONTENT_LENGTH_LEN;
        decodedPacket.bytesConsumed += 4;

        decodedPacket.body.offset = fullBodyBuffer.readUInt32BE(bodyOffset);
        bodyOffset += PACKET_BODY_OFFSET_LEN;
        decodedPacket.bytesConsumed += 4;

        decodedPacket.body.sequence = fullBodyBuffer.readBigUInt64BE(bodyOffset);
        bodyOffset += PACKET_BODY_SEQUENCE_LEN;
        decodedPacket.bytesConsumed += 8;

        decodedPacket.body.functionType = fullBodyBuffer.readUInt8(bodyOffset);
        bodyOffset += PACKET_BODY_FUNCTION_TYPE_LEN;
        decodedPacket.bytesConsumed += 1;

        decodedPacket.body.packetType = fullBodyBuffer.readUInt8(bodyOffset);
        bodyOffset += PACKET_BODY_PACKET_TYPE_LEN;
        decodedPacket.bytesConsumed += 1;

        // 实际的数据内容
        // 这里需要确保实际读取的长度与 totalContentLength 匹配，防止恶意数据
        if (bodyOffset + decodedPacket.body.totalContentLength > fullBodyBuffer.length) {
            console.error('Data content length mismatch in received packet body.');
            return null;
        }
        decodedPacket.body.dataContent = fullBodyBuffer.slice(bodyOffset, bodyOffset + decodedPacket.body.totalContentLength);
        decodedPacket.bytesConsumed += decodedPacket.body.dataContent.length;

        // skip mac and sign

        return decodedPacket;
    }
}

module.exports = PacketUtils;