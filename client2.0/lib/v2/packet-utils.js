// packet-utils.js

const { getConfig, shouldAutoSegment, getRecommendedSegmentSize } = require('./packet-config');

// 报文固定长度
const PACKET_HEADER_START_BYTE_LEN = 1;
const PACKET_HEADER_VERSION_LEN = 2;
const PACKET_HEADER_FLAG_LEN = 1;
const PACKET_HEADER_CURRENT_LENGTH_LEN = 2;
const PACKET_HEADER_SEND_SEQ_LEN = 2;
const PACKET_HEADER_RECEIVE_SEQ_LEN = 2;
const PACKET_HEADER_IV_LEN = 16; // IV值长度

const PACKET_BODY_TOTAL_CONTENT_LENGTH_LEN = 4;
const PACKET_BODY_OFFSET_LEN = 4;
const PACKET_BODY_SEQUENCE_LEN = 8;
const PACKET_BODY_FUNCTION_TYPE_LEN = 1;
const PACKET_BODY_PACKET_TYPE_LEN = 1;

// 可选尾部字段长度
const PACKET_MAC_LEN = 32;     // MAC值长度
const PACKET_SIGNATURE_LEN = 64; // 签名值长度

// 标志位定义
const FLAG_ENCRYPT_BIT = 0x80; // bit7: 对称加密开关
const FLAG_SIGN_BIT = 0x40;    // bit6: 签名开关
const FLAG_MAC_BIT = 0x20;     // bit5: MAC开关
const FLAG_COMPRESS_BIT = 0x10; // bit4: 压缩开关

// 分包相关常量
const MAX_DATA_CONTENT_PER_SEGMENT = 1024 * 64; // 每个分包最大数据内容长度 64KB
const MAX_PACKET_SIZE = 1024 * 1024 * 16; // 最大包长度 16MB

class PacketUtils {
    /**
     * 编码报文（支持分包）
     * @param {object} packetInfo 报文信息对象
     * @param {Buffer} packetInfo.dataContent 报文内容（原始数据）
     * @param {number} packetInfo.version 版本号
     * @param {number} packetInfo.sendSeq 发送序号
     * @param {number} packetInfo.receiveSeq 接收序号
     * @param {number} packetInfo.functionType 功能类型
     * @param {number} packetInfo.packetType 报文类型
     * @param {boolean} [packetInfo.enableSegmentation=false] 是否启用分包
     * @param {number} [packetInfo.maxSegmentSize] 最大分包大小
     * @returns {Buffer|Buffer[]} 编码后的报文Buffer或分包Buffer数组
     */
    static encode(packetInfo) {
        // 如果启用分包且数据超过限制，则进行分包处理
        if (packetInfo.enableSegmentation && packetInfo.dataContent.length > MAX_DATA_CONTENT_PER_SEGMENT) {
            return this.encodeWithSegmentation(packetInfo);
        }

        // 普通单包编码
        return this.encodeSinglePacket(packetInfo);
    }

    /**
     * 编码单个报文
     * @param {object} packetInfo 报文信息对象
     * @returns {Buffer} 编码后的报文Buffer
     */
    static encodeSinglePacket(packetInfo) {
        const {
            dataContent,
            version,
            sendSeq,
            receiveSeq,
            functionType,
            packetType,
            flags = 0x00,
            dataOffset = 0,
            dataTotalLength = dataContent.length,
            iv = null,
            mac = null,
            signature = null,
            padding = null
        } = packetInfo;

        // 1. 构建报文体
        const bodyBufferParts = [];

        // 数据内容总长度
        const totalContentLengthBuffer = Buffer.alloc(PACKET_BODY_TOTAL_CONTENT_LENGTH_LEN);
        totalContentLengthBuffer.writeUInt32BE(dataTotalLength, 0);
        bodyBufferParts.push(totalContentLengthBuffer);

        // 报文偏移量
        const offsetBuffer = Buffer.alloc(PACKET_BODY_OFFSET_LEN);
        offsetBuffer.writeUInt32BE(dataOffset, 0);
        bodyBufferParts.push(offsetBuffer);

        // 报文序号
        const bodySeqBuffer = Buffer.alloc(PACKET_BODY_SEQUENCE_LEN);
        bodySeqBuffer.writeBigUInt64BE(BigInt(packetInfo.bodySequence || 0), 0);
        bodyBufferParts.push(bodySeqBuffer);

        // 功能类型
        bodyBufferParts.push(Buffer.from([functionType]));

        // 报文类型
        bodyBufferParts.push(Buffer.from([packetType]));

        // 数据内容
        bodyBufferParts.push(dataContent);

        // 填充值（如果有）
        if (padding) {
            bodyBufferParts.push(padding);
        }

        // MAC值（如果有）
        if (mac) {
            bodyBufferParts.push(mac);
        }

        // 签名值（如果有）
        if (signature) {
            bodyBufferParts.push(signature);
        }

        const fullBodyBuffer = Buffer.concat(bodyBufferParts);

        // 2. 构建报文头
        const headerBufferParts = [];

        // 启动字符
        headerBufferParts.push(Buffer.from([0x70]));

        // 版本号
        const versionBuffer = Buffer.alloc(PACKET_HEADER_VERSION_LEN);
        versionBuffer.writeUInt16BE(version, 0);
        headerBufferParts.push(versionBuffer);

        // 标志位
        headerBufferParts.push(Buffer.from([flags]));

        // 当前报文长度 (报文体长度)
        const currentPacketLengthBuffer = Buffer.alloc(PACKET_HEADER_CURRENT_LENGTH_LEN);
        currentPacketLengthBuffer.writeUInt16BE(fullBodyBuffer.length, 0);
        headerBufferParts.push(currentPacketLengthBuffer);

        // 发送序号
        const sendSeqBuffer = Buffer.alloc(PACKET_HEADER_SEND_SEQ_LEN);
        sendSeqBuffer.writeUInt16BE(sendSeq, 0);
        headerBufferParts.push(sendSeqBuffer);

        // 接收序号
        const receiveSeqBuffer = Buffer.alloc(PACKET_HEADER_RECEIVE_SEQ_LEN);
        receiveSeqBuffer.writeUInt16BE(receiveSeq, 0);
        headerBufferParts.push(receiveSeqBuffer);

        // IV值（如果加密开启）
        if (iv) {
            headerBufferParts.push(iv);
        }

        const headerBuffer = Buffer.concat(headerBufferParts);

        // 3. 最终组装
        return Buffer.concat([headerBuffer, fullBodyBuffer]);
    }

    /**
     * 分包编码报文
     * @param {object} packetInfo 报文信息对象
     * @returns {Buffer[]} 分包后的报文Buffer数组
     */
    static encodeWithSegmentation(packetInfo) {
        const {
            dataContent,
            version,
            sendSeq: initialSendSeq,
            receiveSeq,
            functionType,
            packetType,
            flags = 0x00,
            maxSegmentSize = MAX_DATA_CONTENT_PER_SEGMENT
        } = packetInfo;

        const segments = [];
        let offset = 0;
        let sendSeq = initialSendSeq;

        while (offset < dataContent.length) {
            // 计算当前分包的数据长度
            const remainingData = dataContent.length - offset;
            const segmentDataLength = Math.min(remainingData, maxSegmentSize);

            // 提取当前分包的数据
            const segmentData = dataContent.slice(offset, offset + segmentDataLength);

            // 构建分包报文信息
            const segmentPacketInfo = {
                dataContent: segmentData,
                version,
                sendSeq,
                receiveSeq,
                functionType,
                packetType,
                flags,
                dataOffset: offset,
                dataTotalLength: dataContent.length,
                bodySequence: packetInfo.bodySequence || 0
            };

            // 编码当前分包
            const segmentBuffer = this.encodeSinglePacket(segmentPacketInfo);
            segments.push(segmentBuffer);

            // 更新偏移量和发送序号
            offset += segmentDataLength;
            sendSeq++;
        }

        return segments;
    }

    /**
     * 解码单个报文
     * @param {Buffer} buffer 接收到的完整报文Buffer
     * @returns {object} 解析后的报文信息对象，或null表示数据不完整/错误
     */
    static decodeSinglePacket(buffer) {
        // 最小报文头长度
        const minHeaderLength = PACKET_HEADER_START_BYTE_LEN + PACKET_HEADER_VERSION_LEN + PACKET_HEADER_FLAG_LEN +
            PACKET_HEADER_CURRENT_LENGTH_LEN + PACKET_HEADER_SEND_SEQ_LEN + PACKET_HEADER_RECEIVE_SEQ_LEN;

        if (buffer.length < minHeaderLength) {
            return null; // 报文头不完整
        }

        let offset = 0;
        const decodedPacket = {};

        // 返回已消耗的字节数，以便上层管理缓冲区
        decodedPacket.bytesConsumed = 0;

        // 启动字符
        decodedPacket.startByte = buffer.readUInt8(offset);
        decodedPacket.bytesConsumed += 1;
        if (decodedPacket.startByte !== 0x70) {
            console.error('Invalid start byte:', decodedPacket.startByte);
            return null;
        }
        offset += PACKET_HEADER_START_BYTE_LEN;

        // 版本号
        decodedPacket.version = buffer.readUInt16BE(offset);
        decodedPacket.bytesConsumed += 2;
        offset += PACKET_HEADER_VERSION_LEN;

        // 标志位
        const flags = buffer.readUInt8(offset);
        decodedPacket.flags = flags;
        decodedPacket.bytesConsumed += 1;
        decodedPacket.encrypt = (flags & FLAG_ENCRYPT_BIT) !== 0;
        decodedPacket.sign = (flags & FLAG_SIGN_BIT) !== 0;
        decodedPacket.mac = (flags & FLAG_MAC_BIT) !== 0;
        decodedPacket.compress = (flags & FLAG_COMPRESS_BIT) !== 0;
        offset += PACKET_HEADER_FLAG_LEN;

        // 当前报文长度 (报文体长度)
        decodedPacket.bodyLength = buffer.readUInt16BE(offset);
        decodedPacket.bytesConsumed += 2;
        offset += PACKET_HEADER_CURRENT_LENGTH_LEN;

        // 发送序号
        decodedPacket.sendSeq = buffer.readUInt16BE(offset);
        decodedPacket.bytesConsumed += 2;
        offset += PACKET_HEADER_SEND_SEQ_LEN;

        // 接收序号
        decodedPacket.receiveSeq = buffer.readUInt16BE(offset);
        decodedPacket.bytesConsumed += 2;
        offset += PACKET_HEADER_RECEIVE_SEQ_LEN;

        // IV值（如果加密开启）
        let iv = null;
        if (decodedPacket.encrypt) {
            if (buffer.length < offset + PACKET_HEADER_IV_LEN) {
                return null; // 等待更多数据
            }
            iv = buffer.slice(offset, offset + PACKET_HEADER_IV_LEN);
            decodedPacket.iv = iv;
            decodedPacket.bytesConsumed += PACKET_HEADER_IV_LEN;
            offset += PACKET_HEADER_IV_LEN;
        }

        // 校验整个报文的长度是否足够
        const expectedTotalLength = offset + decodedPacket.bodyLength;
        if (buffer.length < expectedTotalLength) {
            // 数据不完整，等待更多数据
            console.log(`Received partial packet. Expected: ${expectedTotalLength}, Actual: ${buffer.length}`);
            return null;
        }

        // 提取报文体
        const fullBodyBuffer = buffer.slice(offset, offset + decodedPacket.bodyLength);

        // 解析报文体
        let bodyOffset = 0;
        decodedPacket.body = {};

        decodedPacket.body.totalContentLength = fullBodyBuffer.readUInt32BE(bodyOffset);
        bodyOffset += PACKET_BODY_TOTAL_CONTENT_LENGTH_LEN;
        decodedPacket.bytesConsumed += 4;

        decodedPacket.body.offset = fullBodyBuffer.readUInt32BE(bodyOffset);
        bodyOffset += PACKET_BODY_OFFSET_LEN;
        decodedPacket.bytesConsumed += 4;

        decodedPacket.body.sequence = fullBodyBuffer.readBigUInt64BE(bodyOffset);
        bodyOffset += PACKET_BODY_SEQUENCE_LEN;
        decodedPacket.bytesConsumed += 8;

        decodedPacket.body.functionType = fullBodyBuffer.readUInt8(bodyOffset);
        bodyOffset += PACKET_BODY_FUNCTION_TYPE_LEN;
        decodedPacket.bytesConsumed += 1;

        decodedPacket.body.packetType = fullBodyBuffer.readUInt8(bodyOffset);
        bodyOffset += PACKET_BODY_PACKET_TYPE_LEN;
        decodedPacket.bytesConsumed += 1;

        // 计算尾部可选字段长度
        let tailLength = 0;
        if (decodedPacket.mac) tailLength += PACKET_MAC_LEN;
        if (decodedPacket.sign) tailLength += PACKET_SIGNATURE_LEN;

        // 计算数据内容长度（包括填充）
        const remainingBodyLength = fullBodyBuffer.length - bodyOffset;
        const dataWithPaddingLength = remainingBodyLength - tailLength;

        if (dataWithPaddingLength < 0) {
            console.error('Invalid packet body length');
            return null;
        }

        // 读取数据内容和填充值
        const dataWithPadding = fullBodyBuffer.slice(bodyOffset, bodyOffset + dataWithPaddingLength);
        bodyOffset += dataWithPaddingLength;

        // 处理填充值（如果加密开启）
        let dataContent = dataWithPadding;
        let padding = null;

        if (decodedPacket.encrypt && dataWithPadding.length > 0) {
            // 根据PKCS7规则，最后一个字节表示填充长度
            const paddingLength = dataWithPadding[dataWithPadding.length - 1];

            if (paddingLength > 0 && paddingLength <= 16 && paddingLength <= dataWithPadding.length) {
                const actualDataLength = dataWithPadding.length - paddingLength;
                dataContent = dataWithPadding.slice(0, actualDataLength);
                padding = dataWithPadding.slice(actualDataLength);
                decodedPacket.padding = padding;
            }
        }

        decodedPacket.body.dataContent = dataContent;
        decodedPacket.bytesConsumed += dataWithPaddingLength;

        // 读取MAC值（如果有）
        if (decodedPacket.mac) {
            const macValue = fullBodyBuffer.slice(bodyOffset, bodyOffset + PACKET_MAC_LEN);
            decodedPacket.macValue = macValue;
            bodyOffset += PACKET_MAC_LEN;
            decodedPacket.bytesConsumed += PACKET_MAC_LEN;
        }

        // 读取签名值（如果有）
        if (decodedPacket.sign) {
            const signature = fullBodyBuffer.slice(bodyOffset, bodyOffset + PACKET_SIGNATURE_LEN);
            decodedPacket.signature = signature;
            bodyOffset += PACKET_SIGNATURE_LEN;
            decodedPacket.bytesConsumed += PACKET_SIGNATURE_LEN;
        }

        return decodedPacket;
    }

    /**
     * 解码报文（支持分包重组）
     * @param {Buffer} buffer 接收到的完整报文Buffer
     * @returns {object} 解析后的报文信息对象，或null表示数据不完整/错误
     */
    static decode(buffer) {
        return this.decodeSinglePacket(buffer);
    }

    /**
     * 分包重组器类
     */
    static PacketReassembler = class {
        constructor() {
            this.segments = new Map(); // 存储分包片段，key为消息标识，value为分包数组
        }

        /**
         * 添加分包片段
         * @param {object} decodedPacket 解码后的分包
         * @returns {object|null} 如果重组完成返回完整报文，否则返回null
         */
        addSegment(decodedPacket) {
            const { body } = decodedPacket;
            const { functionType, packetType, sequence, totalContentLength, offset } = body;

            // 生成消息标识
            const messageKey = `${functionType}_${packetType}_${sequence}`;

            if (!this.segments.has(messageKey)) {
                this.segments.set(messageKey, {
                    totalLength: totalContentLength,
                    receivedLength: 0,
                    segments: [],
                    firstPacket: decodedPacket,
                    timestamp: Date.now()
                });
            }

            const messageInfo = this.segments.get(messageKey);

            // 添加当前分包
            messageInfo.segments.push({
                offset: offset,
                data: body.dataContent,
                packet: decodedPacket
            });

            messageInfo.receivedLength += body.dataContent.length;

            // 检查是否接收完整
            if (messageInfo.receivedLength >= messageInfo.totalLength) {
                // 按偏移量排序
                messageInfo.segments.sort((a, b) => a.offset - b.offset);

                // 重组数据
                const reassembledData = Buffer.concat(
                    messageInfo.segments.map(seg => seg.data)
                );

                // 创建完整报文
                const completePacket = {
                    ...messageInfo.firstPacket,
                    body: {
                        ...messageInfo.firstPacket.body,
                        dataContent: reassembledData,
                        offset: 0 // 重组后偏移量为0
                    }
                };

                // 清理已完成的分包信息
                this.segments.delete(messageKey);

                return completePacket;
            }

            return null; // 还未接收完整
        }

        /**
         * 清理超时的分包信息
         * @param {number} timeoutMs 超时时间（毫秒）
         */
        cleanup(timeoutMs = 30000) {
            const now = Date.now();
            for (const [key, messageInfo] of this.segments.entries()) {
                if (now - messageInfo.timestamp > timeoutMs) {
                    this.segments.delete(key);
                }
            }
        }
    };
}

module.exports = PacketUtils;