// packet-utils.js - 严格按照后端Java代码逻辑实现

// 常量定义 - 对应后端的PublicDef和PlatformPacket
const HEADER_FIXED_SIZE = 1 + 2 + 1 + 2 + 2 + 2; // 10字节
const BODY_FIXED_SIZE = 4 + 4 + 8 + 1 + 1; // 18字节
const MIN_PACKET_SIZE = HEADER_FIXED_SIZE + BODY_FIXED_SIZE;
const MAX_PACKET_SIZE = 1024 * 1024 * 16; // 16MB
const MAX_DATA_CONTENT_PER_SEGMENT = 1024 * 64; // 64KB

// 长度常量
const IV_SIZE = 16;
const MAC_SIZE = 32;
const SIGN_SIZE = 64;

// 标志位定义 - 对应后端的标志位
const ENCRYPTION_FLAG = 0x80;  // bit7: 对称加密开关
const SIGNATURE_FLAG = 0x40;   // bit6: 签名开关
const MAC_FLAG = 0x20;         // bit5: MAC开关
const COMPRESSION_FLAG = 0x10; // bit4: 压缩开关

/**
 * PlatformPacket类 - 对应后端的PlatformPacket
 */
class PlatformPacket {
    constructor() {
        // 报文头部分
        this.startChar = 0x70;
        this.version = Buffer.from([0x01, 0x00]);
        this.flags = 0;
        this.bodyLength = 0;
        this.sendSeq = 0;
        this.receiveSeq = 0;
        this.iv = Buffer.alloc(0);

        // 报文体部分
        this.dataTotalLength = 0;
        this.dataOffset = 0;
        this.packetSeq = 0;
        this.functionType = 0;
        this.packetType = 0;
        this.dataContent = []; // 使用数组存储，对应后端的List<byte[]>
        this.padding = Buffer.alloc(0);

        // 尾部可选字段
        this.mac = Buffer.alloc(0);
        this.signature = Buffer.alloc(0);

        // 程序自定义字段
        this.symmetricKey = Buffer.alloc(0);
    }

    // 标志位操作方法 - 对应后端的标志位方法
    isEncrypted() {
        return (this.flags & ENCRYPTION_FLAG) !== 0;
    }

    setEncrypted(enabled) {
        if (enabled) {
            this.flags |= ENCRYPTION_FLAG;
        } else {
            this.flags &= ~ENCRYPTION_FLAG;
        }
    }

    isSignatureEnabled() {
        return (this.flags & SIGNATURE_FLAG) !== 0;
    }

    setSignatureEnabled(enabled) {
        if (enabled) {
            this.flags |= SIGNATURE_FLAG;
        } else {
            this.flags &= ~SIGNATURE_FLAG;
        }
    }

    isMacEnabled() {
        return (this.flags & MAC_FLAG) !== 0;
    }

    setMacEnabled(enabled) {
        if (enabled) {
            this.flags |= MAC_FLAG;
        } else {
            this.flags &= ~MAC_FLAG;
        }
    }

    isCompressionEnabled() {
        return (this.flags & COMPRESSION_FLAG) !== 0;
    }

    setCompressionEnabled(enabled) {
        if (enabled) {
            this.flags |= COMPRESSION_FLAG;
        } else {
            this.flags &= ~COMPRESSION_FLAG;
        }
    }

    // 获取数据内容 - 对应后端的getDataContent()
    getDataContent() {
        let totalLength = 0;
        for (const array of this.dataContent) {
            if (array) {
                totalLength += array.length;
            }
        }

        const result = Buffer.alloc(totalLength);
        let offset = 0;
        for (const array of this.dataContent) {
            if (array && array.length > 0) {
                array.copy(result, offset);
                offset += array.length;
            }
        }

        return result;
    }

    // 设置数据内容 - 对应后端的setDataContent()
    setDataContent(data) {
        // 如果启用压缩，先压缩数据
        if (this.isCompressionEnabled()) {
            // TODO: 实现压缩逻辑
            console.warn('Compression not implemented yet');
        }

        this.dataTotalLength = data.length;

        // 对于大数据，bodyLength在分包时会重新计算
        // 这里先设置为实际数据长度，分包时会调整
        let actualDataLength = Math.min(data.length, MAX_DATA_CONTENT_PER_SEGMENT);
        this.bodyLength = BODY_FIXED_SIZE + actualDataLength;

        if (this.isEncrypted()) {
            this.bodyLength += IV_SIZE;
        }
        if (this.isMacEnabled()) {
            this.bodyLength += MAC_SIZE;
        }
        if (this.isSignatureEnabled()) {
            this.bodyLength += SIGN_SIZE;
        }

        // 清空现有数据内容
        this.dataContent = [];

        if (data.length > 0 && data.length <= MAX_DATA_CONTENT_PER_SEGMENT) {
            this.dataContent.push(data);
        } else {
            // 分片存储
            let offset = 0;
            while (offset < data.length) {
                const chunkSize = Math.min(data.length - offset, MAX_DATA_CONTENT_PER_SEGMENT);
                const chunk = data.slice(offset, offset + chunkSize);
                this.dataContent.push(chunk);
                offset += chunkSize;
            }
        }
    }

    // 添加数据内容 - 对应后端的addDataContent()
    addDataContent(data) {
        this.dataContent.push(data);
    }

    // 分包时拷贝公共属性 - 对应后端的copyForSegment()
    copyForSegment(packet) {
        this.flags = packet.flags;
        this.receiveSeq = packet.receiveSeq;
        this.iv = packet.iv;
        this.dataTotalLength = packet.dataTotalLength;
        this.packetSeq = packet.packetSeq;
        this.functionType = packet.functionType;
        this.packetType = packet.packetType;
    }

    // 计算报文长度 - 对应后端的length()
    length() {
        const dataContentLength = this.getDataContent().length;
        return HEADER_FIXED_SIZE + this.iv.length + BODY_FIXED_SIZE + 
               dataContentLength + this.padding.length + this.mac.length + this.signature.length;
    }

    // 将报文转换为字节数组 - 对应后端的packetToBytes()
    packetToBytes() {
        const totalLength = this.length();
        const buffer = Buffer.alloc(totalLength);
        let offset = 0;

        // 报文头
        buffer.writeUInt8(this.startChar, offset); offset += 1;
        this.version.copy(buffer, offset); offset += 2;
        buffer.writeUInt8(this.flags, offset); offset += 1;

        // bodyLength应该在分包时就确保不超过65535
        if (this.bodyLength > 65535) {
            throw new Error(`bodyLength ${this.bodyLength} exceeds maximum value 65535`);
        }
        buffer.writeUInt16BE(this.bodyLength, offset); offset += 2;
        buffer.writeUInt16BE(this.sendSeq, offset); offset += 2;
        buffer.writeUInt16BE(this.receiveSeq, offset); offset += 2;
        this.iv.copy(buffer, offset); offset += this.iv.length;

        // 报文体
        buffer.writeUInt32BE(this.dataTotalLength, offset); offset += 4;
        buffer.writeUInt32BE(this.dataOffset, offset); offset += 4;
        buffer.writeBigUInt64BE(BigInt(this.packetSeq), offset); offset += 8;
        buffer.writeUInt8(this.functionType, offset); offset += 1;
        buffer.writeUInt8(this.packetType, offset); offset += 1;

        // 数据内容
        for (const array of this.dataContent) {
            array.copy(buffer, offset);
            offset += array.length;
        }

        // 填充值
        this.padding.copy(buffer, offset); offset += this.padding.length;

        // MAC值
        this.mac.copy(buffer, offset); offset += this.mac.length;

        // 签名值
        this.signature.copy(buffer, offset); offset += this.signature.length;

        return buffer;
    }
}

/**
 * PacketUtils类 - 主要的编解码工具类
 */
class PacketUtils {
    /**
     * 编码报文 - 对应后端PacketEncoder的encode方法
     */
    static encode(packetInfo) {
        // 计算单包时的bodyLength
        const dataLength = packetInfo.dataContent.length;
        let estimatedBodyLength = BODY_FIXED_SIZE + dataLength;

        // 添加可选字段长度
        const flags = packetInfo.flags || 0;
        if ((flags & ENCRYPTION_FLAG) !== 0) {
            estimatedBodyLength += IV_SIZE;
        }
        if ((flags & MAC_FLAG) !== 0) {
            estimatedBodyLength += MAC_SIZE;
        }
        if ((flags & SIGNATURE_FLAG) !== 0) {
            estimatedBodyLength += SIGN_SIZE;
        }

        // 如果单包的bodyLength会超过65535或数据超过MAX_DATA_CONTENT_PER_SEGMENT，则需要分包
        if (estimatedBodyLength > 65535 || dataLength > MAX_DATA_CONTENT_PER_SEGMENT) {
            // 分包处理
            return this.encodeWithSegmentation(packetInfo);
        }

        // 单包处理
        const packet = this.createPacketFromInfo(packetInfo);
        return this.encodeSinglePacket(packet);
    }

    /**
     * 分包编码 - 重新实现，更严格按照后端逻辑
     */
    static encodeWithSegmentation(packetInfo) {
        const segments = [];
        const dataContent = packetInfo.dataContent;
        let offset = 0;
        let sendSeq = packetInfo.sendSeq || 0;

        while (offset < dataContent.length) {
            const segmentPacket = new PlatformPacket();

            // 设置基本属性
            segmentPacket.sendSeq = sendSeq++;
            segmentPacket.receiveSeq = packetInfo.receiveSeq || 0;
            segmentPacket.functionType = packetInfo.functionType || 0;
            segmentPacket.packetType = packetInfo.packetType || 0;
            segmentPacket.packetSeq = packetInfo.bodySequence || 0;
            segmentPacket.flags = packetInfo.flags || 0;
            segmentPacket.dataOffset = offset;
            segmentPacket.dataTotalLength = dataContent.length; // 总长度保持不变

            // 计算可选字段长度
            let optionalFieldsLength = 0;
            if (segmentPacket.isEncrypted()) {
                optionalFieldsLength += IV_SIZE;
            }
            if (segmentPacket.isMacEnabled()) {
                optionalFieldsLength += MAC_SIZE;
            }
            if (segmentPacket.isSignatureEnabled()) {
                optionalFieldsLength += SIGN_SIZE;
            }

            // 计算当前分包的最大数据长度，确保bodyLength不超过65535
            const maxBodyLength = 65535;
            const maxDataLength = maxBodyLength - BODY_FIXED_SIZE - optionalFieldsLength;

            // 计算当前分包的实际数据长度
            const remainingData = dataContent.length - offset;
            const segmentDataLength = Math.min(remainingData, Math.min(MAX_DATA_CONTENT_PER_SEGMENT, maxDataLength));

            // 提取当前分包的数据
            const segmentData = dataContent.slice(offset, offset + segmentDataLength);

            // 直接设置数据内容
            segmentPacket.dataContent = [segmentData];
            segmentPacket.bodyLength = BODY_FIXED_SIZE + segmentData.length + optionalFieldsLength;

            // 编码当前分包
            const segmentBuffer = this.encodeSinglePacket(segmentPacket);
            segments.push(segmentBuffer);

            // 更新偏移量
            offset += segmentDataLength;
        }

        return segments;
    }

    /**
     * 从packetInfo创建PlatformPacket对象
     */
    static createPacketFromInfo(packetInfo) {
        const packet = new PlatformPacket();
        
        packet.sendSeq = packetInfo.sendSeq || 0;
        packet.receiveSeq = packetInfo.receiveSeq || 0;
        packet.functionType = packetInfo.functionType || 0;
        packet.packetType = packetInfo.packetType || 0;
        packet.packetSeq = packetInfo.bodySequence || 0;
        packet.flags = packetInfo.flags || 0;
        
        if (packetInfo.iv) {
            packet.iv = packetInfo.iv;
        }
        
        packet.setDataContent(packetInfo.dataContent);
        
        return packet;
    }

    /**
     * 编码单个报文 - 对应后端的encodeSingleMessage
     */
    static encodeSinglePacket(packet) {
        return packet.packetToBytes();
    }

    /**
     * 解码单个报文 - 对应后端PacketDecoder的decodeSinglePacket方法
     */
    static decodeSinglePacket(buffer) {
        if (buffer.length < MIN_PACKET_SIZE) {
            return null; // 等待更多数据
        }

        let offset = 0;

        // 1. 解析固定报文头部分
        const startChar = buffer.readUInt8(offset); offset += 1;
        if (startChar !== 0x70) {
            console.error('启动字符无效:', startChar);
            return null;
        }

        // 读取版本号（2字节）
        const version = buffer.slice(offset, offset + 2); offset += 2;
        if (version[0] !== 0x01 || version[1] !== 0x00) {
            console.error('报文版本不匹配:', version);
            return null;
        }

        // 读取标志位（1字节）
        const flags = buffer.readUInt8(offset); offset += 1;

        // 读取当前报文长度（2字节）
        const bodyLength = buffer.readUInt16BE(offset); offset += 2;

        // 读取发送序号和接收序号（各2字节）
        const sendSeq = buffer.readUInt16BE(offset); offset += 2;
        const receiveSeq = buffer.readUInt16BE(offset); offset += 2;

        // 2. 解析可选字段：IV值（16字节）
        let iv = Buffer.alloc(0);
        const encryptionEnabled = (flags & ENCRYPTION_FLAG) !== 0;
        if (encryptionEnabled) {
            if (buffer.length < offset + IV_SIZE) {
                return null; // 等待更多数据
            }
            iv = buffer.slice(offset, offset + IV_SIZE);
            offset += IV_SIZE;
        }

        // 3. 检查整个报文体的长度是否足够
        if (buffer.length < offset + bodyLength) {
            return null; // 等待完整报文体
        }

        // 4. 解析报文体部分
        // 4.1 读取报文体固定字段
        const dataTotalLength = buffer.readUInt32BE(offset); offset += 4;
        const msgOffset = buffer.readUInt32BE(offset); offset += 4;
        const msgSeq = buffer.readBigUInt64BE(offset); offset += 8;
        const functionType = buffer.readUInt8(offset); offset += 1;
        const messageType = buffer.readUInt8(offset); offset += 1;

        // 4.2 计算数据内容长度 (需处理可选字段)
        const fixedBodyPartLength = BODY_FIXED_SIZE;
        const remainingBodyLength = bodyLength - fixedBodyPartLength;

        // 处理可选尾部字段
        const signatureEnabled = (flags & SIGNATURE_FLAG) !== 0;
        const macEnabled = (flags & MAC_FLAG) !== 0;

        // 计算尾部可选字段长度
        let tailLength = 0;
        if (signatureEnabled) tailLength += SIGN_SIZE;
        if (macEnabled) tailLength += MAC_SIZE;

        // 检查报文体长度是否一致
        if (remainingBodyLength < tailLength) {
            console.warn('报文体长度不匹配');
            return null;
        }

        // 4.3 读取数据内容和填充值
        const dataContentLength = remainingBodyLength - tailLength;
        const dataWithPadding = buffer.slice(offset, offset + dataContentLength);
        offset += dataContentLength;

        // 5. 解析尾部可选字段
        let macValue = Buffer.alloc(0);
        let signature = Buffer.alloc(0);

        // 5.1 读取MAC值（如果有）
        if (macEnabled) {
            macValue = buffer.slice(offset, offset + MAC_SIZE);
            offset += MAC_SIZE;
        }

        // 5.2 读取签名值（如果有）
        if (signatureEnabled) {
            signature = buffer.slice(offset, offset + SIGN_SIZE);
            offset += SIGN_SIZE;
        }

        // 6. 处理填充值（如果加密开启）
        let dataContent = dataWithPadding;
        let padding = Buffer.alloc(0);

        if (encryptionEnabled && dataWithPadding.length > 0) {
            // 根据PKCS7规则，最后一个字节表示填充长度
            const paddingLength = dataWithPadding[dataWithPadding.length - 1];

            // 验证填充长度有效性 (1-16之间)
            if (paddingLength < 1 || paddingLength > 16) {
                console.warn('无效的PKCS7填充值:', paddingLength);
                return null;
            }

            // 分离实际数据和填充
            const actualDataLength = dataWithPadding.length - paddingLength;
            if (actualDataLength < 0) {
                console.warn('填充长度超过数据长度');
                return null;
            }

            dataContent = dataWithPadding.slice(0, actualDataLength);
            padding = dataWithPadding.slice(actualDataLength);
        }

        // 7. 创建解码结果
        const decodedPacket = {
            startChar,
            version,
            flags,
            bodyLength,
            sendSeq,
            receiveSeq,
            iv,
            body: {
                totalContentLength: dataTotalLength,
                offset: msgOffset,
                sequence: msgSeq,
                functionType,
                packetType: messageType,
                dataContent
            },
            padding,
            macValue,
            signature,
            bytesConsumed: offset
        };

        return decodedPacket;
    }

    /**
     * 解码报文 - 主入口
     */
    static decode(buffer) {
        return this.decodeSinglePacket(buffer);
    }

    /**
     * 分包重组器 - 对应后端PacketDecoder的分包处理逻辑
     */
    static PacketReassembler = class {
        constructor() {
            this.wholePackets = new Map(); // 对应后端的session.getAttribute(WHOLE_PACKET)
        }

        /**
         * 处理分包 - 对应后端PacketDecoder的doDecode方法
         */
        processPacket(sessionId, decodedPacket) {
            const { body } = decodedPacket;
            const { functionType, packetType, sequence, totalContentLength, offset } = body;

            console.log(`[REASSEMBLER] Processing: offset=${offset}, dataLength=${body.dataContent.length}`);

            // 生成消息标识
            const messageKey = `${sessionId}_${functionType}_${packetType}_${sequence}`;

            // 简单策略：所有分包都进入重组逻辑，不立即返回单包
            let wholePacket = this.wholePackets.get(messageKey);

            if (!wholePacket) {
                // 创建新的重组结构
                console.log(`[REASSEMBLER] Creating new message structure for key: ${messageKey}`);

                // 动态分配缓冲区
                const estimatedSize = Math.max(offset + body.dataContent.length * 5, 5 * 1024 * 1024);

                wholePacket = {
                    ...decodedPacket,
                    body: {
                        ...decodedPacket.body,
                        dataContent: Buffer.alloc(estimatedSize),
                        offset: 0
                    },
                    segments: [],
                    maxEndOffset: 0,
                    lastActivity: Date.now()
                };
                this.wholePackets.set(messageKey, wholePacket);
            }

            // 更新最后活动时间
            wholePacket.lastActivity = Date.now();

            // 计算当前分包的结束位置
            const currentEndOffset = offset + body.dataContent.length;

            // 扩展缓冲区如果需要
            if (currentEndOffset > wholePacket.body.dataContent.length) {
                const newSize = currentEndOffset * 2;
                const newBuffer = Buffer.alloc(newSize);
                wholePacket.body.dataContent.copy(newBuffer);
                wholePacket.body.dataContent = newBuffer;
                console.log(`[REASSEMBLER] Expanded buffer to ${newSize} bytes`);
            }

            // 拷贝数据
            body.dataContent.copy(wholePacket.body.dataContent, offset);
            wholePacket.maxEndOffset = Math.max(wholePacket.maxEndOffset, currentEndOffset);

            // 记录分包
            wholePacket.segments.push({
                offset: offset,
                length: body.dataContent.length,
                endOffset: currentEndOffset
            });

            console.log(`[REASSEMBLER] Added segment ${wholePacket.segments.length}: offset=${offset}, length=${body.dataContent.length}, maxEndOffset=${wholePacket.maxEndOffset}`);

            // 检测完成条件：当前分包比前一个分包明显小
            if (wholePacket.segments.length > 1) {
                const currentSegment = wholePacket.segments[wholePacket.segments.length - 1];
                const prevSegment = wholePacket.segments[wholePacket.segments.length - 2];

                // 如果当前分包大小不到前一个分包的70%，认为是最后一个分包
                if (currentSegment.length < prevSegment.length * 0.7) {
                    console.log(`[REASSEMBLER] Last segment detected (size: ${currentSegment.length} < ${prevSegment.length * 0.7})`);

                    // 完成重组
                    this.wholePackets.delete(messageKey);

                    // 调整数据大小
                    wholePacket.body.dataContent = wholePacket.body.dataContent.slice(0, wholePacket.maxEndOffset);
                    wholePacket.body.totalContentLength = wholePacket.maxEndOffset;

                    // 清理临时字段
                    delete wholePacket.segments;
                    delete wholePacket.maxEndOffset;
                    delete wholePacket.lastActivity;

                    console.log(`[REASSEMBLER] Reassembly complete, final size: ${wholePacket.body.dataContent.length} bytes`);
                    return wholePacket;
                }
            }

            // 对于第一个分包，也不立即返回，等待可能的后续分包
            console.log(`[REASSEMBLER] Waiting for more segments...`);
            return null;
        }

        /**
         * 清理超时的分包信息，并返回超时的完整包
         */
        cleanup(timeoutMs = 1000) {
            const now = Date.now();
            const completedPackets = [];

            for (const [key, packet] of this.wholePackets.entries()) {
                if (packet.lastActivity && now - packet.lastActivity > timeoutMs) {
                    console.log(`[REASSEMBLER] Timeout cleanup for ${key}, completing reassembly`);

                    // 超时的分包，强制完成重组
                    this.wholePackets.delete(key);

                    // 调整数据大小
                    packet.body.dataContent = packet.body.dataContent.slice(0, packet.maxEndOffset);
                    packet.body.totalContentLength = packet.maxEndOffset;

                    // 清理临时字段
                    delete packet.segments;
                    delete packet.maxEndOffset;
                    delete packet.lastActivity;

                    completedPackets.push(packet);
                }
            }

            return completedPackets;
        }
    };
}

// 导出类和常量
module.exports = {
    PacketUtils,
    PlatformPacket,
    // 常量
    HEADER_FIXED_SIZE,
    BODY_FIXED_SIZE,
    MIN_PACKET_SIZE,
    MAX_PACKET_SIZE,
    MAX_DATA_CONTENT_PER_SEGMENT,
    IV_SIZE,
    MAC_SIZE,
    SIGN_SIZE,
    // 标志位
    ENCRYPTION_FLAG,
    SIGNATURE_FLAG,
    MAC_FLAG,
    COMPRESSION_FLAG
};
