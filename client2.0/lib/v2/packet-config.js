/**
 * 分包相关配置参数
 */

const PacketConfig = {
    // 分包大小配置
    segmentation: {
        // 默认最大分包大小 (64KB)
        defaultMaxSegmentSize: 64 * 1024,
        
        // 最小分包大小 (1KB)
        minSegmentSize: 1024,
        
        // 最大分包大小 (1MB)
        maxSegmentSize: 1024 * 1024,
        
        // 自动分包阈值 - 超过此大小自动启用分包
        autoSegmentationThreshold: 64 * 1024,
        
        // 分包重组超时时间 (毫秒)
        reassemblyTimeout: 30000,
        
        // 清理定时器间隔 (毫秒)
        cleanupInterval: 30000
    },
    
    // 报文大小限制
    packet: {
        // 最大单个报文大小 (16MB)
        maxPacketSize: 16 * 1024 * 1024,
        
        // 最小报文头长度
        minHeaderLength: 10,
        
        // 报文体固定字段长度
        bodyFixedLength: 18
    },
    
    // 缓冲区配置
    buffer: {
        // 接收缓冲区初始大小
        initialReceiveBufferSize: 8192,
        
        // 接收缓冲区最大大小
        maxReceiveBufferSize: 1024 * 1024,
        
        // 发送缓冲区大小
        sendBufferSize: 64 * 1024
    },
    
    // 重试配置
    retry: {
        // 最大重试次数
        maxRetries: 3,
        
        // 重试间隔 (毫秒)
        retryInterval: 1000,
        
        // 重试超时时间 (毫秒)
        retryTimeout: 10000
    },
    
    // 日志配置
    logging: {
        // 是否启用分包日志
        enableSegmentationLog: true,
        
        // 是否启用重组日志
        enableReassemblyLog: true,
        
        // 是否启用性能日志
        enablePerformanceLog: false,
        
        // 日志级别: 'debug', 'info', 'warn', 'error'
        logLevel: 'info'
    },
    
    // 性能配置
    performance: {
        // 是否启用性能监控
        enableMonitoring: false,
        
        // 性能统计间隔 (毫秒)
        statsInterval: 60000,
        
        // 是否启用内存使用监控
        enableMemoryMonitoring: false
    }
};

/**
 * 获取分包配置
 * @param {string} key 配置键名
 * @returns {*} 配置值
 */
function getConfig(key) {
    const keys = key.split('.');
    let value = PacketConfig;
    
    for (const k of keys) {
        if (value && typeof value === 'object' && k in value) {
            value = value[k];
        } else {
            return undefined;
        }
    }
    
    return value;
}

/**
 * 设置分包配置
 * @param {string} key 配置键名
 * @param {*} value 配置值
 */
function setConfig(key, value) {
    const keys = key.split('.');
    const lastKey = keys.pop();
    let target = PacketConfig;
    
    for (const k of keys) {
        if (!target[k] || typeof target[k] !== 'object') {
            target[k] = {};
        }
        target = target[k];
    }
    
    target[lastKey] = value;
}

/**
 * 验证分包大小是否有效
 * @param {number} size 分包大小
 * @returns {boolean} 是否有效
 */
function isValidSegmentSize(size) {
    const minSize = getConfig('segmentation.minSegmentSize');
    const maxSize = getConfig('segmentation.maxSegmentSize');
    
    return size >= minSize && size <= maxSize;
}

/**
 * 获取推荐的分包大小
 * @param {number} totalSize 总数据大小
 * @returns {number} 推荐的分包大小
 */
function getRecommendedSegmentSize(totalSize) {
    const defaultSize = getConfig('segmentation.defaultMaxSegmentSize');
    const maxSize = getConfig('segmentation.maxSegmentSize');
    
    // 如果总大小小于默认分包大小，不需要分包
    if (totalSize <= defaultSize) {
        return totalSize;
    }
    
    // 计算合适的分包数量（避免产生太多小分包）
    const segmentCount = Math.ceil(totalSize / defaultSize);
    
    if (segmentCount <= 10) {
        return defaultSize;
    }
    
    // 如果分包数量太多，增加分包大小
    const recommendedSize = Math.ceil(totalSize / 10);
    return Math.min(recommendedSize, maxSize);
}

/**
 * 检查是否需要自动分包
 * @param {number} dataSize 数据大小
 * @returns {boolean} 是否需要分包
 */
function shouldAutoSegment(dataSize) {
    const threshold = getConfig('segmentation.autoSegmentationThreshold');
    return dataSize > threshold;
}

/**
 * 获取完整配置对象（只读）
 * @returns {object} 配置对象的深拷贝
 */
function getAllConfig() {
    return JSON.parse(JSON.stringify(PacketConfig));
}

/**
 * 重置配置为默认值
 */
function resetConfig() {
    // 这里可以重新加载默认配置
    console.log('Configuration reset to defaults');
}

module.exports = {
    PacketConfig,
    getConfig,
    setConfig,
    isValidSegmentSize,
    getRecommendedSegmentSize,
    shouldAutoSegment,
    getAllConfig,
    resetConfig
};
