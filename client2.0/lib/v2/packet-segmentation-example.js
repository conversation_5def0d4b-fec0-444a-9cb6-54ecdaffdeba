/**
 * 分包功能使用示例
 */

const PacketUtils = require('./packet-utils');
const tcpClient = require('./tls_client');

// 示例1: 手动使用分包编码
function exampleManualSegmentation() {
    console.log('=== 手动分包编码示例 ===');
    
    // 创建一个大的数据包
    const largeData = Buffer.alloc(200 * 1024, 'A'); // 200KB的数据
    
    const packetInfo = {
        dataContent: largeData,
        version: 0x0100,
        sendSeq: 1,
        receiveSeq: 0,
        functionType: 0x01,
        packetType: 0x02,
        enableSegmentation: true,
        maxSegmentSize: 64 * 1024 // 64KB分包
    };
    
    try {
        const segments = PacketUtils.encode(packetInfo);
        
        if (Array.isArray(segments)) {
            console.log(`数据被分成 ${segments.length} 个包:`);
            segments.forEach((segment, index) => {
                console.log(`  分包 ${index + 1}: ${segment.length} 字节`);
            });
        } else {
            console.log('数据未分包，单包大小:', segments.length);
        }
    } catch (error) {
        console.error('分包编码失败:', error);
    }
}

// 示例2: 使用TLS客户端发送大数据包
async function exampleTlsClientLargePacket() {
    console.log('=== TLS客户端大数据包发送示例 ===');
    
    try {
        // 连接到服务器
        await tcpClient.connect('localhost', 8443);
        
        // 创建大数据包
        const largeData = Buffer.alloc(150 * 1024, 'B'); // 150KB的数据
        
        // 方法1: 使用sendLargePacket自动分包
        await tcpClient.sendLargePacket({
            dataContent: largeData,
            functionType: 0x01,
            packetType: 0x03,
            sendSeq: 1,
            receiveSeq: 0
        });
        
        console.log('大数据包发送完成');
        
        // 方法2: 手动启用分包
        await tcpClient.sendPacket({
            dataContent: largeData,
            functionType: 0x01,
            packetType: 0x04,
            sendSeq: 2,
            receiveSeq: 0,
            enableSegmentation: true,
            maxSegmentSize: 32 * 1024 // 32KB分包
        });
        
        console.log('手动分包发送完成');
        
    } catch (error) {
        console.error('发送失败:', error);
    } finally {
        tcpClient.disconnect();
    }
}

// 示例3: 分包重组器使用
function examplePacketReassembler() {
    console.log('=== 分包重组器示例 ===');
    
    const reassembler = new PacketUtils.PacketReassembler();
    
    // 模拟接收到的分包数据
    const mockSegments = [
        {
            body: {
                functionType: 0x01,
                packetType: 0x05,
                sequence: 12345n,
                totalContentLength: 100,
                offset: 0,
                dataContent: Buffer.from('Hello, ')
            }
        },
        {
            body: {
                functionType: 0x01,
                packetType: 0x05,
                sequence: 12345n,
                totalContentLength: 100,
                offset: 7,
                dataContent: Buffer.from('World!')
            }
        }
    ];
    
    // 逐个添加分包
    mockSegments.forEach((segment, index) => {
        console.log(`添加分包 ${index + 1}:`, segment.body.dataContent.toString());
        const result = reassembler.addSegment(segment);
        
        if (result) {
            console.log('重组完成! 完整数据:', result.body.dataContent.toString());
        } else {
            console.log('等待更多分包...');
        }
    });
}

// 示例4: 处理接收到的分包消息
function exampleReceiveSegmentedMessage() {
    console.log('=== 接收分包消息示例 ===');
    
    // 注册消息处理回调
    const messageKey = '1_5'; // functionType_packetType
    
    tcpClient.onMessage(messageKey, (dataContent) => {
        console.log('接收到完整消息:', dataContent.toString());
        console.log('消息长度:', dataContent.length);
    });
    
    console.log(`已注册消息处理器: ${messageKey}`);
    console.log('等待接收分包消息...');
}

// 运行示例
if (require.main === module) {
    console.log('分包功能示例程序');
    console.log('==================');
    
    // 运行各个示例
    exampleManualSegmentation();
    console.log();
    
    examplePacketReassembler();
    console.log();
    
    exampleReceiveSegmentedMessage();
    console.log();
    
    // 注意: TLS客户端示例需要实际的服务器连接
    // exampleTlsClientLargePacket();
}

module.exports = {
    exampleManualSegmentation,
    exampleTlsClientLargePacket,
    examplePacketReassembler,
    exampleReceiveSegmentedMessage
};
