"use strict";
const tls = require('tls');
const fs = require('fs');

const options = {
  host: "***************",
  // Necessary only if the server requires client certificate authentication.
  // key: fs.readFileSync('diagclientCA.key'),
  // cert: fs.readFileSync('diagclientCA.pem'),

  // Necessary only if the server uses a self-signed certificate.
  ca: [fs.readFileSync('diagserverCA.pem')],

  // Necessary only if the server's cert isn't for "localhost".
  checkServerIdentity: () => {
    return null;
  },
};

const socket = tls.connect(8802, options, () => {
  console.log('client connected',
    socket.authorized ? 'authorized' : 'unauthorized');
  process.stdin.pipe(socket);
  process.stdin.resume();
});
// socket.setEncoding('utf8');
socket.on('data', (data) => {
  console.log('recv', data);
});
socket.on('end', () => {
  console.log('server ends connection');
});
socket.on('close', () => {
  console.log('server ends connection');
});

socket.on('error', (error) => {
  console.log('socket error', error);
});

setInterval(function () {
  socket.write(Buffer.from("I am a test message."));
}, 1000);
