"use strict";
let net = require('net');
const util = require('./util');
const PublicDefine_pb = require('../script/pb/PublicDefine_pb');
const safe = require('./safe');
const {ipcRenderer, dialog} = require('electron');
const tcp_codec = require('./tcp_codec');
const config = require('../config');

let global_socket = null;

/**
 * 初始化tcp连接
 * @param message_handler 自定义消息回调函数
 * @param host 服务器ip
 * @param port 服务器端口
 * @param auto_close 收到返回消息后自动关闭连接
 */
function connect(message_handler, host, port, auto_close = true) {
  let socket = null;
  // console.log('start to connect', host, port);
  if (!config.enable_ssl) {
    socket = new net.Socket();
    socket.connect(port, host, function () {
      console.log('CONNECTED TO: ' + host + ':' + port, socket.localPort);
    });
  } else {
    const tls = require('tls');
    const fs = require('fs');
    const options = {
      host: host,
      // Necessary only if the server requires client certificate authentication.
      // key: fs.readFileSync('diagclientCA.key'),
      // cert: fs.readFileSync('diagclientCA.pem'),

      // Necessary only if the server uses a self-signed certificate.
      ca: [fs.readFileSync(__dirname + '/../certs/server-ca.pem')],

      // Necessary only if the server's cert isn't for "localhost".
      checkServerIdentity: () => {
        return null;
      },
    };
    socket = tls.connect(port, options, () => {
      console.log('CONNECTED TO: ' + host + ':' + port, socket.localPort, socket.authorized ? 'authorized' : 'unauthorized');
      // process.stdin.pipe(socket);
      // process.stdin.resume();
    });
  }

  socket.on('data', function (data) {
    console.log('recv', data);
    let isCompletedPacket = false;
    if (config.enable_ssl) {
      isCompletedPacket = tcp_codec.onMsg(data, message_handler);
    } else {
      isCompletedPacket = tcp_codec.decodeEncryptMsg(data, message_handler);
    }

    if (auto_close && isCompletedPacket) {
      socket.destroy();
    }
  });

  socket.on('close', function () {
    // console.log('关闭连接', socket.localPort);
  });

  socket.on('end', () => {
    console.log('server ends connection');
  });

  socket.on('error', function (errorMsg) {
    console.log('error human', errorMsg);
    ipcRenderer.send('asynchronous-message', 'close');
  });

  global_socket = socket;
  return socket;
}

/**
 * 发送字节数据
 * @param socket
 * @param buffer 原始字节数据
 */
function send_msg(buffer, socket = global_socket) {
  if (!config.enable_ssl) {
    safe.send_safe_msg(safe.PACKET_TYPE_ENCRYPT_SM4, buffer, function (data) {
      if (socket.destroyed) {
        console.error('send_msg failed, because of socket is closed');
        return;
      }
      const encryptData = data.toString('base64');
      const wrapBuffer = Buffer.alloc(1 + 4 + encryptData.length);
      let offset = 0;
      wrapBuffer.writeUInt8(PublicDefine_pb.PlatformType.PLATFORM_TYPE_ENCRYPTED_MESSAGE_SM4, offset);
      offset++;
      wrapBuffer.writeInt32BE(encryptData.length, offset);
      offset += 4;
      wrapBuffer.write(encryptData, offset, encryptData.length, "ascii");
      // console.log("buffer", buffer)
      socket.write(wrapBuffer, function (err) {
        console.log('send sm4 msg', buffer, 'err', err);
      });
    });
  } else {
    socket.write(buffer, function (err) {
      console.log('send ssl msg', buffer, 'err', err, socket.localPort);
    });
  }
}

/**
 * 发送sm2加密消息到服务端
 * @param socket
 * @param buffer {Buffer} 原始报文
 */
function send_sm2_msg_java(buffer, socket = global_socket) {
  safe.send_safe_msg(safe.PACKET_TYPE_ENCRYPT_SM2, buffer, function (data) {
    if (socket.destroyed) {
      console.error('send_sm2_msg_java failed, because of socket is closed');
      return;
    }
    const encryptData = data.toString();
    const wrapBuffer = Buffer.alloc(1 + 4 + encryptData.length);
    let offset = 0;
    wrapBuffer.writeUInt8(PublicDefine_pb.PlatformType.PLATFORM_TYPE_ENCRYPTED_MESSAGE_SM2, offset);
    offset++;
    wrapBuffer.writeInt32BE(encryptData.length, offset);
    offset += 4;
    wrapBuffer.write(encryptData, offset, encryptData.length, "ascii");
    socket.write(wrapBuffer, function (err) {
      console.log('send sm2 msg', buffer, 'err', err);
    });
  });
}

/**
 * 发送自定义消息
 * @param msgId 消息id
 * @param sendMsg pb结构
 * @param user_sign 用户认证信息
 */
function sendGuiMsg(msgId, sendMsg, user_sign, socket = global_socket) {
  // todo 正常应该调用send_msg走sm4加密的，但是现在调用send_msg会很大几率发生客户端白屏，继续调用sm2加密吧先
  send_msg(util.packGuiMsg(msgId, sendMsg, user_sign), socket);
}

function sendGuiMsg2(msgId, sendMsg, user_sign, socket = global_socket) {
  if (config.enable_ssl) {
    send_msg(util.packGuiMsg(msgId, sendMsg, user_sign), socket);
  } else {
    send_sm2_msg_java(util.packGuiMsg(msgId, sendMsg, user_sign), socket);
  }
}

// 完全关闭连接
function close(socket = global_socket) {
  socket.destroy();
}

module.exports = {
  /**
   * @deprecated 不再推荐使用该接口，存在覆盖socket的问题，建议使用connectEx，保存返回的socket用于消息收发
   */
  connect: connect,
  connectEx: connect,
  send: send_msg,
  sendGuiMsg: sendGuiMsg,
  close: close,
  sendGuiMsg2
};
