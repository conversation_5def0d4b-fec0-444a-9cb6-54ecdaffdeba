'use strict';

const PublicDefine_pb = require('../script/pb/PublicDefine_pb');
const sm2 = require('sm-crypto').sm2;
const SM4 = require('gm-crypt').sm4;
const crypto = require('crypto');
const log4js = require('log4js');
const logger = log4js.getLogger();
const fs = require('fs');
const config = require('../config');

// 电科院定义服务代理包头所占字节
const PACKET_HEADER_BYTE = 8;

// 自定义报文头所占字节
const PACKET_USER_DEFINE_PACKET_ID_BYTE = 2;

// 自定义报文用户身份信息所占字节
const PACKET_USER_DEFINE_USER_SIGN_BYTE = 36;

// 自定义服务代理类型，用于GUI客户端与服务代理通信
const PLATFORM_TYPE_GUI_MESSAGE = 0x80;

let g_UserSign = "";

/**
 * 启用日志
 * @param level 日志级别字符串 debug info warn error...
 */
function enableLog(level) {
  logger.level = level;
  log4js.configure({
    appenders: {cheese: {type: 'file', filename: 'network-monitor-client.log'}},
    categories: {default: {appenders: ['cheese'], level: 'debug'}}
  });
  return logger;
}

/**
 * 打包自定义消息，将消息打包成Buffer准备发送
 * @param packet_id 消息id
 * @param sendMsg 待发送的pb消息
 * @param user_sign 用户认证信息
 * @returns {Buffer}
 */
function packGuiMsg(packet_id, sendMsg, user_sign = '00000000-0000-0000-0000-000000000000') {
  const user_sign_buf = Buffer.from(user_sign);
  const pb_buf = Buffer.from(sendMsg.serializeBinary());
  let header_buf = Buffer.alloc(PACKET_HEADER_BYTE + PACKET_USER_DEFINE_PACKET_ID_BYTE);  // 包括电科院定义的报头+金煜自定义的消息id
  const contentLength = PACKET_USER_DEFINE_PACKET_ID_BYTE + user_sign_buf.length + pb_buf.length;
  let offset = 0;
  header_buf.writeUInt8(PLATFORM_TYPE_GUI_MESSAGE, offset);
  offset++;
  header_buf.writeUInt8(0, offset);
  offset++;
  header_buf.writeUInt16BE(0, offset);
  offset += 2;
  header_buf.writeUInt32BE(contentLength, offset);
  offset += 4;
  header_buf.writeUInt16BE(packet_id, offset);
  offset += 2;
  const send_buf = Buffer.concat([header_buf, pb_buf, user_sign_buf]);
  // console.log('packGuiMsg packet_id', packet_id, 'send_buf', send_buf, 'user_sign_buf', user_sign_buf);
  return send_buf;
}

/**
 * 解析自定义消息，解析收到服务代理发送过来的数据
 * @param buf
 * @returns {*} 数据内容
 */
function unpackGuiMsg(buf) {
  let offset = 0;
  let type = buf.readUInt8(offset);
  offset++;
  buf.readUInt8(offset);
  offset++;
  buf.readUInt8(offset);
  offset++;
  buf.readUInt8(offset);
  offset++;
  let content_size = buf.readUInt32BE(offset);
  offset += 4;
  return buf.slice(PACKET_HEADER_BYTE, PACKET_HEADER_BYTE + content_size);
}

/**
 * 打包平台消息
 * @param type 类型
 * @param sub_type 子类型
 * @param param 参数
 * @param content_length 数据内容长度
 * @param content 数据内容
 * @returns {Buffer}
 */
function packPlatformMsg(type, sub_type, param, content_length, content) {
  content_length += g_UserSign.length;    // 客户端模拟主站平台的报文需要上报用户身份信息
  let buffer = Buffer.alloc(1 + 1 + 2 + 4 + content_length);
  let offset = 0;

  buffer.writeInt8(type, offset);
  offset++;

  buffer.writeInt8(sub_type, offset);
  offset++;

  buffer.writeUInt16BE(param, offset);
  offset += 2;

  buffer.writeUInt32BE(content_length, offset);
  offset += 4;

  if (content !== null) {
    content.copy(buffer, offset);
    offset += content.length;
  }

  if ((type === PublicDefine_pb.PlatformType.PLATFORM_TYPE_CHECK_BASE_LINE || type === PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONTROL || type === PublicDefine_pb.PLATFORM_TYPE_UPGRADE_SOFTWARE)
    || (type === PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE && param !== PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_LOAD)
    || (type === PublicDefine_pb.PlatformType.PLATFORM_TYPE_MONITORED_OBJECT_MANAGE && param !== PublicDefine_pb.MonitoredObjectParam.MONITORED_OBJECT_PARAM_LOAD)
    || (type === PublicDefine_pb.PlatformType.PLATFORM_TYPE_LEAK_SCAN) || (type === PublicDefine_pb.PlatformType.PLATFORM_TYPE_VERSION_MANAGE)
    || (type === PublicDefine_pb.PlatformType.PLATFORM_TYPE_CHARACTER_UPDATE) || (type === PublicDefine_pb.PlatformType.PLATFORM_TYPE_UPGRADE_SOFTWARE)
    || (type === PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONTROL)||(type === PublicDefine_pb.PlatformType.PLATFORM_TYPE_BACKUP_RESTORE)) {
    // 添加时间戳
    buffer.writeUInt32BE(getTimestamp(), offset);
    offset += 4;

    // 添加签名
    const signData = buffer.slice(0, offset);
    const sign = sm2Sign(signData);
    console.log("sign", signData, sign);
    sign.copy(buffer, offset);
    // buffer.write(sign, offset, 64);
    offset += 64;
  }

  buffer.write(g_UserSign, offset, g_UserSign.length);

  return buffer;
}

/**
 * 解析平台报文，返回解析后的json
 * @param buf
 * @param hasTimestampSign 是否包含时间戳和签名
 * @returns {[]}
 */
function unpackPlatformMsg(buf, hasTimestampSign) {
  let offset = 0;
  let msgQueue = [];
  while (offset < buf.length) {
    let type = buf.readUInt8(offset);
    offset++;
    let sub_type = buf.readUInt8(offset);
    offset++;
    let return_type = buf.readUInt8(offset);
    offset++;
    let return_value = buf.readUInt8(offset);
    offset++;
    let content_size = buf.readUInt32BE(offset);
    offset += 4;
    let content = buf.slice(offset, offset + content_size);
    offset += content_size;

    if (hasTimestampSign) {
      // 去掉时间戳和签名
      content = content.slice(0, content.length - 68);
    }
    content = content.slice(0, content.length - g_UserSign.length);
    msgQueue.push({type, sub_type, return_type, return_value, content});
  }
  return msgQueue;
}

/**
 *
 * @param buffer {Buffer}
 * @param filePath {string}
 * @param encrypt
 * @return {string}
 */
function saveToFile(buffer, filePath, encrypt = true) {
  // filePath = __dirname + "/../" + filePath;
  let encryptData;
  if (encrypt) {
    encryptData = fileEncrypt(buffer);
  } else {
    encryptData = buffer;
  }
  const fd = fs.openSync(filePath, 'w');
  if (fd < 0) {
    logger.error('无法创建文件', filePath);
    return;
  }
  fs.write(fd, encryptData, function (err) {
    if (err) {
      fs.close(fd);
      logger.error(err);
      return;
    }
    logger.info('导出文件成功', filePath);
    fs.closeSync(fd);
  });
  return encryptData;
}

function saveToCSV(buffer, filePath) {
  // filePath = __dirname + "/../" + filePath;
  const encryptData = buffer.toString();
  const fd = fs.openSync(filePath, 'w');
  if (fd < 0) {
    logger.error('无法创建文件', filePath);
    return;
  }
  fs.write(fd, encryptData, function (err) {
    if (err) {
      fs.close(fd);
      logger.error(err);
      return;
    }
    logger.info('导出文件成功', filePath);
    fs.closeSync(fd);
  });
  return encryptData;
}

/**
 * 从文件中读取内容
 * @param filePath
 * @param encrypt
 * @return {*}
 */
function readFromFile(filePath, encrypt = true) {
  if (!fs.existsSync(filePath)) {
    return [];
  }

  if (encrypt) {
    let encryptData = fs.readFileSync(filePath, "ascii");
    const platformData = fileDecrypt(encryptData);
    return unpackPlatformMsg(platformData, true);
  } else {
    return fs.readFileSync(filePath);
  }
}

/**
 * 返回当前时间戳，秒
 * @returns {number}
 */
function getTimestamp() {
  return Date.parse(new Date()) / 1000;
}

/**
 * 返回当前日期的最大值，即23:59:59(秒)
 * @returns {number}
 */
function getDateMax() {
    return new Date(new Date(new Date().toLocaleDateString()).getTime()+24*60*60*1000-1);
}

/**
 * 返回选中时间戳，秒
 */
function getThisTimestamp(tdate) {
  let time = Date.parse(tdate) / 1000;
  if (time < 0) {
    time = 0;
  }
  return time;
}

/**
 * 时间戳返回日期格式
 */
function add0(m) {
  return m < 10 ? '0' + m : m
}

function getThisDate(shijianchuo) {
//shijianchuo是整数，否则要parseInt转换
  let time = new Date(shijianchuo * 1000);
  let y = time.getFullYear();
  let m = time.getMonth() + 1;
  let d = time.getDate();
  let h = time.getHours();
  let mm = time.getMinutes();
  let s = time.getSeconds();
  return y + '/' + add0(m) + '/' + add0(d) + ' ' + add0(h) + ':' + add0(mm) + ':' + add0(s);
}

/**
 * 时间戳返回日期格式(ms)
 */
function getThisDateMs(shijianchuo) {
//shijianchuo是整数，否则要parseInt转换
  let time = new Date(shijianchuo);
  let y = time.getFullYear();
  let m = time.getMonth() + 1;
  let d = time.getDate();
  let h = time.getHours();
  let mm = time.getMinutes();
  let s = time.getSeconds();
  return y + '/' + add0(m) + '/' + add0(d) + ' ' + add0(h) + ':' + add0(mm) + ':' + add0(s);
}

// const keypair = sm2.generateKeyPairHex();
// const publicKey = keypair.publicKey; // 公钥
// const privateKey = keypair.privateKey; // 私钥
const platformPublicKey = "049C43B06E168E5BF6E68E4DF1D75B54306BEB82258AA14EA005F2ACBA5ABFBB08C07BBABB74888422D4CE26142EDEDD78BAA4B63B7FA27775F875C18834D9B7B7";
const platformPrivateKey = "29e18af5b33a9a69f0b8dd2a9714035d";
const cipherMode = 0; // 1 - C1C3C2，0 - C1C2C3，默认为1
const sm4PrivateKey = [0x22, 0x00, 0x11, 0x88, 0x00, 0x55, 0x11, 0x44, 0xff, 0xcc, 0xba, 0x18, 0x26, 0x33, 0x54, 0x45];

/**
 * 对数据进行sm2签名，参考 https://github.com/JuneAndGreen/sm-crypto
 * @param msg {Buffer}
 * @return {Buffer}
 */
function sm2Sign(msg) {
  let sigValueHex = sm2.doSignature(msg, platformPrivateKey + config.loginKey, {
    der: false, hash: true
  });
  return Buffer.from(sigValueHex, "hex");
}

function sm2Verify(msg, sign) {
  // console.log("verify", msg, sign);
  return sm2.doVerifySignature(msg, sign, platformPublicKey, {
    der: false, hash: true
  });
}

/**
 * 将Buffer转换为base64编码的字符串
 * @param buffer {Buffer}
 * @return {string}
 */
function encodeBase64(buffer) {
  return buffer.toString('base64');
}

/**
 * 将base64编码的字符串转换为Buffer
 * @param base64string
 * @return {Buffer}
 */
function decodeBase64(base64string) {
  return Buffer.from(base64string, 'base64');
}

/**
 * sm2加密
 * @param buffer {Buffer} 待加密的字节缓冲区
 * @return {string} ascii编码的hex字符串
 */
function sm2Encrypt(buffer) {
  return sm2.doEncrypt(encodeBase64(buffer), platformPublicKey, cipherMode);
}

/**
 * sm2解密
 * @param encryptData {string} 待解密的ascii编码的hex字符串
 * @return {Buffer} 解密后的字节缓冲区
 */
function sm2Decrypt(encryptData) {
  const base64Data = sm2.doDecrypt(encryptData, platformPrivateKey + config.loginKey, cipherMode);
  // console.log("base64Data", base64Data)
  return decodeBase64(base64Data);
}

let sm4 = null;

function setSM4Param(key, iv) {
  const sm4Config = {
    // encrypt/decypt main key; cannot be omitted
    key: key,

    // optional; can be 'cbc' or 'ecb'
    mode: 'cbc', // default

    // optional; when use cbc mode, it's necessary
    iv: iv,

    // optional: this is the cipher data's type; Can be 'base64' or 'text'
    cipherType: 'base64' // default is base64
  };
  if (typeof window === 'object') {
    window.TextEncoder = require('util').TextEncoder;
    window.TextDecoder = require('util').TextDecoder;
  }

  sm4 = new SM4(sm4Config);
  console.log('setSM4Param', key, iv)
}

setSM4Param("JeF8U9wHFOMfs2Y8", "UISwD9fW6cFh9SNS");

/**
 * SM4加密
 * @param buffer {Buffer}
 * @return {string}
 */
function sm4Encrypt(buffer) {
  let ciphertext = sm4.encrypt(buffer.toString("base64"));
  return ciphertext
}

/**
 * SM4解密
 * @param encryptText {string} 加密的文本
 * @return {Buffer} 解密后的buffer
 */
function sm4Decrypt(encryptText) {
  let decryptText = sm4.decrypt(encryptText);
  return Buffer.from(decryptText, "base64")
}

let commuAesKey, fileAesKey;

function setAesKey(_commuAesKey, _fileAesKey) {
  commuAesKey = _commuAesKey;
  fileAesKey = _fileAesKey;
}

function _aesEncrypt(cipher, buffer) {
  let crypted = cipher.update(buffer, 'hex', 'hex');
  crypted += cipher.final('hex');
  return crypted;
}

function _aesDecrypt(decipher, encryptText) {
  let decrypted = decipher.update(encryptText, 'hex', 'hex') + decipher.final('hex');
  return Buffer.from(decrypted, "hex")
}

function fileEncrypt(buffer) {
  let fileCipher = crypto.createCipher('aes-128-ecb', fileAesKey);
  return _aesEncrypt(fileCipher, buffer);
}

function fileDecrypt(encryptText) {
  let fileDecipher = crypto.createDecipher('aes-128-ecb', fileAesKey);
  return _aesDecrypt(fileDecipher, encryptText);
}

function commuEncrypt(buffer) {
  let commuCipher = crypto.createCipher('aes-128-ecb', commuAesKey);
  return _aesEncrypt(commuCipher, buffer);
}

function commuDecrypt(encryptText) {
  let commuDecipher = crypto.createDecipher('aes-128-ecb', commuAesKey);
  return _aesDecrypt(commuDecipher, encryptText);
}

/**
 * 点分ip地址转换为四个字节的整数
 * @param ip
 * @returns {number}
 */
function ip2int(ip) {
  let num = 0;
  if (ip === "") {
    return num;
  }
  let aNum = ip.split(".");
  if (aNum.length !== 4) {
    return num;
  }
  num += parseInt(aNum[0]) << 24;
  num += parseInt(aNum[1]) << 16;
  num += parseInt(aNum[2]) << 8;
  num += parseInt(aNum[3]) << 0;
  // num = num >>> 0;
  return num;
}

/**
 * 四个字节的整数转换为点分ip地址
 * @param number
 * @returns {string}
 */
function int2ip(number) {
  // if(number <= 0) {
  //     return '';
  // }
  const ip3 = (number << 0) >>> 24;
  const ip2 = (number << 8) >>> 24;
  const ip1 = (number << 16) >>> 24;
  const ip0 = (number << 24) >>> 24;

  return ip3 + "." + ip2 + "." + ip1 + "." + ip0;
}

/**
 * mac地址去冒号
 * @param mac
 * @returns {number}
 */
function mac2str(mac) {
  let str = "000000000000";
  if (mac === "") {
    return str;
  }
  str = mac.replace(new RegExp(/(:)/g), "")
  return str;
}

/**
 * mac地址加冒号
 * @param str
 * @returns {string}
 */
function str2mac(str) {
  str = [...str].map((o, i) => i % 2 == 1 ? o + ":" : o).join("")
  return str.substr(0, str.length - 1)
}

/**
 * 读取json文件
 * @param params
 */
function readJson() {
  fs.readFile(__dirname + '/../config.json', function (err, data) {
    if (err) {
      return console.error(err);
    }
    let string = data.toString();//将二进制的数据转换为字符串
    string = JSON.parse(string);//将字符串转换为json对象
    return string.data;
  })
}

/**
 * 读取json文件并替换host,port
 * @param params
 */
function writeJson(host, port) {
  fs.readFile(__dirname + '/../config.json', function (err, data) {
    if (err) {
      return console.error(err);
    }
    let string = data.toString();//将二进制的数据转换为字符串
    string = JSON.parse(string);//将字符串转换为json对象
    string.host = host;//将传来的对象push进数组对象中
    string.port = port;
    let str = JSON.stringify(string);//因为nodejs的写入文件只认识字符串或者二进制数，所以把json对象转换成字符串重新写入json文件中
    fs.writeFile(__dirname + '/../config.json', str, function (err) {
      if (err) {
        console.error(err);
      }
    })
  })
}

function writeTempJson(host, port) {
    let config_path = '';
    if (process.platform == 'win32') {
        config_path = __dirname + '/../../temp_config.json';
    } else {
        config_path = getPackagePath(__dirname) + 'temp_config.json';
    }
    fs.readFile(config_path, function (err, data) {
        if (err) {
            return console.error(err);
        }
        let string = data.toString();//将二进制的数据转换为字符串
        string = JSON.parse(string);//将字符串转换为json对象
        string.host = host;//将传来的对象push进数组对象中
        string.port = port;
        let str = JSON.stringify(string, null, "\t");//因为nodejs的写入文件只认识字符串或者二进制数，所以把json对象转换成字符串重新写入json文件中
        console.log('写入文件路径', config_path);
        fs.writeFile(config_path, str, function (err) {
            if (err) {
                console.error(err);
            }
        })
    })
}

function getPackagePath(str) {
    if (str.includes('.asar') && str.length) {
        let newStr = str.split('.asar')[0];
        if (process.platform == 'win32') {
            return newStr.substr(0, newStr.lastIndexOf("\\") + 1);
        } else {
            return newStr.substr(0, newStr.lastIndexOf("/") + 1);
        }
    } else {
        return str;
    }
}
// get方式取字段名用到的方法  传入  protobuf对象及字段名 => protobuf对象.get对象名() 对象名会转驼峰
function getProtobufFieldValue(messageInstance, fieldName, isArray = false) {
  // 将下划线命名转换为驼峰命名
  const camelCaseName = fieldName.toLowerCase().replace(/_([a-z])/g, (match, p1) => p1.toUpperCase());
  let methodName = "get" + camelCaseName.charAt(0).toUpperCase() + camelCaseName.slice(1);
  isArray ? methodName = methodName + 'List' : '';
  if (typeof messageInstance[methodName] === "function") {
    return messageInstance[methodName]();
  }
  return undefined;
}
// 从预设字段名模板中循环取值: 如 ['a', 'b'] => getA()  getB()
function getArrayDataByBufDataAndTemp(bufData, Temp) {
  const data = {}
  if (!Temp) console.log('template is null', bufData, Temp);
  Temp.forEach(itm => {
    data[itm] = getProtobufFieldValue(bufData, itm)

  })
  return data
}
// 获取对象类型
function getDataType(data) {
  // NUMBER/STRING/UNDEFINED/BOOLEAN/OBJECT/ARRAY/FUNCTION/NULL
  return Object.prototype.toString.call(data).slice(8, -1).trim().toUpperCase()
  }
// 判断传入的对象有没有某个属性，如果有的话从对象取值
function setEnumNameByFieldAndCode(fieldName, obj, Enum) {
  if (getDataType(obj) === 'OBJECT' && fieldName in obj && obj[fieldName] in Enum) {
    return Enum[obj[fieldName]]
  }
  return obj[fieldName]
}

function buildObjectFromProto(schema, protoData, isMap2China = false) {
  const result = {};
  if (!schema.INFO) return result;
  const fieldNames = Object.keys(schema.INFO);
  // 只保留 schema 中对象类型的字段用于递归
  const recursiveFields = Object.keys(schema).filter(
      key => key !== 'INFO' && typeof schema[key] === 'object' && !Array.isArray(schema[key])
  );
  // 如果启用中文映射，缓存当前层的 MAP2CHINA
  const map2ChinaConfig = isMap2China && schema.MAP2CHINA ? schema.MAP2CHINA : null;
  for (const field of fieldNames) {
    if (recursiveFields.includes(field)) {
      const arrayValue = getProtobufFieldValue(protoData, field, true);
      if (Array.isArray(arrayValue)) {
        result[field] = arrayValue.length
            ? arrayValue.map(item => buildObjectFromProto(schema[field], item, isMap2China))
            : []; // 保持数组类型
      } else {
        const itemProto = getProtobufFieldValue(protoData, field, false);
        result[field] = itemProto
            ? buildObjectFromProto(schema[field], itemProto, isMap2China)
            : null;
      }
    } else {
      let value = getProtobufFieldValue(protoData, field, false);
      value = value ?? '--';
      // 如果需要映射中文，并且 MAP2CHINA 中有该字段的映射表
      if (map2ChinaConfig && map2ChinaConfig[field] && value !== '--') {
        const mappedValue = map2ChinaConfig[field][value];
        value = mappedValue !== undefined ? mappedValue : value; // 找不到就保留原值
      }
      result[field] = value;
    }
  }
  return result;
}

module.exports = {
  buildObjectFromProto, // 逐级解构protobuf数据
  getArrayDataByBufDataAndTemp, // 模板循环取值
  getProtobufFieldValue, // get单个值
  PACKET_HEADER_BYTE: PACKET_HEADER_BYTE,
  PACKET_USER_DEFINE_PACKET_ID_BYTE: PACKET_USER_DEFINE_PACKET_ID_BYTE,
  PLATFORM_TYPE_GUI_MESSAGE: PLATFORM_TYPE_GUI_MESSAGE,
  packGuiMsg: packGuiMsg,
  packPlatformMsg: packPlatformMsg,
  unpackGuiMsg: unpackGuiMsg,
  getTimestamp: getTimestamp,
  getDateMax:getDateMax,
  getThisTimestamp,
  getThisDate,
  getThisDateMs,
  encodeBase64,
  decodeBase64,
  sm2Sign,
  sm2Verify,
  sm2Encrypt,
  sm2Decrypt,
  setSM4Param,
  sm4Encrypt,
  sm4Decrypt,
  setAesKey,
  fileEncrypt,
  fileDecrypt,
  commuEncrypt,
  commuDecrypt,
  ip2int,
  int2ip,
  mac2str,
  str2mac,
  readJson,
  writeJson,
  writeTempJson,
  logger,
  enableLog,

  /**
   * 生成16位md5摘要(字符串表示)
   * @param message 来源数据
   * @deprecated 用md5_bytes代替
   * @returns {string}
   */
  md5: function (message) {
    const md5 = crypto.createHash('md5');

    const result = md5.update(message).digest('hex');
    return result.substr(8, 16);
  },
  /**
   * 生成摘要，原始字节表示
   * @param message 来源数据
   * @return {Buffer} 摘要字节
   */
  md5_bytes: function (message) {
    const md5 = crypto.createHash('md5');

    const result = md5.update(message).digest();
    return result;
  },
  guid() {
    function s4() {
      return Math.floor((1 + Math.random()) * 0x10000)
        .toString(16)
        .substring(1);
    }

    return s4() + s4() + '-' + s4() + '-' + s4() + '-' + s4() + '-' + s4() + s4() + s4();
  },
  genTaskId() {
    return this.guid().substr(0, 16)
  },
  saveToFile,
  saveToCSV,
  readFromFile,
  setUserSign(userSign) {
    g_UserSign = userSign;
  },
  /**
   * 将服务器返回的buffer转换为字符串前，去掉首尾的0
   * @param buf {Buffer}
   * @returns {*}
   */
  bufferToString(buf) {
    let begin = 0, end = buf.length;
    if (buf[0] === 0) {
      for (let i = 1; i < buf.length; i++) {
        if (buf[i] !== 0) {
          begin = i;
          break;
        }
        if (i == buf.length - 1 && buf[i] == 0) {
          begin = 0;
          break;
        }
      }
    }
    if (buf[end - 1] === 0) {
      for (let i = end - 1; i >= 0; i--) {
        if (buf[i] !== 0) {
          end = i;
          break;
        }
        if (i == 0 && buf[i] == 0) {
          end = -1;
          break;
        }
      }
    }
    return buf.slice(begin, end + 1).toString('utf8').trim();
  },
  trimString(src) {
    return this.bufferToString(Buffer.from(src))
  },
  genServerMsgKey(funcType, packetType) {
    return funcType*10000 + packetType
  },
  // 获取对象类型
  getDataType,
  setEnumNameByFieldAndCode,
};
