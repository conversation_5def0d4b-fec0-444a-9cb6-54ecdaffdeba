'use strict';
const device_test = require('./device_test');
const net_card_test = require('./net_card_test');
const route_test = require('./route_test');
const ntp_param_test = require('./ntp_param_test');
const commu_param_test = require('./commu_param_test');
const collect_event_test = require('./collect_event_test');
const upload_event_test = require('./upload_event_test');
const baseline_test = require('./baseline_test');
const control_test = require('./control_test');
const upgrade_test = require('./upgrade_test');
const monitored_object_test = require('./monitored_object_test');

/**
 * 简单的单元测试接口
 * @param testFunction 待执行测试的函数，测试函数需遵循Promise标准
 */
function runUnitTest(testFunction) {
    new Promise(testFunction).then(function (success) {
        if (success) {
            console.log(testFunction.name + ' success');
        } else {
            console.log(testFunction.name + ' failed');
        }
    }).catch(function (reason) {
        console.error(testFunction.name + ' failed', reason);
    });
}

// runUnitTest(device_test.testAddDevice);
// runUnitTest(device_test.testLoadDevice);
// runUnitTest(device_test.testUpdateDevice);
// runUnitTest(device_test.testRemoveDevice);

// runUnitTest(net_card_test.testLoadNetCard);
// runUnitTest(net_card_test.testAddNetCard);
// runUnitTest(net_card_test.testUpdateNetCard);
// runUnitTest(net_card_test.testRemoveNetCard);

// runUnitTest(route_test.testAddRoute);
// runUnitTest(route_test.testUpdateRoute);
// runUnitTest(route_test.testLoadRoute);
// runUnitTest(route_test.testRemoveRoute);

// runUnitTest(ntp_param_test.testUpdateNtpParam);
// runUnitTest(ntp_param_test.testLoadNtpParam);

// runUnitTest(commu_param_test.testUpdateCommuParam);
// runUnitTest(commu_param_test.testLoadCommuParam);

// runUnitTest(collect_event_test.testLoadCollectEvents);


// runUnitTest(upload_event_test.testLoadUploadEvents);

// runUnitTest(baseline_test.testStartBaseline);
// runUnitTest(baseline_test.testStopBaseline);
// runUnitTest(baseline_test.testGetBaselineState);
// runUnitTest(baseline_test.testGetBaselineResult);

// runUnitTest(control_test.testCloseNetwork);


// runUnitTest(upgrade_test.testUpgrade);

// runUnitTest(monitored_object_test.testSetWhiteNetworkList);
// runUnitTest(monitored_object_test.testSetKeyFileList);
// runUnitTest(monitored_object_test.testLoad);
