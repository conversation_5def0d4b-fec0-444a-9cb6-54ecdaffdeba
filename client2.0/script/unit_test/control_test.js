'use strict';

const ProxyServer_pb = require('../pb/ProxyServer_pb');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const util = require('../../lib/util');
const tcp_client = require('../../lib/tcp_client');
const config = require("../../config.json");
const control_codec = require('../platform_codec/control_codec');

// 响应消息等待超时，毫秒
const RESPOND_WAIT_TIMEOUT = 15000;

module.exports = {
    testCloseNetwork: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        tcp_client.connect(function (dp_packet) {
            console.log('dp_packet', dp_packet);
            const baseline = control_codec.unpackCloseNetwork(dp_packet.content);
            console.log('baseline', baseline.getTaskId(), baseline.getResult());

            resolve(true);
            clearTimeout(timer);
        }, config.host, config.port);
        tcp_client.send(control_codec.packCloseNetwork('uuiduuiduuiduuid', util.ip2int('**************')));
    },
};
