'use strict';

const ProxyServer_pb = require('../pb/ProxyServer_pb');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const util = require('../../lib/util');
const tcp_client = require('../../lib/tcp_client');
const config = require("../../config.json");
const monitored_object_codec = require('../platform_codec/monitored_object_codec');

// 响应消息等待超时，毫秒
const RESPOND_WAIT_TIMEOUT = 15000;

module.exports = {
    testSetWhiteNetworkList: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        tcp_client.connect(function (dp_packet) {
            console.log('dp_packet', dp_packet);
            const monitored_object = monitored_object_codec.unpackSetNetworkWhiteList(dp_packet.content);
            console.log('monitored_object', monitored_object.getTaskId(), monitored_object.getResult());

            resolve(true);
            clearTimeout(timer);

            // 释放连接
            tcp_client.close();
        }, config.host, config.port);
        tcp_client.send(monitored_object_codec.packSetNetworkWhiteList('uuiduuiduuiduuid', util.ip2int('**************'),
            'tcp ************* 2404\nudp 0.0.0.0 162'));
    },
    testSetKeyFileList: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        tcp_client.connect(function (dp_packet) {
            console.log('dp_packet', dp_packet);
            const monitored_object = monitored_object_codec.unpackSetKeyFileList(dp_packet.content);
            console.log('monitored_object', monitored_object.getTaskId(), monitored_object.getResult());

            resolve(true);
            clearTimeout(timer);

            // 释放连接
            tcp_client.close();
        }, config.host, config.port);
        tcp_client.send(monitored_object_codec.packSetKeyFileList('uuiduuiduuiduuid', util.ip2int('**************'),
            '/root/1.sh\n/home/<USER>/'));
    },

    testLoad: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        tcp_client.connect(function (dp_packet) {
            console.log('dp_packet', dp_packet);
            const monitored_object = monitored_object_codec.unpackLoadMonitoredObject(dp_packet.content);
            console.log('monitored_object', monitored_object.getContent());

            resolve(true);
            clearTimeout(timer);

            // 释放连接
            tcp_client.close();
        }, config.host, config.port);
        tcp_client.send(monitored_object_codec.packLoadMonitoredObject('uuiduuiduuiduuid', util.ip2int('**************'), PublicDefine_pb.MonitoredObjectSubType.MONITORED_OBJECT_SUBTYPE_DANGEROUS_CMD_LIST));
    },
};
