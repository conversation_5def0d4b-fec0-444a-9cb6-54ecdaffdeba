'use strict';

const ProxyServer_pb = require('../pb/ProxyServer_pb');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const util = require('../../lib/util');
const tcp_client = require('../../lib/tcp_client');
const config = require("../../config.json");
const upgrade_codec = require('../platform_codec/upgrade_codec');
const fs = require("fs");

// 响应消息等待超时，毫秒
const RESPOND_WAIT_TIMEOUT = 15000;

module.exports = {
    testUpgrade: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        tcp_client.connect(function (dp_packet) {
            const baseline = upgrade_codec.unpackUpgrade(dp_packet.content);
            console.log('return_value', dp_packet.return_value);

            resolve(true);
            clearTimeout(timer);
        }, config.host, config.port);
        tcp_client.send(upgrade_codec.packUpgrade(fs.readFileSync('/home/<USER>/network-monitor/client/script/unit_test/tests.js')));
    },
};
