'use strict';

const ProxyServer_pb = require('../pb/ProxyServer_pb');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const util = require('../../lib/util');
const tcp_client = require('../../lib/tcp_client');
const config = require("../../config.json");
const device_codec = require('../platform_codec/device_codec');

// 响应消息等待超时，毫秒
const RESPOND_WAIT_TIMEOUT = 5000;

module.exports = {
    testLoadDevice: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        tcp_client.connect(function (dp_packet) {
            console.log('dp_packet', dp_packet);
            const devices = device_codec.unpackLoadDevice(dp_packet.content);
            for(const device of devices) {
                console.log('device', device.getId(), device.getDeviceName(), device.getSnmpWriteCommunity());
            }

            resolve(true);
            clearTimeout(timer);
        }, config.host, config.port);
        tcp_client.send(device_codec.packLoadDevice());
    },
    testAddDevice: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        let device = new PublicDefine_pb.MSG_Device();
        device.setId(111);
        device.setDeviceName('devic哈哈ename');
        device.setIp1(util.ip2int('*************'));
        device.setMac1('000c297653c3');
        device.setIp2(util.ip2int('*************'));
        device.setMac2('000c297653c9');
        device.setSnmpWriteCommunity('writec');
        tcp_client.connect(function (dp_packet) {
            if (dp_packet.return_value === 0) {
                console.log('资产添加成功');

                resolve(true);
                clearTimeout(timer);
            } else {
                console.log('资产添加失败');

                resolve(false);
            }
        }, config.host, config.port);
        tcp_client.send(device_codec.packAddDevice(device));
    },
    testUpdateDevice: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        let device = new PublicDefine_pb.MSG_Device();
        device.setId(192192);
        device.setDeviceName('new资产');
        device.setIp1(util.ip2int('*************'));
        device.setSnmpWriteCommunity('writec1');
        tcp_client.connect(function (dp_packet) {
            if (dp_packet.return_value === 0) {
                console.log('资产更新成功');

                resolve(true);
                clearTimeout(timer);
            } else {
                console.log('资产更新失败');

                resolve(false);
            }
        }, config.host, config.port);
        tcp_client.send(device_codec.packUpdateDevice(device));
    },
    testRemoveDevice: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        let ids = [192192, 192193];
        tcp_client.connect(function (dp_packet) {
            if (dp_packet.return_value === 0) {
                console.log('资产删除新成功', ids);

                resolve(true);
                clearTimeout(timer);
            } else {
                console.log('资产删除失败', ids);

                resolve(false);
            }
        }, config.host, config.port);
        tcp_client.send(device_codec.packRemoveDevice(ids));
    },
};
