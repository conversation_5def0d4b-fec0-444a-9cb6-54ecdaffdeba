'use strict';

const PublicDefine_pb = require('../pb/PublicDefine_pb');
const util = require('../../lib/util');
const tcp_client = require('../../lib/tcp_client');
const config = require("../../config.json");
const route_codec = require('../platform_codec/route_codec');

// 响应消息等待超时，毫秒
const RESPOND_WAIT_TIMEOUT = 5000;

module.exports = {
    testLoadRoute: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        tcp_client.connect(function (dp_packet) {
            console.log('dp_packet', dp_packet);
            const routes = route_codec.unpackLoadRoute(dp_packet.content);
            console.log('route', util.int2ip(routes[0].getDestSegment()), util.int2ip(routes[0].getGateway()));

            resolve(true);
            clearTimeout(timer);
        }, config.host, config.port);
        tcp_client.send(route_codec.packLoadRoute());
    },
    testAddRoute: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        let route = new PublicDefine_pb.MSG_Route();
        route.setId(1);
        route.setDestSegment(util.ip2int('************'));
        route.setDestMask(util.ip2int('*************'));
        route.setGateway(util.ip2int('***********'));
        tcp_client.connect(function (dp_packet) {
            if (dp_packet.return_value === 0) {
                console.log('路由添加成功');

                resolve(true);
                clearTimeout(timer);
            } else {
                console.log('路由添加失败');

                resolve(false);
            }
        }, config.host, config.port);
        tcp_client.send(route_codec.packAddRoute(route));
    },
    testUpdateRoute: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        let route = new PublicDefine_pb.MSG_Route();
        route.setId(1);
        route.setDestSegment(util.ip2int('************'));
        route.setDestMask(util.ip2int('*************'));
        route.setGateway(util.ip2int('************'));
        tcp_client.connect(function (dp_packet) {
            if (dp_packet.return_value === 0) {
                console.log('路由更新成功');

                resolve(true);
                clearTimeout(timer);
            } else {
                console.log('路由更新失败');

                resolve(false);
            }
        }, config.host, config.port);
        tcp_client.send(route_codec.packUpdateRoute(route));
    },
    testRemoveRoute: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        let ids = [1,2];
        tcp_client.connect(function (dp_packet) {
            if (dp_packet.return_value === 0) {
                console.log('路由删除成功', ids);

                resolve(true);
                clearTimeout(timer);
            } else {
                console.log('路由删除失败', ids);

                resolve(false);
            }
        }, config.host, config.port);
        tcp_client.send(route_codec.packRemoveRoute(ids));
    },
};
