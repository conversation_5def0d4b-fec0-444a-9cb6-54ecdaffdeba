'use strict';

const PublicDefine_pb = require('../pb/PublicDefine_pb');
const util = require('../../lib/util');
const tcp_client = require('../../lib/tcp_client');
const config = require("../../config.json");
const net_card_codec = require('../platform_codec/net_card_codec');

// 响应消息等待超时，毫秒
const RESPOND_WAIT_TIMEOUT = 5000;

module.exports = {
    testLoadNetCard: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        tcp_client.connect(function (dp_packet) {
            console.log('dp_packet', dp_packet);
            const net_cards = net_card_codec.unpackLoadNetCard(dp_packet.content);
            console.log('net_card', net_cards[0].getNetcardName(), net_cards[0].getMask());

            resolve(true);
            clearTimeout(timer);
        }, config.host, config.port);
        tcp_client.send(net_card_codec.packLoadNetCard());
    },
    testAddNetCard: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        let net_card = new PublicDefine_pb.MSG_NetCard();
        net_card.setId(1);
        net_card.setNetcardName('devic哈哈ename');
        net_card.setIp(util.ip2int('*************'));
        net_card.setMask(util.ip2int('*************'));
        tcp_client.connect(function (dp_packet) {
            if (dp_packet.return_value === 0) {
                console.log('网卡添加成功');

                resolve(true);
                clearTimeout(timer);
            } else {
                console.log('网卡添加失败');

                resolve(false);
            }
        }, config.host, config.port);
        tcp_client.send(net_card_codec.packAddNetCard(net_card));
    },
    testUpdateNetCard: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        let net_card = new PublicDefine_pb.MSG_NetCard();
        net_card.setId(1);
        net_card.setNetcardName('new网卡');
        net_card.setIp(util.ip2int('*************'));
        net_card.setMask(util.ip2int('*************'));
        tcp_client.connect(function (dp_packet) {
            if (dp_packet.return_value === 0) {
                console.log('网卡更新成功');

                resolve(true);
                clearTimeout(timer);
            } else {
                console.log('网卡更新失败');

                resolve(false);
            }
        }, config.host, config.port);
        tcp_client.send(net_card_codec.packUpdateNetCard(net_card));
    },
    testRemoveNetCard: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        let ids = [1,2];
        tcp_client.connect(function (dp_packet) {
            if (dp_packet.return_value === 0) {
                console.log('网卡删除成功', ids);

                resolve(true);
                clearTimeout(timer);
            } else {
                console.log('网卡删除失败', ids);

                resolve(false);
            }
        }, config.host, config.port);
        tcp_client.send(net_card_codec.packRemoveNetCard(ids));
    },
};
