'use strict';

const ProxyServer_pb = require('../pb/ProxyServer_pb');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const util = require('../../lib/util');
const tcp_client = require('../../lib/tcp_client');
const config = require("../../config.json");
const event_param_codec = require('../platform_codec/event_param_codec');

// 响应消息等待超时，毫秒
const RESPOND_WAIT_TIMEOUT = 5000;

module.exports = {
    testAddEventParam: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        let event_param = new PublicDefine_pb.MSG_EventParam();
        event_param.setId(1);
        event_param.setCpuThreshold(66);
        tcp_client.connect(function (dp_packet) {
            if (dp_packet.return_value === 0) {
                console.log('事件参数添加成功');

                resolve(true);
                clearTimeout(timer);
            } else {
                console.log('事件参数添加失败');
                resolve(false);
            }
        }, config.host, config.port);
        tcp_client.send(event_param_codec.packAddEventParam(event_param));
    },
    testLoadEventParam: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        tcp_client.connect(function (dp_packet) {
            console.log('dp_packet', dp_packet);
            const event_param = event_param_codec.unpackLoadEventParam(dp_packet.content);
            console.log('event_param', event_param.getCpuThreshold(), event_param.getHistoryEventReportInterval());

            resolve(true);
            clearTimeout(timer);
        }, config.host, config.port);
        tcp_client.send(event_param_codec.packLoadEventParam());
    }
};
