'use strict';

const ProxyServer_pb = require('../pb/ProxyServer_pb');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const util = require('../../lib/util');
const tcp_client = require('../../lib/tcp_client');
const config = require("../../config.json");
const baseline_codec = require('../platform_codec/baseline_codec');

// 响应消息等待超时，毫秒
const RESPOND_WAIT_TIMEOUT = 15000;

module.exports = {
    testStartBaseline: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        tcp_client.connect(function (dp_packet) {
            console.log('dp_packet', dp_packet);
            const baseline = baseline_codec.unpackStartBaseline(dp_packet.content);
            console.log('baseline', baseline.getTaskId(), baseline.getResult());

            resolve(true);
            clearTimeout(timer);
        }, config.host, config.port);
        tcp_client.send(baseline_codec.packStartBaseline('uuiduuiduuiduuid', util.ip2int('***********')));
    },
    testStopBaseline: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        tcp_client.connect(function (dp_packet) {
            console.log('dp_packet', dp_packet);
            const baseline = baseline_codec.unpackStopBaseline(dp_packet.content);
            console.log('baseline', baseline.getTaskId(), baseline.getResult());

            resolve(true);
            clearTimeout(timer);
        }, config.host, config.port);
        tcp_client.send(baseline_codec.packStopBaseline('uuiduuiduuiduuid', util.ip2int('**************')));
    },
    testGetBaselineState: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        tcp_client.connect(function (dp_packet) {
            console.log('dp_packet', dp_packet);
            const baseline = baseline_codec.unpackBaselineGetState(dp_packet.content);
            console.log('baseline', baseline.getTaskId(), baseline.getResult());

            resolve(true);
            clearTimeout(timer);
        }, config.host, config.port);
        tcp_client.send(baseline_codec.packBaselineGetState('uuiduuiduuiduuid', util.ip2int('**************')));
    },
    testGetBaselineResult: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        tcp_client.connect(function (dp_packet) {
            console.log('dp_packet', dp_packet);
            const baseline = baseline_codec.unpackBaselineGetResult(dp_packet.content);
            console.log('baseline', baseline.getTaskId(), baseline.getResult());

            resolve(true);
            clearTimeout(timer);
        }, config.host, config.port);
        tcp_client.send(baseline_codec.packBaselineGetResult('uuiduuiduuiduuid', util.ip2int('**************')));
    },
    testLoadBaselines: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        tcp_client.connect(function (msgId, pb) {
            if (msgId !== ProxyServer_pb.PSMessageId.PSMESSAGEID_PCLOADBASELINES) {
                console.error('invalid msg', msgId);
                return;
            }
            const msg = ProxyServer_pb.MSG_PCLoadBaselines.deserializeBinary(pb);
            const baselines = msg.getBaselinesList();
            console.log('BaselinesList', baselines);
            for (const baseline of baselines) {
                console.log('baseline', baseline.getTaskId(), baseline.getResult(), baseline.getStartTime(), baseline.getStopTime(), baseline.getCompleteTime(), baseline.getError(), baseline.getDestIp())
            }

            resolve(true);
            clearTimeout(timer);
        }, config.host, config.port);

        let sendMsg = new ProxyServer_pb.MSG_CPLoadBaselines();
        tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADBASELINES, sendMsg, "user_sign");
    },
};
