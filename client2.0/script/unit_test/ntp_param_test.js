'use strict';

const ProxyServer_pb = require('../pb/ProxyServer_pb');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const util = require('../../lib/util');
const tcp_client = require('../../lib/tcp_client');
const config = require("../../config.json");
const ntp_param_codec = require('../platform_codec/ntp_param_codec');

// 响应消息等待超时，毫秒
const RESPOND_WAIT_TIMEOUT = 5000;

module.exports = {
    testUpdateNtpParam: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        let ntp_param = new PublicDefine_pb.MSG_NtpParam();
        ntp_param.setId(1);
        ntp_param.setMainClockMainNetworkIp(66);
        ntp_param.setNtpIsBroadcast(22);
        tcp_client.connect(function (dp_packet) {
            if (dp_packet.return_value === 0) {
                console.log('NTP参数添加成功');

                resolve(true);
                clearTimeout(timer);
            } else {
                console.log('NTP参数添加失败');
                resolve(false);
            }
        }, config.host, config.port);
        tcp_client.send(ntp_param_codec.packUpdateNtpParam(ntp_param));
    },
    testLoadNtpParam: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        tcp_client.connect(function (dp_packet) {
            console.log('dp_packet', dp_packet);
            const ntp_param = ntp_param_codec.unpackLoadNtpParam(dp_packet.content);
            console.log('ntp_param', ntp_param.getMainClockMainNetworkIp(), ntp_param.getNtpIsBroadcast());

            resolve(true);
            clearTimeout(timer);
        }, config.host, config.port);
        tcp_client.send(ntp_param_codec.packLoadNtpParam());
    }
};
