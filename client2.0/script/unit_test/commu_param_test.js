'use strict';

const ProxyServer_pb = require('../pb/ProxyServer_pb');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const util = require('../../lib/util');
const tcp_client = require('../../lib/tcp_client');
const config = require("../../config.json");
const commu_param_codec = require('../platform_codec/commu_param_codec');

// 响应消息等待超时，毫秒
const RESPOND_WAIT_TIMEOUT = 5000;

module.exports = {
    testUpdateCommuParam: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        let commu_param = new PublicDefine_pb.MSG_CommuParam();
        commu_param.setId(1);
        commu_param.setServerListenPort(66);
        commu_param.setProxyListenPport(22);
        let platform = new PublicDefine_pb.MSG_Platform();
        platform.setIp(util.ip2int('*************'));
        platform.setGroup(11);
        commu_param.addPlatforms(platform);
        tcp_client.connect(function (dp_packet) {
            if (dp_packet.return_value === 0) {
                console.log('COMMU参数添加成功');

                resolve(true);
                clearTimeout(timer);
            } else {
                console.log('COMMU参数添加失败');
                resolve(false);
            }
        }, config.host, config.port);
        tcp_client.send(commu_param_codec.packUpdateCommuParam(commu_param));
    },
    testLoadCommuParam: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        tcp_client.connect(function (dp_packet) {
            console.log('dp_packet', dp_packet);
            const commu_param = commu_param_codec.unpackLoadCommuParam(dp_packet.content);
            console.log('commu_param', commu_param.getServerListenPort(), commu_param.getProxyListenPport(), util.int2ip(commu_param.getPlatformsList()[0].getIp()), commu_param.getPlatformsList()[0].getGroup());

            resolve(true);
            clearTimeout(timer);
        }, config.host, config.port);
        tcp_client.send(commu_param_codec.packLoadCommuParam());
    }
};
