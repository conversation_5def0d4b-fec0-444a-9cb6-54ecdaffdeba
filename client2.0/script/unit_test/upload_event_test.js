'use strict';

const ProxyServer_pb = require('../pb/ProxyServer_pb');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const util = require('../../lib/util');
const tcp_client = require('../../lib/tcp_client');
const config = require("../../config.json");
const upload_event_codec = require('../platform_codec/upload_event_codec');

// 响应消息等待超时，毫秒
const RESPOND_WAIT_TIMEOUT = 5000;

module.exports = {
    testLoadUploadEvents: function (resolve, reject) {
        const timer = setTimeout(function () {
            reject('timeout');
        }, RESPOND_WAIT_TIMEOUT);

        tcp_client.connect(function (dp_packet) {
            console.log('dp_packet', dp_packet);
            const upload_events = upload_event_codec.unpackLoadUploadEvent(dp_packet.content);
            console.log('upload_event', upload_events);

            resolve(true);
            clearTimeout(timer);
        }, config.host, config.port);
        tcp_client.send(upload_event_codec.packLoadUploadEvent(1000, PublicDefine_pb.DeviceType.DEVICETYPE_VEAD, 0, 1542853468, 1542853535));
    }
};
