'use strict';
/**
 * 特征数据更新编码解码器
 */

const util = require('../../lib/util');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const FEATURE_DATA_BLOCK_SIZE = 16 + 4;

function _encodeFeatureData(task_id, dest_ip,feature_data) {
    let buffer = Buffer.alloc(FEATURE_DATA_BLOCK_SIZE+feature_data.length);
    let offset = 0;

    buffer.write(task_id, offset);
    offset += 16;

    buffer.writeInt32BE(dest_ip, offset);
    offset += 4;
    buffer.write(feature_data, offset);
    offset += feature_data.length;

    return buffer;
}

function _decodeFeatureData(content) {
    if (content.length < 16) {
        return;
    }
    let buffer = Buffer.from(content);
    let featureDate = new PublicDefine_pb.MSG_CharacterUpdate();
    let offset = 0;

    featureDate.setTaskId(util.trimString(buffer.toString('utf8', offset, offset + 16)));
    offset += 16;

    featureDate.setResult(util.trimString(buffer.toString('utf8', offset)));
    return featureDate;
}

module.exports = {
    /**
     * 漏扫特征数据更新
     * @param task_id 任务唯一id
     * @param dest_ip 目标主机ip
     * @returns {Buffer}
     */
    packUpdateXscanFeatureData: function (task_id, dest_ip,feature_data) {
        const content = _encodeFeatureData(task_id, dest_ip,feature_data);
        return util.packPlatformMsg(
          PublicDefine_pb.PlatformType.PLATFORM_TYPE_CHARACTER_UPDATE,
          PublicDefine_pb.CharacterUpdateSubType.CHARACTER_UPDATE_SUBTYPE_LEAK,
          0,
          content.length + 4 + 64,
          content);
    },
    unpackUpdateXscanFeatureData: function (content) {
        return _decodeFeatureData(content);
    },
    /**
     * 版本管理特征数据更新
     * @param task_id 任务唯一id
     * @param dest_ip 目标主机ip
     * @returns {Buffer}
     */
    packUpdateVersionFeatureData: function (task_id, dest_ip,feature_data) {
        const content = _encodeFeatureData(task_id, dest_ip,feature_data);
        return util.packPlatformMsg(
          PublicDefine_pb.PlatformType.PLATFORM_TYPE_CHARACTER_UPDATE,
          PublicDefine_pb.CharacterUpdateSubType.CHARACTER_UPDATE_SUBTYPE_VERSION,
          0,
          content.length + 4 + 64,
          content);
    },
    unpackUpdateVersionFeatureData: function (content) {
        return _decodeFeatureData(content);
    },
};
