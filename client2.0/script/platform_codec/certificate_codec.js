'use strict';
/**
 * 证书编码解码器
 */

const util = require('../../lib/util');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const CERT_BLOCK_SIZE = 4 + 1 + 1 + 2 + 4 + 64;

/**
 * 编码证书，针对添加和修改的编码
 * @param cert
 * @returns {Buffer}
 */
function encodeCert(cert) {
  let buffer = Buffer.alloc(CERT_BLOCK_SIZE + cert.getCertContentLength());
  let offset = 0;

  buffer.writeInt32BE(cert.getId(), offset);
  offset += 4;

  buffer.writeInt8(cert.getCertType(), offset);
  offset++;

  buffer.writeInt8(cert.getReserved(), offset);
  offset++;

  buffer.writeUInt16BE(cert.getCertContentLength(), offset);
  offset += 2;

  buffer.writeInt32BE(cert.getPlatformIp(), offset);
  offset += 4;

  buffer.write(cert.getCertName(), offset);
  offset += 64;

  for (const cont of cert.getCertContent()) {
    buffer.writeUInt8(cont, offset);
    offset++;
  }
  return buffer;
}

/**
 *
 * @param content
 * @returns [PublicDefine_pb.MSG_Cert]
 */
function decodeCert(content) {
  // const cert_count = content.length / CERT_BLOCK_SIZE;
  // if (cert_count < 0.9) {
  //     return [];
  // }
  let buffer = Buffer.from(content);
  let offset = 0;
  let certs = [];
  while (offset < content.length) {
    let cert = new PublicDefine_pb.MSG_Cert();

    cert.setId(buffer.readInt32BE(offset));
    offset += 4;

    cert.setCertType(buffer.readInt8(offset));
    offset += 1;

    cert.setReserved(buffer.readInt8(offset));
    offset += 1;

    cert.setCertContentLength(buffer.readUInt16BE(offset));
    offset += 2;

    cert.setPlatformIp(buffer.readInt32BE(offset));
    offset += 4;

    cert.setCertName(util.trimString(buffer.toString('utf8', offset, offset + 64)));
    offset += 64;

    cert.setCertContent(buffer.slice(offset, cert.getCertContentLength()+offset));
    offset += cert.getCertContentLength();

    certs.push(cert);
  }

  return certs;
}

/**
 * 编码证书，针对删除编码
 * @param deviceIds
 * @returns {Buffer}
 * @private
 */
function _encodeRemoveCert(certIds) {
  let buffer = Buffer.alloc(certIds.length * 4);
  let offset = 0;
  for (let i = 0; i < certIds.length; i++) {
    buffer.writeUInt32BE(certIds[i], offset);
    offset += 4;
  }

  return buffer;
}

/**
 * 查看证书
 * @returns {Buffer}
 */
function packLoadCert() {
  return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE, PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_CERT, PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_LOAD, 0, null);
}

/**
 *
 * @param content
 * @returns {PublicDefine_pb.MSG_Device[]}
 */
function unpackLoadCert(content) {
  return decodeCert(content);
}

/**
 * 添加证书
 * @param cert 结构PublicDefine_pb.MSG_Device
 * @returns {Buffer}
 */
function packAddCert(cert) {
  const content = encodeCert(cert);
  return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE, PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_CERT, PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_ADD, content.length + 4 + 64, content);
}

/**
 * 修改证书
 * @param cert 结构PublicDefine_pb.MSG_Device
 * @returns {Buffer}
 */
function packEditCert(cert) {
  const content = encodeCert(cert);
  return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE, PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_CERT, PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_UPDATE, content.length + 4 + 64, content);
}

/**
 * 删除证书
 * @param cert 结构PublicDefine_pb.MSG_Device
 * @returns {Buffer}
 */
function packRemoveCert(cert) {
  const content = encodeCert(cert);
  return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE, PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_CERT, PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_DELETE, content.length + 4 + 64, content);
}

module.exports = {
  encodeCert,
  decodeCert,
  packLoadCert,
  unpackLoadCert,
  packAddCert,
  packEditCert,
  packRemoveCert
};
