'use strict';
/**
 * 事件参数编码解码器
 */

const util = require('../../lib/util');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const EVENT_BLOCK_SIZE = 4 + 1 + 1 + 4 + 1 + 2 + 1 + 4;

/**
 * 编码事件处理参数，针对添加和修改的编码
 * @param event_param
 * @returns {Buffer}
 * @private
 */
function _encodeEventParam(event_param) {
    let buffer = Buffer.alloc(EVENT_BLOCK_SIZE);
    let offset = 0;

    buffer.writeInt32BE(1, offset);// event_param.getId()
    offset += 4;

    buffer.writeUInt8(event_param.getCpuThreshold(), offset);
    offset += 1;

    buffer.writeUInt8(event_param.getMemThreshold(), offset);
    offset += 1;

    buffer.writeUInt32BE(event_param.getIfFlowThreshold(), offset);
    offset += 4;

    buffer.writeUInt8(event_param.getLoginFailedThreshold(), offset);
    offset += 1;

    buffer.writeUInt16BE(event_param.getEventMergeCycle(), offset);
    offset += 2;

    buffer.writeUInt8(event_param.getDiskThreshold(), offset);
    offset += 1;

    buffer.writeUInt32BE(event_param.getHistoryEventReportInterval(), offset);
    offset += 4;

    return buffer;
}

function decodeEventParam(content) {
    if (content.length < EVENT_BLOCK_SIZE) {
        return;
    }
    let buffer = Buffer.from(content);
    let event_param = new PublicDefine_pb.MSG_EventParam();
    let offset = 0;

    event_param.setId(buffer.readInt32BE(offset));
    offset += 4;

    event_param.setCpuThreshold(buffer.readUInt8(offset));
    offset++;

    event_param.setMemThreshold(buffer.readUInt8(offset));
    offset++;

    event_param.setIfFlowThreshold(buffer.readUInt32BE(offset));
    offset += 4;

    event_param.setLoginFailedThreshold(buffer.readUInt8(offset));
    offset++;

    event_param.setEventMergeCycle(buffer.readUInt16BE(offset));
    offset += 2;

    event_param.setDiskThreshold(buffer.readUInt8(offset));
    offset++;

    event_param.setHistoryEventReportInterval(buffer.readUInt32BE(offset));
    offset += 4;

    return event_param;
}

/**
 * 编码事件参数，针对删除编码
 * @param id_list
 * @returns {Buffer}
 * @private
 */
function _encodeRemoveEventParam(id_list) {
    let buffer = Buffer.alloc(id_list.length * 4);
    let offset = 0;
    for (let i = 0; i < id_list.length; i++) {
        buffer.writeUInt32BE(id_list[i], offset);
        offset += 4;
    }

    return buffer;
}

/**
 * 查看
 * @returns {Buffer}
 */
function packLoadEventParam() {
    return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE, PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_EVENT, PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_LOAD, 0, null);
}

/**
 * 解析收到的事件参数
 * @param content
 * @returns {PublicDefine_pb.MSG_EventParam}
 */
function unpackLoadEventParam(content) {
    return decodeEventParam(content);
}


/**
 * 添加
 * @param event_param 结构PublicDefine_pb.MSG_EventParam
 * @returns {Buffer}
 */
function packAddEventParam(event_param) {
    const content = _encodeEventParam(event_param);
    return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE, PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_EVENT, PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_ADD, content.length + 4 + 64, content);
}

/**
 * 修改资产
 * @param event_param 结构PublicDefine_pb.MSG_EventParam
 * @returns {Buffer}
 */
function packChangeEventParam(event_param) {
    const content = _encodeEventParam(event_param);
    return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE, PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_EVENT, PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_UPDATE, content.length + 4 + 64, content);
}

/**
 * 删除资产
 * @param deviceIds 待删除资产id数组
 * @returns {Buffer}
 */
function packRemoveEventParam(deviceIds) {
    const content = _encodeRemoveEventParam(deviceIds);
    return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE, PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_EVENT, PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_DELETE, content.length + 4 + 64, content);
}

module.exports = {
    decodeEventParam,
    packLoadEventParam,
    unpackLoadEventParam,
    packAddEventParam,
    packChangeEventParam,
    packRemoveEventParam
};
