'use strict';
/**
 * NTP参数编码解码器
 */

const util = require('../../lib/util');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const NTP_BLOCK_SIZE = 4 + 4 + 4 + 4 + 4 + 2 + 2 + 2;

/**
 * 针对添加和修改的编码
 * @param ntp_param
 * @returns {<PERSON>uff<PERSON>}
 * @private
 */
function _encodeNtpParam(ntp_param) {
    let buffer = Buffer.alloc(NTP_BLOCK_SIZE);
    let offset = 0;

    buffer.writeInt32BE(ntp_param.getId(), offset);
    offset += 4;

    buffer.writeInt32BE(ntp_param.getMainClockMainNetworkIp(), offset);
    offset += 4;

    buffer.writeInt32BE(ntp_param.getMainClockBackupNetworkIp(), offset);
    offset += 4;

    buffer.writeInt32BE(ntp_param.getBackupClockMainNetworkIp(), offset);
    offset += 4;

    buffer.writeInt32BE(ntp_param.getBackupClockBackupNetworkIp(), offset);
    offset += 4;

    buffer.writeUInt16BE(ntp_param.getNtpServerPort(), offset);
    offset += 2;

    buffer.writeUInt16BE(ntp_param.getNtpVerifyTimeCycle(), offset);
    offset += 2;

    buffer.writeUInt16BE(ntp_param.getNtpIsBroadcast(), offset);
    offset += 2;

    return buffer;
}

function decodeNtpParam(content) {
    if (content.length < NTP_BLOCK_SIZE) {
        return;
    }
    let buffer = Buffer.from(content);
    let ntp_param = new PublicDefine_pb.MSG_NtpParam();
    let offset = 0;

    ntp_param.setId(buffer.readInt32BE(offset));
    offset += 4;

    ntp_param.setMainClockMainNetworkIp(buffer.readInt32BE(offset));
    offset += 4;

    ntp_param.setMainClockBackupNetworkIp(buffer.readInt32BE(offset));
    offset += 4;

    ntp_param.setBackupClockMainNetworkIp(buffer.readInt32BE(offset));
    offset += 4;

    ntp_param.setBackupClockBackupNetworkIp(buffer.readInt32BE(offset));
    offset += 4;

    ntp_param.setNtpServerPort(buffer.readUInt16BE(offset));
    offset += 2;

    ntp_param.setNtpVerifyTimeCycle(buffer.readUInt16BE(offset));
    offset += 2;

    ntp_param.setNtpIsBroadcast(buffer.readUInt16BE(offset));
    offset += 2;

    return ntp_param;
}

/**
 * 编码事件参数，针对删除编码
 * @param id_list
 * @returns {Buffer}
 * @private
 */
function _encodeRemoveNtpParam(id_list) {
    let buffer = Buffer.alloc(id_list.length * 4);
    let offset = 0;
    for (let i = 0; i < id_list.length; i++) {
        buffer.writeUInt32BE(id_list[i], offset);
        offset += 4;
    }

    return buffer;
}

/**
 * 查看
 * @returns {Buffer}
 */
function packLoadNtpParam() {
    return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE, PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_NTP, PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_LOAD, 0, null);
}

/**
 * 解析收到的事件参数
 * @param content
 * @returns {PublicDefine_pb.MSG_NtpParam}
 */
function unpackLoadNtpParam(content) {
    return decodeNtpParam(content);
}


/**
 * 添加
 * @param ntp_param 结构PublicDefine_pb.MSG_NtpParam
 * @returns {Buffer}
 */
function packAddNtpParam(ntp_param) {
    const content = _encodeNtpParam(ntp_param);
    return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE, PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_NTP, PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_ADD, content.length + 4 + 64, content);
}

/**
 * 修改资产
 * @param ntp_param 结构PublicDefine_pb.MSG_NtpParam
 * @returns {Buffer}
 */
function packUpdateNtpParam(ntp_param) {
    const content = _encodeNtpParam(ntp_param);
    return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE, PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_NTP, PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_UPDATE, content.length + 4 + 64, content);
}

/**
 * 删除资产
 * @param deviceIds 待删除资产id数组
 * @returns {Buffer}
 */
function packRemoveNtpParam(deviceIds) {
    const content = _encodeRemoveNtpParam(deviceIds);
    return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE, PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_NTP, PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_DELETE, content.length + 4 + 64, content);
}

module.exports = {
    decodeNtpParam,
    packLoadNtpParam,
    unpackLoadNtpParam,
    packAddNtpParam,
    packUpdateNtpParam,
    packRemoveNtpParam
};
