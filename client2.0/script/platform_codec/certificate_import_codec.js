'use strict';
/**
 * 证书导入编码解码器
 */

const util = require('../../lib/util');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const CERT_BLOCK_SIZE = 4 +1+1+2+4+64;

/**
 * 加载证书导入编码
 * @returns {Buffer}
 * @private
 * @param cert_param
 */
function encodeImportCerf(cert_param,cert_content) {
    let buffer = Buffer.alloc(CERT_BLOCK_SIZE+cert_content.length);
    let offset = 0;

    buffer.writeInt32BE(cert_param.getId(), offset);
    offset += 4;
    buffer.writeUInt8(cert_param.getCertType(), offset);
    offset += 1;
    buffer.writeUInt8(cert_param.getReserved(), offset);
    offset += 1;
    buffer.writeUInt16BE(cert_param.getCertContentLength(), offset);
    offset += 2;
    buffer.writeInt32BE(cert_param.getPlatformIp(), offset);
    offset += 4;
    buffer.write(cert_param.getCertName(), offset);
    offset += 64;
    for (const cont of cert_content){
        buffer.writeUInt8(cont, offset);
        offset ++;
    }

    return buffer;
}

/**
 * 加载证书导入解码，返回待解析的e语言格式字符串数组
 * @param content
 * @returns {Array}
 * @private
 */
function _decodeImportCerf(content) {
    const cont = content.toString();
    return cont.split('\n');
}

/**
 * 导入证书
 * @returns {Buffer}
 * @param cert_param
 * @param cert_content
 */
function importCerf(cert_param,cert_content) {
    const content = encodeImportCerf(cert_param,cert_content);
    return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE,
      PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_CERT,
      PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_ADD,
      content.length + 4 + 64,
      content);
}

module.exports = {
    importCerf,
};