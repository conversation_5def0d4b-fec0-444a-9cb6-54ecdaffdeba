'use strict';
/**
 * 版本管理编码解码器
 */

const util = require('../../lib/util');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const VERSION_BLOCK_SIZE = 16 + 4;

function _encodeVersion(task_id, dest_ip,version_data) {
    let buffer = Buffer.alloc(VERSION_BLOCK_SIZE+version_data.length);
    let offset = 0;

    buffer.write(task_id, offset);
    offset += 16;

    buffer.writeInt32BE(dest_ip, offset);
    offset += 4;
    version_data.copy(buffer, offset, 0, version_data.length);
    offset += version_data.length;

    return buffer;
}

function _decodeVersion(content) {
    if (content.length < 16) {
        return;
    }
    let buffer = Buffer.from(content);
    let versionVerify = new PublicDefine_pb.MSG_VersionVerify();
    let offset = 0;

    versionVerify.setTaskId(util.trimString(buffer.toString('utf8', offset, offset + 16)));
    offset += 16;

    versionVerify.setResult(util.trimString(buffer.toString('utf8', offset)));


    return versionVerify;
}

module.exports = {
    /**
     * 启动版本校验
     * @param task_id 任务唯一id
     * @param dest_ip 目标主机ip
     * @returns {Buffer}
     */
    packStartVersionVerify: function (task_id, dest_ip,version_data) {
        const content = _encodeVersion(task_id, dest_ip,version_data);
        return util.packPlatformMsg(
          PublicDefine_pb.PlatformType.PLATFORM_TYPE_VERSION_MANAGE,
          PublicDefine_pb.VersionManageSubType.VERSION_MANAGE_SUBTYPE_VERIFY,
          PublicDefine_pb.VersionVerifyParam.VERSION_MANAGE_PARAM_START,
          content.length + 4 + 64,
          content);
    },
    /**
     * 启动版本校验结果
     * @param content
     * @returns {PublicDefine_pb.MSG_VersionVerify}
     */
    unpackStartVersionVerify: function (content) {
        return _decodeVersion(content);
    },

    /**
     * 停止/取消版本校验
     * @param task_id
     * @param dest_ip
     * @returns {Buffer}
     */
    packStopVersionVerify: function (task_id, dest_ip,version_data) {
        const content = _encodeVersion(task_id, dest_ip,version_data);
        return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_VERSION_MANAGE,
          PublicDefine_pb.VersionManageSubType.VERSION_MANAGE_SUBTYPE_VERIFY,
            PublicDefine_pb.VersionVerifyParam.VERSION_MANAGE_PARAM_STOP,
          content.length + 4 + 64,
          content);
    },
    unpackStopVersionVerify: function (content) {
        return _decodeVersion(content);
    },

    /**
     * 获取版本校验结果
     * @param task_id
     * @param dest_ip
     * @returns {Buffer}
     */
    packVersionVerifyGetResult: function (task_id, dest_ip,version_data) {
        const content = _encodeVersion(task_id, dest_ip,version_data);
        return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_VERSION_MANAGE,
          PublicDefine_pb.VersionManageSubType.VERSION_MANAGE_SUBTYPE_VERIFY,
            PublicDefine_pb.VersionVerifyParam.VERSION_MANAGE_PARAM_GET_RESULT,
          content.length + 4 + 64,
          content);
    },
    unpackVersionVerifyGetResult: function (content) {
        return _decodeVersion(content);
    },
    /**
     * 版本匹配
     * @param task_id 任务唯一id
     * @param dest_ip 目标主机ip
     * @returns {Buffer}
     */
    packMatchVersionVerify: function (task_id, dest_ip,version_data) {
        const content = _encodeVersion(task_id, dest_ip,version_data);
        return util.packPlatformMsg(
          PublicDefine_pb.PlatformType.PLATFORM_TYPE_VERSION_MANAGE, PublicDefine_pb.VersionManageSubType.VERSION_MANAGE_SUBTYPE_MATCH, 0,
          content.length + 4 + 64,
          content);
    },
    unpackMatchVersionVerify: function (content) {
        return _decodeVersion(content);
    },
    /**
     * 版本配置更新
     * @param task_id 任务唯一id
     * @param dest_ip 目标主机ip
     * @returns {Buffer}
     */
    packUpdateVersionVerify: function (task_id, dest_ip,version_data) {
        const content = _encodeVersion(task_id, dest_ip,version_data);
        return util.packPlatformMsg(
          PublicDefine_pb.PlatformType.PLATFORM_TYPE_VERSION_MANAGE, PublicDefine_pb.VersionManageSubType.VERSION_MANAGE_SUBTYPE_UPDATE_CONFIG, 0,
          content.length + 4 + 64,
          content);
    },
    unpackUpdateVersionVerify: function (content) {
        return _decodeVersion(content);
    },
};
