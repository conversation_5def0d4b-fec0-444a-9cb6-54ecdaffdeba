'use strict';
/**
 * 事件参数编码解码器
 */

const util = require('../../lib/util');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const NETCARD_BLOCK_SIZE = 4 + 16 + 4 + 4;

/**
 * 编码网卡参数，针对添加和修改的编码
 * @param net_card
 * @returns {Buffer}
 * @private
 */
function _encodeNetCard(net_card) {
    let buffer = Buffer.alloc(NETCARD_BLOCK_SIZE);
    let offset = 0;

    buffer.writeInt32BE(net_card.getId(), offset);
    offset += 4;

    buffer.write(net_card.getNetcardName(), offset);
    offset += 16;

    buffer.writeInt32BE(net_card.getIp(), offset);
    offset += 4;

    buffer.writeInt32BE(net_card.getMask(), offset);
    offset += 4;

    return buffer;
}

/**
 * 解码网卡参数
 * @param content
 * @returns {Array}
 * @private
 */
function decodeNetCard(content) {
    const count = content.length / NETCARD_BLOCK_SIZE;
    if (count < 0.9) {
        return [];
    }
    let buffer = Buffer.from(content);
    let offset = 0;
    let net_cards = [];
    for (let i = 0; i < count; i++) {
        let net_card = new PublicDefine_pb.MSG_NetCard();
        net_card.setId(buffer.readInt32BE(offset));
        offset += 4;

        net_card.setNetcardName(util.trimString(buffer.toString('utf8', offset, offset + 16)));
        offset += 16;

        net_card.setIp(buffer.readInt32BE(offset));
        offset += 4;

        net_card.setMask(buffer.readInt32BE(offset));
        offset += 4;

        net_cards.push(net_card);
    }

    return net_cards;
}

/**
 * 针对删除编码
 * @param id_list
 * @returns {Buffer}
 * @private
 */
function _encodeRemoveNetCard(id_list) {
    let buffer = Buffer.alloc(id_list.length * 4);
    let offset = 0;
    for (let i = 0; i < id_list.length; i++) {
        buffer.writeUInt32BE(id_list[i], offset);
        offset += 4;
    }

    return buffer;
}

/**
 * 查看
 * @returns {Buffer}
 */
function packLoadNetCard() {
    return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE, PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_NET_CARD, PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_LOAD, 0, null);
}

/**
 * 解析收到的事件参数
 * @param content
 * @returns [PublicDefine_pb.MSG_NetCard]
 */
function unpackLoadNetCard(content) {
    return decodeNetCard(content);
}


/**
 * 添加
 * @param net_card 结构PublicDefine_pb.MSG_NetCard
 * @returns {Buffer}
 */
function packAddNetCard(net_card) {
    const content = _encodeNetCard(net_card);
    return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE, PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_NET_CARD, PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_ADD, content.length + 4 + 64, content);
}

/**
 * 修改资产
 * @param net_card {PublicDefine_pb.MSG_NetCard}
 * @returns {Buffer}
 */
function packUpdateNetCard(net_card) {
    const content = _encodeNetCard(net_card);
    return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE, PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_NET_CARD, PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_UPDATE, content.length + 4 + 64, content);
}

/**
 * 删除资产
 * @param deviceIds 待删除资产id数组
 * @returns {Buffer}
 */
function packRemoveNetCard(deviceIds) {
    const content = _encodeRemoveNetCard(deviceIds);
    return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE, PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_NET_CARD, PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_DELETE, content.length + 4 + 64, content);
}

module.exports = {
    decodeNetCard,
    packLoadNetCard,
    unpackLoadNetCard,
    packAddNetCard,
    packUpdateNetCard,
    packRemoveNetCard
};
