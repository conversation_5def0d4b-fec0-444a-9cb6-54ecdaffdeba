'use strict';
/**
 * 漏洞扫描编码解码器
 */

const util = require('../../lib/util');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const X_SCAN_BLOCK_SIZE = 16 + 4;

function _encodeXscan(task_id, dest_ip) {
    let buffer = Buffer.alloc(X_SCAN_BLOCK_SIZE);
    let offset = 0;

    buffer.write(task_id, offset);
    offset += 16;

    buffer.writeInt32BE(dest_ip, offset);
    offset += 4;

    return buffer;
}

function _decodeXscan(content) {
    if (content.length < 16) {
        return;
    }
    let buffer = Buffer.from(content);
    let leakScan = new PublicDefine_pb.MSG_LeakScann();
    let offset = 0;

    leakScan.setTaskId(util.trimString(buffer.toString('utf8', offset, offset + 16)));
    offset += 16;

    leakScan.setResult(util.trimString(buffer.toString('utf8', offset)));


    return leakScan;
}

module.exports = {
    /**
     * 启动漏洞扫描
     * @param task_id 任务唯一id
     * @param dest_ip 目标主机ip
     * @returns {Buffer}
     */
    packStartXscan: function (task_id, dest_ip) {
        const content = _encodeXscan(task_id, dest_ip);
        return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_LEAK_SCAN,
            PublicDefine_pb.LeakScanSubType.LEAK_SCAN_SUBTYPE_START, 0, content.length + 4 + 64, content);
    },
    /**
     * 启动漏洞扫描结果
     * @param content
     * @returns {PublicDefine_pb.MSG_LeakScann}
     */
    unpackStartXscan: function (content) {
        return _decodeXscan(content);
    },

    /**
     * 停止漏洞扫描
     * @param task_id
     * @param dest_ip
     * @returns {Buffer}
     */
    packStopXscan: function (task_id, dest_ip) {
        const content = _encodeXscan(task_id, dest_ip);
        return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_LEAK_SCAN,
            PublicDefine_pb.LeakScanSubType.LEAK_SCAN_SUBTYPE_STOP, 0, content.length + 4 + 64, content);
    },
    unpackStopXscan: function (content) {
        return _decodeXscan(content);
    },

    /**
     * 查询漏洞扫描状态
     * @param task_id
     * @param dest_ip
     * @returns {Buffer}
     */
    packXscanGetState: function (task_id, dest_ip) {
        const content = _encodeXscan(task_id, dest_ip);
        return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_LEAK_SCAN,
            PublicDefine_pb.LeakScanSubType.LEAK_SCAN_SUBTYPE_GET_STATE, 0, content.length + 4 + 64, content);
    },
    unpackXscanGetState: function (content) {
        return _decodeXscan(content);
    },

    /**
     * 查询漏洞扫描结果
     * @param task_id
     * @param dest_ip
     * @returns {Buffer}
     */
    packXscanGetResult: function (task_id, dest_ip) {
        const content = _encodeXscan(task_id, dest_ip);
        return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_LEAK_SCAN,
            PublicDefine_pb.LeakScanSubType.LEAK_SCAN_SUBTYPE_GET_RESULT, 0, content.length + 4 + 64, content);
    },
    unpackXscanGetResult: function (content) {
        return _decodeXscan(content);
    },
};
