'use strict';
/**
 * 命令控制编码解码器
 */

const util = require('../../lib/util');
const PublicDefine_pb = require('../pb/PublicDefine_pb');

function _encodeUpgrade(upgrade_bytes) {
    let buffer = Buffer.alloc(upgrade_bytes.length + 16);
    let offset = 0;

    for (let i = 0; i < upgrade_bytes.length; i++) {
        buffer.writeUInt8(upgrade_bytes[i], offset);
        offset ++;
    }

    buffer.write(util.md5(upgrade_bytes), offset);
    offset += 16;

    return buffer;
}

function _decodeUpgrade(content) {
    return content;
}

module.exports = {
    /**
     * 升级软件
     * @param upgrade_bytes 升级数据
     * @returns {Buffer}
     */
    packUpgrade: function (upgrade_bytes) {
        const content = _encodeUpgrade(upgrade_bytes);
        return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_UPGRADE_SOFTWARE,
            0, 0, content.length + 4 + 64, content);
    },
    unpackUpgrade: function (content) {
        return _decodeUpgrade(content);
    },
};
