'use strict';
/**
 * 设备控制编码解码器
 */

const util = require('../../lib/util');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const DEV_CTRL_BLOCK_SIZE = 16 + 4;

function _encodeDevCtrlData(task_id, dest_ip) {
    let buffer = Buffer.alloc(DEV_CTRL_BLOCK_SIZE);
    let offset = 0;

    buffer.write(task_id, offset);
    offset += 16;

    buffer.writeInt32BE(dest_ip, offset);
    offset += 4;
    return buffer;
}

function _decodeDevCtrlData(content) {
    if (content.length < 16) {
        return;
    }
    let buffer = Buffer.from(content);
    let deviceCtrlDate = new PublicDefine_pb.MSG_Control();
    let offset = 0;

    deviceCtrlDate.setTaskId(util.trimString(buffer.toString('utf8', offset, offset + 16)));
    offset += 16;

    deviceCtrlDate.setResult(util.trimString(buffer.toString('utf8', offset)));
    return deviceCtrlDate;
}

module.exports = {
    /**
     * 主动断网
     * @param task_id 任务唯一id
     * @param dest_ip 目标主机ip
     * @returns {Buffer}
     */
    packCloseNet: function (task_id, dest_ip) {
        const content = _encodeDevCtrlData(task_id, dest_ip);
        return util.packPlatformMsg(
          PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONTROL,
          PublicDefine_pb.ControlPacketSubType.CONTROL_PACKET_SUBTYPE_CLOSE_NETWORK,
          0,
          content.length + 4 + 64,
          content);
    },
    unpackCloseNet: function (content) {
        return _decodeDevCtrlData(content);
    }
};
