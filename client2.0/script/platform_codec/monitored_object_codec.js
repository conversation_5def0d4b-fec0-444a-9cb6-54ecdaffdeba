'use strict';
/**
 * 监控对象参数编码解码器
 */

const util = require('../../lib/util');
const PublicDefine_pb = require('../pb/PublicDefine_pb');

function _encodeMonitoredObject(task_id, dest_ip, set_content = '') {
    let buffer = Buffer.alloc(16 + 4 + set_content.length);
    let offset = 0;

    buffer.write(task_id, offset);
    offset += 16;

    buffer.writeInt32BE(dest_ip, offset);
    offset += 4;

    buffer.write(set_content, offset);
    offset += set_content.length;

    return buffer;
}

function _decodeMonitoredObject(content) {
    if (content.length < 16) {
        return;
    }
    let buffer = Buffer.from(content);
    let mo = new PublicDefine_pb.MSG_MonitoredObject();
    let offset = 0;

    mo.setTaskId(util.trimString(buffer.toString('utf8', offset, offset + 16)));
    offset += 16;

    mo.setContent(util.bufferToString(buffer.slice(offset)));
    return mo;
}

module.exports = {
    /**
     * 设置网络连接白名单，每条记录格式为“协议号(tcp/udp),远端 IP 地址(0 表示不限),远端端口(0 表示不限)\n”,可多条
     * 注:远端 IP 地址和远端端口均可以是单个值,也可以是一个范围,当设置成范围时,采用“-”隔开,如 ***********-***********00
     * @param task_id
     * @param dest_ip
     * @param white_list
     * @returns {Buffer}
     */
    packSetNetworkWhiteList: function (task_id, dest_ip, white_list) {
        const content = _encodeMonitoredObject(task_id, dest_ip, white_list);
        return util.packPlatformMsg(
            PublicDefine_pb.PlatformType.PLATFORM_TYPE_MONITORED_OBJECT_MANAGE,
            PublicDefine_pb.MonitoredObjectSubType.MONITORED_OBJECT_SUBTYPE_NETWORK_WHITE_LIST,
            PublicDefine_pb.MonitoredObjectParam.MONITORED_OBJECT_PARAM_SET,
            content.length + 4 + 64,
            content);
    },
    unpackSetNetworkWhiteList: function (content) {
        return _decodeMonitoredObject(content);
    },

    /**
     * 设置开放端口名单,每条记录格式为“端口号,服务名称\n”,可多条
     * @param task_id
     * @param dest_ip
     * @param port_list
     * @returns {Buffer}
     */
    packSetOpenedPortList: function (task_id, dest_ip, port_list) {
        const content = _encodeMonitoredObject(task_id, dest_ip, port_list);
        return util.packPlatformMsg(
            PublicDefine_pb.PlatformType.PLATFORM_TYPE_MONITORED_OBJECT_MANAGE,
            PublicDefine_pb.MonitoredObjectSubType.MONITORED_OBJECT_SUBTYPE_OPENED_PORT_LIST,
            PublicDefine_pb.MonitoredObjectParam.MONITORED_OBJECT_PARAM_SET,
            content.length + 4 + 64,
            content);
    },
    unpackSetOpenedPortList: function (content) {
        return _decodeMonitoredObject(content);
    },

    /**
     * 设置关键文件/目录清单，每条记录格式为“文件/目录的路径\n”,可多条。当表示目录时,路径的结尾必须为“/”符号
     * @param task_id
     * @param dest_ip
     * @param file_list
     * @returns {Buffer}
     */
    packSetKeyFileList: function (task_id, dest_ip, file_list) {
        const content = _encodeMonitoredObject(task_id, dest_ip, file_list);
        return util.packPlatformMsg(
            PublicDefine_pb.PlatformType.PLATFORM_TYPE_MONITORED_OBJECT_MANAGE,
            PublicDefine_pb.MonitoredObjectSubType.MONITORED_OBJECT_SUBTYPE_KEY_FILE_LIST,
            PublicDefine_pb.MonitoredObjectParam.MONITORED_OBJECT_PARAM_SET,
            content.length + 4 + 64,
            content);
    },
    unpackSetKeyFileList: function (content) {
        return _decodeMonitoredObject(content);
    },

    /**
     * 设置光驱设备检测周期,整数值,字符串 ASCII 码,如“60”,单位秒
     * @param task_id
     * @param dest_ip
     * @param detect_sec
     * @returns {Buffer}
     */
    packSetCdDetectCycle: function (task_id, dest_ip, detect_sec) {
        const content = _encodeMonitoredObject(task_id, dest_ip, detect_sec);
        return util.packPlatformMsg(
            PublicDefine_pb.PlatformType.PLATFORM_TYPE_MONITORED_OBJECT_MANAGE,
            PublicDefine_pb.MonitoredObjectSubType.MONITORED_OBJECT_SUBTYPE_CD_DETECT_CYCLE,
            PublicDefine_pb.MonitoredObjectParam.MONITORED_OBJECT_PARAM_SET,
            content.length + 4 + 64,
            content);
    },
    unpackSetCdDetectCycle: function (content) {
        return _decodeMonitoredObject(content);
    },

    /**
     * 设置非法端口检测周期,整数值,字符串 ASCII 码,如“60”,单位秒
     * @param task_id
     * @param dest_ip
     * @param detect_sec
     * @returns {Buffer}
     */
    packSetInvalidPortDetectCycle: function (task_id, dest_ip, detect_sec) {
        const content = _encodeMonitoredObject(task_id, dest_ip, detect_sec);
        return util.packPlatformMsg(
            PublicDefine_pb.PlatformType.PLATFORM_TYPE_MONITORED_OBJECT_MANAGE,
            PublicDefine_pb.MonitoredObjectSubType.MONITORED_OBJECT_SUBTYPE_INVALID_PORT_DETECT_CYCLE,
            PublicDefine_pb.MonitoredObjectParam.MONITORED_OBJECT_PARAM_SET,
            content.length + 4 + 64,
            content);
    },
    unpackSetInvalidPortDetectCycle: function (content) {
        return _decodeMonitoredObject(content);
    },

    /**
     * 设置危险操作命令清单,每条记录格式为“危险命令\n”,可多条。(注:厂站内主机的危险操作行为由监测装置分析)
     * @param task_id
     * @param dest_ip
     * @param cmd_list
     * @returns {Buffer}
     */
    packSetDangerousCMDList: function (task_id, dest_ip, cmd_list) {
        const content = _encodeMonitoredObject(task_id, dest_ip, cmd_list);
        return util.packPlatformMsg(
            PublicDefine_pb.PlatformType.PLATFORM_TYPE_MONITORED_OBJECT_MANAGE,
            PublicDefine_pb.MonitoredObjectSubType.MONITORED_OBJECT_SUBTYPE_DANGEROUS_CMD_LIST,
            PublicDefine_pb.MonitoredObjectParam.MONITORED_OBJECT_PARAM_SET,
            content.length + 4 + 64,
            content);
    },
    unpackSetDangerousCMDList: function (content) {
        return _decodeMonitoredObject(content);
    },

    /**
     * 查看监控对象参数
     * @param task_id
     * @param dest_ip
     * @param sub_type 要查看的监控对象参数子类型，参考 PublicDefine_pb.MonitoredObjectSubType
     * @returns {Buffer}
     */
    packLoadMonitoredObject: function (task_id, dest_ip, sub_type) {
        const content = _encodeMonitoredObject(task_id, dest_ip);
        return util.packPlatformMsg(
            PublicDefine_pb.PlatformType.PLATFORM_TYPE_MONITORED_OBJECT_MANAGE,
            sub_type,
            PublicDefine_pb.MonitoredObjectParam.MONITORED_OBJECT_PARAM_LOAD,
            content.length,
            content);
    },
    unpackLoadMonitoredObject: function (content) {
        return _decodeMonitoredObject(content);
    },
};
