'use strict';
/**
 * 命令控制编码解码器
 */

const util = require('../../lib/util');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const CONTROL_BLOCK_SIZE = 16 + 4;

function _encodeControl(task_id, dest_ip) {
    let buffer = Buffer.alloc(CONTROL_BLOCK_SIZE);
    let offset = 0;

    buffer.write(task_id, offset);
    offset += 16;

    buffer.writeInt32BE(dest_ip, offset);
    offset += 4;

    return buffer;
}

function _decodeControl(content) {
    if (content.length < 16) {
        return;
    }
    let buffer = Buffer.from(content);
    let control = new PublicDefine_pb.MSG_Control();
    let offset = 0;

    control.setTaskId(util.trimString(buffer.toString('utf8', offset, offset + 16)));
    offset += 16;

    control.setResult(util.trimString(buffer.toString('utf8', offset)));


    return control;
}

module.exports = {
    /**
     * 启动断网命令
     * @param task_id 任务唯一id
     * @param dest_ip 目标主机ip
     * @returns {Buffer}
     */
    packCloseNetwork: function (task_id, dest_ip) {
        const content = _encodeControl(task_id, dest_ip);
        return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONTROL,
            PublicDefine_pb.ControlPacketSubType.CONTROL_PACKET_SUBTYPE_CLOSE_NETWORK, 0, content.length + 4 + 64, content);
    },
    /**
     * 启动断网命令结果
     * @param content
     * @returns {PublicDefine_pb.MSG_Control}
     */
    unpackCloseNetwork: function (content) {
        return _decodeControl(content);
    },
};
