'use strict';
/**
 * 资产编码解码器
 */

const util = require('../../lib/util');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const DEVICE_BLOCK_SIZE = 4 + 32 + 4 + 12 + 4 + 12 + 6 + 20 + 16 + 20 + 1 + 1 + 20 + 1 + 1 + 20 + 20;

/**
 * 编码资产，针对添加和修改的编码
 * @param device
 * @returns {Buffer}
 */
function encodeDevice(device) {
    let buffer = Buffer.alloc(DEVICE_BLOCK_SIZE);
    let offset = 0;

    buffer.writeInt32BE(device.getId(), offset);
    offset += 4;

    buffer.write(device.getDeviceName(), offset);
    offset += 32;

    buffer.writeInt32BE(device.getIp1(), offset);
    offset += 4;

    buffer.write(device.getMac1(), offset);
    offset += 12;

    buffer.writeInt32BE(device.getIp2(), offset);
    offset += 4;

    buffer.write(device.getMac2(), offset);
    offset += 12;

    buffer.write(device.getDeviceType(), offset);
    offset += 6;

    buffer.write(device.getManufacturer(), offset);
    offset += 20;

    buffer.write(device.getSerialNo(), offset);
    offset += 16;

    buffer.write(device.getSysVersion(), offset);
    offset += 20;

    buffer.writeInt8(device.getReserved(), offset);
    offset++;

    buffer.writeInt8(device.getSnmpVersion(), offset);
    offset++;

    buffer.write(device.getSnmpUsername(), offset);
    offset += 20;

    buffer.writeInt8(device.getSnmpAuth(), offset);
    offset++;

    buffer.writeInt8(device.getSnmpEncrypt(), offset);
    offset++;

    buffer.write(device.getSnmpReadCommunity(), offset);
    offset += 20;

    buffer.write(device.getSnmpWriteCommunity(), offset);
    offset += 20;

    return buffer;
}

/**
 *
 * @param content
 * @returns [PublicDefine_pb.MSG_Device]
 */
function decodeDevice(content) {
    const device_count = content.length / DEVICE_BLOCK_SIZE;
    if (device_count < 0.9) {
        return [];
    }
    let buffer = Buffer.from(content);
    let offset = 0;
    let devices = [];
    for (let i = 0; i < device_count; i++) {
        let device = new PublicDefine_pb.MSG_Device();

        device.setId(buffer.readInt32BE(offset));
        offset += 4;

        device.setDeviceName(util.trimString(buffer.toString('utf8', offset, offset + 32)));
        offset += 32;

        device.setIp1(buffer.readInt32BE(offset));
        offset += 4;

        device.setMac1(util.trimString(buffer.toString('utf8', offset, offset + 12)));
        offset += 12;

        device.setIp2(buffer.readInt32BE(offset));
        offset += 4;

        device.setMac2(util.trimString(buffer.toString('utf8', offset, offset + 12)));
        offset += 12;

        device.setDeviceType(util.trimString(buffer.toString('utf8', offset, offset + 6)));
        offset += 6;

        device.setManufacturer(util.trimString(buffer.toString('utf8', offset, offset + 20)));
        offset += 20;

        device.setSerialNo(util.trimString(buffer.toString('utf8', offset, offset + 16)));
        // console.log(util.trimString(buffer.toString('utf8', offset, offset + 16)))
        offset += 16;

        device.setSysVersion(util.trimString(buffer.toString('utf8', offset, offset + 20)));
        // console.log(util.trimString(buffer.toString('utf8', offset, offset + 20)))
        offset += 20;

        device.setReserved(buffer.readInt8(offset));
        offset += 1;

        device.setSnmpVersion(buffer.readInt8(offset));
        offset += 1;

        device.setSnmpUsername(util.trimString(buffer.toString('utf8', offset, offset + 20)));
        offset += 20;

        device.setSnmpAuth(buffer.readInt8(offset));
        offset += 1;

        device.setSnmpEncrypt(buffer.readInt8(offset));
        offset += 1;

        device.setSnmpReadCommunity(util.trimString(buffer.toString('utf8', offset, offset + 20)));
        offset += 20;

        device.setSnmpWriteCommunity(util.trimString(buffer.toString('utf8', offset, offset + 20)));
        offset += 20;

        devices.push(device);
    }

    return devices;
}

/**
 * 编码资产，针对删除编码
 * @param deviceIds
 * @returns {Buffer}
 * @private
 */
function _encodeRemoveDevice(deviceIds) {
    let buffer = Buffer.alloc(deviceIds.length * 4);
    let offset = 0;
    for (let i = 0; i < deviceIds.length; i++) {
        buffer.writeUInt32BE(deviceIds[i], offset);
        offset += 4;
    }

    return buffer;
}

/**
 * 查看资产
 * @returns {Buffer}
 */
function packLoadDevice() {
    return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE, PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_DEVICE, PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_LOAD, 0, null);
}

/**
 *
 * @param content
 * @returns {PublicDefine_pb.MSG_Device[]}
 */
function unpackLoadDevice(content) {
    return decodeDevice(content);
}


/**
 * 添加资产
 * @param device 结构PublicDefine_pb.MSG_Device
 * @returns {Buffer}
 */
function packAddDevice(device) {
    const content = encodeDevice(device);
    return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE, PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_DEVICE, PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_ADD, content.length + 4 + 64, content);
}

/**
 * 修改资产
 * @param device 结构PublicDefine_pb.MSG_Device
 * @returns {Buffer}
 */
function packUpdateDevice(device) {
    const content = encodeDevice(device);
    return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE, PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_DEVICE, PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_UPDATE, content.length + 4 + 64, content);
}

/**
 * 删除资产
 * @param deviceIds 待删除资产id数组
 * @returns {Buffer}
 */
function packRemoveDevice(deviceIds) {
    const content = _encodeRemoveDevice(deviceIds);
    return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE, PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_DEVICE, PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_DELETE, content.length + 4 + 64, content);
}

module.exports = {
    encodeDevice,
    decodeDevice,
    packLoadDevice,
    unpackLoadDevice,
    packAddDevice,
    packUpdateDevice,
    packRemoveDevice,
};
