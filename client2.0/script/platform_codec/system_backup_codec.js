'use strict';
/**
 * 软件升级编码解码器
 */

const util = require('../../lib/util');
const PublicDefine_pb = require('../pb/PublicDefine_pb');

/**
 * 加载数据恢复编码
 * @returns {Buffer}
 * @private
 * @param fileContent 恢复数据
 */
function _encodeBackupRecovery(fileContent) {
    let buffer = Buffer.alloc(fileContent.length+16);
    let offset = 0;
    for (const cont of fileContent){
        buffer.writeUInt8(cont, offset);
        offset ++;
    }
    // console.log(util.md5_bytes(fileContent))
  for (const cont of util.md5_bytes(fileContent)){
    buffer.writeUInt8(cont, offset);
    offset ++;
  }

    return buffer;
}

/**
 * 加载备份数据
 * @param content {Buffer}
 * @returns {Buffer}
 * @private
 */
function _decodeBackupRecovery(content) {
    return content.slice(0, content.length - 16);
}

/**
 * 请求数据恢复
 * @returns {Buffer}
 * @param fileContent 升级数据
 */
function packDataRecovery(fileContent) {
    const content = _encodeBackupRecovery(fileContent);
    return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_BACKUP_RESTORE,
      PublicDefine_pb.BackupRestoreSubType.BACKUP_RESTORE_SUBTYPE_RESTORE,
      0,
      content.length + 4 + 64,
      content);
}
/**
 * 请求数据备份
 * @returns {Buffer}
 */
function packDataBackup() {
    return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_BACKUP_RESTORE,
      PublicDefine_pb.BackupRestoreSubType.BACKUP_RESTORE_SUBTYPE_BACKUP,
      0,
      4 + 64,
      null);
}
/**
 * 解析收到的数据
 * @param content
 * @returns {Array}
 */
function unpackDataBackup(content) {
    return _decodeBackupRecovery(content);
}

module.exports = {
    packDataRecovery,
    packDataBackup,
    unpackDataBackup
};