'use strict';
/**
 * 上传事件编码解码器
 */

const util = require('../../lib/util');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const COLLECT_EVENT_BLOCK_SIZE = 4 + 2 + 2 + 4 + 4+4;

function _encodeUploadEvent(recordCount, deviceType, eventLevel, beginTime, endTime, pageNo) {
  let buffer = Buffer.alloc(COLLECT_EVENT_BLOCK_SIZE);
  let offset = 0;

  buffer.writeInt32BE(recordCount, offset);
  offset += 4;

  buffer.writeInt16BE(deviceType, offset);
  offset += 2;

  buffer.writeInt16BE(eventLevel, offset);
  offset += 2;

  buffer.writeInt32BE(beginTime, offset);
  offset += 4;

  buffer.writeInt32BE(endTime, offset);
  offset += 4;

  buffer.writeInt32BE(pageNo, offset);
  offset += 4;

  return buffer;
}

/**
 * 加载采集事件解码，返回待解析的e语言格式字符串数组
 * @param content
 * @returns {Array}
 * @private
 */
function _decodeUploadEvent(content) {
  const events = content.toString();
  return events.split('\n');
}

/**
 * 请求加载上传事件
 * @param recordCount 一次调阅的记录条数上限,规定一次调阅记录条数上限最大值为 1000,当此项为 0 时(未设置时)或超过 1000 时,按照最大值 1000 处理。
 * @param deviceType 0 表示未设置,其他定义如下:0x01 服务器工作站类, 0x02 数据库, 0x04 网络设备,0x08 横向正向隔离装置,0x10 横向反向隔离装置,0x20 纵向加密装置,0x40 防火墙,0x80入侵检测系统,0x100 防病毒系统,0x200 网络安全监测装置。可通过或(|)的方式进行组合
 * @param eventLevel 0 表示未设置,其他定义如下:0x01 事件级别 1,0x02 事件级别 2,0x04 事件级别 3,0x8 事件级别 4,0x10 事件级别 5。可通过或(|)的方式进行组合
 * @param beginTime 起始时间，1970，秒
 * @param endTime 结束时间，1970，秒
 * @param pageNo 查询页码，从1开始
 * @returns {Buffer}
 */
function packLoadUploadEvent(recordCount, deviceType, eventLevel, beginTime, endTime, pageNo = 1) {
  const content = _encodeUploadEvent(recordCount, deviceType, eventLevel, beginTime, endTime, pageNo);
  return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_LOAD_UPLOAD_EVENT, 0, 0, content.length, content);
}

/**
 * 解析收到的数据
 * @param content
 * @returns {string}
 */
function unpackLoadUploadEvent(content) {
  return _decodeUploadEvent(content);
}


module.exports = {
  packLoadUploadEvent,
  unpackLoadUploadEvent,
};
