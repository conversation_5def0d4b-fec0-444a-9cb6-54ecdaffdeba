'use strict';
/**
 * COMMU参数编码解码器
 */

const util = require('../../lib/util');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const COMMU_BLOCK_BASIC_SIZE = 4 + 2 + 2 + 2 + 2;
const PLATFORM_SIZE = 4 + 2 + 1 + 1;

/**
 * 针对添加和修改的编码
 * @param commu_param
 * @returns {<PERSON><PERSON><PERSON>}
 * @private
 */
function _encodeCommuParam(commu_param) {
    let buffer = Buffer.alloc(COMMU_BLOCK_BASIC_SIZE + commu_param.getPlatformsList().length * PLATFORM_SIZE);
    let offset = 0;

    buffer.writeInt32BE(commu_param.getId(), offset);
    offset += 4;

    buffer.writeUInt16BE(commu_param.getServerListenPort(), offset);
    offset += 2;

    buffer.writeUInt16BE(commu_param.getSyslogListenPort(), offset);
    offset += 2;

    buffer.writeUInt16BE(commu_param.getSnmpListenPort(), offset);
    offset += 2;

    buffer.writeUInt16BE(commu_param.getProxyListenPport(), offset);
    offset += 2;

    for (const platform of commu_param.getPlatformsList()) {
        buffer.writeInt32BE(platform.getIp(), offset);
        offset += 4;

        buffer.writeUInt16BE(platform.getPort(), offset);
        offset += 2;

        buffer.writeUInt8(platform.getAuthority(), offset);
        offset += 1;

        buffer.writeUInt8(platform.getGroup(), offset);
        offset += 1;
    }

    return buffer;
}

function decodeCommuParam(content) {
    if (content.length < COMMU_BLOCK_BASIC_SIZE) {
        return;
    }
    let buffer = Buffer.from(content);
    let commu_param = new PublicDefine_pb.MSG_CommuParam();
    let offset = 0;

    commu_param.setId(buffer.readInt32BE(offset));
    offset += 4;

    commu_param.setServerListenPort(buffer.readUInt16BE(offset));
    offset += 2;

    commu_param.setSyslogListenPort(buffer.readUInt16BE(offset));
    offset += 2;

    commu_param.setSnmpListenPort(buffer.readUInt16BE(offset));
    offset += 2;

    commu_param.setProxyListenPport(buffer.readUInt16BE(offset));
    offset += 2;

    const platform_count = (content.length - offset) / PLATFORM_SIZE;
    for (let i = 0; platform_count >= 1.0 && i < platform_count; i++) {
        let platform = new PublicDefine_pb.MSG_Platform();

        platform.setIp(buffer.readInt32BE(offset));
        offset += 4;

        platform.setPort(buffer.readUInt16BE(offset));
        offset += 2;

        platform.setAuthority(buffer.readUInt8(offset));
        offset += 1;

        platform.setGroup(buffer.readUInt8(offset));
        offset += 1;

        commu_param.addPlatforms(platform);
    }

    return commu_param;
}

/**
 * 针对删除编码
 * @param id_list
 * @returns {Buffer}
 * @private
 */
function _encodeRemoveCommuParam(id_list) {
    let buffer = Buffer.alloc(id_list.length * 4);
    let offset = 0;
    for (let i = 0; i < id_list.length; i++) {
        buffer.writeUInt32BE(id_list[i], offset);
        offset += 4;
    }

    return buffer;
}

/**
 * 查看
 * @returns {Buffer}
 */
function packLoadCommuParam() {
    return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE, PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_COMMUNICATE, PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_LOAD, 0, null);
}

/**
 * 解析收到的事件参数
 * @param content
 * @returns {PublicDefine_pb.MSG_CommuParam}
 */
function unpackLoadCommuParam(content) {
    return decodeCommuParam(content);
}


/**
 * 添加
 * @param commu_param 结构PublicDefine_pb.MSG_CommuParam
 * @returns {Buffer}
 */
function packAddCommuParam(commu_param) {
    const content = _encodeCommuParam(commu_param);
    return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE, PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_COMMUNICATE, PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_ADD, content.length + 4 + 64, content);
}

/**
 * 修改
 * @param commu_param 结构PublicDefine_pb.MSG_CommuParam
 * @returns {Buffer}
 */
function packUpdateCommuParam(commu_param) {
    const content = _encodeCommuParam(commu_param);
    return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE, PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_COMMUNICATE, PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_UPDATE, content.length + 4 + 64, content);
}

/**
 * 删除
 * @param deviceIds 待删除资产id数组
 * @returns {Buffer}
 */
function packRemoveCommuParam(deviceIds) {
    const content = _encodeRemoveCommuParam(deviceIds);
    return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE, PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_COMMUNICATE, PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_DELETE, content.length + 4 + 64, content);
}

module.exports = {
    decodeCommuParam,
    packLoadCommuParam,
    unpackLoadCommuParam,
    packAddCommuParam,
    packUpdateCommuParam,
    packRemoveCommuParam
};
