'use strict';
/**
 * 基线核查编码解码器
 */

const util = require('../../lib/util');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const BASELINE_BLOCK_SIZE = 16 + 4;

function _encodeBaseline(task_id, dest_ip) {
    let buffer = Buffer.alloc(BASELINE_BLOCK_SIZE);
    let offset = 0;

    buffer.write(task_id, offset);
    offset += 16;

    buffer.writeInt32BE(dest_ip, offset);
    offset += 4;

    return buffer;
}

function _decodeBaseline(content) {
    if (content.length < 16) {
        return;
    }
    let buffer = Buffer.from(content);
    let baseline = new PublicDefine_pb.MSG_Baseline();
    let offset = 0;

    baseline.setTaskId(util.trimString(buffer.toString('utf8', offset, offset + 16)));
    offset += 16;

    baseline.setResult(util.trimString(buffer.toString('utf8', offset)));


    return baseline;
}

module.exports = {
    /**
     * 启动基线核查
     * @param task_id 任务唯一id
     * @param dest_ip 目标主机ip
     * @returns {Buffer}
     */
    packStartBaseline: function (task_id, dest_ip) {
        const content = _encodeBaseline(task_id, dest_ip);
        return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CHECK_BASE_LINE,
            PublicDefine_pb.BaselinePacketSubType.BASELINE_PACKET_SUBTYPE_START, 0, content.length + 4 + 64, content);
    },
    /**
     * 启动基线核查结果
     * @param content
     * @returns {PublicDefine_pb.MSG_Baseline}
     */
    unpackStartBaseline: function (content) {
        return _decodeBaseline(content);
    },

    /**
     * 停止基线核查
     * @param task_id
     * @param dest_ip
     * @returns {Buffer}
     */
    packStopBaseline: function (task_id, dest_ip) {
        const content = _encodeBaseline(task_id, dest_ip);
        return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CHECK_BASE_LINE,
            PublicDefine_pb.BaselinePacketSubType.BASELINE_PACKET_SUBTYPE_STOP, 0, content.length + 4 + 64, content);
    },
    unpackStopBaseline: function (content) {
        return _decodeBaseline(content);
    },

    /**
     * 查询基线核查状态
     * @param task_id
     * @param dest_ip
     * @returns {Buffer}
     */
    packBaselineGetState: function (task_id, dest_ip) {
        const content = _encodeBaseline(task_id, dest_ip);
        return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CHECK_BASE_LINE,
            PublicDefine_pb.BaselinePacketSubType.BASELINE_PACKET_SUBTYPE_GET_STATE, 0, content.length + 4 + 64, content);
    },
    unpackBaselineGetState: function (content) {
        return _decodeBaseline(content);
    },

    /**
     * 查询基线核查结果
     * @param task_id
     * @param dest_ip
     * @returns {Buffer}
     */
    packBaselineGetResult: function (task_id, dest_ip) {
        const content = _encodeBaseline(task_id, dest_ip);
        return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CHECK_BASE_LINE,
            PublicDefine_pb.BaselinePacketSubType.BASELINE_PACKET_SUBTYPE_GET_RESULT, 0, content.length + 4 + 64, content);
    },
    unpackBaselineGetResult: function (content) {
        return _decodeBaseline(content);
    },
};
