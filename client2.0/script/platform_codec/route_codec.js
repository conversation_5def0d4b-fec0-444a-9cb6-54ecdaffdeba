'use strict';
/**
 * 事件参数编码解码器
 */

const util = require('../../lib/util');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const ROUTE_BLOCK_SIZE = 4 + 4 + 4 + 4;

/**
 * 针对添加和修改的编码
 * @param route
 * @returns {Buffer}
 * @private
 */
function _encodeRoute(route) {
    let buffer = Buffer.alloc(ROUTE_BLOCK_SIZE);
    let offset = 0;

    buffer.writeInt32BE(route.getId(), offset);
    offset += 4;

    buffer.writeInt32BE(route.getDestSegment(), offset);
    offset += 4;

    buffer.writeInt32BE(route.getDestMask(), offset);
    offset += 4;

    buffer.writeInt32BE(route.getGateway(), offset);
    offset += 4;

    return buffer;
}

/**
 * 解码
 * @param content
 * @returns {Array}
 * @private
 */
function decodeRoute(content) {
    const count = content.length / ROUTE_BLOCK_SIZE;
    if (count < 0.9) {
        return [];
    }
    let buffer = Buffer.from(content);
    let offset = 0;
    let routes = [];
    for (let i = 0; i < count; i++) {
        let route = new PublicDefine_pb.MSG_Route();
        route.setId(buffer.readInt32BE(offset));
        offset += 4;

        route.setDestSegment(buffer.readInt32BE(offset));
        offset += 4;

        route.setDestMask(buffer.readInt32BE(offset));
        offset += 4;

        route.setGateway(buffer.readInt32BE(offset));
        offset += 4;

        routes.push(route);
    }

    return routes;
}

/**
 * 针对删除编码
 * @param id_list
 * @returns {Buffer}
 * @private
 */
function _encodeRemoveRoute(id_list) {
    let buffer = Buffer.alloc(id_list.length * 4);
    let offset = 0;
    for (let i = 0; i < id_list.length; i++) {
        buffer.writeUInt32BE(id_list[i], offset);
        offset += 4;
    }

    return buffer;
}

/**
 * 查看
 * @returns {Buffer}
 */
function packLoadRoute() {
    return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE, PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_ROUTE, PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_LOAD, 0, null);
}

/**
 * 解析收到的数据
 * @param content
 * @returns [PublicDefine_pb.MSG_Route]
 */
function unpackLoadRoute(content) {
    return decodeRoute(content);
}


/**
 * 添加
 * @param route 结构PublicDefine_pb.MSG_Route
 * @returns {Buffer}
 */
function packAddRoute(route) {
    const content = _encodeRoute(route);
    return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE, PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_ROUTE, PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_ADD, content.length + 4 + 64, content);
}

/**
 * 修改
 * @param route {PublicDefine_pb.MSG_Route}
 * @returns {Buffer}
 */
function packUpdateRoute(route) {
    const content = _encodeRoute(route);
    return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE, PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_ROUTE, PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_UPDATE, content.length + 4 + 64, content);
}

/**
 * 删除
 * @param deviceIds 待删除资产id数组
 * @returns {Buffer}
 */
function packRemoveRoute(deviceIds) {
    const content = _encodeRemoveRoute(deviceIds);
    return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_CONFIG_MANAGE, PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_ROUTE, PublicDefine_pb.ConfigPacketParam.CONFIG_PACKET_PARAM_DELETE, content.length + 4 + 64, content);
}

module.exports = {
    decodeRoute,
    packLoadRoute,
    unpackLoadRoute,
    packAddRoute,
    packUpdateRoute,
    packRemoveRoute
};
