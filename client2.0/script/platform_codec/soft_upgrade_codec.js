'use strict';
/**
 * 软件升级编码解码器
 */

const util = require('../../lib/util');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
// const SOFT_UPGRADE_BLOCK_SIZE = 4 + 2 + 2 + 4 + 4;

/**
 * 加载软件升级编码
 * @returns {Buffer}
 * @private
 * @param fileContent 升级数据
 */
function _encodeSoftUpgrade(fileContent) {
    let buffer = Buffer.alloc(fileContent.length+16);
    let offset = 0;
    console.log(fileContent,fileContent.length)
    for (const cont of fileContent){
        buffer.writeUInt8(cont, offset);
        offset ++;
    }
    let md5 = util.md5_bytes(fileContent);
    for (const b of md5) {
        buffer.writeUInt8(b, offset);
        offset++;
    }

    return buffer;
}

/**
 * 加载软件升级解码，返回待解析的e语言格式字符串数组
 * @param content
 * @returns {Array}
 * @private
 */
function _decodeSoftUpgrade(content) {
    const cont = content.toString();
    return cont.split('\n');
}

/**
 * 请求软件升级
 * @returns {Buffer}
 * @param fileContent 升级数据
 */
function packSoftUpgrade(fileContent) {
    const content = _encodeSoftUpgrade(fileContent);
    return util.packPlatformMsg(PublicDefine_pb.PlatformType.PLATFORM_TYPE_UPGRADE_SOFTWARE,
      0,
      0,
      content.length + 4 + 64,
      content);
}

/**
 * 解析收到的数据
 * @param content
 * @returns {Array}
 */
function unpackSoftUpgrade(content) {
    return _decodeSoftUpgrade(content);
}

module.exports = {
    packSoftUpgrade,
    unpackSoftUpgrade,
};