let dgram = require('dgram');

let clientSocket = dgram.createSocket('udp4');

let messages = [
    '<14>2018-06-29 09:32:36 DPTECH %%--FW/WEB/6/OPERLOG(l): client-type(84):web;user-name(85):admin;host-ip(86):*************;error-code(87):0;User [admin] logged in from IP address: [*************]. result: Success.',
    '<91>Jan  1 01:50:28 2011 H3C %%10WEB/5/LOGIN: -DevIP=*************; admin logged in from *************',
];


//start a timer to send message to echoServer
// setInterval(sendMsg, 0);

clientSocket.on('message', function (msg, rinfo) {
    console.log('recv %s(%d) from server\n', msg, msg.length);
});

clientSocket.on('error', function (err) {
    console.log('error, msg - %s, stack - %s\n', err.message, err.stack);
});

clientSocket.bind(54321);

let index = 0;
let count = 0;
function sendMsg() {
    count++;
    if (count>= 40000) {
        console.log('finished');
        return
    }

    let msg = messages[index];
    index = index + 1;
    if (index === messages.length) {
        index = 0;
    }
    clientSocket.send(msg, 0, msg.length, 514, "***************");
}

for (let i = 0; i < 40000; i++) {
    sendMsg();
}

console.log("ooo");
