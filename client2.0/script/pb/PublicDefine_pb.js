/**
 * @fileoverview
 * @enhanceable
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!

var jspb = require('google-protobuf');
var goog = jspb;
var global = Function('return this')();

goog.exportSymbol('proto.com.jy.network_monitor.proto.BackupRestoreSubType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.BaselinePacketSubType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.CertType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.CharacterUpdateSubType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.CompressFormat', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.ConfigPacketParam', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.ConfigPacketSubType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.ControlPacketSubType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.DcdModule', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.DeviceState', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.DeviceType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.EncryptType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.ErrorCode', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.ExceptionLogType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.GeneralParamKey', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.GeneralTaskCache', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.LeakScanSubType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.LogLevel', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.LogModule', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.LogSource', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.LogType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_AppUser', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_AppUserAuthority', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_Baseline', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_Cert', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_CharacterUpdate', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_CommuParam', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_Control', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_Device', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_Event', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_EventParam', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_EventReason', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_File', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_FiledEvent', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_GeneralParam', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_LeakScann', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_MonitoredObject', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_NetCard', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_NtpParam', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_Platform', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_RawEvent', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_Route', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_RunLog', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_RunLogReport', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_ServiceInfo', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_UpgradeData', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_VersionVerify', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MonitoredObjectParam', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MonitoredObjectSubType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.ParamManageTaskCache', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.PlatformType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.ResumeFileTask', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.RoleType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.SnmpAuthType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.SnmpEncryptType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.SwitchStatus', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.SystemFuncCode', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.UpgradeType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.VersionManageSubType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.VersionVerifyParam', null, global);

/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_GeneralParam = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_GeneralParam, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_GeneralParam.displayName = 'proto.com.jy.network_monitor.proto.MSG_GeneralParam';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_GeneralParam.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_GeneralParam.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_GeneralParam} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_GeneralParam.toObject = function(includeInstance, msg) {
  var f, obj = {
    key: jspb.Message.getFieldWithDefault(msg, 1, 0),
    value: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_GeneralParam}
 */
proto.com.jy.network_monitor.proto.MSG_GeneralParam.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_GeneralParam;
  return proto.com.jy.network_monitor.proto.MSG_GeneralParam.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_GeneralParam} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_GeneralParam}
 */
proto.com.jy.network_monitor.proto.MSG_GeneralParam.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.com.jy.network_monitor.proto.GeneralParamKey} */ (reader.readEnum());
      msg.setKey(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setValue(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_GeneralParam.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_GeneralParam.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_GeneralParam} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_GeneralParam.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getKey();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getValue();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional GeneralParamKey key = 1;
 * @return {!proto.com.jy.network_monitor.proto.GeneralParamKey}
 */
proto.com.jy.network_monitor.proto.MSG_GeneralParam.prototype.getKey = function() {
  return /** @type {!proto.com.jy.network_monitor.proto.GeneralParamKey} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {!proto.com.jy.network_monitor.proto.GeneralParamKey} value */
proto.com.jy.network_monitor.proto.MSG_GeneralParam.prototype.setKey = function(value) {
  jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional string value = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_GeneralParam.prototype.getValue = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_GeneralParam.prototype.setValue = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.GeneralTaskCache = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.GeneralTaskCache, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.GeneralTaskCache.displayName = 'proto.com.jy.network_monitor.proto.GeneralTaskCache';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.GeneralTaskCache.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.GeneralTaskCache.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.GeneralTaskCache} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.GeneralTaskCache.toObject = function(includeInstance, msg) {
  var f, obj = {
    taskId: jspb.Message.getFieldWithDefault(msg, 1, ""),
    srcIp: jspb.Message.getFieldWithDefault(msg, 2, ""),
    destIp: jspb.Message.getFieldWithDefault(msg, 3, ""),
    startTime: jspb.Message.getFieldWithDefault(msg, 4, 0),
    stopTime: jspb.Message.getFieldWithDefault(msg, 5, 0),
    completeTime: jspb.Message.getFieldWithDefault(msg, 6, 0),
    result: jspb.Message.getFieldWithDefault(msg, 7, ""),
    errorCode: jspb.Message.getFieldWithDefault(msg, 8, 0),
    subType: jspb.Message.getFieldWithDefault(msg, 9, 0),
    param: jspb.Message.getFieldWithDefault(msg, 10, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.GeneralTaskCache}
 */
proto.com.jy.network_monitor.proto.GeneralTaskCache.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.GeneralTaskCache;
  return proto.com.jy.network_monitor.proto.GeneralTaskCache.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.GeneralTaskCache} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.GeneralTaskCache}
 */
proto.com.jy.network_monitor.proto.GeneralTaskCache.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setTaskId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSrcIp(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setDestIp(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setStartTime(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setStopTime(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setCompleteTime(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setResult(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setErrorCode(value);
      break;
    case 9:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSubType(value);
      break;
    case 10:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setParam(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.GeneralTaskCache.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.GeneralTaskCache.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.GeneralTaskCache} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.GeneralTaskCache.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTaskId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getSrcIp();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getDestIp();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getStartTime();
  if (f !== 0) {
    writer.writeInt64(
      4,
      f
    );
  }
  f = message.getStopTime();
  if (f !== 0) {
    writer.writeInt64(
      5,
      f
    );
  }
  f = message.getCompleteTime();
  if (f !== 0) {
    writer.writeInt64(
      6,
      f
    );
  }
  f = message.getResult();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getErrorCode();
  if (f !== 0) {
    writer.writeInt32(
      8,
      f
    );
  }
  f = message.getSubType();
  if (f !== 0) {
    writer.writeInt32(
      9,
      f
    );
  }
  f = message.getParam();
  if (f !== 0) {
    writer.writeInt32(
      10,
      f
    );
  }
};


/**
 * optional string task_id = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.GeneralTaskCache.prototype.getTaskId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.GeneralTaskCache.prototype.setTaskId = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string src_ip = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.GeneralTaskCache.prototype.getSrcIp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.GeneralTaskCache.prototype.setSrcIp = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string dest_ip = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.GeneralTaskCache.prototype.getDestIp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.GeneralTaskCache.prototype.setDestIp = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional int64 start_time = 4;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.GeneralTaskCache.prototype.getStartTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.GeneralTaskCache.prototype.setStartTime = function(value) {
  jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional int64 stop_time = 5;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.GeneralTaskCache.prototype.getStopTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.GeneralTaskCache.prototype.setStopTime = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional int64 complete_time = 6;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.GeneralTaskCache.prototype.getCompleteTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.GeneralTaskCache.prototype.setCompleteTime = function(value) {
  jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional string result = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.GeneralTaskCache.prototype.getResult = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.GeneralTaskCache.prototype.setResult = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional int32 error_code = 8;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.GeneralTaskCache.prototype.getErrorCode = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.GeneralTaskCache.prototype.setErrorCode = function(value) {
  jspb.Message.setProto3IntField(this, 8, value);
};


/**
 * optional int32 sub_type = 9;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.GeneralTaskCache.prototype.getSubType = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 9, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.GeneralTaskCache.prototype.setSubType = function(value) {
  jspb.Message.setProto3IntField(this, 9, value);
};


/**
 * optional int32 param = 10;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.GeneralTaskCache.prototype.getParam = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 10, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.GeneralTaskCache.prototype.setParam = function(value) {
  jspb.Message.setProto3IntField(this, 10, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.ParamManageTaskCache = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.ParamManageTaskCache, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.ParamManageTaskCache.displayName = 'proto.com.jy.network_monitor.proto.ParamManageTaskCache';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.ParamManageTaskCache.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.ParamManageTaskCache.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.ParamManageTaskCache} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.ParamManageTaskCache.toObject = function(includeInstance, msg) {
  var f, obj = {
    taskId: jspb.Message.getFieldWithDefault(msg, 1, ""),
    srcIp: jspb.Message.getFieldWithDefault(msg, 2, ""),
    destIp: jspb.Message.getFieldWithDefault(msg, 3, ""),
    subType: jspb.Message.getFieldWithDefault(msg, 4, 0),
    paramType: jspb.Message.getFieldWithDefault(msg, 5, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.ParamManageTaskCache}
 */
proto.com.jy.network_monitor.proto.ParamManageTaskCache.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.ParamManageTaskCache;
  return proto.com.jy.network_monitor.proto.ParamManageTaskCache.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.ParamManageTaskCache} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.ParamManageTaskCache}
 */
proto.com.jy.network_monitor.proto.ParamManageTaskCache.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setTaskId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSrcIp(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setDestIp(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSubType(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setParamType(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.ParamManageTaskCache.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.ParamManageTaskCache.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.ParamManageTaskCache} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.ParamManageTaskCache.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTaskId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getSrcIp();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getDestIp();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getSubType();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
  f = message.getParamType();
  if (f !== 0) {
    writer.writeInt32(
      5,
      f
    );
  }
};


/**
 * optional string task_id = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.ParamManageTaskCache.prototype.getTaskId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.ParamManageTaskCache.prototype.setTaskId = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string src_ip = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.ParamManageTaskCache.prototype.getSrcIp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.ParamManageTaskCache.prototype.setSrcIp = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string dest_ip = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.ParamManageTaskCache.prototype.getDestIp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.ParamManageTaskCache.prototype.setDestIp = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional int32 sub_type = 4;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.ParamManageTaskCache.prototype.getSubType = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.ParamManageTaskCache.prototype.setSubType = function(value) {
  jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional int32 param_type = 5;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.ParamManageTaskCache.prototype.getParamType = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.ParamManageTaskCache.prototype.setParamType = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_EventReason = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_EventReason, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_EventReason.displayName = 'proto.com.jy.network_monitor.proto.MSG_EventReason';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_EventReason.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_EventReason.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_EventReason} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_EventReason.toObject = function(includeInstance, msg) {
  var f, obj = {
    eventType: jspb.Message.getFieldWithDefault(msg, 1, 0),
    eventSubType: jspb.Message.getFieldWithDefault(msg, 2, 0),
    content: jspb.Message.getFieldWithDefault(msg, 3, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_EventReason}
 */
proto.com.jy.network_monitor.proto.MSG_EventReason.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_EventReason;
  return proto.com.jy.network_monitor.proto.MSG_EventReason.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_EventReason} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_EventReason}
 */
proto.com.jy.network_monitor.proto.MSG_EventReason.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setEventType(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setEventSubType(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setContent(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_EventReason.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_EventReason.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_EventReason} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_EventReason.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getEventType();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getEventSubType();
  if (f !== 0) {
    writer.writeInt32(
      2,
      f
    );
  }
  f = message.getContent();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
};


/**
 * optional int32 event_type = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_EventReason.prototype.getEventType = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_EventReason.prototype.setEventType = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional int32 event_sub_type = 2;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_EventReason.prototype.getEventSubType = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_EventReason.prototype.setEventSubType = function(value) {
  jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional string content = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_EventReason.prototype.getContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_EventReason.prototype.setContent = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_Event = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_Event, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_Event.displayName = 'proto.com.jy.network_monitor.proto.MSG_Event';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_Event.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_Event.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_Event} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_Event.toObject = function(includeInstance, msg) {
  var f, obj = {
    level: jspb.Message.getFieldWithDefault(msg, 1, 0),
    time: jspb.Message.getFieldWithDefault(msg, 2, 0),
    deviceName: jspb.Message.getFieldWithDefault(msg, 3, ""),
    deviceType: jspb.Message.getFieldWithDefault(msg, 4, 0),
    reason: (f = msg.getReason()) && proto.com.jy.network_monitor.proto.MSG_EventReason.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_Event}
 */
proto.com.jy.network_monitor.proto.MSG_Event.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_Event;
  return proto.com.jy.network_monitor.proto.MSG_Event.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_Event} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_Event}
 */
proto.com.jy.network_monitor.proto.MSG_Event.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setLevel(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint64());
      msg.setTime(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setDeviceName(value);
      break;
    case 4:
      var value = /** @type {!proto.com.jy.network_monitor.proto.DeviceType} */ (reader.readEnum());
      msg.setDeviceType(value);
      break;
    case 5:
      var value = new proto.com.jy.network_monitor.proto.MSG_EventReason;
      reader.readMessage(value,proto.com.jy.network_monitor.proto.MSG_EventReason.deserializeBinaryFromReader);
      msg.setReason(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_Event.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_Event.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_Event} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_Event.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLevel();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getTime();
  if (f !== 0) {
    writer.writeUint64(
      2,
      f
    );
  }
  f = message.getDeviceName();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getDeviceType();
  if (f !== 0.0) {
    writer.writeEnum(
      4,
      f
    );
  }
  f = message.getReason();
  if (f != null) {
    writer.writeMessage(
      5,
      f,
      proto.com.jy.network_monitor.proto.MSG_EventReason.serializeBinaryToWriter
    );
  }
};


/**
 * optional int32 level = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_Event.prototype.getLevel = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_Event.prototype.setLevel = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional uint64 time = 2;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_Event.prototype.getTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_Event.prototype.setTime = function(value) {
  jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional string device_name = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Event.prototype.getDeviceName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Event.prototype.setDeviceName = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional DeviceType device_type = 4;
 * @return {!proto.com.jy.network_monitor.proto.DeviceType}
 */
proto.com.jy.network_monitor.proto.MSG_Event.prototype.getDeviceType = function() {
  return /** @type {!proto.com.jy.network_monitor.proto.DeviceType} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {!proto.com.jy.network_monitor.proto.DeviceType} value */
proto.com.jy.network_monitor.proto.MSG_Event.prototype.setDeviceType = function(value) {
  jspb.Message.setProto3EnumField(this, 4, value);
};


/**
 * optional MSG_EventReason reason = 5;
 * @return {?proto.com.jy.network_monitor.proto.MSG_EventReason}
 */
proto.com.jy.network_monitor.proto.MSG_Event.prototype.getReason = function() {
  return /** @type{?proto.com.jy.network_monitor.proto.MSG_EventReason} */ (
    jspb.Message.getWrapperField(this, proto.com.jy.network_monitor.proto.MSG_EventReason, 5));
};


/** @param {?proto.com.jy.network_monitor.proto.MSG_EventReason|undefined} value */
proto.com.jy.network_monitor.proto.MSG_Event.prototype.setReason = function(value) {
  jspb.Message.setWrapperField(this, 5, value);
};


proto.com.jy.network_monitor.proto.MSG_Event.prototype.clearReason = function() {
  this.setReason(undefined);
};


/**
 * Returns whether this field is set.
 * @return {!boolean}
 */
proto.com.jy.network_monitor.proto.MSG_Event.prototype.hasReason = function() {
  return jspb.Message.getField(this, 5) != null;
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_RawEvent = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_RawEvent, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_RawEvent.displayName = 'proto.com.jy.network_monitor.proto.MSG_RawEvent';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_RawEvent.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_RawEvent.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_RawEvent} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_RawEvent.toObject = function(includeInstance, msg) {
  var f, obj = {
    deviceId: jspb.Message.getFieldWithDefault(msg, 1, 0),
    deviceIp: jspb.Message.getFieldWithDefault(msg, 2, ""),
    content: jspb.Message.getFieldWithDefault(msg, 3, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_RawEvent}
 */
proto.com.jy.network_monitor.proto.MSG_RawEvent.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_RawEvent;
  return proto.com.jy.network_monitor.proto.MSG_RawEvent.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_RawEvent} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_RawEvent}
 */
proto.com.jy.network_monitor.proto.MSG_RawEvent.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setDeviceId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setDeviceIp(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setContent(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_RawEvent.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_RawEvent.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_RawEvent} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_RawEvent.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getDeviceId();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getDeviceIp();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getContent();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
};


/**
 * optional int32 device_id = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_RawEvent.prototype.getDeviceId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_RawEvent.prototype.setDeviceId = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string device_ip = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_RawEvent.prototype.getDeviceIp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_RawEvent.prototype.setDeviceIp = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string content = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_RawEvent.prototype.getContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_RawEvent.prototype.setContent = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_Device = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_Device, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_Device.displayName = 'proto.com.jy.network_monitor.proto.MSG_Device';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_Device.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_Device} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_Device.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, 0),
    deviceName: jspb.Message.getFieldWithDefault(msg, 2, ""),
    ip1: jspb.Message.getFieldWithDefault(msg, 3, 0),
    mac1: jspb.Message.getFieldWithDefault(msg, 4, ""),
    ip2: jspb.Message.getFieldWithDefault(msg, 5, 0),
    mac2: jspb.Message.getFieldWithDefault(msg, 6, ""),
    deviceType: jspb.Message.getFieldWithDefault(msg, 7, ""),
    manufacturer: jspb.Message.getFieldWithDefault(msg, 8, ""),
    serialNo: jspb.Message.getFieldWithDefault(msg, 9, ""),
    sysVersion: jspb.Message.getFieldWithDefault(msg, 10, ""),
    reserved: jspb.Message.getFieldWithDefault(msg, 11, 0),
    snmpVersion: jspb.Message.getFieldWithDefault(msg, 12, 0),
    snmpUsername: jspb.Message.getFieldWithDefault(msg, 13, ""),
    snmpAuth: jspb.Message.getFieldWithDefault(msg, 14, 0),
    snmpEncrypt: jspb.Message.getFieldWithDefault(msg, 15, 0),
    snmpReadCommunity: jspb.Message.getFieldWithDefault(msg, 16, ""),
    snmpWriteCommunity: jspb.Message.getFieldWithDefault(msg, 17, ""),
    state: jspb.Message.getFieldWithDefault(msg, 31, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_Device}
 */
proto.com.jy.network_monitor.proto.MSG_Device.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_Device;
  return proto.com.jy.network_monitor.proto.MSG_Device.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_Device} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_Device}
 */
proto.com.jy.network_monitor.proto.MSG_Device.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setDeviceName(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setIp1(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setMac1(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setIp2(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setMac2(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setDeviceType(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setManufacturer(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setSerialNo(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setSysVersion(value);
      break;
    case 11:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setReserved(value);
      break;
    case 12:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSnmpVersion(value);
      break;
    case 13:
      var value = /** @type {string} */ (reader.readString());
      msg.setSnmpUsername(value);
      break;
    case 14:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSnmpAuth(value);
      break;
    case 15:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSnmpEncrypt(value);
      break;
    case 16:
      var value = /** @type {string} */ (reader.readString());
      msg.setSnmpReadCommunity(value);
      break;
    case 17:
      var value = /** @type {string} */ (reader.readString());
      msg.setSnmpWriteCommunity(value);
      break;
    case 31:
      var value = /** @type {!proto.com.jy.network_monitor.proto.DeviceState} */ (reader.readEnum());
      msg.setState(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_Device.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_Device} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_Device.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getDeviceName();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getIp1();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getMac1();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getIp2();
  if (f !== 0) {
    writer.writeInt32(
      5,
      f
    );
  }
  f = message.getMac2();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getDeviceType();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getManufacturer();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getSerialNo();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
  f = message.getSysVersion();
  if (f.length > 0) {
    writer.writeString(
      10,
      f
    );
  }
  f = message.getReserved();
  if (f !== 0) {
    writer.writeInt32(
      11,
      f
    );
  }
  f = message.getSnmpVersion();
  if (f !== 0) {
    writer.writeInt32(
      12,
      f
    );
  }
  f = message.getSnmpUsername();
  if (f.length > 0) {
    writer.writeString(
      13,
      f
    );
  }
  f = message.getSnmpAuth();
  if (f !== 0) {
    writer.writeInt32(
      14,
      f
    );
  }
  f = message.getSnmpEncrypt();
  if (f !== 0) {
    writer.writeInt32(
      15,
      f
    );
  }
  f = message.getSnmpReadCommunity();
  if (f.length > 0) {
    writer.writeString(
      16,
      f
    );
  }
  f = message.getSnmpWriteCommunity();
  if (f.length > 0) {
    writer.writeString(
      17,
      f
    );
  }
  f = message.getState();
  if (f !== 0.0) {
    writer.writeEnum(
      31,
      f
    );
  }
};


/**
 * optional int32 id = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.getId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.setId = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string device_name = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.getDeviceName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.setDeviceName = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int32 ip1 = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.getIp1 = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.setIp1 = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional string mac1 = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.getMac1 = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.setMac1 = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional int32 ip2 = 5;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.getIp2 = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.setIp2 = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional string mac2 = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.getMac2 = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.setMac2 = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string device_type = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.getDeviceType = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.setDeviceType = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string manufacturer = 8;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.getManufacturer = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.setManufacturer = function(value) {
  jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional string serial_no = 9;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.getSerialNo = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.setSerialNo = function(value) {
  jspb.Message.setProto3StringField(this, 9, value);
};


/**
 * optional string sys_version = 10;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.getSysVersion = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.setSysVersion = function(value) {
  jspb.Message.setProto3StringField(this, 10, value);
};


/**
 * optional int32 reserved = 11;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.getReserved = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 11, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.setReserved = function(value) {
  jspb.Message.setProto3IntField(this, 11, value);
};


/**
 * optional int32 snmp_version = 12;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.getSnmpVersion = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 12, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.setSnmpVersion = function(value) {
  jspb.Message.setProto3IntField(this, 12, value);
};


/**
 * optional string snmp_username = 13;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.getSnmpUsername = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 13, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.setSnmpUsername = function(value) {
  jspb.Message.setProto3StringField(this, 13, value);
};


/**
 * optional int32 snmp_auth = 14;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.getSnmpAuth = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 14, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.setSnmpAuth = function(value) {
  jspb.Message.setProto3IntField(this, 14, value);
};


/**
 * optional int32 snmp_encrypt = 15;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.getSnmpEncrypt = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 15, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.setSnmpEncrypt = function(value) {
  jspb.Message.setProto3IntField(this, 15, value);
};


/**
 * optional string snmp_read_community = 16;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.getSnmpReadCommunity = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 16, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.setSnmpReadCommunity = function(value) {
  jspb.Message.setProto3StringField(this, 16, value);
};


/**
 * optional string snmp_write_community = 17;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.getSnmpWriteCommunity = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 17, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.setSnmpWriteCommunity = function(value) {
  jspb.Message.setProto3StringField(this, 17, value);
};


/**
 * optional DeviceState state = 31;
 * @return {!proto.com.jy.network_monitor.proto.DeviceState}
 */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.getState = function() {
  return /** @type {!proto.com.jy.network_monitor.proto.DeviceState} */ (jspb.Message.getFieldWithDefault(this, 31, 0));
};


/** @param {!proto.com.jy.network_monitor.proto.DeviceState} value */
proto.com.jy.network_monitor.proto.MSG_Device.prototype.setState = function(value) {
  jspb.Message.setProto3EnumField(this, 31, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_EventParam = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_EventParam, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_EventParam.displayName = 'proto.com.jy.network_monitor.proto.MSG_EventParam';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_EventParam.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_EventParam.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_EventParam} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_EventParam.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, 0),
    cpuThreshold: jspb.Message.getFieldWithDefault(msg, 2, 0),
    memThreshold: jspb.Message.getFieldWithDefault(msg, 3, 0),
    ifFlowThreshold: jspb.Message.getFieldWithDefault(msg, 4, 0),
    loginFailedThreshold: jspb.Message.getFieldWithDefault(msg, 5, 0),
    eventMergeCycle: jspb.Message.getFieldWithDefault(msg, 6, 0),
    diskThreshold: jspb.Message.getFieldWithDefault(msg, 7, 0),
    historyEventReportInterval: jspb.Message.getFieldWithDefault(msg, 8, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_EventParam}
 */
proto.com.jy.network_monitor.proto.MSG_EventParam.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_EventParam;
  return proto.com.jy.network_monitor.proto.MSG_EventParam.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_EventParam} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_EventParam}
 */
proto.com.jy.network_monitor.proto.MSG_EventParam.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setCpuThreshold(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setMemThreshold(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setIfFlowThreshold(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setLoginFailedThreshold(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setEventMergeCycle(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setDiskThreshold(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setHistoryEventReportInterval(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_EventParam.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_EventParam.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_EventParam} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_EventParam.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getCpuThreshold();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getMemThreshold();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getIfFlowThreshold();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
  f = message.getLoginFailedThreshold();
  if (f !== 0) {
    writer.writeUint32(
      5,
      f
    );
  }
  f = message.getEventMergeCycle();
  if (f !== 0) {
    writer.writeUint32(
      6,
      f
    );
  }
  f = message.getDiskThreshold();
  if (f !== 0) {
    writer.writeUint32(
      7,
      f
    );
  }
  f = message.getHistoryEventReportInterval();
  if (f !== 0) {
    writer.writeUint32(
      8,
      f
    );
  }
};


/**
 * optional int32 id = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_EventParam.prototype.getId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_EventParam.prototype.setId = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional uint32 cpu_threshold = 2;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_EventParam.prototype.getCpuThreshold = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_EventParam.prototype.setCpuThreshold = function(value) {
  jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 mem_threshold = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_EventParam.prototype.getMemThreshold = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_EventParam.prototype.setMemThreshold = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional uint32 if_flow_threshold = 4;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_EventParam.prototype.getIfFlowThreshold = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_EventParam.prototype.setIfFlowThreshold = function(value) {
  jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional uint32 login_failed_threshold = 5;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_EventParam.prototype.getLoginFailedThreshold = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_EventParam.prototype.setLoginFailedThreshold = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional uint32 event_merge_cycle = 6;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_EventParam.prototype.getEventMergeCycle = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_EventParam.prototype.setEventMergeCycle = function(value) {
  jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional uint32 disk_threshold = 7;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_EventParam.prototype.getDiskThreshold = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_EventParam.prototype.setDiskThreshold = function(value) {
  jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional uint32 history_event_report_interval = 8;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_EventParam.prototype.getHistoryEventReportInterval = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_EventParam.prototype.setHistoryEventReportInterval = function(value) {
  jspb.Message.setProto3IntField(this, 8, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_NtpParam = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_NtpParam, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_NtpParam.displayName = 'proto.com.jy.network_monitor.proto.MSG_NtpParam';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_NtpParam.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_NtpParam.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_NtpParam} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_NtpParam.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, 0),
    mainClockMainNetworkIp: jspb.Message.getFieldWithDefault(msg, 2, 0),
    mainClockBackupNetworkIp: jspb.Message.getFieldWithDefault(msg, 3, 0),
    backupClockMainNetworkIp: jspb.Message.getFieldWithDefault(msg, 4, 0),
    backupClockBackupNetworkIp: jspb.Message.getFieldWithDefault(msg, 5, 0),
    ntpServerPort: jspb.Message.getFieldWithDefault(msg, 6, 0),
    ntpVerifyTimeCycle: jspb.Message.getFieldWithDefault(msg, 7, 0),
    ntpIsBroadcast: jspb.Message.getFieldWithDefault(msg, 8, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_NtpParam}
 */
proto.com.jy.network_monitor.proto.MSG_NtpParam.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_NtpParam;
  return proto.com.jy.network_monitor.proto.MSG_NtpParam.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_NtpParam} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_NtpParam}
 */
proto.com.jy.network_monitor.proto.MSG_NtpParam.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setMainClockMainNetworkIp(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setMainClockBackupNetworkIp(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setBackupClockMainNetworkIp(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setBackupClockBackupNetworkIp(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setNtpServerPort(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setNtpVerifyTimeCycle(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setNtpIsBroadcast(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_NtpParam.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_NtpParam.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_NtpParam} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_NtpParam.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getMainClockMainNetworkIp();
  if (f !== 0) {
    writer.writeInt32(
      2,
      f
    );
  }
  f = message.getMainClockBackupNetworkIp();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getBackupClockMainNetworkIp();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
  f = message.getBackupClockBackupNetworkIp();
  if (f !== 0) {
    writer.writeInt32(
      5,
      f
    );
  }
  f = message.getNtpServerPort();
  if (f !== 0) {
    writer.writeInt32(
      6,
      f
    );
  }
  f = message.getNtpVerifyTimeCycle();
  if (f !== 0) {
    writer.writeInt32(
      7,
      f
    );
  }
  f = message.getNtpIsBroadcast();
  if (f !== 0) {
    writer.writeInt32(
      8,
      f
    );
  }
};


/**
 * optional int32 id = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_NtpParam.prototype.getId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_NtpParam.prototype.setId = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional int32 main_clock_main_network_ip = 2;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_NtpParam.prototype.getMainClockMainNetworkIp = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_NtpParam.prototype.setMainClockMainNetworkIp = function(value) {
  jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional int32 main_clock_backup_network_ip = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_NtpParam.prototype.getMainClockBackupNetworkIp = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_NtpParam.prototype.setMainClockBackupNetworkIp = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional int32 backup_clock_main_network_ip = 4;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_NtpParam.prototype.getBackupClockMainNetworkIp = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_NtpParam.prototype.setBackupClockMainNetworkIp = function(value) {
  jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional int32 backup_clock_backup_network_ip = 5;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_NtpParam.prototype.getBackupClockBackupNetworkIp = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_NtpParam.prototype.setBackupClockBackupNetworkIp = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional int32 ntp_server_port = 6;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_NtpParam.prototype.getNtpServerPort = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_NtpParam.prototype.setNtpServerPort = function(value) {
  jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional int32 ntp_verify_time_cycle = 7;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_NtpParam.prototype.getNtpVerifyTimeCycle = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_NtpParam.prototype.setNtpVerifyTimeCycle = function(value) {
  jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional int32 ntp_is_broadcast = 8;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_NtpParam.prototype.getNtpIsBroadcast = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_NtpParam.prototype.setNtpIsBroadcast = function(value) {
  jspb.Message.setProto3IntField(this, 8, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_Platform = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_Platform, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_Platform.displayName = 'proto.com.jy.network_monitor.proto.MSG_Platform';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_Platform.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_Platform.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_Platform} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_Platform.toObject = function(includeInstance, msg) {
  var f, obj = {
    ip: jspb.Message.getFieldWithDefault(msg, 1, 0),
    port: jspb.Message.getFieldWithDefault(msg, 2, 0),
    authority: jspb.Message.getFieldWithDefault(msg, 3, 0),
    group: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_Platform}
 */
proto.com.jy.network_monitor.proto.MSG_Platform.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_Platform;
  return proto.com.jy.network_monitor.proto.MSG_Platform.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_Platform} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_Platform}
 */
proto.com.jy.network_monitor.proto.MSG_Platform.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setIp(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setPort(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setAuthority(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setGroup(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_Platform.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_Platform.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_Platform} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_Platform.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getIp();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getPort();
  if (f !== 0) {
    writer.writeInt32(
      2,
      f
    );
  }
  f = message.getAuthority();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getGroup();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
};


/**
 * optional int32 ip = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_Platform.prototype.getIp = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_Platform.prototype.setIp = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional int32 port = 2;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_Platform.prototype.getPort = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_Platform.prototype.setPort = function(value) {
  jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional int32 authority = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_Platform.prototype.getAuthority = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_Platform.prototype.setAuthority = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional int32 group = 4;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_Platform.prototype.getGroup = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_Platform.prototype.setGroup = function(value) {
  jspb.Message.setProto3IntField(this, 4, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_CommuParam = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.com.jy.network_monitor.proto.MSG_CommuParam.repeatedFields_, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_CommuParam, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_CommuParam.displayName = 'proto.com.jy.network_monitor.proto.MSG_CommuParam';
}
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.com.jy.network_monitor.proto.MSG_CommuParam.repeatedFields_ = [6];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_CommuParam.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_CommuParam.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_CommuParam} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_CommuParam.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, 0),
    serverListenPort: jspb.Message.getFieldWithDefault(msg, 2, 0),
    snmpListenPort: jspb.Message.getFieldWithDefault(msg, 3, 0),
    syslogListenPort: jspb.Message.getFieldWithDefault(msg, 4, 0),
    proxyListenPport: jspb.Message.getFieldWithDefault(msg, 5, 0),
    platformsList: jspb.Message.toObjectList(msg.getPlatformsList(),
    proto.com.jy.network_monitor.proto.MSG_Platform.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_CommuParam}
 */
proto.com.jy.network_monitor.proto.MSG_CommuParam.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_CommuParam;
  return proto.com.jy.network_monitor.proto.MSG_CommuParam.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_CommuParam} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_CommuParam}
 */
proto.com.jy.network_monitor.proto.MSG_CommuParam.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setServerListenPort(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSnmpListenPort(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSyslogListenPort(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setProxyListenPport(value);
      break;
    case 6:
      var value = new proto.com.jy.network_monitor.proto.MSG_Platform;
      reader.readMessage(value,proto.com.jy.network_monitor.proto.MSG_Platform.deserializeBinaryFromReader);
      msg.addPlatforms(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_CommuParam.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_CommuParam.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_CommuParam} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_CommuParam.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getServerListenPort();
  if (f !== 0) {
    writer.writeInt32(
      2,
      f
    );
  }
  f = message.getSnmpListenPort();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getSyslogListenPort();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
  f = message.getProxyListenPport();
  if (f !== 0) {
    writer.writeInt32(
      5,
      f
    );
  }
  f = message.getPlatformsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      6,
      f,
      proto.com.jy.network_monitor.proto.MSG_Platform.serializeBinaryToWriter
    );
  }
};


/**
 * optional int32 id = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_CommuParam.prototype.getId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_CommuParam.prototype.setId = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional int32 server_listen_port = 2;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_CommuParam.prototype.getServerListenPort = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_CommuParam.prototype.setServerListenPort = function(value) {
  jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional int32 snmp_listen_port = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_CommuParam.prototype.getSnmpListenPort = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_CommuParam.prototype.setSnmpListenPort = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional int32 syslog_listen_port = 4;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_CommuParam.prototype.getSyslogListenPort = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_CommuParam.prototype.setSyslogListenPort = function(value) {
  jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional int32 proxy_listen_pPort = 5;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_CommuParam.prototype.getProxyListenPport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_CommuParam.prototype.setProxyListenPport = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * repeated MSG_Platform platforms = 6;
 * @return {!Array<!proto.com.jy.network_monitor.proto.MSG_Platform>}
 */
proto.com.jy.network_monitor.proto.MSG_CommuParam.prototype.getPlatformsList = function() {
  return /** @type{!Array<!proto.com.jy.network_monitor.proto.MSG_Platform>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.com.jy.network_monitor.proto.MSG_Platform, 6));
};


/** @param {!Array<!proto.com.jy.network_monitor.proto.MSG_Platform>} value */
proto.com.jy.network_monitor.proto.MSG_CommuParam.prototype.setPlatformsList = function(value) {
  jspb.Message.setRepeatedWrapperField(this, 6, value);
};


/**
 * @param {!proto.com.jy.network_monitor.proto.MSG_Platform=} opt_value
 * @param {number=} opt_index
 * @return {!proto.com.jy.network_monitor.proto.MSG_Platform}
 */
proto.com.jy.network_monitor.proto.MSG_CommuParam.prototype.addPlatforms = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 6, opt_value, proto.com.jy.network_monitor.proto.MSG_Platform, opt_index);
};


proto.com.jy.network_monitor.proto.MSG_CommuParam.prototype.clearPlatformsList = function() {
  this.setPlatformsList([]);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_NetCard = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_NetCard, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_NetCard.displayName = 'proto.com.jy.network_monitor.proto.MSG_NetCard';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_NetCard.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_NetCard.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_NetCard} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_NetCard.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, 0),
    netcardName: jspb.Message.getFieldWithDefault(msg, 2, ""),
    ip: jspb.Message.getFieldWithDefault(msg, 3, 0),
    mask: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_NetCard}
 */
proto.com.jy.network_monitor.proto.MSG_NetCard.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_NetCard;
  return proto.com.jy.network_monitor.proto.MSG_NetCard.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_NetCard} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_NetCard}
 */
proto.com.jy.network_monitor.proto.MSG_NetCard.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setNetcardName(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setIp(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setMask(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_NetCard.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_NetCard.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_NetCard} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_NetCard.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getNetcardName();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getIp();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getMask();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
};


/**
 * optional int32 id = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_NetCard.prototype.getId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_NetCard.prototype.setId = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string netcard_name = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_NetCard.prototype.getNetcardName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_NetCard.prototype.setNetcardName = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int32 ip = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_NetCard.prototype.getIp = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_NetCard.prototype.setIp = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional int32 mask = 4;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_NetCard.prototype.getMask = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_NetCard.prototype.setMask = function(value) {
  jspb.Message.setProto3IntField(this, 4, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_Route = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_Route, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_Route.displayName = 'proto.com.jy.network_monitor.proto.MSG_Route';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_Route.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_Route.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_Route} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_Route.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, 0),
    destSegment: jspb.Message.getFieldWithDefault(msg, 2, 0),
    destMask: jspb.Message.getFieldWithDefault(msg, 3, 0),
    gateway: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_Route}
 */
proto.com.jy.network_monitor.proto.MSG_Route.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_Route;
  return proto.com.jy.network_monitor.proto.MSG_Route.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_Route} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_Route}
 */
proto.com.jy.network_monitor.proto.MSG_Route.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setDestSegment(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setDestMask(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setGateway(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_Route.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_Route.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_Route} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_Route.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getDestSegment();
  if (f !== 0) {
    writer.writeInt32(
      2,
      f
    );
  }
  f = message.getDestMask();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getGateway();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
};


/**
 * optional int32 id = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_Route.prototype.getId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_Route.prototype.setId = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional int32 dest_segment = 2;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_Route.prototype.getDestSegment = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_Route.prototype.setDestSegment = function(value) {
  jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional int32 dest_mask = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_Route.prototype.getDestMask = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_Route.prototype.setDestMask = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional int32 gateway = 4;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_Route.prototype.getGateway = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_Route.prototype.setGateway = function(value) {
  jspb.Message.setProto3IntField(this, 4, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_Cert = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_Cert, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_Cert.displayName = 'proto.com.jy.network_monitor.proto.MSG_Cert';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_Cert.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_Cert.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_Cert} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_Cert.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, 0),
    certType: jspb.Message.getFieldWithDefault(msg, 2, 0),
    reserved: jspb.Message.getFieldWithDefault(msg, 3, 0),
    certContentLength: jspb.Message.getFieldWithDefault(msg, 4, 0),
    platformIp: jspb.Message.getFieldWithDefault(msg, 5, 0),
    certName: jspb.Message.getFieldWithDefault(msg, 6, ""),
    certContent: msg.getCertContent_asB64()
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_Cert}
 */
proto.com.jy.network_monitor.proto.MSG_Cert.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_Cert;
  return proto.com.jy.network_monitor.proto.MSG_Cert.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_Cert} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_Cert}
 */
proto.com.jy.network_monitor.proto.MSG_Cert.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {!proto.com.jy.network_monitor.proto.CertType} */ (reader.readEnum());
      msg.setCertType(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setReserved(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setCertContentLength(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setPlatformIp(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setCertName(value);
      break;
    case 7:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setCertContent(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_Cert.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_Cert.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_Cert} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_Cert.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getCertType();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
  f = message.getReserved();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getCertContentLength();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
  f = message.getPlatformIp();
  if (f !== 0) {
    writer.writeInt32(
      5,
      f
    );
  }
  f = message.getCertName();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getCertContent_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      7,
      f
    );
  }
};


/**
 * optional int32 id = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_Cert.prototype.getId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_Cert.prototype.setId = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional CertType cert_type = 2;
 * @return {!proto.com.jy.network_monitor.proto.CertType}
 */
proto.com.jy.network_monitor.proto.MSG_Cert.prototype.getCertType = function() {
  return /** @type {!proto.com.jy.network_monitor.proto.CertType} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/** @param {!proto.com.jy.network_monitor.proto.CertType} value */
proto.com.jy.network_monitor.proto.MSG_Cert.prototype.setCertType = function(value) {
  jspb.Message.setProto3EnumField(this, 2, value);
};


/**
 * optional int32 reserved = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_Cert.prototype.getReserved = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_Cert.prototype.setReserved = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional int32 cert_content_length = 4;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_Cert.prototype.getCertContentLength = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_Cert.prototype.setCertContentLength = function(value) {
  jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional int32 platform_ip = 5;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_Cert.prototype.getPlatformIp = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_Cert.prototype.setPlatformIp = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional string cert_name = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Cert.prototype.getCertName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Cert.prototype.setCertName = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional bytes cert_content = 7;
 * @return {!(string|Uint8Array)}
 */
proto.com.jy.network_monitor.proto.MSG_Cert.prototype.getCertContent = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * optional bytes cert_content = 7;
 * This is a type-conversion wrapper around `getCertContent()`
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Cert.prototype.getCertContent_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getCertContent()));
};


/**
 * optional bytes cert_content = 7;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getCertContent()`
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_Cert.prototype.getCertContent_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getCertContent()));
};


/** @param {!(string|Uint8Array)} value */
proto.com.jy.network_monitor.proto.MSG_Cert.prototype.setCertContent = function(value) {
  jspb.Message.setProto3BytesField(this, 7, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_Baseline = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_Baseline, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_Baseline.displayName = 'proto.com.jy.network_monitor.proto.MSG_Baseline';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_Baseline.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_Baseline.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_Baseline} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_Baseline.toObject = function(includeInstance, msg) {
  var f, obj = {
    taskId: jspb.Message.getFieldWithDefault(msg, 1, ""),
    result: jspb.Message.getFieldWithDefault(msg, 2, ""),
    startTime: jspb.Message.getFieldWithDefault(msg, 11, 0),
    stopTime: jspb.Message.getFieldWithDefault(msg, 12, 0),
    completeTime: jspb.Message.getFieldWithDefault(msg, 13, 0),
    error: jspb.Message.getFieldWithDefault(msg, 14, 0),
    destIp: jspb.Message.getFieldWithDefault(msg, 15, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_Baseline}
 */
proto.com.jy.network_monitor.proto.MSG_Baseline.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_Baseline;
  return proto.com.jy.network_monitor.proto.MSG_Baseline.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_Baseline} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_Baseline}
 */
proto.com.jy.network_monitor.proto.MSG_Baseline.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setTaskId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setResult(value);
      break;
    case 11:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setStartTime(value);
      break;
    case 12:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setStopTime(value);
      break;
    case 13:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setCompleteTime(value);
      break;
    case 14:
      var value = /** @type {!proto.com.jy.network_monitor.proto.ErrorCode} */ (reader.readEnum());
      msg.setError(value);
      break;
    case 15:
      var value = /** @type {string} */ (reader.readString());
      msg.setDestIp(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_Baseline.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_Baseline.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_Baseline} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_Baseline.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTaskId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getResult();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getStartTime();
  if (f !== 0) {
    writer.writeInt64(
      11,
      f
    );
  }
  f = message.getStopTime();
  if (f !== 0) {
    writer.writeInt64(
      12,
      f
    );
  }
  f = message.getCompleteTime();
  if (f !== 0) {
    writer.writeInt64(
      13,
      f
    );
  }
  f = message.getError();
  if (f !== 0.0) {
    writer.writeEnum(
      14,
      f
    );
  }
  f = message.getDestIp();
  if (f.length > 0) {
    writer.writeString(
      15,
      f
    );
  }
};


/**
 * optional string task_id = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Baseline.prototype.getTaskId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Baseline.prototype.setTaskId = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string result = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Baseline.prototype.getResult = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Baseline.prototype.setResult = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int64 start_time = 11;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_Baseline.prototype.getStartTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 11, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_Baseline.prototype.setStartTime = function(value) {
  jspb.Message.setProto3IntField(this, 11, value);
};


/**
 * optional int64 stop_time = 12;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_Baseline.prototype.getStopTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 12, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_Baseline.prototype.setStopTime = function(value) {
  jspb.Message.setProto3IntField(this, 12, value);
};


/**
 * optional int64 complete_time = 13;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_Baseline.prototype.getCompleteTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 13, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_Baseline.prototype.setCompleteTime = function(value) {
  jspb.Message.setProto3IntField(this, 13, value);
};


/**
 * optional ErrorCode error = 14;
 * @return {!proto.com.jy.network_monitor.proto.ErrorCode}
 */
proto.com.jy.network_monitor.proto.MSG_Baseline.prototype.getError = function() {
  return /** @type {!proto.com.jy.network_monitor.proto.ErrorCode} */ (jspb.Message.getFieldWithDefault(this, 14, 0));
};


/** @param {!proto.com.jy.network_monitor.proto.ErrorCode} value */
proto.com.jy.network_monitor.proto.MSG_Baseline.prototype.setError = function(value) {
  jspb.Message.setProto3EnumField(this, 14, value);
};


/**
 * optional string dest_ip = 15;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Baseline.prototype.getDestIp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 15, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Baseline.prototype.setDestIp = function(value) {
  jspb.Message.setProto3StringField(this, 15, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_Control = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_Control, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_Control.displayName = 'proto.com.jy.network_monitor.proto.MSG_Control';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_Control.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_Control.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_Control} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_Control.toObject = function(includeInstance, msg) {
  var f, obj = {
    taskId: jspb.Message.getFieldWithDefault(msg, 1, ""),
    result: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_Control}
 */
proto.com.jy.network_monitor.proto.MSG_Control.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_Control;
  return proto.com.jy.network_monitor.proto.MSG_Control.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_Control} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_Control}
 */
proto.com.jy.network_monitor.proto.MSG_Control.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setTaskId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setResult(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_Control.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_Control.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_Control} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_Control.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTaskId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getResult();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string task_id = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Control.prototype.getTaskId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Control.prototype.setTaskId = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string result = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Control.prototype.getResult = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Control.prototype.setResult = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_MonitoredObject = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_MonitoredObject, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_MonitoredObject.displayName = 'proto.com.jy.network_monitor.proto.MSG_MonitoredObject';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_MonitoredObject.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_MonitoredObject.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_MonitoredObject} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_MonitoredObject.toObject = function(includeInstance, msg) {
  var f, obj = {
    taskId: jspb.Message.getFieldWithDefault(msg, 1, ""),
    result: jspb.Message.getFieldWithDefault(msg, 2, ""),
    content: jspb.Message.getFieldWithDefault(msg, 3, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_MonitoredObject}
 */
proto.com.jy.network_monitor.proto.MSG_MonitoredObject.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_MonitoredObject;
  return proto.com.jy.network_monitor.proto.MSG_MonitoredObject.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_MonitoredObject} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_MonitoredObject}
 */
proto.com.jy.network_monitor.proto.MSG_MonitoredObject.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setTaskId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setResult(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setContent(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_MonitoredObject.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_MonitoredObject.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_MonitoredObject} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_MonitoredObject.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTaskId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getResult();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getContent();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
};


/**
 * optional string task_id = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_MonitoredObject.prototype.getTaskId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_MonitoredObject.prototype.setTaskId = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string result = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_MonitoredObject.prototype.getResult = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_MonitoredObject.prototype.setResult = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string content = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_MonitoredObject.prototype.getContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_MonitoredObject.prototype.setContent = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_LeakScann = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_LeakScann, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_LeakScann.displayName = 'proto.com.jy.network_monitor.proto.MSG_LeakScann';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_LeakScann.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_LeakScann.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_LeakScann} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_LeakScann.toObject = function(includeInstance, msg) {
  var f, obj = {
    taskId: jspb.Message.getFieldWithDefault(msg, 1, ""),
    result: jspb.Message.getFieldWithDefault(msg, 2, ""),
    startTime: jspb.Message.getFieldWithDefault(msg, 11, 0),
    stopTime: jspb.Message.getFieldWithDefault(msg, 12, 0),
    completeTime: jspb.Message.getFieldWithDefault(msg, 13, 0),
    error: jspb.Message.getFieldWithDefault(msg, 14, 0),
    destIp: jspb.Message.getFieldWithDefault(msg, 15, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_LeakScann}
 */
proto.com.jy.network_monitor.proto.MSG_LeakScann.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_LeakScann;
  return proto.com.jy.network_monitor.proto.MSG_LeakScann.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_LeakScann} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_LeakScann}
 */
proto.com.jy.network_monitor.proto.MSG_LeakScann.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setTaskId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setResult(value);
      break;
    case 11:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setStartTime(value);
      break;
    case 12:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setStopTime(value);
      break;
    case 13:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setCompleteTime(value);
      break;
    case 14:
      var value = /** @type {!proto.com.jy.network_monitor.proto.ErrorCode} */ (reader.readEnum());
      msg.setError(value);
      break;
    case 15:
      var value = /** @type {string} */ (reader.readString());
      msg.setDestIp(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_LeakScann.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_LeakScann.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_LeakScann} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_LeakScann.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTaskId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getResult();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getStartTime();
  if (f !== 0) {
    writer.writeInt64(
      11,
      f
    );
  }
  f = message.getStopTime();
  if (f !== 0) {
    writer.writeInt64(
      12,
      f
    );
  }
  f = message.getCompleteTime();
  if (f !== 0) {
    writer.writeInt64(
      13,
      f
    );
  }
  f = message.getError();
  if (f !== 0.0) {
    writer.writeEnum(
      14,
      f
    );
  }
  f = message.getDestIp();
  if (f.length > 0) {
    writer.writeString(
      15,
      f
    );
  }
};


/**
 * optional string task_id = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_LeakScann.prototype.getTaskId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_LeakScann.prototype.setTaskId = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string result = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_LeakScann.prototype.getResult = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_LeakScann.prototype.setResult = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int64 start_time = 11;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_LeakScann.prototype.getStartTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 11, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_LeakScann.prototype.setStartTime = function(value) {
  jspb.Message.setProto3IntField(this, 11, value);
};


/**
 * optional int64 stop_time = 12;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_LeakScann.prototype.getStopTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 12, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_LeakScann.prototype.setStopTime = function(value) {
  jspb.Message.setProto3IntField(this, 12, value);
};


/**
 * optional int64 complete_time = 13;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_LeakScann.prototype.getCompleteTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 13, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_LeakScann.prototype.setCompleteTime = function(value) {
  jspb.Message.setProto3IntField(this, 13, value);
};


/**
 * optional ErrorCode error = 14;
 * @return {!proto.com.jy.network_monitor.proto.ErrorCode}
 */
proto.com.jy.network_monitor.proto.MSG_LeakScann.prototype.getError = function() {
  return /** @type {!proto.com.jy.network_monitor.proto.ErrorCode} */ (jspb.Message.getFieldWithDefault(this, 14, 0));
};


/** @param {!proto.com.jy.network_monitor.proto.ErrorCode} value */
proto.com.jy.network_monitor.proto.MSG_LeakScann.prototype.setError = function(value) {
  jspb.Message.setProto3EnumField(this, 14, value);
};


/**
 * optional string dest_ip = 15;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_LeakScann.prototype.getDestIp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 15, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_LeakScann.prototype.setDestIp = function(value) {
  jspb.Message.setProto3StringField(this, 15, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_VersionVerify = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_VersionVerify, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_VersionVerify.displayName = 'proto.com.jy.network_monitor.proto.MSG_VersionVerify';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_VersionVerify.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_VersionVerify.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_VersionVerify} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_VersionVerify.toObject = function(includeInstance, msg) {
  var f, obj = {
    taskId: jspb.Message.getFieldWithDefault(msg, 1, ""),
    result: jspb.Message.getFieldWithDefault(msg, 2, ""),
    startTime: jspb.Message.getFieldWithDefault(msg, 11, 0),
    stopTime: jspb.Message.getFieldWithDefault(msg, 12, 0),
    completeTime: jspb.Message.getFieldWithDefault(msg, 13, 0),
    error: jspb.Message.getFieldWithDefault(msg, 14, 0),
    destIp: jspb.Message.getFieldWithDefault(msg, 15, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_VersionVerify}
 */
proto.com.jy.network_monitor.proto.MSG_VersionVerify.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_VersionVerify;
  return proto.com.jy.network_monitor.proto.MSG_VersionVerify.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_VersionVerify} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_VersionVerify}
 */
proto.com.jy.network_monitor.proto.MSG_VersionVerify.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setTaskId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setResult(value);
      break;
    case 11:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setStartTime(value);
      break;
    case 12:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setStopTime(value);
      break;
    case 13:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setCompleteTime(value);
      break;
    case 14:
      var value = /** @type {!proto.com.jy.network_monitor.proto.ErrorCode} */ (reader.readEnum());
      msg.setError(value);
      break;
    case 15:
      var value = /** @type {string} */ (reader.readString());
      msg.setDestIp(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_VersionVerify.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_VersionVerify.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_VersionVerify} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_VersionVerify.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTaskId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getResult();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getStartTime();
  if (f !== 0) {
    writer.writeInt64(
      11,
      f
    );
  }
  f = message.getStopTime();
  if (f !== 0) {
    writer.writeInt64(
      12,
      f
    );
  }
  f = message.getCompleteTime();
  if (f !== 0) {
    writer.writeInt64(
      13,
      f
    );
  }
  f = message.getError();
  if (f !== 0.0) {
    writer.writeEnum(
      14,
      f
    );
  }
  f = message.getDestIp();
  if (f.length > 0) {
    writer.writeString(
      15,
      f
    );
  }
};


/**
 * optional string task_id = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_VersionVerify.prototype.getTaskId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_VersionVerify.prototype.setTaskId = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string result = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_VersionVerify.prototype.getResult = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_VersionVerify.prototype.setResult = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int64 start_time = 11;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_VersionVerify.prototype.getStartTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 11, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_VersionVerify.prototype.setStartTime = function(value) {
  jspb.Message.setProto3IntField(this, 11, value);
};


/**
 * optional int64 stop_time = 12;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_VersionVerify.prototype.getStopTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 12, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_VersionVerify.prototype.setStopTime = function(value) {
  jspb.Message.setProto3IntField(this, 12, value);
};


/**
 * optional int64 complete_time = 13;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_VersionVerify.prototype.getCompleteTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 13, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_VersionVerify.prototype.setCompleteTime = function(value) {
  jspb.Message.setProto3IntField(this, 13, value);
};


/**
 * optional ErrorCode error = 14;
 * @return {!proto.com.jy.network_monitor.proto.ErrorCode}
 */
proto.com.jy.network_monitor.proto.MSG_VersionVerify.prototype.getError = function() {
  return /** @type {!proto.com.jy.network_monitor.proto.ErrorCode} */ (jspb.Message.getFieldWithDefault(this, 14, 0));
};


/** @param {!proto.com.jy.network_monitor.proto.ErrorCode} value */
proto.com.jy.network_monitor.proto.MSG_VersionVerify.prototype.setError = function(value) {
  jspb.Message.setProto3EnumField(this, 14, value);
};


/**
 * optional string dest_ip = 15;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_VersionVerify.prototype.getDestIp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 15, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_VersionVerify.prototype.setDestIp = function(value) {
  jspb.Message.setProto3StringField(this, 15, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_CharacterUpdate = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_CharacterUpdate, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_CharacterUpdate.displayName = 'proto.com.jy.network_monitor.proto.MSG_CharacterUpdate';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_CharacterUpdate.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_CharacterUpdate.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_CharacterUpdate} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_CharacterUpdate.toObject = function(includeInstance, msg) {
  var f, obj = {
    taskId: jspb.Message.getFieldWithDefault(msg, 1, ""),
    result: jspb.Message.getFieldWithDefault(msg, 2, ""),
    startTime: jspb.Message.getFieldWithDefault(msg, 11, 0),
    stopTime: jspb.Message.getFieldWithDefault(msg, 12, 0),
    completeTime: jspb.Message.getFieldWithDefault(msg, 13, 0),
    error: jspb.Message.getFieldWithDefault(msg, 14, 0),
    destIp: jspb.Message.getFieldWithDefault(msg, 15, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_CharacterUpdate}
 */
proto.com.jy.network_monitor.proto.MSG_CharacterUpdate.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_CharacterUpdate;
  return proto.com.jy.network_monitor.proto.MSG_CharacterUpdate.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_CharacterUpdate} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_CharacterUpdate}
 */
proto.com.jy.network_monitor.proto.MSG_CharacterUpdate.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setTaskId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setResult(value);
      break;
    case 11:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setStartTime(value);
      break;
    case 12:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setStopTime(value);
      break;
    case 13:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setCompleteTime(value);
      break;
    case 14:
      var value = /** @type {!proto.com.jy.network_monitor.proto.ErrorCode} */ (reader.readEnum());
      msg.setError(value);
      break;
    case 15:
      var value = /** @type {string} */ (reader.readString());
      msg.setDestIp(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_CharacterUpdate.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_CharacterUpdate.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_CharacterUpdate} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_CharacterUpdate.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTaskId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getResult();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getStartTime();
  if (f !== 0) {
    writer.writeInt64(
      11,
      f
    );
  }
  f = message.getStopTime();
  if (f !== 0) {
    writer.writeInt64(
      12,
      f
    );
  }
  f = message.getCompleteTime();
  if (f !== 0) {
    writer.writeInt64(
      13,
      f
    );
  }
  f = message.getError();
  if (f !== 0.0) {
    writer.writeEnum(
      14,
      f
    );
  }
  f = message.getDestIp();
  if (f.length > 0) {
    writer.writeString(
      15,
      f
    );
  }
};


/**
 * optional string task_id = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_CharacterUpdate.prototype.getTaskId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_CharacterUpdate.prototype.setTaskId = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string result = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_CharacterUpdate.prototype.getResult = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_CharacterUpdate.prototype.setResult = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int64 start_time = 11;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_CharacterUpdate.prototype.getStartTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 11, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_CharacterUpdate.prototype.setStartTime = function(value) {
  jspb.Message.setProto3IntField(this, 11, value);
};


/**
 * optional int64 stop_time = 12;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_CharacterUpdate.prototype.getStopTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 12, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_CharacterUpdate.prototype.setStopTime = function(value) {
  jspb.Message.setProto3IntField(this, 12, value);
};


/**
 * optional int64 complete_time = 13;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_CharacterUpdate.prototype.getCompleteTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 13, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_CharacterUpdate.prototype.setCompleteTime = function(value) {
  jspb.Message.setProto3IntField(this, 13, value);
};


/**
 * optional ErrorCode error = 14;
 * @return {!proto.com.jy.network_monitor.proto.ErrorCode}
 */
proto.com.jy.network_monitor.proto.MSG_CharacterUpdate.prototype.getError = function() {
  return /** @type {!proto.com.jy.network_monitor.proto.ErrorCode} */ (jspb.Message.getFieldWithDefault(this, 14, 0));
};


/** @param {!proto.com.jy.network_monitor.proto.ErrorCode} value */
proto.com.jy.network_monitor.proto.MSG_CharacterUpdate.prototype.setError = function(value) {
  jspb.Message.setProto3EnumField(this, 14, value);
};


/**
 * optional string dest_ip = 15;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_CharacterUpdate.prototype.getDestIp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 15, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_CharacterUpdate.prototype.setDestIp = function(value) {
  jspb.Message.setProto3StringField(this, 15, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_AppUserAuthority = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.com.jy.network_monitor.proto.MSG_AppUserAuthority.repeatedFields_, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_AppUserAuthority, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_AppUserAuthority.displayName = 'proto.com.jy.network_monitor.proto.MSG_AppUserAuthority';
}
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.com.jy.network_monitor.proto.MSG_AppUserAuthority.repeatedFields_ = [3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_AppUserAuthority.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_AppUserAuthority.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_AppUserAuthority} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_AppUserAuthority.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, 0),
    authorityName: jspb.Message.getFieldWithDefault(msg, 2, ""),
    urlsList: jspb.Message.getRepeatedField(msg, 3)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_AppUserAuthority}
 */
proto.com.jy.network_monitor.proto.MSG_AppUserAuthority.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_AppUserAuthority;
  return proto.com.jy.network_monitor.proto.MSG_AppUserAuthority.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_AppUserAuthority} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_AppUserAuthority}
 */
proto.com.jy.network_monitor.proto.MSG_AppUserAuthority.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setAuthorityName(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.addUrls(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_AppUserAuthority.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_AppUserAuthority.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_AppUserAuthority} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_AppUserAuthority.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getAuthorityName();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getUrlsList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      3,
      f
    );
  }
};


/**
 * optional int32 id = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_AppUserAuthority.prototype.getId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_AppUserAuthority.prototype.setId = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string authority_name = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_AppUserAuthority.prototype.getAuthorityName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_AppUserAuthority.prototype.setAuthorityName = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * repeated string urls = 3;
 * @return {!Array<string>}
 */
proto.com.jy.network_monitor.proto.MSG_AppUserAuthority.prototype.getUrlsList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 3));
};


/** @param {!Array<string>} value */
proto.com.jy.network_monitor.proto.MSG_AppUserAuthority.prototype.setUrlsList = function(value) {
  jspb.Message.setField(this, 3, value || []);
};


/**
 * @param {!string} value
 * @param {number=} opt_index
 */
proto.com.jy.network_monitor.proto.MSG_AppUserAuthority.prototype.addUrls = function(value, opt_index) {
  jspb.Message.addToRepeatedField(this, 3, value, opt_index);
};


proto.com.jy.network_monitor.proto.MSG_AppUserAuthority.prototype.clearUrlsList = function() {
  this.setUrlsList([]);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_AppUser = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.com.jy.network_monitor.proto.MSG_AppUser.repeatedFields_, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_AppUser, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_AppUser.displayName = 'proto.com.jy.network_monitor.proto.MSG_AppUser';
}
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.com.jy.network_monitor.proto.MSG_AppUser.repeatedFields_ = [9];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_AppUser.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_AppUser.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_AppUser} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_AppUser.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, 0),
    appGroupId: jspb.Message.getFieldWithDefault(msg, 2, 0),
    appGroupName: jspb.Message.getFieldWithDefault(msg, 3, ""),
    appUserName: jspb.Message.getFieldWithDefault(msg, 4, ""),
    password: jspb.Message.getFieldWithDefault(msg, 5, ""),
    loginIp: jspb.Message.getFieldWithDefault(msg, 6, ""),
    desc: jspb.Message.getFieldWithDefault(msg, 7, ""),
    createTime: jspb.Message.getFieldWithDefault(msg, 8, 0),
    authoritiesList: jspb.Message.toObjectList(msg.getAuthoritiesList(),
    proto.com.jy.network_monitor.proto.MSG_AppUserAuthority.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_AppUser}
 */
proto.com.jy.network_monitor.proto.MSG_AppUser.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_AppUser;
  return proto.com.jy.network_monitor.proto.MSG_AppUser.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_AppUser} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_AppUser}
 */
proto.com.jy.network_monitor.proto.MSG_AppUser.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setAppGroupId(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setAppGroupName(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setAppUserName(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setPassword(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setLoginIp(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setDesc(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setCreateTime(value);
      break;
    case 9:
      var value = new proto.com.jy.network_monitor.proto.MSG_AppUserAuthority;
      reader.readMessage(value,proto.com.jy.network_monitor.proto.MSG_AppUserAuthority.deserializeBinaryFromReader);
      msg.addAuthorities(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_AppUser.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_AppUser.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_AppUser} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_AppUser.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getAppGroupId();
  if (f !== 0) {
    writer.writeInt32(
      2,
      f
    );
  }
  f = message.getAppGroupName();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getAppUserName();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getPassword();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getLoginIp();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getDesc();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getCreateTime();
  if (f !== 0) {
    writer.writeInt64(
      8,
      f
    );
  }
  f = message.getAuthoritiesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      9,
      f,
      proto.com.jy.network_monitor.proto.MSG_AppUserAuthority.serializeBinaryToWriter
    );
  }
};


/**
 * optional int32 id = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_AppUser.prototype.getId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_AppUser.prototype.setId = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional int32 app_group_id = 2;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_AppUser.prototype.getAppGroupId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_AppUser.prototype.setAppGroupId = function(value) {
  jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional string app_group_name = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_AppUser.prototype.getAppGroupName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_AppUser.prototype.setAppGroupName = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string app_user_name = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_AppUser.prototype.getAppUserName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_AppUser.prototype.setAppUserName = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string password = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_AppUser.prototype.getPassword = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_AppUser.prototype.setPassword = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string login_ip = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_AppUser.prototype.getLoginIp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_AppUser.prototype.setLoginIp = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string desc = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_AppUser.prototype.getDesc = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_AppUser.prototype.setDesc = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional int64 create_time = 8;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_AppUser.prototype.getCreateTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_AppUser.prototype.setCreateTime = function(value) {
  jspb.Message.setProto3IntField(this, 8, value);
};


/**
 * repeated MSG_AppUserAuthority authorities = 9;
 * @return {!Array<!proto.com.jy.network_monitor.proto.MSG_AppUserAuthority>}
 */
proto.com.jy.network_monitor.proto.MSG_AppUser.prototype.getAuthoritiesList = function() {
  return /** @type{!Array<!proto.com.jy.network_monitor.proto.MSG_AppUserAuthority>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.com.jy.network_monitor.proto.MSG_AppUserAuthority, 9));
};


/** @param {!Array<!proto.com.jy.network_monitor.proto.MSG_AppUserAuthority>} value */
proto.com.jy.network_monitor.proto.MSG_AppUser.prototype.setAuthoritiesList = function(value) {
  jspb.Message.setRepeatedWrapperField(this, 9, value);
};


/**
 * @param {!proto.com.jy.network_monitor.proto.MSG_AppUserAuthority=} opt_value
 * @param {number=} opt_index
 * @return {!proto.com.jy.network_monitor.proto.MSG_AppUserAuthority}
 */
proto.com.jy.network_monitor.proto.MSG_AppUser.prototype.addAuthorities = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 9, opt_value, proto.com.jy.network_monitor.proto.MSG_AppUserAuthority, opt_index);
};


proto.com.jy.network_monitor.proto.MSG_AppUser.prototype.clearAuthoritiesList = function() {
  this.setAuthoritiesList([]);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_UpgradeData = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.com.jy.network_monitor.proto.MSG_UpgradeData.repeatedFields_, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_UpgradeData, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_UpgradeData.displayName = 'proto.com.jy.network_monitor.proto.MSG_UpgradeData';
}
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.com.jy.network_monitor.proto.MSG_UpgradeData.repeatedFields_ = [8];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_UpgradeData.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_UpgradeData.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_UpgradeData} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_UpgradeData.toObject = function(includeInstance, msg) {
  var f, obj = {
    upgradeType: jspb.Message.getFieldWithDefault(msg, 1, 0),
    content: msg.getContent_asB64(),
    fullPath: jspb.Message.getFieldWithDefault(msg, 3, ""),
    beforeCommand: jspb.Message.getFieldWithDefault(msg, 4, ""),
    afterCommand: jspb.Message.getFieldWithDefault(msg, 5, ""),
    encrypt: jspb.Message.getFieldWithDefault(msg, 6, 0),
    isCharge: jspb.Message.getFieldWithDefault(msg, 7, false),
    afterCommandsList: jspb.Message.getRepeatedField(msg, 8)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_UpgradeData}
 */
proto.com.jy.network_monitor.proto.MSG_UpgradeData.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_UpgradeData;
  return proto.com.jy.network_monitor.proto.MSG_UpgradeData.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_UpgradeData} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_UpgradeData}
 */
proto.com.jy.network_monitor.proto.MSG_UpgradeData.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.com.jy.network_monitor.proto.UpgradeType} */ (reader.readEnum());
      msg.setUpgradeType(value);
      break;
    case 2:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setContent(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setFullPath(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setBeforeCommand(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setAfterCommand(value);
      break;
    case 6:
      var value = /** @type {!proto.com.jy.network_monitor.proto.EncryptType} */ (reader.readEnum());
      msg.setEncrypt(value);
      break;
    case 7:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIsCharge(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.addAfterCommands(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_UpgradeData.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_UpgradeData.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_UpgradeData} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_UpgradeData.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getUpgradeType();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getContent_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      2,
      f
    );
  }
  f = message.getFullPath();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getBeforeCommand();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getAfterCommand();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getEncrypt();
  if (f !== 0.0) {
    writer.writeEnum(
      6,
      f
    );
  }
  f = message.getIsCharge();
  if (f) {
    writer.writeBool(
      7,
      f
    );
  }
  f = message.getAfterCommandsList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      8,
      f
    );
  }
};


/**
 * optional UpgradeType upgrade_type = 1;
 * @return {!proto.com.jy.network_monitor.proto.UpgradeType}
 */
proto.com.jy.network_monitor.proto.MSG_UpgradeData.prototype.getUpgradeType = function() {
  return /** @type {!proto.com.jy.network_monitor.proto.UpgradeType} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {!proto.com.jy.network_monitor.proto.UpgradeType} value */
proto.com.jy.network_monitor.proto.MSG_UpgradeData.prototype.setUpgradeType = function(value) {
  jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional bytes content = 2;
 * @return {!(string|Uint8Array)}
 */
proto.com.jy.network_monitor.proto.MSG_UpgradeData.prototype.getContent = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * optional bytes content = 2;
 * This is a type-conversion wrapper around `getContent()`
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_UpgradeData.prototype.getContent_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getContent()));
};


/**
 * optional bytes content = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getContent()`
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_UpgradeData.prototype.getContent_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getContent()));
};


/** @param {!(string|Uint8Array)} value */
proto.com.jy.network_monitor.proto.MSG_UpgradeData.prototype.setContent = function(value) {
  jspb.Message.setProto3BytesField(this, 2, value);
};


/**
 * optional string full_path = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_UpgradeData.prototype.getFullPath = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_UpgradeData.prototype.setFullPath = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string before_command = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_UpgradeData.prototype.getBeforeCommand = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_UpgradeData.prototype.setBeforeCommand = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string after_command = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_UpgradeData.prototype.getAfterCommand = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_UpgradeData.prototype.setAfterCommand = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional EncryptType encrypt = 6;
 * @return {!proto.com.jy.network_monitor.proto.EncryptType}
 */
proto.com.jy.network_monitor.proto.MSG_UpgradeData.prototype.getEncrypt = function() {
  return /** @type {!proto.com.jy.network_monitor.proto.EncryptType} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {!proto.com.jy.network_monitor.proto.EncryptType} value */
proto.com.jy.network_monitor.proto.MSG_UpgradeData.prototype.setEncrypt = function(value) {
  jspb.Message.setProto3EnumField(this, 6, value);
};


/**
 * optional bool is_charge = 7;
 * Note that Boolean fields may be set to 0/1 when serialized from a Java server.
 * You should avoid comparisons like {@code val === true/false} in those cases.
 * @return {boolean}
 */
proto.com.jy.network_monitor.proto.MSG_UpgradeData.prototype.getIsCharge = function() {
  return /** @type {boolean} */ (jspb.Message.getFieldWithDefault(this, 7, false));
};


/** @param {boolean} value */
proto.com.jy.network_monitor.proto.MSG_UpgradeData.prototype.setIsCharge = function(value) {
  jspb.Message.setProto3BooleanField(this, 7, value);
};


/**
 * repeated string after_commands = 8;
 * @return {!Array<string>}
 */
proto.com.jy.network_monitor.proto.MSG_UpgradeData.prototype.getAfterCommandsList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 8));
};


/** @param {!Array<string>} value */
proto.com.jy.network_monitor.proto.MSG_UpgradeData.prototype.setAfterCommandsList = function(value) {
  jspb.Message.setField(this, 8, value || []);
};


/**
 * @param {!string} value
 * @param {number=} opt_index
 */
proto.com.jy.network_monitor.proto.MSG_UpgradeData.prototype.addAfterCommands = function(value, opt_index) {
  jspb.Message.addToRepeatedField(this, 8, value, opt_index);
};


proto.com.jy.network_monitor.proto.MSG_UpgradeData.prototype.clearAfterCommandsList = function() {
  this.setAfterCommandsList([]);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_RunLog = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_RunLog, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_RunLog.displayName = 'proto.com.jy.network_monitor.proto.MSG_RunLog';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_RunLog.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_RunLog.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_RunLog} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_RunLog.toObject = function(includeInstance, msg) {
  var f, obj = {
    source: jspb.Message.getFieldWithDefault(msg, 1, 0),
    level: jspb.Message.getFieldWithDefault(msg, 2, 0),
    time: jspb.Message.getFieldWithDefault(msg, 3, 0),
    type: jspb.Message.getFieldWithDefault(msg, 4, 0),
    module: jspb.Message.getFieldWithDefault(msg, 5, 0),
    success: jspb.Message.getFieldWithDefault(msg, 6, false),
    content: jspb.Message.getFieldWithDefault(msg, 7, ""),
    operatorName: jspb.Message.getFieldWithDefault(msg, 8, ""),
    operatorIp: jspb.Message.getFieldWithDefault(msg, 9, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_RunLog}
 */
proto.com.jy.network_monitor.proto.MSG_RunLog.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_RunLog;
  return proto.com.jy.network_monitor.proto.MSG_RunLog.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_RunLog} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_RunLog}
 */
proto.com.jy.network_monitor.proto.MSG_RunLog.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.com.jy.network_monitor.proto.LogSource} */ (reader.readEnum());
      msg.setSource(value);
      break;
    case 2:
      var value = /** @type {!proto.com.jy.network_monitor.proto.LogLevel} */ (reader.readEnum());
      msg.setLevel(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setTime(value);
      break;
    case 4:
      var value = /** @type {!proto.com.jy.network_monitor.proto.LogType} */ (reader.readEnum());
      msg.setType(value);
      break;
    case 5:
      var value = /** @type {!proto.com.jy.network_monitor.proto.LogModule} */ (reader.readEnum());
      msg.setModule(value);
      break;
    case 6:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setSuccess(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setContent(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setOperatorName(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setOperatorIp(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_RunLog.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_RunLog.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_RunLog} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_RunLog.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSource();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getLevel();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
  f = message.getTime();
  if (f !== 0) {
    writer.writeInt64(
      3,
      f
    );
  }
  f = message.getType();
  if (f !== 0.0) {
    writer.writeEnum(
      4,
      f
    );
  }
  f = message.getModule();
  if (f !== 0.0) {
    writer.writeEnum(
      5,
      f
    );
  }
  f = message.getSuccess();
  if (f) {
    writer.writeBool(
      6,
      f
    );
  }
  f = message.getContent();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getOperatorName();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getOperatorIp();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
};


/**
 * optional LogSource source = 1;
 * @return {!proto.com.jy.network_monitor.proto.LogSource}
 */
proto.com.jy.network_monitor.proto.MSG_RunLog.prototype.getSource = function() {
  return /** @type {!proto.com.jy.network_monitor.proto.LogSource} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {!proto.com.jy.network_monitor.proto.LogSource} value */
proto.com.jy.network_monitor.proto.MSG_RunLog.prototype.setSource = function(value) {
  jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional LogLevel level = 2;
 * @return {!proto.com.jy.network_monitor.proto.LogLevel}
 */
proto.com.jy.network_monitor.proto.MSG_RunLog.prototype.getLevel = function() {
  return /** @type {!proto.com.jy.network_monitor.proto.LogLevel} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/** @param {!proto.com.jy.network_monitor.proto.LogLevel} value */
proto.com.jy.network_monitor.proto.MSG_RunLog.prototype.setLevel = function(value) {
  jspb.Message.setProto3EnumField(this, 2, value);
};


/**
 * optional int64 time = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_RunLog.prototype.getTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_RunLog.prototype.setTime = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional LogType type = 4;
 * @return {!proto.com.jy.network_monitor.proto.LogType}
 */
proto.com.jy.network_monitor.proto.MSG_RunLog.prototype.getType = function() {
  return /** @type {!proto.com.jy.network_monitor.proto.LogType} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {!proto.com.jy.network_monitor.proto.LogType} value */
proto.com.jy.network_monitor.proto.MSG_RunLog.prototype.setType = function(value) {
  jspb.Message.setProto3EnumField(this, 4, value);
};


/**
 * optional LogModule module = 5;
 * @return {!proto.com.jy.network_monitor.proto.LogModule}
 */
proto.com.jy.network_monitor.proto.MSG_RunLog.prototype.getModule = function() {
  return /** @type {!proto.com.jy.network_monitor.proto.LogModule} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {!proto.com.jy.network_monitor.proto.LogModule} value */
proto.com.jy.network_monitor.proto.MSG_RunLog.prototype.setModule = function(value) {
  jspb.Message.setProto3EnumField(this, 5, value);
};


/**
 * optional bool success = 6;
 * Note that Boolean fields may be set to 0/1 when serialized from a Java server.
 * You should avoid comparisons like {@code val === true/false} in those cases.
 * @return {boolean}
 */
proto.com.jy.network_monitor.proto.MSG_RunLog.prototype.getSuccess = function() {
  return /** @type {boolean} */ (jspb.Message.getFieldWithDefault(this, 6, false));
};


/** @param {boolean} value */
proto.com.jy.network_monitor.proto.MSG_RunLog.prototype.setSuccess = function(value) {
  jspb.Message.setProto3BooleanField(this, 6, value);
};


/**
 * optional string content = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_RunLog.prototype.getContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_RunLog.prototype.setContent = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string operator_name = 8;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_RunLog.prototype.getOperatorName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_RunLog.prototype.setOperatorName = function(value) {
  jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional string operator_ip = 9;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_RunLog.prototype.getOperatorIp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_RunLog.prototype.setOperatorIp = function(value) {
  jspb.Message.setProto3StringField(this, 9, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog.displayName = 'proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog.toObject = function(includeInstance, msg) {
  var f, obj = {
    type: jspb.Message.getFieldWithDefault(msg, 1, 0),
    time: jspb.Message.getFieldWithDefault(msg, 2, 0),
    userId: jspb.Message.getFieldWithDefault(msg, 3, ""),
    content: jspb.Message.getFieldWithDefault(msg, 4, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog}
 */
proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog;
  return proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog}
 */
proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.com.jy.network_monitor.proto.ExceptionLogType} */ (reader.readEnum());
      msg.setType(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setTime(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setUserId(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setContent(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getType();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getTime();
  if (f !== 0) {
    writer.writeInt64(
      2,
      f
    );
  }
  f = message.getUserId();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getContent();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
};


/**
 * optional ExceptionLogType type = 1;
 * @return {!proto.com.jy.network_monitor.proto.ExceptionLogType}
 */
proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog.prototype.getType = function() {
  return /** @type {!proto.com.jy.network_monitor.proto.ExceptionLogType} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {!proto.com.jy.network_monitor.proto.ExceptionLogType} value */
proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog.prototype.setType = function(value) {
  jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional int64 time = 2;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog.prototype.getTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog.prototype.setTime = function(value) {
  jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional string user_id = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog.prototype.getUserId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog.prototype.setUserId = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string content = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog.prototype.getContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog.prototype.setContent = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_RunLogReport = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.com.jy.network_monitor.proto.MSG_RunLogReport.repeatedFields_, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_RunLogReport, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_RunLogReport.displayName = 'proto.com.jy.network_monitor.proto.MSG_RunLogReport';
}
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.com.jy.network_monitor.proto.MSG_RunLogReport.repeatedFields_ = [4];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_RunLogReport.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_RunLogReport.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_RunLogReport} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_RunLogReport.toObject = function(includeInstance, msg) {
  var f, obj = {
    exceptionCountMap: (f = msg.getExceptionCountMap()) ? f.toObject(includeInstance, undefined) : [],
    typeCountMap: (f = msg.getTypeCountMap()) ? f.toObject(includeInstance, undefined) : [],
    totalCount: jspb.Message.getFieldWithDefault(msg, 3, 0),
    logsList: jspb.Message.toObjectList(msg.getLogsList(),
    proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog.toObject, includeInstance),
    sourceCountMap: (f = msg.getSourceCountMap()) ? f.toObject(includeInstance, undefined) : [],
    levelCountMap: (f = msg.getLevelCountMap()) ? f.toObject(includeInstance, undefined) : [],
    moduleCountMap: (f = msg.getModuleCountMap()) ? f.toObject(includeInstance, undefined) : []
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_RunLogReport}
 */
proto.com.jy.network_monitor.proto.MSG_RunLogReport.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_RunLogReport;
  return proto.com.jy.network_monitor.proto.MSG_RunLogReport.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_RunLogReport} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_RunLogReport}
 */
proto.com.jy.network_monitor.proto.MSG_RunLogReport.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = msg.getExceptionCountMap();
      reader.readMessage(value, function(message, reader) {
        jspb.Map.deserializeBinary(message, reader, jspb.BinaryReader.prototype.readInt32, jspb.BinaryReader.prototype.readInt32);
         });
      break;
    case 2:
      var value = msg.getTypeCountMap();
      reader.readMessage(value, function(message, reader) {
        jspb.Map.deserializeBinary(message, reader, jspb.BinaryReader.prototype.readInt32, jspb.BinaryReader.prototype.readInt32);
         });
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setTotalCount(value);
      break;
    case 4:
      var value = new proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog;
      reader.readMessage(value,proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog.deserializeBinaryFromReader);
      msg.addLogs(value);
      break;
    case 5:
      var value = msg.getSourceCountMap();
      reader.readMessage(value, function(message, reader) {
        jspb.Map.deserializeBinary(message, reader, jspb.BinaryReader.prototype.readInt32, jspb.BinaryReader.prototype.readInt32);
         });
      break;
    case 6:
      var value = msg.getLevelCountMap();
      reader.readMessage(value, function(message, reader) {
        jspb.Map.deserializeBinary(message, reader, jspb.BinaryReader.prototype.readInt32, jspb.BinaryReader.prototype.readInt32);
         });
      break;
    case 7:
      var value = msg.getModuleCountMap();
      reader.readMessage(value, function(message, reader) {
        jspb.Map.deserializeBinary(message, reader, jspb.BinaryReader.prototype.readInt32, jspb.BinaryReader.prototype.readInt32);
         });
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_RunLogReport.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_RunLogReport.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_RunLogReport} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_RunLogReport.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getExceptionCountMap(true);
  if (f && f.getLength() > 0) {
    f.serializeBinary(1, writer, jspb.BinaryWriter.prototype.writeInt32, jspb.BinaryWriter.prototype.writeInt32);
  }
  f = message.getTypeCountMap(true);
  if (f && f.getLength() > 0) {
    f.serializeBinary(2, writer, jspb.BinaryWriter.prototype.writeInt32, jspb.BinaryWriter.prototype.writeInt32);
  }
  f = message.getTotalCount();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getLogsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      4,
      f,
      proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog.serializeBinaryToWriter
    );
  }
  f = message.getSourceCountMap(true);
  if (f && f.getLength() > 0) {
    f.serializeBinary(5, writer, jspb.BinaryWriter.prototype.writeInt32, jspb.BinaryWriter.prototype.writeInt32);
  }
  f = message.getLevelCountMap(true);
  if (f && f.getLength() > 0) {
    f.serializeBinary(6, writer, jspb.BinaryWriter.prototype.writeInt32, jspb.BinaryWriter.prototype.writeInt32);
  }
  f = message.getModuleCountMap(true);
  if (f && f.getLength() > 0) {
    f.serializeBinary(7, writer, jspb.BinaryWriter.prototype.writeInt32, jspb.BinaryWriter.prototype.writeInt32);
  }
};


/**
 * map<int32, int32> exception_count = 1;
 * @param {boolean=} opt_noLazyCreate Do not create the map if
 * empty, instead returning `undefined`
 * @return {!jspb.Map<number,number>}
 */
proto.com.jy.network_monitor.proto.MSG_RunLogReport.prototype.getExceptionCountMap = function(opt_noLazyCreate) {
  return /** @type {!jspb.Map<number,number>} */ (
      jspb.Message.getMapField(this, 1, opt_noLazyCreate,
      null));
};


proto.com.jy.network_monitor.proto.MSG_RunLogReport.prototype.clearExceptionCountMap = function() {
  this.getExceptionCountMap().clear();
};


/**
 * map<int32, int32> type_count = 2;
 * @param {boolean=} opt_noLazyCreate Do not create the map if
 * empty, instead returning `undefined`
 * @return {!jspb.Map<number,number>}
 */
proto.com.jy.network_monitor.proto.MSG_RunLogReport.prototype.getTypeCountMap = function(opt_noLazyCreate) {
  return /** @type {!jspb.Map<number,number>} */ (
      jspb.Message.getMapField(this, 2, opt_noLazyCreate,
      null));
};


proto.com.jy.network_monitor.proto.MSG_RunLogReport.prototype.clearTypeCountMap = function() {
  this.getTypeCountMap().clear();
};


/**
 * optional int32 total_count = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_RunLogReport.prototype.getTotalCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_RunLogReport.prototype.setTotalCount = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * repeated MSG_ExceptionRunLog logs = 4;
 * @return {!Array<!proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog>}
 */
proto.com.jy.network_monitor.proto.MSG_RunLogReport.prototype.getLogsList = function() {
  return /** @type{!Array<!proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog, 4));
};


/** @param {!Array<!proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog>} value */
proto.com.jy.network_monitor.proto.MSG_RunLogReport.prototype.setLogsList = function(value) {
  jspb.Message.setRepeatedWrapperField(this, 4, value);
};


/**
 * @param {!proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog=} opt_value
 * @param {number=} opt_index
 * @return {!proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog}
 */
proto.com.jy.network_monitor.proto.MSG_RunLogReport.prototype.addLogs = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 4, opt_value, proto.com.jy.network_monitor.proto.MSG_ExceptionRunLog, opt_index);
};


proto.com.jy.network_monitor.proto.MSG_RunLogReport.prototype.clearLogsList = function() {
  this.setLogsList([]);
};


/**
 * map<int32, int32> source_count = 5;
 * @param {boolean=} opt_noLazyCreate Do not create the map if
 * empty, instead returning `undefined`
 * @return {!jspb.Map<number,number>}
 */
proto.com.jy.network_monitor.proto.MSG_RunLogReport.prototype.getSourceCountMap = function(opt_noLazyCreate) {
  return /** @type {!jspb.Map<number,number>} */ (
      jspb.Message.getMapField(this, 5, opt_noLazyCreate,
      null));
};


proto.com.jy.network_monitor.proto.MSG_RunLogReport.prototype.clearSourceCountMap = function() {
  this.getSourceCountMap().clear();
};


/**
 * map<int32, int32> level_count = 6;
 * @param {boolean=} opt_noLazyCreate Do not create the map if
 * empty, instead returning `undefined`
 * @return {!jspb.Map<number,number>}
 */
proto.com.jy.network_monitor.proto.MSG_RunLogReport.prototype.getLevelCountMap = function(opt_noLazyCreate) {
  return /** @type {!jspb.Map<number,number>} */ (
      jspb.Message.getMapField(this, 6, opt_noLazyCreate,
      null));
};


proto.com.jy.network_monitor.proto.MSG_RunLogReport.prototype.clearLevelCountMap = function() {
  this.getLevelCountMap().clear();
};


/**
 * map<int32, int32> module_count = 7;
 * @param {boolean=} opt_noLazyCreate Do not create the map if
 * empty, instead returning `undefined`
 * @return {!jspb.Map<number,number>}
 */
proto.com.jy.network_monitor.proto.MSG_RunLogReport.prototype.getModuleCountMap = function(opt_noLazyCreate) {
  return /** @type {!jspb.Map<number,number>} */ (
      jspb.Message.getMapField(this, 7, opt_noLazyCreate,
      null));
};


proto.com.jy.network_monitor.proto.MSG_RunLogReport.prototype.clearModuleCountMap = function() {
  this.getModuleCountMap().clear();
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_ServiceInfo = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_ServiceInfo, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_ServiceInfo.displayName = 'proto.com.jy.network_monitor.proto.MSG_ServiceInfo';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_ServiceInfo.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_ServiceInfo.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_ServiceInfo} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ServiceInfo.toObject = function(includeInstance, msg) {
  var f, obj = {
    pid: jspb.Message.getFieldWithDefault(msg, 1, 0),
    serviceName: jspb.Message.getFieldWithDefault(msg, 2, ""),
    cmd: jspb.Message.getFieldWithDefault(msg, 3, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ServiceInfo}
 */
proto.com.jy.network_monitor.proto.MSG_ServiceInfo.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_ServiceInfo;
  return proto.com.jy.network_monitor.proto.MSG_ServiceInfo.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ServiceInfo} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ServiceInfo}
 */
proto.com.jy.network_monitor.proto.MSG_ServiceInfo.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setPid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setServiceName(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setCmd(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_ServiceInfo.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_ServiceInfo.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ServiceInfo} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ServiceInfo.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPid();
  if (f !== 0) {
    writer.writeInt64(
      1,
      f
    );
  }
  f = message.getServiceName();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getCmd();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
};


/**
 * optional int64 pid = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ServiceInfo.prototype.getPid = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ServiceInfo.prototype.setPid = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string service_name = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServiceInfo.prototype.getServiceName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServiceInfo.prototype.setServiceName = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string cmd = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServiceInfo.prototype.getCmd = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServiceInfo.prototype.setCmd = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.SwitchStatus = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.SwitchStatus, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.SwitchStatus.displayName = 'proto.com.jy.network_monitor.proto.SwitchStatus';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.SwitchStatus.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.SwitchStatus.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.SwitchStatus} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.SwitchStatus.toObject = function(includeInstance, msg) {
  var f, obj = {
    eth: jspb.Message.getFieldWithDefault(msg, 1, 0),
    up: jspb.Message.getFieldWithDefault(msg, 2, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.SwitchStatus}
 */
proto.com.jy.network_monitor.proto.SwitchStatus.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.SwitchStatus;
  return proto.com.jy.network_monitor.proto.SwitchStatus.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.SwitchStatus} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.SwitchStatus}
 */
proto.com.jy.network_monitor.proto.SwitchStatus.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setEth(value);
      break;
    case 2:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setUp(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.SwitchStatus.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.SwitchStatus.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.SwitchStatus} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.SwitchStatus.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getEth();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getUp();
  if (f) {
    writer.writeBool(
      2,
      f
    );
  }
};


/**
 * optional int32 eth = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.SwitchStatus.prototype.getEth = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.SwitchStatus.prototype.setEth = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional bool up = 2;
 * Note that Boolean fields may be set to 0/1 when serialized from a Java server.
 * You should avoid comparisons like {@code val === true/false} in those cases.
 * @return {boolean}
 */
proto.com.jy.network_monitor.proto.SwitchStatus.prototype.getUp = function() {
  return /** @type {boolean} */ (jspb.Message.getFieldWithDefault(this, 2, false));
};


/** @param {boolean} value */
proto.com.jy.network_monitor.proto.SwitchStatus.prototype.setUp = function(value) {
  jspb.Message.setProto3BooleanField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_FiledEvent = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_FiledEvent, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_FiledEvent.displayName = 'proto.com.jy.network_monitor.proto.MSG_FiledEvent';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_FiledEvent.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_FiledEvent.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_FiledEvent} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_FiledEvent.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, 0),
    deviceType: jspb.Message.getFieldWithDefault(msg, 2, 0),
    eventType: jspb.Message.getFieldWithDefault(msg, 3, 0),
    eventSubType: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_FiledEvent}
 */
proto.com.jy.network_monitor.proto.MSG_FiledEvent.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_FiledEvent;
  return proto.com.jy.network_monitor.proto.MSG_FiledEvent.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_FiledEvent} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_FiledEvent}
 */
proto.com.jy.network_monitor.proto.MSG_FiledEvent.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setDeviceType(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setEventType(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setEventSubType(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_FiledEvent.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_FiledEvent.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_FiledEvent} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_FiledEvent.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getDeviceType();
  if (f !== 0) {
    writer.writeInt32(
      2,
      f
    );
  }
  f = message.getEventType();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getEventSubType();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
};


/**
 * optional int32 id = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_FiledEvent.prototype.getId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_FiledEvent.prototype.setId = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional int32 device_type = 2;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_FiledEvent.prototype.getDeviceType = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_FiledEvent.prototype.setDeviceType = function(value) {
  jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional int32 event_type = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_FiledEvent.prototype.getEventType = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_FiledEvent.prototype.setEventType = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional int32 event_sub_type = 4;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_FiledEvent.prototype.getEventSubType = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_FiledEvent.prototype.setEventSubType = function(value) {
  jspb.Message.setProto3IntField(this, 4, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_File = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_File, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_File.displayName = 'proto.com.jy.network_monitor.proto.MSG_File';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_File.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_File.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_File} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_File.toObject = function(includeInstance, msg) {
  var f, obj = {
    isDir: jspb.Message.getFieldWithDefault(msg, 1, false),
    owner: jspb.Message.getFieldWithDefault(msg, 2, ""),
    authority: jspb.Message.getFieldWithDefault(msg, 3, ""),
    size: jspb.Message.getFieldWithDefault(msg, 4, 0),
    name: jspb.Message.getFieldWithDefault(msg, 5, ""),
    modifyDate: jspb.Message.getFieldWithDefault(msg, 6, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_File}
 */
proto.com.jy.network_monitor.proto.MSG_File.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_File;
  return proto.com.jy.network_monitor.proto.MSG_File.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_File} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_File}
 */
proto.com.jy.network_monitor.proto.MSG_File.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIsDir(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setOwner(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setAuthority(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint64());
      msg.setSize(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setModifyDate(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_File.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_File.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_File} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_File.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getIsDir();
  if (f) {
    writer.writeBool(
      1,
      f
    );
  }
  f = message.getOwner();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getAuthority();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getSize();
  if (f !== 0) {
    writer.writeUint64(
      4,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getModifyDate();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
};


/**
 * optional bool is_dir = 1;
 * Note that Boolean fields may be set to 0/1 when serialized from a Java server.
 * You should avoid comparisons like {@code val === true/false} in those cases.
 * @return {boolean}
 */
proto.com.jy.network_monitor.proto.MSG_File.prototype.getIsDir = function() {
  return /** @type {boolean} */ (jspb.Message.getFieldWithDefault(this, 1, false));
};


/** @param {boolean} value */
proto.com.jy.network_monitor.proto.MSG_File.prototype.setIsDir = function(value) {
  jspb.Message.setProto3BooleanField(this, 1, value);
};


/**
 * optional string owner = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_File.prototype.getOwner = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_File.prototype.setOwner = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string authority = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_File.prototype.getAuthority = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_File.prototype.setAuthority = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional uint64 size = 4;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_File.prototype.getSize = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_File.prototype.setSize = function(value) {
  jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional string name = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_File.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_File.prototype.setName = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string modify_date = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_File.prototype.getModifyDate = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_File.prototype.setModifyDate = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.ResumeFileTask = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.ResumeFileTask, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.ResumeFileTask.displayName = 'proto.com.jy.network_monitor.proto.ResumeFileTask';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.ResumeFileTask.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.ResumeFileTask.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.ResumeFileTask} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.ResumeFileTask.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, 0),
    uuid: jspb.Message.getFieldWithDefault(msg, 2, ""),
    subType: jspb.Message.getFieldWithDefault(msg, 3, 0),
    pluginId: jspb.Message.getFieldWithDefault(msg, 4, ""),
    fileName: jspb.Message.getFieldWithDefault(msg, 5, ""),
    filePath: jspb.Message.getFieldWithDefault(msg, 6, ""),
    totalSize: jspb.Message.getFieldWithDefault(msg, 7, 0),
    transferredSize: jspb.Message.getFieldWithDefault(msg, 8, 0),
    md5: jspb.Message.getFieldWithDefault(msg, 9, ""),
    createTime: jspb.Message.getFieldWithDefault(msg, 10, 0),
    updateTime: jspb.Message.getFieldWithDefault(msg, 11, 0),
    lastTransferTime: jspb.Message.getFieldWithDefault(msg, 12, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.ResumeFileTask}
 */
proto.com.jy.network_monitor.proto.ResumeFileTask.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.ResumeFileTask;
  return proto.com.jy.network_monitor.proto.ResumeFileTask.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.ResumeFileTask} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.ResumeFileTask}
 */
proto.com.jy.network_monitor.proto.ResumeFileTask.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setUuid(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSubType(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setPluginId(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setFileName(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setFilePath(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setTotalSize(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setTransferredSize(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setMd5(value);
      break;
    case 10:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setCreateTime(value);
      break;
    case 11:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setUpdateTime(value);
      break;
    case 12:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setLastTransferTime(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.ResumeFileTask.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.ResumeFileTask.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.ResumeFileTask} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.ResumeFileTask.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f !== 0) {
    writer.writeInt64(
      1,
      f
    );
  }
  f = message.getUuid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSubType();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getPluginId();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getFileName();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getFilePath();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getTotalSize();
  if (f !== 0) {
    writer.writeInt64(
      7,
      f
    );
  }
  f = message.getTransferredSize();
  if (f !== 0) {
    writer.writeInt64(
      8,
      f
    );
  }
  f = message.getMd5();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
  f = message.getCreateTime();
  if (f !== 0) {
    writer.writeInt64(
      10,
      f
    );
  }
  f = message.getUpdateTime();
  if (f !== 0) {
    writer.writeInt64(
      11,
      f
    );
  }
  f = message.getLastTransferTime();
  if (f !== 0) {
    writer.writeInt64(
      12,
      f
    );
  }
};


/**
 * optional int64 id = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.ResumeFileTask.prototype.getId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.ResumeFileTask.prototype.setId = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string uuid = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.ResumeFileTask.prototype.getUuid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.ResumeFileTask.prototype.setUuid = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int32 sub_type = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.ResumeFileTask.prototype.getSubType = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.ResumeFileTask.prototype.setSubType = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional string plugin_id = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.ResumeFileTask.prototype.getPluginId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.ResumeFileTask.prototype.setPluginId = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string file_name = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.ResumeFileTask.prototype.getFileName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.ResumeFileTask.prototype.setFileName = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string file_path = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.ResumeFileTask.prototype.getFilePath = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.ResumeFileTask.prototype.setFilePath = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional int64 total_size = 7;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.ResumeFileTask.prototype.getTotalSize = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.ResumeFileTask.prototype.setTotalSize = function(value) {
  jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional int64 transferred_size = 8;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.ResumeFileTask.prototype.getTransferredSize = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.ResumeFileTask.prototype.setTransferredSize = function(value) {
  jspb.Message.setProto3IntField(this, 8, value);
};


/**
 * optional string md5 = 9;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.ResumeFileTask.prototype.getMd5 = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.ResumeFileTask.prototype.setMd5 = function(value) {
  jspb.Message.setProto3StringField(this, 9, value);
};


/**
 * optional int64 create_time = 10;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.ResumeFileTask.prototype.getCreateTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 10, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.ResumeFileTask.prototype.setCreateTime = function(value) {
  jspb.Message.setProto3IntField(this, 10, value);
};


/**
 * optional int64 update_time = 11;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.ResumeFileTask.prototype.getUpdateTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 11, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.ResumeFileTask.prototype.setUpdateTime = function(value) {
  jspb.Message.setProto3IntField(this, 11, value);
};


/**
 * optional int64 last_transfer_time = 12;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.ResumeFileTask.prototype.getLastTransferTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 12, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.ResumeFileTask.prototype.setLastTransferTime = function(value) {
  jspb.Message.setProto3IntField(this, 12, value);
};


/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.DeviceType = {
  DEVICETYPE_FW: 0,
  DEVICETYPE_FID: 1,
  DEVICETYPE_BID: 2,
  DEVICETYPE_SVR: 3,
  DEVICETYPE_SW: 4,
  DEVICETYPE_VEAD: 5,
  DEVICETYPE_AV: 6,
  DEVICETYPE_IDS: 7,
  DEVICETYPE_DB: 8,
  DEVICETYPE_DCD: 9
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.ErrorCode = {
  ERRORCODE_SUCCESS: 0,
  ERRORCODE_INNERERROR: 1,
  ERRORCODE_UNSUPPORTEDTYPE: 2,
  ERRORCODE_WRONGPARAM: 3,
  ERRORCODE_OBJECTNOTEXIST: 4,
  ERRORCODE_OBJECTUNREACHABLE: 5,
  ERRORCODE_INVALIDCONTENTFORMAT: 6,
  ERRORCODE_TIMESTAMPMISMATCH: 7,
  ERRORCODE_INVALIDSIGN: 8,
  ERRORCODE_CANNOT_EXEC_CMD: 9,
  ERRORCODE_DATAVERIFYFAILED: 10,
  ERRORCODE_INVALIDCERTFORMAT: 11,
  ERRORCODE_CAERROR: 12,
  ERRORCODE_NOCACHAIN: 13,
  ERRORCODE_CERTVALIDATEERROR: 14,
  ERRORCODE_DEVICENOAUTHORITY: 15,
  ERRORCODE_ASSETNOTEXIST: 16,
  ERRORCODE_PACKETCONTENTNOTENOUGH: 17,
  ERRORCODE_PLUGINPERMISSIONCONFLICT: 18,
  ERRORCODE_ASSETMODELCONFLICT: 19,
  ERRORCODE_INVALIDUSER: 1001,
  ERRORCODE_BASELINEISRUNNING: 1002,
  ERRORCODE_LOGINTRIEDTIMESLIMIT: 1003,
  ERRORCODE_VERSIONMATCHING: 1004,
  ERRORCODE_VERSIONCONFIGUPDATING: 1005,
  ERRORCODE_LEAKSCANISRUNNING: 1006,
  ERRORCODE_VERSIONVERIFYISRUNNING: 1007,
  ERRORCODE_PASSWORDEXPIRED: 1008,
  ERRORCODE_NEEDUPDATEPASSWORD: 1009,
  ERRORCODE_PASSWORDCONTAINUSERNAME: 1010,
  ERRORCODE_PASSWORDREPEATED: 1011,
  ERRORCODE_INVALIDLOGINIP: 1012,
  ERRORCODE_UKEYNOTFOUND: 1013,
  ERRORCODE_UKEYMISMATCH: 1014,
  ERRORCODE_INVALIDPASSWORD: 1015,
  ERRORCODE_USEREXIST: 1016,
  ERRORCODE_UKEYEXIST: 1017
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.RoleType = {
  ROLETYPE_INVALID: 0,
  ROLETYPE_ADMIN: 1,
  ROLETYPE_AUDITOR: 2,
  ROLETYPE_OPERATOR: 3
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.GeneralParamKey = {
  GENERALPARAMKEY_DUMMY: 0,
  PARAMKEY_PARAMCONFIG_SNTP: 1,
  PARAMKEY_PARAMCONFIG_COMMUNICATION: 2,
  PARAMKEY_PARAMCONFIG_EVENT: 3,
  PARAMKEY_PARAMCONFIG_DCDRUNTIME: 4,
  PARAMKEY_LOGINCOOLDOWN: 1001,
  PARAMKEY_REPEATEDPASSWORDCOUNT: 1002,
  PARAMKEY_PASSWORDEXPIRETIME: 1003,
  PARAMKEY_CONNWHITELIST: 1004,
  PARAMKEY_PORTWHITELIST: 1005,
  PARAMKEY_INVALIDPORTDETECTINTERVAL: 1006,
  PARAMKEY_LOGINFAILEDTHRESHOLDTIME: 1007,
  PARAMKEY_TIMINGFAILRETRYCOUNT: 1008,
  PARAMKEY_ENABLECERTCHAINVERIFY: 1009,
  PARAMKEY_CLIENTLOCKTIMEOUT: 1010
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.EncryptType = {
  ENCRYPTTYPE_NIL: 0,
  ENCRYPTTYPE_SM2: 1,
  ENCRYPTTYPE_SM4: 2,
  ENCRYPTTYPE_AES: 3
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.PlatformType = {
  PLATFORM_TYPE_DUMMY: 0,
  PLATFORM_TYPE_LOAD_RAW_EVENT: 1,
  PLATFORM_TYPE_LOAD_UPLOAD_EVENT: 2,
  PLATFORM_TYPE_CHECK_BASE_LINE: 3,
  PLATFORM_TYPE_CONTROL: 4,
  PLATFORM_TYPE_CONFIG_MANAGE: 5,
  PLATFORM_TYPE_UPGRADE_SOFTWARE: 6,
  PLATFORM_TYPE_MONITORED_OBJECT_MANAGE: 7,
  PLATFORM_TYPE_LEAK_SCAN: 8,
  PLATFORM_TYPE_VERSION_MANAGE: 9,
  PLATFORM_TYPE_CHARACTER_UPDATE: 10,
  PLATFORM_TYPE_BACKUP_RESTORE: 11,
  PLATFORM_TYPE_GUI_MESSAGE: 128,
  PLATFORM_TYPE_ENCRYPTED_MESSAGE_SM2: 129,
  PLATFORM_TYPE_ENCRYPTED_MESSAGE_SM4: 130,
  PLATFORM_TYPE_ENCRYPTED_MESSAGE_AES: 131
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.ConfigPacketSubType = {
  CONFIG_PACKET_SUB_TYPE_DUMMY: 0,
  CONFIG_PACKET_SUB_TYPE_DEVICE: 1,
  CONFIG_PACKET_SUB_TYPE_NET_CARD: 2,
  CONFIG_PACKET_SUB_TYPE_ROUTE: 3,
  CONFIG_PACKET_SUB_TYPE_NTP: 4,
  CONFIG_PACKET_SUB_TYPE_COMMUNICATE: 5,
  CONFIG_PACKET_SUB_TYPE_EVENT: 6,
  CONFIG_PACKET_SUB_TYPE_CERT: 7
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.ConfigPacketParam = {
  CONFIG_PACKET_PARAM_DUMMY: 0,
  CONFIG_PACKET_PARAM_LOAD: 1,
  CONFIG_PACKET_PARAM_ADD: 2,
  CONFIG_PACKET_PARAM_UPDATE: 3,
  CONFIG_PACKET_PARAM_DELETE: 4
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.MonitoredObjectParam = {
  MONITORED_OBJECT_PARAM_DUMMY: 0,
  MONITORED_OBJECT_PARAM_LOAD: 1,
  MONITORED_OBJECT_PARAM_SET: 2
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.VersionVerifyParam = {
  VERSION_MANAGE_PARAM_DUMMY: 0,
  VERSION_MANAGE_PARAM_START: 1,
  VERSION_MANAGE_PARAM_STOP: 2,
  VERSION_MANAGE_PARAM_GET_STATE: 3,
  VERSION_MANAGE_PARAM_GET_RESULT: 4
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.BaselinePacketSubType = {
  BASELINEPACKETSUBTYPE_DUMMY: 0,
  BASELINE_PACKET_SUBTYPE_START: 1,
  BASELINE_PACKET_SUBTYPE_STOP: 2,
  BASELINE_PACKET_SUBTYPE_GET_STATE: 3,
  BASELINE_PACKET_SUBTYPE_GET_RESULT: 4
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.ControlPacketSubType = {
  CONTROL_PACKET_SUBTYPE_DUMMY: 0,
  CONTROL_PACKET_SUBTYPE_CLOSE_NETWORK: 1,
  CONTROL_PACKET_SUBTYPE_BLOCK_LINK: 2
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.MonitoredObjectSubType = {
  MONITORED_OBJECT_SUBTYPE_DUMMY: 0,
  MONITORED_OBJECT_SUBTYPE_NETWORK_WHITE_LIST: 1,
  MONITORED_OBJECT_SUBTYPE_OPENED_PORT_LIST: 2,
  MONITORED_OBJECT_SUBTYPE_KEY_FILE_LIST: 3,
  MONITORED_OBJECT_SUBTYPE_CD_DETECT_CYCLE: 4,
  MONITORED_OBJECT_SUBTYPE_INVALID_PORT_DETECT_CYCLE: 5,
  MONITORED_OBJECT_SUBTYPE_DANGEROUS_CMD_LIST: 6
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.LeakScanSubType = {
  LEAK_SCAN_SUBTYPE_DUMMY: 0,
  LEAK_SCAN_SUBTYPE_START: 1,
  LEAK_SCAN_SUBTYPE_STOP: 2,
  LEAK_SCAN_SUBTYPE_GET_STATE: 3,
  LEAK_SCAN_SUBTYPE_GET_RESULT: 4
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.VersionManageSubType = {
  VERSION_MANAGE_SUBTYPE_DUMMY: 0,
  VERSION_MANAGE_SUBTYPE_MATCH: 1,
  VERSION_MANAGE_SUBTYPE_VERIFY: 2,
  VERSION_MANAGE_SUBTYPE_UPDATE_CONFIG: 3
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.CharacterUpdateSubType = {
  CHARACTER_UPDATE_SUBTYPE_DUMMY: 0,
  CHARACTER_UPDATE_SUBTYPE_LEAK: 1,
  CHARACTER_UPDATE_SUBTYPE_VERSION: 2
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.BackupRestoreSubType = {
  BACKUP_RESTORE_SUBTYPE_DUMMU: 0,
  BACKUP_RESTORE_SUBTYPE_BACKUP: 1,
  BACKUP_RESTORE_SUBTYPE_RESTORE: 2
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.SnmpAuthType = {
  SNMPAUTHTYPE_NIL: 0,
  SNMPAUTHTYPE_MD5: 1,
  SNMPAUTHTYPE_SHA: 2
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.SnmpEncryptType = {
  SNMPENCRYPTTYPE_NIL: 0,
  SNMPENCRYPTTYPE_DES: 1,
  SNMPENCRYPTTYPE_AES: 2
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.DeviceState = {
  DEVICESTATEINIT: 0,
  DEVICESTATEONLINE: 1,
  DEVICESTATEOFFLINE: 2
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.CertType = {
  CERT_TYPE_DUMMY: 0,
  CERT_TYPE_CA: 1,
  CERT_TYPE_PLATFORM: 2,
  CERT_TYPE_DEVICE: 32
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.UpgradeType = {
  UPGRADE_TYPE_CONFIG: 0,
  UPGRADE_TYPE_PROGRAM: 1,
  UPGRADE_TYPE_SQL: 2,
  UPGRADE_TYPE_REDIS: 3
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.LogSource = {
  LOG_SOURCE_APP: 0,
  LOG_SOURCE_OS: 1
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.LogType = {
  LOG_TYPE_LOGIN: 0,
  LOG_TYPE_OPERATE: 1,
  LOG_TYPE_MAINTAIN: 2,
  LOG_TYPE_SYSTEM: 3,
  LOG_TYPE_OTHER: 4
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.LogModule = {
  LOG_MODULE_UI: 0,
  LOG_MODULE_COLLECT: 1,
  LOG_MODULE_ANALYSIS: 2,
  LOG_MODULE_PROXY: 3,
  LOG_MODULE_COMMUNICATE: 4
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.LogLevel = {
  LOG_LEVEL_FATAL: 0,
  LOG_LEVEL_ERROR: 1,
  LOG_LEVEL_WARN: 2,
  LOG_LEVEL_INFO: 3,
  LOG_LEVEL_DEBUG: 4
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.ExceptionLogType = {
  EXCEPTION_LOG_TYPE_COMMU: 0,
  EXCEPTION_LOG_TYPE_INVALID_ACCESS: 1,
  EXCEPTION_LOG_TYPE_LOGIN_FAIL_MAX: 2,
  EXCEPTION_LOG_TYPE_LOG_MAX: 3,
  EXCEPTION_LOG_TYPE_CPU_MAX: 4,
  EXCEPTION_LOG_TYPE_DISK_MAX: 5,
  EXCEPTION_LOG_TYPE_MEM_MAX: 6,
  EXCEPTION_LOG_TYPE_PROCESS: 7,
  EXCEPTION_LOG_TYPE_HARDWARE: 8,
  EXCEPTION_LOG_TYPE_OTHER: 9,
  EXCEPTION_LOG_TYPE_IP: 10
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.SystemFuncCode = {
  SSH: 0,
  MYSQL: 1,
  REDIS: 2,
  FIREWALL: 3
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.CompressFormat = {
  NIL: 0,
  ZIP: 1,
  GZIP: 2,
  JAR: 3
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.DcdModule = {
  DCDMODULE_DUMMY: 0,
  DCDMODULE_SENSOR: 1,
  DCDMODULE_ANALYSIS: 2,
  DCDMODULE_PLUGINENGINE: 3,
  DCDMODULE_PLATFORM: 4
};

goog.object.extend(exports, proto.com.jy.network_monitor.proto);
