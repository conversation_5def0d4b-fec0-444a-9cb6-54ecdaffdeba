/**
 * @fileoverview
 * @enhanceable
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!

var jspb = require('google-protobuf');
var goog = jspb;
var global = Function('return this')();

goog.exportSymbol('proto.com.jy.network_monitor.proto.HostNetworkAccessDetail', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.SecurityDeviceNetworkAccessDetail', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail', null, global);

/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.HostNetworkAccessDetail, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.displayName = 'proto.com.jy.network_monitor.proto.HostNetworkAccessDetail';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.HostNetworkAccessDetail} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.toObject = function(includeInstance, msg) {
  var f, obj = {
    sesseq: jspb.Message.getFieldWithDefault(msg, 1, ""),
    spid: jspb.Message.getFieldWithDefault(msg, 2, ""),
    seid: jspb.Message.getFieldWithDefault(msg, 3, ""),
    sexenm: jspb.Message.getFieldWithDefault(msg, 4, ""),
    sexedir: jspb.Message.getFieldWithDefault(msg, 5, ""),
    ssparam: jspb.Message.getFieldWithDefault(msg, 6, ""),
    sstime: jspb.Message.getFieldWithDefault(msg, 7, 0),
    dpid: jspb.Message.getFieldWithDefault(msg, 8, ""),
    deid: jspb.Message.getFieldWithDefault(msg, 9, ""),
    dexenm: jspb.Message.getFieldWithDefault(msg, 10, ""),
    dexedir: jspb.Message.getFieldWithDefault(msg, 11, ""),
    dsparam: jspb.Message.getFieldWithDefault(msg, 12, ""),
    dstime: jspb.Message.getFieldWithDefault(msg, 13, 0),
    lproto: jspb.Message.getFieldWithDefault(msg, 14, ""),
    nproto: jspb.Message.getFieldWithDefault(msg, 15, ""),
    tproto: jspb.Message.getFieldWithDefault(msg, 16, ""),
    aproto: jspb.Message.getFieldWithDefault(msg, 17, ""),
    datadr: jspb.Message.getFieldWithDefault(msg, 18, 0),
    role: jspb.Message.getFieldWithDefault(msg, 19, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.HostNetworkAccessDetail}
 */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.HostNetworkAccessDetail;
  return proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.HostNetworkAccessDetail} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.HostNetworkAccessDetail}
 */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setSesseq(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSpid(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setSeid(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setSexenm(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setSexedir(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setSsparam(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setSstime(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setDpid(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setDeid(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setDexenm(value);
      break;
    case 11:
      var value = /** @type {string} */ (reader.readString());
      msg.setDexedir(value);
      break;
    case 12:
      var value = /** @type {string} */ (reader.readString());
      msg.setDsparam(value);
      break;
    case 13:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setDstime(value);
      break;
    case 14:
      var value = /** @type {string} */ (reader.readString());
      msg.setLproto(value);
      break;
    case 15:
      var value = /** @type {string} */ (reader.readString());
      msg.setNproto(value);
      break;
    case 16:
      var value = /** @type {string} */ (reader.readString());
      msg.setTproto(value);
      break;
    case 17:
      var value = /** @type {string} */ (reader.readString());
      msg.setAproto(value);
      break;
    case 18:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setDatadr(value);
      break;
    case 19:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRole(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.HostNetworkAccessDetail} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSesseq();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getSpid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSeid();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getSexenm();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getSexedir();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getSsparam();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getSstime();
  if (f !== 0) {
    writer.writeInt64(
      7,
      f
    );
  }
  f = message.getDpid();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getDeid();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
  f = message.getDexenm();
  if (f.length > 0) {
    writer.writeString(
      10,
      f
    );
  }
  f = message.getDexedir();
  if (f.length > 0) {
    writer.writeString(
      11,
      f
    );
  }
  f = message.getDsparam();
  if (f.length > 0) {
    writer.writeString(
      12,
      f
    );
  }
  f = message.getDstime();
  if (f !== 0) {
    writer.writeInt64(
      13,
      f
    );
  }
  f = message.getLproto();
  if (f.length > 0) {
    writer.writeString(
      14,
      f
    );
  }
  f = message.getNproto();
  if (f.length > 0) {
    writer.writeString(
      15,
      f
    );
  }
  f = message.getTproto();
  if (f.length > 0) {
    writer.writeString(
      16,
      f
    );
  }
  f = message.getAproto();
  if (f.length > 0) {
    writer.writeString(
      17,
      f
    );
  }
  f = message.getDatadr();
  if (f !== 0) {
    writer.writeInt32(
      18,
      f
    );
  }
  f = message.getRole();
  if (f !== 0) {
    writer.writeInt32(
      19,
      f
    );
  }
};


/**
 * optional string SESSEQ = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.getSesseq = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.setSesseq = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string SPID = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.getSpid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.setSpid = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string SEID = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.getSeid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.setSeid = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string SEXENM = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.getSexenm = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.setSexenm = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string SEXEDIR = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.getSexedir = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.setSexedir = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string SSPARAM = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.getSsparam = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.setSsparam = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional int64 SSTIME = 7;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.getSstime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.setSstime = function(value) {
  jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional string DPID = 8;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.getDpid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.setDpid = function(value) {
  jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional string DEID = 9;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.getDeid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.setDeid = function(value) {
  jspb.Message.setProto3StringField(this, 9, value);
};


/**
 * optional string DEXENM = 10;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.getDexenm = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.setDexenm = function(value) {
  jspb.Message.setProto3StringField(this, 10, value);
};


/**
 * optional string DEXEDIR = 11;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.getDexedir = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 11, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.setDexedir = function(value) {
  jspb.Message.setProto3StringField(this, 11, value);
};


/**
 * optional string DSPARAM = 12;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.getDsparam = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 12, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.setDsparam = function(value) {
  jspb.Message.setProto3StringField(this, 12, value);
};


/**
 * optional int64 DSTIME = 13;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.getDstime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 13, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.setDstime = function(value) {
  jspb.Message.setProto3IntField(this, 13, value);
};


/**
 * optional string LPROTO = 14;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.getLproto = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 14, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.setLproto = function(value) {
  jspb.Message.setProto3StringField(this, 14, value);
};


/**
 * optional string NPROTO = 15;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.getNproto = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 15, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.setNproto = function(value) {
  jspb.Message.setProto3StringField(this, 15, value);
};


/**
 * optional string TPROTO = 16;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.getTproto = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 16, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.setTproto = function(value) {
  jspb.Message.setProto3StringField(this, 16, value);
};


/**
 * optional string APROTO = 17;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.getAproto = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 17, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.setAproto = function(value) {
  jspb.Message.setProto3StringField(this, 17, value);
};


/**
 * optional int32 DATADR = 18;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.getDatadr = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 18, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.setDatadr = function(value) {
  jspb.Message.setProto3IntField(this, 18, value);
};


/**
 * optional int32 ROLE = 19;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.getRole = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 19, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.HostNetworkAccessDetail.prototype.setRole = function(value) {
  jspb.Message.setProto3IntField(this, 19, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.displayName = 'proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.toObject = function(includeInstance, msg) {
  var f, obj = {
    sesseq: jspb.Message.getFieldWithDefault(msg, 1, ""),
    lproto: jspb.Message.getFieldWithDefault(msg, 2, ""),
    nproto: jspb.Message.getFieldWithDefault(msg, 3, ""),
    tproto: jspb.Message.getFieldWithDefault(msg, 4, ""),
    aproto: jspb.Message.getFieldWithDefault(msg, 5, ""),
    datadr: jspb.Message.getFieldWithDefault(msg, 6, 0),
    repack: jspb.Message.getFieldWithDefault(msg, 7, 0),
    snpack: jspb.Message.getFieldWithDefault(msg, 8, 0),
    rebyte: jspb.Message.getFieldWithDefault(msg, 9, 0),
    snbyte: jspb.Message.getFieldWithDefault(msg, 10, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail}
 */
proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail;
  return proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail}
 */
proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setSesseq(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setLproto(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setNproto(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setTproto(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setAproto(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setDatadr(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRepack(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSnpack(value);
      break;
    case 9:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRebyte(value);
      break;
    case 10:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSnbyte(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSesseq();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getLproto();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getNproto();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getTproto();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getAproto();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getDatadr();
  if (f !== 0) {
    writer.writeInt32(
      6,
      f
    );
  }
  f = message.getRepack();
  if (f !== 0) {
    writer.writeInt32(
      7,
      f
    );
  }
  f = message.getSnpack();
  if (f !== 0) {
    writer.writeInt32(
      8,
      f
    );
  }
  f = message.getRebyte();
  if (f !== 0) {
    writer.writeInt32(
      9,
      f
    );
  }
  f = message.getSnbyte();
  if (f !== 0) {
    writer.writeInt32(
      10,
      f
    );
  }
};


/**
 * optional string SESSEQ = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.prototype.getSesseq = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.prototype.setSesseq = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string LPROTO = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.prototype.getLproto = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.prototype.setLproto = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string NPROTO = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.prototype.getNproto = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.prototype.setNproto = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string TPROTO = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.prototype.getTproto = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.prototype.setTproto = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string APROTO = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.prototype.getAproto = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.prototype.setAproto = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional int32 DATADR = 6;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.prototype.getDatadr = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.prototype.setDatadr = function(value) {
  jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional int32 REPACK = 7;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.prototype.getRepack = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.prototype.setRepack = function(value) {
  jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional int32 SNPACK = 8;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.prototype.getSnpack = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.prototype.setSnpack = function(value) {
  jspb.Message.setProto3IntField(this, 8, value);
};


/**
 * optional int32 REBYTE = 9;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.prototype.getRebyte = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 9, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.prototype.setRebyte = function(value) {
  jspb.Message.setProto3IntField(this, 9, value);
};


/**
 * optional int32 SNBYTE = 10;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.prototype.getSnbyte = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 10, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.TrafficNetworkAccessDetail.prototype.setSnbyte = function(value) {
  jspb.Message.setProto3IntField(this, 10, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.SecurityDeviceNetworkAccessDetail = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.SecurityDeviceNetworkAccessDetail, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.SecurityDeviceNetworkAccessDetail.displayName = 'proto.com.jy.network_monitor.proto.SecurityDeviceNetworkAccessDetail';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.SecurityDeviceNetworkAccessDetail.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.SecurityDeviceNetworkAccessDetail.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.SecurityDeviceNetworkAccessDetail} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.SecurityDeviceNetworkAccessDetail.toObject = function(includeInstance, msg) {
  var f, obj = {
    tproto: jspb.Message.getFieldWithDefault(msg, 1, ""),
    desr: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.SecurityDeviceNetworkAccessDetail}
 */
proto.com.jy.network_monitor.proto.SecurityDeviceNetworkAccessDetail.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.SecurityDeviceNetworkAccessDetail;
  return proto.com.jy.network_monitor.proto.SecurityDeviceNetworkAccessDetail.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.SecurityDeviceNetworkAccessDetail} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.SecurityDeviceNetworkAccessDetail}
 */
proto.com.jy.network_monitor.proto.SecurityDeviceNetworkAccessDetail.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setTproto(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setDesr(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.SecurityDeviceNetworkAccessDetail.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.SecurityDeviceNetworkAccessDetail.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.SecurityDeviceNetworkAccessDetail} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.SecurityDeviceNetworkAccessDetail.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTproto();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getDesr();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string TPROTO = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.SecurityDeviceNetworkAccessDetail.prototype.getTproto = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.SecurityDeviceNetworkAccessDetail.prototype.setTproto = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string DESR = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.SecurityDeviceNetworkAccessDetail.prototype.getDesr = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.SecurityDeviceNetworkAccessDetail.prototype.setDesr = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


goog.object.extend(exports, proto.com.jy.network_monitor.proto);
