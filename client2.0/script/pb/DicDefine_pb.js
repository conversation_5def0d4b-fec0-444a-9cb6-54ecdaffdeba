/**
 * @fileoverview
 * @enhanceable
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!

var jspb = require('google-protobuf');
var goog = jspb;
var global = Function('return this')();

goog.exportSymbol('proto.com.jy.network_monitor.proto.ApplicationType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.AssetOperationStatus', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.AssetPurpose', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.AssetType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.CpuArchitecture', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.DataSource', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.HardwareExceptionStatus', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.InterfaceBindingMode', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.LoginMethod', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.LongitudinalPolicyProtocol', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MonitoringStatus', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.NetworkException', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.NetworkType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.PeripheralDeviceType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.PeripheralInterfaceType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.PeripheralProtocol', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.PluginBusinessType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.PolicyType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.RaidExceptionStatus', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.SecurityZone', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.SensorExceptionStatus', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.SoftwareException', null, global);
/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.NetworkType = {
  NETWORK_TYPE_DUMMY: 0,
  NETWORK_TYPE_BACKBONE_PLANE1: 1,
  NETWORK_TYPE_BACKBONE_PLANE2: 2,
  NETWORK_TYPE_NATIONAL_DISPATCH_ACCESS: 3,
  NETWORK_TYPE_REGIONAL_DISPATCH_ACCESS: 4,
  NETWORK_TYPE_PROVINCIAL_ACCESS: 5,
  NETWORK_TYPE_PREFECTURAL_ACCESS: 6,
  NETWORK_TYPE_DISPATCH_INTRANET: 7,
  NETWORK_TYPE_DISPATCH_INTRANET_B: 8
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.AssetType = {
  ASSET_TYPE_DUMMY: 0,
  ASSET_TYPE_UNKNOWN: 1000,
  ASSET_TYPE_SERVER: 7101,
  ASSET_TYPE_WORKSTATION: 7102,
  ASSET_TYPE_STORAGE_DEVICE: 7103,
  ASSET_TYPE_BLADE_CHASSIS: 7104,
  ASSET_TYPE_BLADE_SERVER: 7105,
  ASSET_TYPE_ROUTER: 7201,
  ASSET_TYPE_SWITCH: 7202,
  ASSET_TYPE_INDUSTRIAL_SWITCH: 7203,
  ASSET_TYPE_MEDIA_CONVERTER: 7204,
  ASSET_TYPE_SERIAL_SERVER: 7205,
  ASSET_TYPE_LATERAL_ISOLATION: 7301,
  ASSET_TYPE_LONGITUDINAL_ENCRYPTION: 7302,
  ASSET_TYPE_FIREWALL: 7303,
  ASSET_TYPE_IDS: 7304,
  ASSET_TYPE_NET_SEC_MONITOR: 7305,
  ASSET_TYPE_ENCRYPTION_CARD: 7306,
  ASSET_TYPE_LARGE_SCREEN: 7402,
  ASSET_TYPE_PRECISION_AC: 7403,
  ASSET_TYPE_KVM: 7404,
  ASSET_TYPE_TIME_SYNC: 7405,
  ASSET_TYPE_PRINTER: 7406,
  ASSET_TYPE_NETWORK_CABLE: 7602,
  ASSET_TYPE_MERGING_UNIT: 2101,
  ASSET_TYPE_INTELLIGENT_TERMINAL: 2102,
  ASSET_TYPE_SPECIAL_TELEMETRY_GATEWAY: 2701,
  ASSET_TYPE_TELEMETRY_DEVICE: 2702,
  ASSET_TYPE_MEASUREMENT_CONTROL: 2703,
  ASSET_TYPE_PMU: 2711,
  ASSET_TYPE_ENERGY_ACQUISITION: 2713,
  ASSET_TYPE_NETWORK_ANALYZER: 2731,
  ASSET_TYPE_SMART_PD_TERMINAL: 2501,
  ASSET_TYPE_SMART_PD_MONITORING: 2502,
  ASSET_TYPE_TRANSFORMER_MONITORING: 2503,
  ASSET_TYPE_SMART_PD_SYNC_MEAS: 2504,
  ASSET_TYPE_OS: 7801,
  ASSET_TYPE_DATABASE: 7802,
  ASSET_TYPE_MIDDLEWARE: 7803,
  ASSET_TYPE_APP_SOFTWARE_PKG: 7811,
  ASSET_TYPE_APP_SOFTWARE: 7812,
  ASSET_TYPE_APPLICATION: 7813,
  ASSET_TYPE_MALWARE_MONITOR: 9901,
  ASSET_TYPE_ANTIVIRUS: 9902,
  ASSET_TYPE_OPS_GATEWAY: 9903,
  ASSET_TYPE_OTHER: 9999
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.AssetOperationStatus = {
  ASSET_OPERATION_DUMMY: 0,
  ASSET_OPERATION_PRE_ACCESS: 1,
  ASSET_OPERATION_IN_SERVICE: 2,
  ASSET_OPERATION_MAINTENANCE: 3,
  ASSET_OPERATION_DEACTIVATED: 4
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.SecurityZone = {
  SECURITY_ZONE_UNKNOWN: 0,
  SECURITY_ZONE_I: 1,
  SECURITY_ZONE_II: 2,
  SECURITY_ZONE_III: 3,
  SECURITY_ZONE_IV: 4,
  SECURITY_ZONE_I_II: 5,
  SECURITY_ZONE_PROD_CTRL_MGMT: 6,
  SECURITY_ZONE_II_III: 7,
  SECURITY_ZONE_III_IV: 8,
  SECURITY_ZONE_SEC_ACCESS: 9,
  SECURITY_ZONE_OTHER: 99
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.AssetPurpose = {
  ASSET_PURPOSE_DUMMY: 0,
  ASSET_PURPOSE_GATEWAY: 1,
  ASSET_PURPOSE_ROUTING: 2,
  ASSET_PURPOSE_SWITCHING: 4,
  ASSET_PURPOSE_LONGITUDINAL: 8,
  ASSET_PURPOSE_ISOLATION: 15,
  ASSET_PURPOSE_FIREWALL: 32
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.CpuArchitecture = {
  CPU_ARCH_UNKNOWN: 0,
  CPU_ARCH_X86: 1,
  CPU_ARCH_ARM: 2,
  CPU_ARCH_POWERPC: 3,
  CPU_ARCH_RISC_V: 4,
  CPU_ARCH_MIPS: 5,
  CPU_ARCH_LOONGARCH: 6,
  CPU_ARCH_OTHER: 99
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.PeripheralInterfaceType = {
  PERIPHERAL_INTERFACE_DUMMY: 0,
  PERIPHERAL_INTERFACE_USB: 1,
  PERIPHERAL_INTERFACE_BLUETOOTH: 2,
  PERIPHERAL_INTERFACE_SERIAL: 3,
  PERIPHERAL_INTERFACE_PARALLEL: 4,
  PERIPHERAL_INTERFACE_OPTICAL_DRIVE: 5,
  PERIPHERAL_INTERFACE_OTHER: 99
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.PeripheralDeviceType = {
  PERIPHERAL_DEVICE_UNKNOWN: 0,
  PERIPHERAL_DEVICE_STORAGE: 1,
  PERIPHERAL_DEVICE_COMM: 2,
  PERIPHERAL_DEVICE_INPUT: 3,
  PERIPHERAL_DEVICE_OPTICAL_DISC: 4,
  PERIPHERAL_DEVICE_DISPLAY: 5,
  PERIPHERAL_DEVICE_PRINTER: 6,
  PERIPHERAL_DEVICE_WIRELESS_NIC: 7,
  PERIPHERAL_DEVICE_OTHER: 99
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.HardwareExceptionStatus = {
  HW_EX_DUMMY: 0,
  HW_EX_POWER_OVERLOAD: 1,
  HW_EX_POWER_CTRL_FAILURE: 2,
  HW_EX_MAIN_POWER_FAILURE: 4,
  HW_EX_DISK_ALERT: 8,
  HW_EX_MEM_ECC_ERROR: 16,
  HW_EX_FAN_FAILURE: 32,
  HW_EX_POWER_REDUNDANCY_ALERT: 64,
  HW_EX_TEMPERATURE_FAILURE: 128
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.SoftwareException = {
  SOFTWARE_EXCEPTION_DUMMY: 0,
  SOFTWARE_EXCEPTION_SYSTEM_LOAD: 1,
  SOFTWARE_EXCEPTION_ZOMBIE_PROCESS: 2,
  SOFTWARE_EXCEPTION_HIGH_RISK_PORT: 4,
  SOFTWARE_EXCEPTION_TIME_SYNC: 8,
  SOFTWARE_EXCEPTION_KERNEL_LOCK: 16
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.NetworkException = {
  NETWORK_EXCEPTION_DUMMY: 0,
  NETWORK_EXCEPTION_DEVICE_IP_CONFLICT: 1,
  NETWORK_EXCEPTION_ACCESS_IP_CONFLICT: 2,
  NETWORK_EXCEPTION_DEVICE_MAC_CONFLICT: 4,
  NETWORK_EXCEPTION_DEVICE_IP_DUPLICATE: 8,
  NETWORK_EXCEPTION_ILLEGAL_CONNECTION: 16,
  NETWORK_EXCEPTION_TRAFFIC_OVERFLOW: 32
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.InterfaceBindingMode = {
  BINDING_MODE_DUMMY: 0,
  BINDING_MODE_DISABLED: 1,
  BINDING_MODE_0: 2,
  BINDING_MODE_1: 3,
  BINDING_MODE_2: 4,
  BINDING_MODE_3: 5,
  BINDING_MODE_4: 6,
  BINDING_MODE_5: 7,
  BINDING_MODE_6: 8,
  BINDING_MODE_BAGG: 9,
  BINDING_MODE_LAGG: 10
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.ApplicationType = {
  APPLICATION_TYPE_DUMMY: 0,
  APPLICATION_TYPE_VERTICAL_ENCRYPTION: 1,
  APPLICATION_TYPE_HORIZONTAL_ISOLATION: 2,
  APPLICATION_TYPE_FIREWALL: 3,
  APPLICATION_TYPE_SWITCH: 4,
  APPLICATION_TYPE_ROUTER: 5,
  APPLICATION_TYPE_HOST_MONITORING: 6,
  APPLICATION_TYPE_TRUSTED_VERIFICATION: 7,
  APPLICATION_TYPE_INTRUSION_DETECTION: 8,
  APPLICATION_TYPE_OPERATION_GATEWAY: 9,
  APPLICATION_TYPE_MALICIOUS_CODE_MONITORING: 10,
  APPLICATION_TYPE_MONITORING_DEVICE: 11,
  APPLICATION_TYPE_DATABASE: 12,
  APPLICATION_TYPE_CRYPTOGRAPHIC_SERVER: 13,
  APPLICATION_TYPE_SECURITY_MONITORING_SYSTEM: 14,
  APPLICATION_TYPE_OTHER: 99
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.PeripheralProtocol = {
  PERIPHERAL_PROTOCOL_DUMMY: 0,
  PERIPHERAL_PROTOCOL_AUDIO: 1,
  PERIPHERAL_PROTOCOL_CDC_CTRL: 2,
  PERIPHERAL_PROTOCOL_HID: 3,
  PERIPHERAL_PROTOCOL_PHYSICAL: 5,
  PERIPHERAL_PROTOCOL_IMAGE: 6,
  PERIPHERAL_PROTOCOL_PRINTER: 7,
  PERIPHERAL_PROTOCOL_MASS_STORAGE: 8,
  PERIPHERAL_PROTOCOL_HUB: 9,
  PERIPHERAL_PROTOCOL_CDC_DATA: 10,
  PERIPHERAL_PROTOCOL_SMART_CARD: 11,
  PERIPHERAL_PROTOCOL_CONTENT_SECURITY: 13,
  PERIPHERAL_PROTOCOL_VIDEO: 14,
  PERIPHERAL_PROTOCOL_HEALTHCARE: 15,
  PERIPHERAL_PROTOCOL_DIAGNOSTIC: 220,
  PERIPHERAL_PROTOCOL_WIRELESS_CTRL: 224,
  PERIPHERAL_PROTOCOL_MISCELLANEOUS: 239,
  PERIPHERAL_PROTOCOL_APP_SPECIFIC: 254,
  PERIPHERAL_PROTOCOL_VENDOR_SPECIFIC: 255
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.PolicyType = {
  POLICY_TYPE_DUMMY: 0,
  POLICY_TYPE_FIREWALL: 1,
  POLICY_TYPE_LONGITUDINAL_ENCRYPT: 2,
  POLICY_TYPE_ISOLATION_DEVICE: 3,
  POLICY_TYPE_SWITCH: 4,
  POLICY_TYPE_ROUTER: 5,
  POLICY_TYPE_OTHER: 99
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.LoginMethod = {
  LOGIN_METHOD_DUMMY: 0,
  LOGIN_METHOD_FTP: 1,
  LOGIN_METHOD_LOCAL: 2,
  LOGIN_METHOD_RADIUS: 3,
  LOGIN_METHOD_RDP: 4,
  LOGIN_METHOD_SSH: 5,
  LOGIN_METHOD_TELNET: 6,
  LOGIN_METHOD_TFTP: 7,
  LOGIN_METHOD_VNC: 8,
  LOGIN_METHOD_X11: 9,
  LOGIN_METHOD_SERIAL: 10,
  LOGIN_METHOD_OTHER: 99
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.LongitudinalPolicyProtocol = {
  LONGITUDINAL_PROTOCOL_ALL: 0,
  LONGITUDINAL_PROTOCOL_ICMP: 1,
  LONGITUDINAL_PROTOCOL_TCP: 2,
  LONGITUDINAL_PROTOCOL_UDP: 3,
  LONGITUDINAL_PROTOCOL_RESERVED: 4,
  LONGITUDINAL_PROTOCOL_MGMT_253: 5,
  LONGITUDINAL_PROTOCOL_MGMT_254: 6
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.DataSource = {
  DATA_SOURCE_DUMMY: 0,
  DATA_SOURCE_HOST_MONITORING: 1,
  DATA_SOURCE_SWITCH: 2,
  DATA_SOURCE_ROUTER: 4,
  DATA_SOURCE_ISOLATION_DEVICE: 8,
  DATA_SOURCE_VERTICAL_ENCRYPTION: 16,
  DATA_SOURCE_FIREWALL: 32,
  DATA_SOURCE_MONITORING_DEVICE: 64,
  DATA_SOURCE_MONITORING_PLATFORM: 128,
  DATA_SOURCE_OPERATION_GATEWAY: 256,
  DATA_SOURCE_ASSET_DETECTION: 512,
  DATA_SOURCE_TRAFFIC_COLLECTION: 1024,
  DATA_SOURCE_PLUGIN: 2048,
  DATA_SOURCE_DB: 4096,
  DATA_SOURCE_MALICIOUS_CODE_MONITORING: 8192,
  DATA_SOURCE_INTRUSION_DETECTION: 16384,
  DATA_SOURCE_SERVER_CIPHER_MACHINE: 32768,
  DATA_SOURCE_TRUSTED_VERIFICATION: 65536
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.PluginBusinessType = {
  PLUGIN_BUSINESS_DUMMY: 0,
  PLUGIN_BUSINESS_PERIPHERAL_CONTROL: 1,
  PLUGIN_BUSINESS_D60_MONITORING: 2,
  PLUGIN_BUSINESS_HOST_CONFIG_CHECK: 3,
  PLUGIN_BUSINESS_HOST_CONNECTION_DETECTION: 4,
  PLUGIN_BUSINESS_VULNERABILITY_SCAN: 5,
  PLUGIN_BUSINESS_BASIC_RULE_DETECTION: 6,
  PLUGIN_BUSINESS_INTELLIGENT_DETECTION: 7,
  PLUGIN_BUSINESS_ASSET_IDENTIFICATION: 8,
  PLUGIN_BUSINESS_SWITCH_COLLECTION: 9,
  PLUGIN_BUSINESS_FIREWALL_COLLECTION: 10,
  PLUGIN_BUSINESS_SYSLOG_PARSING: 11,
  PLUGIN_BUSINESS_HOST_TRUST_VERIFICATION: 12,
  PLUGIN_BUSINESS_MALICIOUS_CODE_MONITORING: 13,
  PLUGIN_BUSINESS_MAINTENANCE_GATEWAY_MONITORING: 14
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.MonitoringStatus = {
  MONITORING_STATUS_DUMMY: 0,
  MONITORING_STATUS_WEAK: 1,
  MONITORING_STATUS_STRONG: 2
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.SensorExceptionStatus = {
  SENSOR_EX_DUMMY: 0,
  SENSOR_EX_FAN_SPEED: 1,
  SENSOR_EX_TEMPERATURE: 2
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.RaidExceptionStatus = {
  RAID_EX_DUMMY: 0,
  RAID_EX_ABNORMAL: 1,
  RAID_EX_DISK_DAMAGED: 2,
  RAID_EX_DISK_OFFLINE: 4,
  RAID_EX_CTRL_DAMAGED: 8
};

goog.object.extend(exports, proto.com.jy.network_monitor.proto);
