/**
 * @fileoverview
 * @enhanceable
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!

var jspb = require('google-protobuf');
var goog = jspb;
var global = Function('return this')();

var google_protobuf_any_pb = require('google-protobuf/google/protobuf/any_pb.js');
goog.exportSymbol('proto.com.jy.network_monitor.proto.AssetConfig', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.AssetConfigSet', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.BehaviorMergeConfig', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.DataTransferConfig', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_CommunicationConfig', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PermissionLevel', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_EventConfig', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE.MtypeCode', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_RouteConfig', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SNTPConfig', null, global);

/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.repeatedFields_, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.displayName = 'proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig';
}
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.repeatedFields_ = [1,2];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.toObject = function(includeInstance, msg) {
  var f, obj = {
    netinterList: jspb.Message.toObjectList(msg.getNetinterList(),
    proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface.toObject, includeInstance),
    flowinterList: jspb.Message.toObjectList(msg.getFlowinterList(),
    proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig;
  return proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface;
      reader.readMessage(value,proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface.deserializeBinaryFromReader);
      msg.addNetinter(value);
      break;
    case 2:
      var value = new proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface;
      reader.readMessage(value,proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface.deserializeBinaryFromReader);
      msg.addFlowinter(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getNetinterList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface.serializeBinaryToWriter
    );
  }
  f = message.getFlowinterList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      2,
      f,
      proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface.serializeBinaryToWriter
    );
  }
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface.displayName = 'proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface.toObject = function(includeInstance, msg) {
  var f, obj = {
    pb_interface: jspb.Message.getFieldWithDefault(msg, 1, ""),
    netip: jspb.Message.getFieldWithDefault(msg, 2, ""),
    netmask: jspb.Message.getFieldWithDefault(msg, 3, ""),
    netmtu: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface;
  return proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setInterface(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setNetip(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setNetmask(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setNetmtu(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getInterface();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getNetip();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getNetmask();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getNetmtu();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
};


/**
 * optional string interface = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface.prototype.getInterface = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface.prototype.setInterface = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string netIp = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface.prototype.getNetip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface.prototype.setNetip = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string netmask = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface.prototype.getNetmask = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface.prototype.setNetmask = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional uint32 netMtu = 4;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface.prototype.getNetmtu = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface.prototype.setNetmtu = function(value) {
  jspb.Message.setProto3IntField(this, 4, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface.displayName = 'proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface.toObject = function(includeInstance, msg) {
  var f, obj = {
    pb_interface: jspb.Message.getFieldWithDefault(msg, 1, ""),
    area: jspb.Message.getFieldWithDefault(msg, 2, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface;
  return proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setInterface(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setArea(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getInterface();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getArea();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
};


/**
 * optional string interface = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface.prototype.getInterface = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface.prototype.setInterface = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional uint32 area = 2;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface.prototype.getArea = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface.prototype.setArea = function(value) {
  jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * repeated NetInterface netInter = 1;
 * @return {!Array<!proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface>}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.prototype.getNetinterList = function() {
  return /** @type{!Array<!proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface, 1));
};


/** @param {!Array<!proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface>} value */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.prototype.setNetinterList = function(value) {
  jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface=} opt_value
 * @param {number=} opt_index
 * @return {!proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.prototype.addNetinter = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.NetInterface, opt_index);
};


proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.prototype.clearNetinterList = function() {
  this.setNetinterList([]);
};


/**
 * repeated FlowInterface flowInter = 2;
 * @return {!Array<!proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface>}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.prototype.getFlowinterList = function() {
  return /** @type{!Array<!proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface, 2));
};


/** @param {!Array<!proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface>} value */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.prototype.setFlowinterList = function(value) {
  jspb.Message.setRepeatedWrapperField(this, 2, value);
};


/**
 * @param {!proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface=} opt_value
 * @param {number=} opt_index
 * @return {!proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.prototype.addFlowinter = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 2, opt_value, proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.FlowInterface, opt_index);
};


proto.com.jy.network_monitor.proto.MSG_NetworkInterfaceConfig.prototype.clearFlowinterList = function() {
  this.setFlowinterList([]);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_RouteConfig = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_RouteConfig, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_RouteConfig.displayName = 'proto.com.jy.network_monitor.proto.MSG_RouteConfig';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_RouteConfig.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_RouteConfig.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_RouteConfig} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_RouteConfig.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, 0),
    dstnetseg: jspb.Message.getFieldWithDefault(msg, 2, ""),
    dstnetmask: jspb.Message.getFieldWithDefault(msg, 3, ""),
    netgateway: jspb.Message.getFieldWithDefault(msg, 4, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_RouteConfig}
 */
proto.com.jy.network_monitor.proto.MSG_RouteConfig.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_RouteConfig;
  return proto.com.jy.network_monitor.proto.MSG_RouteConfig.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_RouteConfig} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_RouteConfig}
 */
proto.com.jy.network_monitor.proto.MSG_RouteConfig.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setDstnetseg(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setDstnetmask(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setNetgateway(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_RouteConfig.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_RouteConfig.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_RouteConfig} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_RouteConfig.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getDstnetseg();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getDstnetmask();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getNetgateway();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
};


/**
 * optional int32 id = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_RouteConfig.prototype.getId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_RouteConfig.prototype.setId = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string dstNetSeg = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_RouteConfig.prototype.getDstnetseg = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_RouteConfig.prototype.setDstnetseg = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string dstNetMask = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_RouteConfig.prototype.getDstnetmask = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_RouteConfig.prototype.setDstnetmask = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string netGateway = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_RouteConfig.prototype.getNetgateway = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_RouteConfig.prototype.setNetgateway = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SNTPConfig = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SNTPConfig, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SNTPConfig.displayName = 'proto.com.jy.network_monitor.proto.MSG_SNTPConfig';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SNTPConfig.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SNTPConfig.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SNTPConfig} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SNTPConfig.toObject = function(includeInstance, msg) {
  var f, obj = {
    mainclockmainnet: jspb.Message.getFieldWithDefault(msg, 1, ""),
    mainclocksubnet: jspb.Message.getFieldWithDefault(msg, 2, ""),
    subclockmainnet: jspb.Message.getFieldWithDefault(msg, 3, ""),
    subclocksubnet: jspb.Message.getFieldWithDefault(msg, 4, ""),
    ports: jspb.Message.getFieldWithDefault(msg, 5, 0),
    synperiod: jspb.Message.getFieldWithDefault(msg, 6, 0),
    broadcast: jspb.Message.getFieldWithDefault(msg, 7, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SNTPConfig}
 */
proto.com.jy.network_monitor.proto.MSG_SNTPConfig.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SNTPConfig;
  return proto.com.jy.network_monitor.proto.MSG_SNTPConfig.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SNTPConfig} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SNTPConfig}
 */
proto.com.jy.network_monitor.proto.MSG_SNTPConfig.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setMainclockmainnet(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMainclocksubnet(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setSubclockmainnet(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setSubclocksubnet(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setPorts(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setSynperiod(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setBroadcast(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SNTPConfig.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SNTPConfig.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SNTPConfig} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SNTPConfig.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMainclockmainnet();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getMainclocksubnet();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSubclockmainnet();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getSubclocksubnet();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getPorts();
  if (f !== 0) {
    writer.writeUint32(
      5,
      f
    );
  }
  f = message.getSynperiod();
  if (f !== 0) {
    writer.writeUint32(
      6,
      f
    );
  }
  f = message.getBroadcast();
  if (f !== 0) {
    writer.writeUint32(
      7,
      f
    );
  }
};


/**
 * optional string mainClockMainNet = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SNTPConfig.prototype.getMainclockmainnet = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SNTPConfig.prototype.setMainclockmainnet = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string mainClockSubNet = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SNTPConfig.prototype.getMainclocksubnet = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SNTPConfig.prototype.setMainclocksubnet = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string subClockMainNet = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SNTPConfig.prototype.getSubclockmainnet = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SNTPConfig.prototype.setSubclockmainnet = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string subClockSubNet = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SNTPConfig.prototype.getSubclocksubnet = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SNTPConfig.prototype.setSubclocksubnet = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional uint32 ports = 5;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SNTPConfig.prototype.getPorts = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SNTPConfig.prototype.setPorts = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional uint32 synPeriod = 6;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SNTPConfig.prototype.getSynperiod = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SNTPConfig.prototype.setSynperiod = function(value) {
  jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional uint32 broadcast = 7;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SNTPConfig.prototype.getBroadcast = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SNTPConfig.prototype.setBroadcast = function(value) {
  jspb.Message.setProto3IntField(this, 7, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.repeatedFields_, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_CommunicationConfig, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.displayName = 'proto.com.jy.network_monitor.proto.MSG_CommunicationConfig';
}
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.repeatedFields_ = [5];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_CommunicationConfig} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.toObject = function(includeInstance, msg) {
  var f, obj = {
    servport: jspb.Message.getFieldWithDefault(msg, 1, 0),
    selfport: jspb.Message.getFieldWithDefault(msg, 2, 0),
    syslogport: jspb.Message.getFieldWithDefault(msg, 3, 0),
    trapport: jspb.Message.getFieldWithDefault(msg, 4, 0),
    platinfoList: jspb.Message.toObjectList(msg.getPlatinfoList(),
    proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_CommunicationConfig}
 */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_CommunicationConfig;
  return proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_CommunicationConfig} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_CommunicationConfig}
 */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setServport(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setSelfport(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setSyslogport(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setTrapport(value);
      break;
    case 5:
      var value = new proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo;
      reader.readMessage(value,proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo.deserializeBinaryFromReader);
      msg.addPlatinfo(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_CommunicationConfig} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getServport();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getSelfport();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getSyslogport();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getTrapport();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
  f = message.getPlatinfoList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      5,
      f,
      proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo.serializeBinaryToWriter
    );
  }
};


/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PermissionLevel = {
  PERMISSIONLEVEL_NIL: 0,
  EVENT_UPLOAD: 1,
  DCD_PARAM_READ: 2,
  DCD_PARAM_WRITE: 3,
  OBJECT_CONTROL: 4,
  REGISTER: 5
};


/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo.displayName = 'proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo.toObject = function(includeInstance, msg) {
  var f, obj = {
    platip: jspb.Message.getFieldWithDefault(msg, 1, ""),
    permission: jspb.Message.getFieldWithDefault(msg, 2, 0),
    groupid: jspb.Message.getFieldWithDefault(msg, 3, 0),
    grouppriority: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo}
 */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo;
  return proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo}
 */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setPlatip(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setPermission(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setGroupid(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setGrouppriority(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPlatip();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getPermission();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getGroupid();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getGrouppriority();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
};


/**
 * optional string platIP = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo.prototype.getPlatip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo.prototype.setPlatip = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional uint32 permission = 2;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo.prototype.getPermission = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo.prototype.setPermission = function(value) {
  jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 groupId = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo.prototype.getGroupid = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo.prototype.setGroupid = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional uint32 groupPriority = 4;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo.prototype.getGrouppriority = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo.prototype.setGrouppriority = function(value) {
  jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional uint32 servPort = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.prototype.getServport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.prototype.setServport = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional uint32 selfPort = 2;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.prototype.getSelfport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.prototype.setSelfport = function(value) {
  jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 SYSLOGPort = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.prototype.getSyslogport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.prototype.setSyslogport = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional uint32 TRAPPort = 4;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.prototype.getTrapport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.prototype.setTrapport = function(value) {
  jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * repeated PlatformInfo platInfo = 5;
 * @return {!Array<!proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo>}
 */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.prototype.getPlatinfoList = function() {
  return /** @type{!Array<!proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo, 5));
};


/** @param {!Array<!proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo>} value */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.prototype.setPlatinfoList = function(value) {
  jspb.Message.setRepeatedWrapperField(this, 5, value);
};


/**
 * @param {!proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo=} opt_value
 * @param {number=} opt_index
 * @return {!proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo}
 */
proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.prototype.addPlatinfo = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 5, opt_value, proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.PlatformInfo, opt_index);
};


proto.com.jy.network_monitor.proto.MSG_CommunicationConfig.prototype.clearPlatinfoList = function() {
  this.setPlatinfoList([]);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_EventConfig = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_EventConfig, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_EventConfig.displayName = 'proto.com.jy.network_monitor.proto.MSG_EventConfig';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_EventConfig.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_EventConfig.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_EventConfig} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_EventConfig.toObject = function(includeInstance, msg) {
  var f, obj = {
    actionmercyc: jspb.Message.getFieldWithDefault(msg, 1, 0),
    devicenoticecyc: jspb.Message.getFieldWithDefault(msg, 2, 0),
    hiseventuploadtime: jspb.Message.getFieldWithDefault(msg, 3, 0),
    maxstorrat: jspb.Message.getFieldWithDefault(msg, 4, 0),
    modelstomax: jspb.Message.getFieldWithDefault(msg, 5, 0),
    collectstomax: jspb.Message.getFieldWithDefault(msg, 6, 0),
    flowstomax: jspb.Message.getFieldWithDefault(msg, 7, 0),
    otherstomax: jspb.Message.getFieldWithDefault(msg, 8, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_EventConfig}
 */
proto.com.jy.network_monitor.proto.MSG_EventConfig.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_EventConfig;
  return proto.com.jy.network_monitor.proto.MSG_EventConfig.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_EventConfig} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_EventConfig}
 */
proto.com.jy.network_monitor.proto.MSG_EventConfig.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setActionmercyc(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setDevicenoticecyc(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setHiseventuploadtime(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setMaxstorrat(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setModelstomax(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setCollectstomax(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setFlowstomax(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setOtherstomax(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_EventConfig.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_EventConfig.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_EventConfig} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_EventConfig.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getActionmercyc();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getDevicenoticecyc();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getHiseventuploadtime();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getMaxstorrat();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
  f = message.getModelstomax();
  if (f !== 0) {
    writer.writeUint32(
      5,
      f
    );
  }
  f = message.getCollectstomax();
  if (f !== 0) {
    writer.writeUint32(
      6,
      f
    );
  }
  f = message.getFlowstomax();
  if (f !== 0) {
    writer.writeUint32(
      7,
      f
    );
  }
  f = message.getOtherstomax();
  if (f !== 0) {
    writer.writeUint32(
      8,
      f
    );
  }
};


/**
 * optional uint32 actionMerCyc = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_EventConfig.prototype.getActionmercyc = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_EventConfig.prototype.setActionmercyc = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional uint32 deviceNoticeCyc = 2;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_EventConfig.prototype.getDevicenoticecyc = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_EventConfig.prototype.setDevicenoticecyc = function(value) {
  jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 hisEventUploadTime = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_EventConfig.prototype.getHiseventuploadtime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_EventConfig.prototype.setHiseventuploadtime = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional uint32 maxStorRat = 4;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_EventConfig.prototype.getMaxstorrat = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_EventConfig.prototype.setMaxstorrat = function(value) {
  jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional uint32 modelStoMAX = 5;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_EventConfig.prototype.getModelstomax = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_EventConfig.prototype.setModelstomax = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional uint32 collectStoMAX = 6;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_EventConfig.prototype.getCollectstomax = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_EventConfig.prototype.setCollectstomax = function(value) {
  jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional uint32 flowStoMAX = 7;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_EventConfig.prototype.getFlowstomax = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_EventConfig.prototype.setFlowstomax = function(value) {
  jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional uint32 otherStoMax = 8;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_EventConfig.prototype.getOtherstomax = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_EventConfig.prototype.setOtherstomax = function(value) {
  jspb.Message.setProto3IntField(this, 8, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig.displayName = 'proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig.toObject = function(includeInstance, msg) {
  var f, obj = {
    cpumax: jspb.Message.getFieldWithDefault(msg, 1, 0),
    memmax: jspb.Message.getFieldWithDefault(msg, 2, 0),
    diskmax: jspb.Message.getFieldWithDefault(msg, 3, 0),
    logfailmax: jspb.Message.getFieldWithDefault(msg, 4, 0),
    mergecycle: jspb.Message.getFieldWithDefault(msg, 5, 0),
    offtimejudge: jspb.Message.getFieldWithDefault(msg, 6, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig}
 */
proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig;
  return proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig}
 */
proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setCpumax(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setMemmax(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setDiskmax(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setLogfailmax(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setMergecycle(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setOfftimejudge(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getCpumax();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getMemmax();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getDiskmax();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getLogfailmax();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
  f = message.getMergecycle();
  if (f !== 0) {
    writer.writeUint32(
      5,
      f
    );
  }
  f = message.getOfftimejudge();
  if (f !== 0) {
    writer.writeUint32(
      6,
      f
    );
  }
};


/**
 * optional uint32 cpuMax = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig.prototype.getCpumax = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig.prototype.setCpumax = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional uint32 memMax = 2;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig.prototype.getMemmax = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig.prototype.setMemmax = function(value) {
  jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 diskMax = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig.prototype.getDiskmax = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig.prototype.setDiskmax = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional uint32 logfailMax = 4;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig.prototype.getLogfailmax = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig.prototype.setLogfailmax = function(value) {
  jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional uint32 mergeCycle = 5;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig.prototype.getMergecycle = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig.prototype.setMergecycle = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional uint32 offTimeJudge = 6;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig.prototype.getOfftimejudge = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DCDRuntimeConfig.prototype.setOfftimejudge = function(value) {
  jspb.Message.setProto3IntField(this, 6, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.DataTransferConfig = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.DataTransferConfig, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.DataTransferConfig.displayName = 'proto.com.jy.network_monitor.proto.DataTransferConfig';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.DataTransferConfig.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.DataTransferConfig.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.DataTransferConfig} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.DataTransferConfig.toObject = function(includeInstance, msg) {
  var f, obj = {
    functype: jspb.Message.getFieldWithDefault(msg, 1, 0),
    logtype: jspb.Message.getFieldWithDefault(msg, 2, 0),
    funcopt: jspb.Message.getFieldWithDefault(msg, 3, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.DataTransferConfig}
 */
proto.com.jy.network_monitor.proto.DataTransferConfig.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.DataTransferConfig;
  return proto.com.jy.network_monitor.proto.DataTransferConfig.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.DataTransferConfig} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.DataTransferConfig}
 */
proto.com.jy.network_monitor.proto.DataTransferConfig.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setFunctype(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setLogtype(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setFuncopt(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.DataTransferConfig.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.DataTransferConfig.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.DataTransferConfig} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.DataTransferConfig.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFunctype();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getLogtype();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getFuncopt();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
};


/**
 * optional uint32 funcType = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.DataTransferConfig.prototype.getFunctype = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.DataTransferConfig.prototype.setFunctype = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional uint32 logType = 2;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.DataTransferConfig.prototype.getLogtype = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.DataTransferConfig.prototype.setLogtype = function(value) {
  jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional string funcOpt = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.DataTransferConfig.prototype.getFuncopt = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.DataTransferConfig.prototype.setFuncopt = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.BehaviorMergeConfig = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.BehaviorMergeConfig, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.BehaviorMergeConfig.displayName = 'proto.com.jy.network_monitor.proto.BehaviorMergeConfig';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.BehaviorMergeConfig.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.BehaviorMergeConfig.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.BehaviorMergeConfig} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.BehaviorMergeConfig.toObject = function(includeInstance, msg) {
  var f, obj = {
    modeltype: jspb.Message.getFieldWithDefault(msg, 1, 0),
    flag: jspb.Message.getFieldWithDefault(msg, 2, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.BehaviorMergeConfig}
 */
proto.com.jy.network_monitor.proto.BehaviorMergeConfig.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.BehaviorMergeConfig;
  return proto.com.jy.network_monitor.proto.BehaviorMergeConfig.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.BehaviorMergeConfig} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.BehaviorMergeConfig}
 */
proto.com.jy.network_monitor.proto.BehaviorMergeConfig.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setModeltype(value);
      break;
    case 2:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setFlag(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.BehaviorMergeConfig.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.BehaviorMergeConfig.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.BehaviorMergeConfig} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.BehaviorMergeConfig.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getModeltype();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getFlag();
  if (f) {
    writer.writeBool(
      2,
      f
    );
  }
};


/**
 * optional uint32 modelType = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.BehaviorMergeConfig.prototype.getModeltype = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.BehaviorMergeConfig.prototype.setModeltype = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional bool flag = 2;
 * Note that Boolean fields may be set to 0/1 when serialized from a Java server.
 * You should avoid comparisons like {@code val === true/false} in those cases.
 * @return {boolean}
 */
proto.com.jy.network_monitor.proto.BehaviorMergeConfig.prototype.getFlag = function() {
  return /** @type {boolean} */ (jspb.Message.getFieldWithDefault(this, 2, false));
};


/** @param {boolean} value */
proto.com.jy.network_monitor.proto.BehaviorMergeConfig.prototype.setFlag = function(value) {
  jspb.Message.setProto3BooleanField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.AssetConfigSet = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.com.jy.network_monitor.proto.AssetConfigSet.repeatedFields_, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.AssetConfigSet, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.AssetConfigSet.displayName = 'proto.com.jy.network_monitor.proto.AssetConfigSet';
}
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.com.jy.network_monitor.proto.AssetConfigSet.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.AssetConfigSet.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.AssetConfigSet.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.AssetConfigSet} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.AssetConfigSet.toObject = function(includeInstance, msg) {
  var f, obj = {
    assetconfigsList: jspb.Message.toObjectList(msg.getAssetconfigsList(),
    proto.com.jy.network_monitor.proto.AssetConfig.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.AssetConfigSet}
 */
proto.com.jy.network_monitor.proto.AssetConfigSet.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.AssetConfigSet;
  return proto.com.jy.network_monitor.proto.AssetConfigSet.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.AssetConfigSet} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.AssetConfigSet}
 */
proto.com.jy.network_monitor.proto.AssetConfigSet.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.com.jy.network_monitor.proto.AssetConfig;
      reader.readMessage(value,proto.com.jy.network_monitor.proto.AssetConfig.deserializeBinaryFromReader);
      msg.addAssetconfigs(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.AssetConfigSet.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.AssetConfigSet.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.AssetConfigSet} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.AssetConfigSet.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAssetconfigsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.com.jy.network_monitor.proto.AssetConfig.serializeBinaryToWriter
    );
  }
};


/**
 * repeated AssetConfig assetConfigs = 1;
 * @return {!Array<!proto.com.jy.network_monitor.proto.AssetConfig>}
 */
proto.com.jy.network_monitor.proto.AssetConfigSet.prototype.getAssetconfigsList = function() {
  return /** @type{!Array<!proto.com.jy.network_monitor.proto.AssetConfig>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.com.jy.network_monitor.proto.AssetConfig, 1));
};


/** @param {!Array<!proto.com.jy.network_monitor.proto.AssetConfig>} value */
proto.com.jy.network_monitor.proto.AssetConfigSet.prototype.setAssetconfigsList = function(value) {
  jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.com.jy.network_monitor.proto.AssetConfig=} opt_value
 * @param {number=} opt_index
 * @return {!proto.com.jy.network_monitor.proto.AssetConfig}
 */
proto.com.jy.network_monitor.proto.AssetConfigSet.prototype.addAssetconfigs = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.com.jy.network_monitor.proto.AssetConfig, opt_index);
};


proto.com.jy.network_monitor.proto.AssetConfigSet.prototype.clearAssetconfigsList = function() {
  this.setAssetconfigsList([]);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.AssetConfig = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.com.jy.network_monitor.proto.AssetConfig.repeatedFields_, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.AssetConfig, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.AssetConfig.displayName = 'proto.com.jy.network_monitor.proto.AssetConfig';
}
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.com.jy.network_monitor.proto.AssetConfig.repeatedFields_ = [2,5];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.AssetConfig.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.AssetConfig.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.AssetConfig} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.AssetConfig.toObject = function(includeInstance, msg) {
  var f, obj = {
    asset: (f = msg.getAsset()) && proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET.toObject(includeInstance, f),
    assetNodeList: jspb.Message.toObjectList(msg.getAssetNodeList(),
    proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE.toObject, includeInstance),
    interconf: (f = msg.getInterconf()) && proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.toObject(includeInstance, f),
    management: (f = msg.getManagement()) && proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.toObject(includeInstance, f),
    applicationList: jspb.Message.toObjectList(msg.getApplicationList(),
    proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.AssetConfig}
 */
proto.com.jy.network_monitor.proto.AssetConfig.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.AssetConfig;
  return proto.com.jy.network_monitor.proto.AssetConfig.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.AssetConfig} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.AssetConfig}
 */
proto.com.jy.network_monitor.proto.AssetConfig.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET;
      reader.readMessage(value,proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET.deserializeBinaryFromReader);
      msg.setAsset(value);
      break;
    case 2:
      var value = new proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE;
      reader.readMessage(value,proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE.deserializeBinaryFromReader);
      msg.addAssetNode(value);
      break;
    case 3:
      var value = new proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF;
      reader.readMessage(value,proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.deserializeBinaryFromReader);
      msg.setInterconf(value);
      break;
    case 4:
      var value = new proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT;
      reader.readMessage(value,proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.deserializeBinaryFromReader);
      msg.setManagement(value);
      break;
    case 5:
      var value = new proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION;
      reader.readMessage(value,proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.deserializeBinaryFromReader);
      msg.addApplication(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.AssetConfig.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.AssetConfig.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.AssetConfig} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.AssetConfig.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAsset();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET.serializeBinaryToWriter
    );
  }
  f = message.getAssetNodeList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      2,
      f,
      proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE.serializeBinaryToWriter
    );
  }
  f = message.getInterconf();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.serializeBinaryToWriter
    );
  }
  f = message.getManagement();
  if (f != null) {
    writer.writeMessage(
      4,
      f,
      proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.serializeBinaryToWriter
    );
  }
  f = message.getApplicationList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      5,
      f,
      proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.serializeBinaryToWriter
    );
  }
};


/**
 * optional MSG_ParamConfig_ASSET ASSET = 1;
 * @return {?proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET}
 */
proto.com.jy.network_monitor.proto.AssetConfig.prototype.getAsset = function() {
  return /** @type{?proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET} */ (
    jspb.Message.getWrapperField(this, proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET, 1));
};


/** @param {?proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET|undefined} value */
proto.com.jy.network_monitor.proto.AssetConfig.prototype.setAsset = function(value) {
  jspb.Message.setWrapperField(this, 1, value);
};


proto.com.jy.network_monitor.proto.AssetConfig.prototype.clearAsset = function() {
  this.setAsset(undefined);
};


/**
 * Returns whether this field is set.
 * @return {!boolean}
 */
proto.com.jy.network_monitor.proto.AssetConfig.prototype.hasAsset = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * repeated MSG_ParamConfig_ASSET_NODE ASSET_NODE = 2;
 * @return {!Array<!proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE>}
 */
proto.com.jy.network_monitor.proto.AssetConfig.prototype.getAssetNodeList = function() {
  return /** @type{!Array<!proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE, 2));
};


/** @param {!Array<!proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE>} value */
proto.com.jy.network_monitor.proto.AssetConfig.prototype.setAssetNodeList = function(value) {
  jspb.Message.setRepeatedWrapperField(this, 2, value);
};


/**
 * @param {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE=} opt_value
 * @param {number=} opt_index
 * @return {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE}
 */
proto.com.jy.network_monitor.proto.AssetConfig.prototype.addAssetNode = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 2, opt_value, proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE, opt_index);
};


proto.com.jy.network_monitor.proto.AssetConfig.prototype.clearAssetNodeList = function() {
  this.setAssetNodeList([]);
};


/**
 * optional MSG_ParamConfig_INTERCONF INTERCONF = 3;
 * @return {?proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF}
 */
proto.com.jy.network_monitor.proto.AssetConfig.prototype.getInterconf = function() {
  return /** @type{?proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF} */ (
    jspb.Message.getWrapperField(this, proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF, 3));
};


/** @param {?proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF|undefined} value */
proto.com.jy.network_monitor.proto.AssetConfig.prototype.setInterconf = function(value) {
  jspb.Message.setWrapperField(this, 3, value);
};


proto.com.jy.network_monitor.proto.AssetConfig.prototype.clearInterconf = function() {
  this.setInterconf(undefined);
};


/**
 * Returns whether this field is set.
 * @return {!boolean}
 */
proto.com.jy.network_monitor.proto.AssetConfig.prototype.hasInterconf = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional MSG_ParamConfig_MANAGEMENT MANAGEMENT = 4;
 * @return {?proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT}
 */
proto.com.jy.network_monitor.proto.AssetConfig.prototype.getManagement = function() {
  return /** @type{?proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT} */ (
    jspb.Message.getWrapperField(this, proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT, 4));
};


/** @param {?proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT|undefined} value */
proto.com.jy.network_monitor.proto.AssetConfig.prototype.setManagement = function(value) {
  jspb.Message.setWrapperField(this, 4, value);
};


proto.com.jy.network_monitor.proto.AssetConfig.prototype.clearManagement = function() {
  this.setManagement(undefined);
};


/**
 * Returns whether this field is set.
 * @return {!boolean}
 */
proto.com.jy.network_monitor.proto.AssetConfig.prototype.hasManagement = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * repeated MSG_ParamConfig_APPLICATION APPLICATION = 5;
 * @return {!Array<!proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION>}
 */
proto.com.jy.network_monitor.proto.AssetConfig.prototype.getApplicationList = function() {
  return /** @type{!Array<!proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION, 5));
};


/** @param {!Array<!proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION>} value */
proto.com.jy.network_monitor.proto.AssetConfig.prototype.setApplicationList = function(value) {
  jspb.Message.setRepeatedWrapperField(this, 5, value);
};


/**
 * @param {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION=} opt_value
 * @param {number=} opt_index
 * @return {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION}
 */
proto.com.jy.network_monitor.proto.AssetConfig.prototype.addApplication = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 5, opt_value, proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION, opt_index);
};


proto.com.jy.network_monitor.proto.AssetConfig.prototype.clearApplicationList = function() {
  this.setApplicationList([]);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET.displayName = 'proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET.toObject = function(includeInstance, msg) {
  var f, obj = {
    gid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    atag: jspb.Message.getFieldWithDefault(msg, 2, ""),
    dname: jspb.Message.getFieldWithDefault(msg, 3, ""),
    type: jspb.Message.getFieldWithDefault(msg, 4, 0),
    label: jspb.Message.getFieldWithDefault(msg, 5, ""),
    source: jspb.Message.getFieldWithDefault(msg, 6, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET;
  return proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setGid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setAtag(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setDname(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setType(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setLabel(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSource(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getGid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getAtag();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getDname();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getType();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
  f = message.getLabel();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getSource();
  if (f !== 0) {
    writer.writeInt32(
      6,
      f
    );
  }
};


/**
 * optional string GID = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET.prototype.getGid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET.prototype.setGid = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string ATAG = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET.prototype.getAtag = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET.prototype.setAtag = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string DNAME = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET.prototype.getDname = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET.prototype.setDname = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional int32 TYPE = 4;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET.prototype.getType = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET.prototype.setType = function(value) {
  jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional string LABEL = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET.prototype.getLabel = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET.prototype.setLabel = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional int32 SOURCE = 6;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET.prototype.getSource = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET.prototype.setSource = function(value) {
  jspb.Message.setProto3IntField(this, 6, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE.displayName = 'proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE.toObject = function(includeInstance, msg) {
  var f, obj = {
    ndmtype: jspb.Message.getFieldWithDefault(msg, 1, 0),
    gid: jspb.Message.getFieldWithDefault(msg, 2, ""),
    ip: jspb.Message.getFieldWithDefault(msg, 3, ""),
    mac: jspb.Message.getFieldWithDefault(msg, 4, ""),
    ifid: jspb.Message.getFieldWithDefault(msg, 5, ""),
    source: jspb.Message.getFieldWithDefault(msg, 6, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE;
  return proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setNdmtype(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setGid(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setIp(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setMac(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setIfid(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSource(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getNdmtype();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getGid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getIp();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getMac();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getIfid();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getSource();
  if (f !== 0) {
    writer.writeInt32(
      6,
      f
    );
  }
};


/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE.MtypeCode = {
  MTYPE_DUMMY: 0,
  MTYPE_ADD: 1,
  MTYPE_MODIFY: 2,
  MTYPE_DELETE: 3
};

/**
 * optional int32 NDMTYPE = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE.prototype.getNdmtype = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE.prototype.setNdmtype = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string GID = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE.prototype.getGid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE.prototype.setGid = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string IP = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE.prototype.getIp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE.prototype.setIp = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string MAC = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE.prototype.getMac = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE.prototype.setMac = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string IFID = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE.prototype.getIfid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE.prototype.setIfid = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional int32 SOURCE = 6;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE.prototype.getSource = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_ASSET_NODE.prototype.setSource = function(value) {
  jspb.Message.setProto3IntField(this, 6, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.displayName = 'proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.toObject = function(includeInstance, msg) {
  var f, obj = {
    rmtport: jspb.Message.getFieldWithDefault(msg, 1, 0),
    rmttp: jspb.Message.getFieldWithDefault(msg, 2, 0),
    rmtact: jspb.Message.getFieldWithDefault(msg, 3, ""),
    rmtpsd: jspb.Message.getFieldWithDefault(msg, 4, ""),
    snmpport: jspb.Message.getFieldWithDefault(msg, 5, 0),
    snmpver: jspb.Message.getFieldWithDefault(msg, 6, 0),
    snmpv3user: jspb.Message.getFieldWithDefault(msg, 7, ""),
    snmpauth: jspb.Message.getFieldWithDefault(msg, 8, 0),
    snmpenc: jspb.Message.getFieldWithDefault(msg, 9, 0),
    snmpread: jspb.Message.getFieldWithDefault(msg, 10, ""),
    snmpwrite: jspb.Message.getFieldWithDefault(msg, 11, ""),
    anameth: jspb.Message.getFieldWithDefault(msg, 12, 0),
    statusupflag: jspb.Message.getFieldWithDefault(msg, 13, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF;
  return proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRmtport(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRmttp(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setRmtact(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setRmtpsd(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSnmpport(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSnmpver(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setSnmpv3user(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSnmpauth(value);
      break;
    case 9:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSnmpenc(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setSnmpread(value);
      break;
    case 11:
      var value = /** @type {string} */ (reader.readString());
      msg.setSnmpwrite(value);
      break;
    case 12:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setAnameth(value);
      break;
    case 13:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setStatusupflag(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRmtport();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getRmttp();
  if (f !== 0) {
    writer.writeInt32(
      2,
      f
    );
  }
  f = message.getRmtact();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getRmtpsd();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getSnmpport();
  if (f !== 0) {
    writer.writeInt32(
      5,
      f
    );
  }
  f = message.getSnmpver();
  if (f !== 0) {
    writer.writeInt32(
      6,
      f
    );
  }
  f = message.getSnmpv3user();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getSnmpauth();
  if (f !== 0) {
    writer.writeInt32(
      8,
      f
    );
  }
  f = message.getSnmpenc();
  if (f !== 0) {
    writer.writeInt32(
      9,
      f
    );
  }
  f = message.getSnmpread();
  if (f.length > 0) {
    writer.writeString(
      10,
      f
    );
  }
  f = message.getSnmpwrite();
  if (f.length > 0) {
    writer.writeString(
      11,
      f
    );
  }
  f = message.getAnameth();
  if (f !== 0) {
    writer.writeInt32(
      12,
      f
    );
  }
  f = message.getStatusupflag();
  if (f !== 0) {
    writer.writeInt32(
      13,
      f
    );
  }
};


/**
 * optional int32 RMTPORT = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.prototype.getRmtport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.prototype.setRmtport = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional int32 RMTTP = 2;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.prototype.getRmttp = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.prototype.setRmttp = function(value) {
  jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional string RMTACT = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.prototype.getRmtact = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.prototype.setRmtact = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string RMTPSD = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.prototype.getRmtpsd = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.prototype.setRmtpsd = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional int32 SNMPPORT = 5;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.prototype.getSnmpport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.prototype.setSnmpport = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional int32 SNMPVER = 6;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.prototype.getSnmpver = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.prototype.setSnmpver = function(value) {
  jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional string SNMPV3USER = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.prototype.getSnmpv3user = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.prototype.setSnmpv3user = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional int32 SNMPAUTH = 8;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.prototype.getSnmpauth = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.prototype.setSnmpauth = function(value) {
  jspb.Message.setProto3IntField(this, 8, value);
};


/**
 * optional int32 SNMPENC = 9;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.prototype.getSnmpenc = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 9, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.prototype.setSnmpenc = function(value) {
  jspb.Message.setProto3IntField(this, 9, value);
};


/**
 * optional string SNMPREAD = 10;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.prototype.getSnmpread = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.prototype.setSnmpread = function(value) {
  jspb.Message.setProto3StringField(this, 10, value);
};


/**
 * optional string SNMPWRITE = 11;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.prototype.getSnmpwrite = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 11, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.prototype.setSnmpwrite = function(value) {
  jspb.Message.setProto3StringField(this, 11, value);
};


/**
 * optional int32 ANAMETH = 12;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.prototype.getAnameth = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 12, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.prototype.setAnameth = function(value) {
  jspb.Message.setProto3IntField(this, 12, value);
};


/**
 * optional int32 STATUSUPFLAG = 13;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.prototype.getStatusupflag = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 13, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_INTERCONF.prototype.setStatusupflag = function(value) {
  jspb.Message.setProto3IntField(this, 13, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.displayName = 'proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.toObject = function(includeInstance, msg) {
  var f, obj = {
    dcloudid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    dccid: jspb.Message.getFieldWithDefault(msg, 2, ""),
    staid: jspb.Message.getFieldWithDefault(msg, 3, ""),
    fname: jspb.Message.getFieldWithDefault(msg, 4, ""),
    sname: jspb.Message.getFieldWithDefault(msg, 5, ""),
    atype: jspb.Message.getFieldWithDefault(msg, 6, 0),
    abrand: jspb.Message.getFieldWithDefault(msg, 7, ""),
    amodel: jspb.Message.getFieldWithDefault(msg, 8, ""),
    status: jspb.Message.getFieldWithDefault(msg, 9, 0),
    iscii: jspb.Message.getFieldWithDefault(msg, 10, false),
    pb_class: jspb.Message.getFieldWithDefault(msg, 11, 0),
    mtype: jspb.Message.getFieldWithDefault(msg, 12, false),
    nttype: jspb.Message.getFieldWithDefault(msg, 13, 0),
    area: jspb.Message.getFieldWithDefault(msg, 14, 0),
    notes: jspb.Message.getFieldWithDefault(msg, 15, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT;
  return proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setDcloudid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setDccid(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setStaid(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setFname(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setSname(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setAtype(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setAbrand(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setAmodel(value);
      break;
    case 9:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setStatus(value);
      break;
    case 10:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIscii(value);
      break;
    case 11:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setClass(value);
      break;
    case 12:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setMtype(value);
      break;
    case 13:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setNttype(value);
      break;
    case 14:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setArea(value);
      break;
    case 15:
      var value = /** @type {string} */ (reader.readString());
      msg.setNotes(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getDcloudid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getDccid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getStaid();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getFname();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getSname();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getAtype();
  if (f !== 0) {
    writer.writeInt32(
      6,
      f
    );
  }
  f = message.getAbrand();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getAmodel();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getStatus();
  if (f !== 0) {
    writer.writeInt32(
      9,
      f
    );
  }
  f = message.getIscii();
  if (f) {
    writer.writeBool(
      10,
      f
    );
  }
  f = message.getClass();
  if (f !== 0) {
    writer.writeInt32(
      11,
      f
    );
  }
  f = message.getMtype();
  if (f) {
    writer.writeBool(
      12,
      f
    );
  }
  f = message.getNttype();
  if (f !== 0) {
    writer.writeInt32(
      13,
      f
    );
  }
  f = message.getArea();
  if (f !== 0) {
    writer.writeInt32(
      14,
      f
    );
  }
  f = message.getNotes();
  if (f.length > 0) {
    writer.writeString(
      15,
      f
    );
  }
};


/**
 * optional string DCLOUDID = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.getDcloudid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.setDcloudid = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string DCCID = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.getDccid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.setDccid = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string STAID = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.getStaid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.setStaid = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string FNAME = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.getFname = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.setFname = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string SNAME = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.getSname = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.setSname = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional int32 ATYPE = 6;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.getAtype = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.setAtype = function(value) {
  jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional string ABRAND = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.getAbrand = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.setAbrand = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string AMODEL = 8;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.getAmodel = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.setAmodel = function(value) {
  jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional int32 STATUS = 9;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.getStatus = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 9, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.setStatus = function(value) {
  jspb.Message.setProto3IntField(this, 9, value);
};


/**
 * optional bool ISCII = 10;
 * Note that Boolean fields may be set to 0/1 when serialized from a Java server.
 * You should avoid comparisons like {@code val === true/false} in those cases.
 * @return {boolean}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.getIscii = function() {
  return /** @type {boolean} */ (jspb.Message.getFieldWithDefault(this, 10, false));
};


/** @param {boolean} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.setIscii = function(value) {
  jspb.Message.setProto3BooleanField(this, 10, value);
};


/**
 * optional int32 CLASS = 11;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.getClass = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 11, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.setClass = function(value) {
  jspb.Message.setProto3IntField(this, 11, value);
};


/**
 * optional bool MTYPE = 12;
 * Note that Boolean fields may be set to 0/1 when serialized from a Java server.
 * You should avoid comparisons like {@code val === true/false} in those cases.
 * @return {boolean}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.getMtype = function() {
  return /** @type {boolean} */ (jspb.Message.getFieldWithDefault(this, 12, false));
};


/** @param {boolean} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.setMtype = function(value) {
  jspb.Message.setProto3BooleanField(this, 12, value);
};


/**
 * optional int32 NTTYPE = 13;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.getNttype = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 13, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.setNttype = function(value) {
  jspb.Message.setProto3IntField(this, 13, value);
};


/**
 * optional int32 AREA = 14;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.getArea = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 14, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.setArea = function(value) {
  jspb.Message.setProto3IntField(this, 14, value);
};


/**
 * optional string NOTES = 15;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.getNotes = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 15, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_MANAGEMENT.prototype.setNotes = function(value) {
  jspb.Message.setProto3StringField(this, 15, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.displayName = 'proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.toObject = function(includeInstance, msg) {
  var f, obj = {
    appmtype: jspb.Message.getFieldWithDefault(msg, 1, 0),
    appid: jspb.Message.getFieldWithDefault(msg, 2, ""),
    name: jspb.Message.getFieldWithDefault(msg, 3, ""),
    brand: jspb.Message.getFieldWithDefault(msg, 4, ""),
    ver: jspb.Message.getFieldWithDefault(msg, 5, ""),
    type: jspb.Message.getFieldWithDefault(msg, 6, 0),
    stype: jspb.Message.getFieldWithDefault(msg, 7, ""),
    mdipa: jspb.Message.getFieldWithDefault(msg, 8, ""),
    mdipb: jspb.Message.getFieldWithDefault(msg, 9, ""),
    appdesc: jspb.Message.getFieldWithDefault(msg, 10, ""),
    source: jspb.Message.getFieldWithDefault(msg, 11, 0),
    enstat: jspb.Message.getFieldWithDefault(msg, 12, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION;
  return proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setAppmtype(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setAppid(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setBrand(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setVer(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setType(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setStype(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setMdipa(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setMdipb(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setAppdesc(value);
      break;
    case 11:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSource(value);
      break;
    case 12:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setEnstat(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAppmtype();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getAppid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getBrand();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getVer();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getType();
  if (f !== 0) {
    writer.writeInt32(
      6,
      f
    );
  }
  f = message.getStype();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getMdipa();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getMdipb();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
  f = message.getAppdesc();
  if (f.length > 0) {
    writer.writeString(
      10,
      f
    );
  }
  f = message.getSource();
  if (f !== 0) {
    writer.writeInt32(
      11,
      f
    );
  }
  f = message.getEnstat();
  if (f) {
    writer.writeBool(
      12,
      f
    );
  }
};


/**
 * optional int32 APPMTYPE = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.prototype.getAppmtype = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.prototype.setAppmtype = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string APPID = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.prototype.getAppid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.prototype.setAppid = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string NAME = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.prototype.setName = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string BRAND = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.prototype.getBrand = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.prototype.setBrand = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string VER = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.prototype.getVer = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.prototype.setVer = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional int32 TYPE = 6;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.prototype.getType = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.prototype.setType = function(value) {
  jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional string STYPE = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.prototype.getStype = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.prototype.setStype = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string MDIPA = 8;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.prototype.getMdipa = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.prototype.setMdipa = function(value) {
  jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional string MDIPB = 9;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.prototype.getMdipb = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.prototype.setMdipb = function(value) {
  jspb.Message.setProto3StringField(this, 9, value);
};


/**
 * optional string APPDESC = 10;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.prototype.getAppdesc = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.prototype.setAppdesc = function(value) {
  jspb.Message.setProto3StringField(this, 10, value);
};


/**
 * optional int32 SOURCE = 11;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.prototype.getSource = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 11, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.prototype.setSource = function(value) {
  jspb.Message.setProto3IntField(this, 11, value);
};


/**
 * optional bool ENSTAT = 12;
 * Note that Boolean fields may be set to 0/1 when serialized from a Java server.
 * You should avoid comparisons like {@code val === true/false} in those cases.
 * @return {boolean}
 */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.prototype.getEnstat = function() {
  return /** @type {boolean} */ (jspb.Message.getFieldWithDefault(this, 12, false));
};


/** @param {boolean} value */
proto.com.jy.network_monitor.proto.MSG_ParamConfig_APPLICATION.prototype.setEnstat = function(value) {
  jspb.Message.setProto3BooleanField(this, 12, value);
};


goog.object.extend(exports, proto.com.jy.network_monitor.proto);
