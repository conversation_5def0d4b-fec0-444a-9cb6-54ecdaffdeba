/**
 * @fileoverview
 * @enhanceable
 * @suppress {messageConventions} <PERSON><PERSON> Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!

var jspb = require('google-protobuf');
var goog = jspb;
var global = Function('return this')();

goog.exportSymbol('proto.com.jy.network_monitor.proto.HardwareAssetModelCode', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.ManagementAssetModelCode', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.NetworkAssetModelCode', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.SoftwareAssetModelCode', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.SpaceAssetModelCode', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.SpaceConnectionModelCode', null, global);
/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.SpaceAssetModelCode = {
  ASSETMODELCODE_DUMMY: 0,
  SPACE_ASSET: 268435456,
  ASSET: 285212672,
  ASSET_NODE: 285278208,
  ASSET_PERCEPTION: 285343744
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.HardwareAssetModelCode = {
  HARDWAREASSETMODELCODE_DUMMY: 0,
  HARDWARE: 301989888,
  HW_STATUS: 302055424,
  CPU_MODEL: 302120960,
  CPU_STATUS: 302186496,
  LOGICAL_CORE_MODEL: 302252032,
  LOGICAL_CORE_STATUS: 302317568,
  MEMORY_MODEL: 302383104,
  DISK_MODEL: 302448640,
  DISK_STATUS: 302514176,
  ETH_NIC_MODEL: 302579712,
  PHYSICAL_PORT_MODEL: 302645248,
  WIFI_NIC_MODEL: 302710784,
  PERIPHERAL_INTERFACE: 302776320,
  PERIPHERAL_DEVICE: 302841856,
  POWER_MODEL: 302907392,
  GPU_MODEL: 302972928,
  HBA_MODEL: 303038464,
  RAID_MODEL: 303104000,
  BMC_MODEL: 303169536,
  SENSOR_MODEL: 303235072
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.SoftwareAssetModelCode = {
  SOFTWAREASSETMODELCODE_DUMMY: 0,
  SOFTWARE: 318767104,
  SYSTEM_STATUS: 318832640,
  USER_MODEL: 318898176,
  USER_STATUS: 318963712,
  USER_GROUP_MODEL: 319029248,
  SESSION_MODEL: 319094784,
  PARTITION_MODEL: 319160320,
  PARTITION_STATUS: 319225856,
  PROCESS_MODEL: 319291392,
  PROCESS_RES_USAGE: 319356928,
  SERVICE_MODEL: 319422464,
  STARTUP_TASK_MODEL: 319488000,
  DRIVER_MODEL: 319553536,
  FILE_MODEL: 319619072,
  EXECUTABLE_MODEL: 319684608,
  SOFTWARE_PKG_MODEL: 319750144,
  APPLICATION_MODEL: 319815680,
  MONITOR_AGENT_SW_MODEL: 319815936,
  HOST_FW_POLICY_MODEL: 319815937,
  TRUST_VERIFICATION_MODEL: 319816192,
  ANTI_MALWARE_MODEL: 319816448,
  MALWARE_SCAN_RECORD_MODEL: 319816449,
  VEAD_APP_MODEL: 319816704,
  VEAD_TUNNEL: 319816705,
  VEAD_POLICY: 319816706,
  DCD_APPLICATION: 319816960,
  FBID_APPLICATION_MODEL: 319817216,
  FBID_TUNNEL_MODEL: 319817217,
  FBID_POLICY_MODEL: 319817218,
  FBID_TRAN_APP_MODEL: 319817472,
  FBID_TRAN_DETECT_MODEL: 319817473,
  FW_APP_MODEL: 319817728,
  FW_ADDRESS_GROUP_MODEL: 319817729,
  FW_SERVICE_GROUP_MODEL: 319817730,
  FW_POLICY_MODEL: 319817731,
  IDS_APP_MODEL: 319817984,
  CS_APP_MODEL: 319818240,
  CRYPTO_STATUS: 319818241,
  ACCESS_STATUS: 319818242,
  SW_APP_MODEL: 319818496,
  SW_ACL_MODEL: 319818497,
  ROUTER_APP_MODEL: 319818752,
  ROUTER_ACL_MODEL: 319818753,
  SASH_APP_MODEL: 319819008,
  MCMS_APP_MODEL: 319819264,
  MCMS_NETWORK_TRAFFIC_MODEL: 319819520,
  DATABASE_MODEL: 319819776,
  VULN_SCAN_MODEL: 320864256,
  CONFIG_CHECK_MODEL: *********,
  PLUGIN_MODEL: *********,
  PLUGIN_STATUS: *********
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.NetworkAssetModelCode = {
  NETWORKASSETMODELCODE_DUMMY: 0,
  NETWORK: *********,
  NETWORK_INTERFACE_MODEL: *********,
  NETWORK_INTERFACE_STATUS: *********,
  NETWORK_ROUTE_MODEL: *********,
  ADDR_LEARNING_TABLE: *********,
  NETWORK_LISTENING_MODEL: *********,
  NETWORK_NEIGHBOR_MODEL: *********,
  VLAN_MODEL: *********,
  ACCESS_CONTROL_POLICY: *********,
  VIRTUAL_ROUTING_PROTOCOL: *********,
  NETWORK_STATUS: *********
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.ManagementAssetModelCode = {
  MANAGEMENTASSETMODELCODE_DUMMY: 0,
  MANAGEMENT: *********,
  DISPATCH_CENTER_INFO: *********,
  REGION_INFO: *********,
  ASSET_RECORD_INFO: *********
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.SpaceConnectionModelCode = {
  SPACECONNECTIONMODELCODE_DUMMY: 0,
  SPACECONNECTIONMODELCODE_SPACE_CONN: *********,
  SPACECONNECTIONMODELCODE_CONNECTION_RELATION: *********,
  SPACECONNECTIONMODELCODE_PHYSICAL_NET_CONN: *********,
  SPACECONNECTIONMODELCODE_WIRELESS_NET_CONN: *********,
  SPACECONNECTIONMODELCODE_CONNECTIVITY_RELATION: *********,
  SPACECONNECTIONMODELCODE_NETWORK_CONNECTIVITY: *********,
  SPACECONNECTIONMODELCODE_REACHABLE_PATH: *********,
  SPACECONNECTIONMODELCODE_CONNECTION_SEQUENCE: *********,
  SPACECONNECTIONMODELCODE_ACCESS_RELATION: *********,
  SPACECONNECTIONMODELCODE_ASSET_ACCESS: *********
};

goog.object.extend(exports, proto.com.jy.network_monitor.proto);
