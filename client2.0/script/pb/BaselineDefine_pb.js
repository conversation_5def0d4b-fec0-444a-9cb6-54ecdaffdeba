/**
 * @fileoverview
 * @enhanceable
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!

var jspb = require('google-protobuf');
var goog = jspb;
var global = Function('return this')();

goog.exportSymbol('proto.com.jy.network_monitor.proto.AttackFrameworkDic', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.BaselineGenMethod', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.BaselineLabel', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.BaselineType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.BehaviorLabel', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.BehaviorResult', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.BehaviorResultCode', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.LabelType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_ArpBL', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DriverBL', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_PortListeningBL', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_ProcessBL', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_ProcessSyscallBL', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_ServerAccessBL', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_ServiceBL', null, global);

/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.BehaviorResult = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.com.jy.network_monitor.proto.BehaviorResult.repeatedFields_, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.BehaviorResult, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.BehaviorResult.displayName = 'proto.com.jy.network_monitor.proto.BehaviorResult';
}
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.com.jy.network_monitor.proto.BehaviorResult.repeatedFields_ = [2];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.BehaviorResult.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.BehaviorResult.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.BehaviorResult} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.BehaviorResult.toObject = function(includeInstance, msg) {
  var f, obj = {
    ret: jspb.Message.getFieldWithDefault(msg, 1, 0),
    lblList: jspb.Message.toObjectList(msg.getLblList(),
    proto.com.jy.network_monitor.proto.BehaviorLabel.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.BehaviorResult}
 */
proto.com.jy.network_monitor.proto.BehaviorResult.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.BehaviorResult;
  return proto.com.jy.network_monitor.proto.BehaviorResult.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.BehaviorResult} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.BehaviorResult}
 */
proto.com.jy.network_monitor.proto.BehaviorResult.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRet(value);
      break;
    case 2:
      var value = new proto.com.jy.network_monitor.proto.BehaviorLabel;
      reader.readMessage(value,proto.com.jy.network_monitor.proto.BehaviorLabel.deserializeBinaryFromReader);
      msg.addLbl(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.BehaviorResult.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.BehaviorResult.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.BehaviorResult} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.BehaviorResult.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRet();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getLblList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      2,
      f,
      proto.com.jy.network_monitor.proto.BehaviorLabel.serializeBinaryToWriter
    );
  }
};


/**
 * optional int32 RET = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.BehaviorResult.prototype.getRet = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.BehaviorResult.prototype.setRet = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * repeated BehaviorLabel LBL = 2;
 * @return {!Array<!proto.com.jy.network_monitor.proto.BehaviorLabel>}
 */
proto.com.jy.network_monitor.proto.BehaviorResult.prototype.getLblList = function() {
  return /** @type{!Array<!proto.com.jy.network_monitor.proto.BehaviorLabel>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.com.jy.network_monitor.proto.BehaviorLabel, 2));
};


/** @param {!Array<!proto.com.jy.network_monitor.proto.BehaviorLabel>} value */
proto.com.jy.network_monitor.proto.BehaviorResult.prototype.setLblList = function(value) {
  jspb.Message.setRepeatedWrapperField(this, 2, value);
};


/**
 * @param {!proto.com.jy.network_monitor.proto.BehaviorLabel=} opt_value
 * @param {number=} opt_index
 * @return {!proto.com.jy.network_monitor.proto.BehaviorLabel}
 */
proto.com.jy.network_monitor.proto.BehaviorResult.prototype.addLbl = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 2, opt_value, proto.com.jy.network_monitor.proto.BehaviorLabel, opt_index);
};


proto.com.jy.network_monitor.proto.BehaviorResult.prototype.clearLblList = function() {
  this.setLblList([]);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.BehaviorLabel = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.BehaviorLabel, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.BehaviorLabel.displayName = 'proto.com.jy.network_monitor.proto.BehaviorLabel';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.BehaviorLabel.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.BehaviorLabel.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.BehaviorLabel} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.BehaviorLabel.toObject = function(includeInstance, msg) {
  var f, obj = {
    n: jspb.Message.getFieldWithDefault(msg, 1, ""),
    l: jspb.Message.getFieldWithDefault(msg, 2, ""),
    r: jspb.Message.getFieldWithDefault(msg, 3, ""),
    id: jspb.Message.getFieldWithDefault(msg, 4, ""),
    rds: jspb.Message.getFieldWithDefault(msg, 5, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.BehaviorLabel}
 */
proto.com.jy.network_monitor.proto.BehaviorLabel.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.BehaviorLabel;
  return proto.com.jy.network_monitor.proto.BehaviorLabel.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.BehaviorLabel} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.BehaviorLabel}
 */
proto.com.jy.network_monitor.proto.BehaviorLabel.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setN(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setL(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setR(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setRds(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.BehaviorLabel.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.BehaviorLabel.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.BehaviorLabel} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.BehaviorLabel.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getN();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getL();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getR();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getId();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getRds();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
};


/**
 * optional string N = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.BehaviorLabel.prototype.getN = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.BehaviorLabel.prototype.setN = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string L = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.BehaviorLabel.prototype.getL = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.BehaviorLabel.prototype.setL = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string R = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.BehaviorLabel.prototype.getR = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.BehaviorLabel.prototype.setR = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string ID = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.BehaviorLabel.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.BehaviorLabel.prototype.setId = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string RDS = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.BehaviorLabel.prototype.getRds = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.BehaviorLabel.prototype.setRds = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.AttackFrameworkDic = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.AttackFrameworkDic, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.AttackFrameworkDic.displayName = 'proto.com.jy.network_monitor.proto.AttackFrameworkDic';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.AttackFrameworkDic.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.AttackFrameworkDic} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.toObject = function(includeInstance, msg) {
  var f, obj = {
    tacticId: jspb.Message.getFieldWithDefault(msg, 1, ""),
    tacticName: jspb.Message.getFieldWithDefault(msg, 2, ""),
    techId: jspb.Message.getFieldWithDefault(msg, 3, ""),
    techName: jspb.Message.getFieldWithDefault(msg, 4, ""),
    subTechId: jspb.Message.getFieldWithDefault(msg, 5, ""),
    subTechName: jspb.Message.getFieldWithDefault(msg, 6, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.AttackFrameworkDic}
 */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.AttackFrameworkDic;
  return proto.com.jy.network_monitor.proto.AttackFrameworkDic.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.AttackFrameworkDic} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.AttackFrameworkDic}
 */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setTacticId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setTacticName(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setTechId(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setTechName(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setSubTechId(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setSubTechName(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.AttackFrameworkDic.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.AttackFrameworkDic} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTacticId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getTacticName();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getTechId();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getTechName();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getSubTechId();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getSubTechName();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
};


/**
 * optional string tactic_id = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.prototype.getTacticId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.prototype.setTacticId = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string tactic_name = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.prototype.getTacticName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.prototype.setTacticName = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string tech_id = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.prototype.getTechId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.prototype.setTechId = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string tech_name = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.prototype.getTechName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.prototype.setTechName = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string sub_tech_id = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.prototype.getSubTechId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.prototype.setSubTechId = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string sub_tech_name = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.prototype.getSubTechName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.prototype.setSubTechName = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_ServerAccessBL = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_ServerAccessBL, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_ServerAccessBL.displayName = 'proto.com.jy.network_monitor.proto.MSG_ServerAccessBL';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_ServerAccessBL.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_ServerAccessBL.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_ServerAccessBL} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ServerAccessBL.toObject = function(includeInstance, msg) {
  var f, obj = {
    proto: jspb.Message.getFieldWithDefault(msg, 1, ""),
    clientip: jspb.Message.getFieldWithDefault(msg, 2, ""),
    gid: jspb.Message.getFieldWithDefault(msg, 3, ""),
    serverip: jspb.Message.getFieldWithDefault(msg, 4, ""),
    serverport: jspb.Message.getFieldWithDefault(msg, 5, ""),
    genmethod: jspb.Message.getFieldWithDefault(msg, 6, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ServerAccessBL}
 */
proto.com.jy.network_monitor.proto.MSG_ServerAccessBL.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_ServerAccessBL;
  return proto.com.jy.network_monitor.proto.MSG_ServerAccessBL.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ServerAccessBL} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ServerAccessBL}
 */
proto.com.jy.network_monitor.proto.MSG_ServerAccessBL.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setProto(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setClientip(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setGid(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setServerip(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setServerport(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setGenmethod(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_ServerAccessBL.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_ServerAccessBL.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ServerAccessBL} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ServerAccessBL.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getProto();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getClientip();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getGid();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getServerip();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getServerport();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getGenmethod();
  if (f !== 0) {
    writer.writeInt32(
      6,
      f
    );
  }
};


/**
 * optional string PROTO = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerAccessBL.prototype.getProto = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServerAccessBL.prototype.setProto = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string CLIENTIP = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerAccessBL.prototype.getClientip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServerAccessBL.prototype.setClientip = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string GID = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerAccessBL.prototype.getGid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServerAccessBL.prototype.setGid = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string SERVERIP = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerAccessBL.prototype.getServerip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServerAccessBL.prototype.setServerip = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string SERVERPORT = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerAccessBL.prototype.getServerport = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServerAccessBL.prototype.setServerport = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional int32 GENMETHOD = 6;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ServerAccessBL.prototype.getGenmethod = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ServerAccessBL.prototype.setGenmethod = function(value) {
  jspb.Message.setProto3IntField(this, 6, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL.displayName = 'proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL.toObject = function(includeInstance, msg) {
  var f, obj = {
    exenm: jspb.Message.getFieldWithDefault(msg, 1, ""),
    exedir: jspb.Message.getFieldWithDefault(msg, 2, ""),
    gid: jspb.Message.getFieldWithDefault(msg, 3, ""),
    clientip: jspb.Message.getFieldWithDefault(msg, 4, ""),
    serverip: jspb.Message.getFieldWithDefault(msg, 5, ""),
    serverport: jspb.Message.getFieldWithDefault(msg, 6, ""),
    proto: jspb.Message.getFieldWithDefault(msg, 7, ""),
    genmethod: jspb.Message.getFieldWithDefault(msg, 8, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL;
  return proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setExenm(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setExedir(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setGid(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setClientip(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setServerip(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setServerport(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setProto(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setGenmethod(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getExenm();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getExedir();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getGid();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getClientip();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getServerip();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getServerport();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getProto();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getGenmethod();
  if (f !== 0) {
    writer.writeInt32(
      8,
      f
    );
  }
};


/**
 * optional string EXENM = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL.prototype.getExenm = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL.prototype.setExenm = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string EXEDIR = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL.prototype.getExedir = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL.prototype.setExedir = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string GID = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL.prototype.getGid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL.prototype.setGid = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string CLIENTIP = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL.prototype.getClientip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL.prototype.setClientip = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string SERVERIP = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL.prototype.getServerip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL.prototype.setServerip = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string SERVERPORT = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL.prototype.getServerport = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL.prototype.setServerport = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string PROTO = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL.prototype.getProto = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL.prototype.setProto = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional int32 GENMETHOD = 8;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL.prototype.getGenmethod = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ProcessNetworkAccessBL.prototype.setGenmethod = function(value) {
  jspb.Message.setProto3IntField(this, 8, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_PortListeningBL = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_PortListeningBL, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_PortListeningBL.displayName = 'proto.com.jy.network_monitor.proto.MSG_PortListeningBL';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_PortListeningBL.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_PortListeningBL.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_PortListeningBL} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_PortListeningBL.toObject = function(includeInstance, msg) {
  var f, obj = {
    fname: jspb.Message.getFieldWithDefault(msg, 1, ""),
    gid: jspb.Message.getFieldWithDefault(msg, 2, ""),
    srcip: jspb.Message.getFieldWithDefault(msg, 3, ""),
    port: jspb.Message.getFieldWithDefault(msg, 4, ""),
    proto: jspb.Message.getFieldWithDefault(msg, 5, ""),
    genmethod: jspb.Message.getFieldWithDefault(msg, 6, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_PortListeningBL}
 */
proto.com.jy.network_monitor.proto.MSG_PortListeningBL.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_PortListeningBL;
  return proto.com.jy.network_monitor.proto.MSG_PortListeningBL.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_PortListeningBL} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_PortListeningBL}
 */
proto.com.jy.network_monitor.proto.MSG_PortListeningBL.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setFname(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setGid(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setSrcip(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setPort(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setProto(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setGenmethod(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_PortListeningBL.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_PortListeningBL.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_PortListeningBL} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_PortListeningBL.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFname();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getGid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSrcip();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getPort();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getProto();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getGenmethod();
  if (f !== 0) {
    writer.writeInt32(
      6,
      f
    );
  }
};


/**
 * optional string FNAME = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_PortListeningBL.prototype.getFname = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_PortListeningBL.prototype.setFname = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string GID = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_PortListeningBL.prototype.getGid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_PortListeningBL.prototype.setGid = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string SRCIP = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_PortListeningBL.prototype.getSrcip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_PortListeningBL.prototype.setSrcip = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string PORT = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_PortListeningBL.prototype.getPort = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_PortListeningBL.prototype.setPort = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string PROTO = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_PortListeningBL.prototype.getProto = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_PortListeningBL.prototype.setProto = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional int32 GENMETHOD = 6;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_PortListeningBL.prototype.getGenmethod = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_PortListeningBL.prototype.setGenmethod = function(value) {
  jspb.Message.setProto3IntField(this, 6, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_ArpBL = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_ArpBL, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_ArpBL.displayName = 'proto.com.jy.network_monitor.proto.MSG_ArpBL';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_ArpBL.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_ArpBL.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_ArpBL} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ArpBL.toObject = function(includeInstance, msg) {
  var f, obj = {
    gid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    ip: jspb.Message.getFieldWithDefault(msg, 2, ""),
    mac: jspb.Message.getFieldWithDefault(msg, 3, ""),
    ifnm: jspb.Message.getFieldWithDefault(msg, 4, ""),
    genmethod: jspb.Message.getFieldWithDefault(msg, 5, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ArpBL}
 */
proto.com.jy.network_monitor.proto.MSG_ArpBL.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_ArpBL;
  return proto.com.jy.network_monitor.proto.MSG_ArpBL.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ArpBL} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ArpBL}
 */
proto.com.jy.network_monitor.proto.MSG_ArpBL.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setGid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setIp(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setMac(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setIfnm(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setGenmethod(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_ArpBL.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_ArpBL.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ArpBL} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ArpBL.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getGid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getIp();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getMac();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getIfnm();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getGenmethod();
  if (f !== 0) {
    writer.writeInt32(
      5,
      f
    );
  }
};


/**
 * optional string GID = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ArpBL.prototype.getGid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ArpBL.prototype.setGid = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string IP = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ArpBL.prototype.getIp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ArpBL.prototype.setIp = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string MAC = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ArpBL.prototype.getMac = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ArpBL.prototype.setMac = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string IFNM = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ArpBL.prototype.getIfnm = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ArpBL.prototype.setIfnm = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional int32 GENMETHOD = 5;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ArpBL.prototype.getGenmethod = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ArpBL.prototype.setGenmethod = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_ProcessBL = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_ProcessBL, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_ProcessBL.displayName = 'proto.com.jy.network_monitor.proto.MSG_ProcessBL';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessBL.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_ProcessBL.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_ProcessBL} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ProcessBL.toObject = function(includeInstance, msg) {
  var f, obj = {
    gid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    fname: jspb.Message.getFieldWithDefault(msg, 2, ""),
    scmd: jspb.Message.getFieldWithDefault(msg, 3, ""),
    user: jspb.Message.getFieldWithDefault(msg, 4, ""),
    genmethod: jspb.Message.getFieldWithDefault(msg, 5, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ProcessBL}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessBL.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_ProcessBL;
  return proto.com.jy.network_monitor.proto.MSG_ProcessBL.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ProcessBL} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ProcessBL}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessBL.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setGid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setFname(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setScmd(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setUser(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setGenmethod(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessBL.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_ProcessBL.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ProcessBL} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ProcessBL.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getGid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getFname();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getScmd();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getUser();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getGenmethod();
  if (f !== 0) {
    writer.writeInt32(
      5,
      f
    );
  }
};


/**
 * optional string GID = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessBL.prototype.getGid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ProcessBL.prototype.setGid = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string FNAME = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessBL.prototype.getFname = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ProcessBL.prototype.setFname = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string SCMD = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessBL.prototype.getScmd = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ProcessBL.prototype.setScmd = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string USER = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessBL.prototype.getUser = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ProcessBL.prototype.setUser = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional int32 GENMETHOD = 5;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessBL.prototype.getGenmethod = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ProcessBL.prototype.setGenmethod = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL.displayName = 'proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL.toObject = function(includeInstance, msg) {
  var f, obj = {
    scmd: jspb.Message.getFieldWithDefault(msg, 1, ""),
    gid: jspb.Message.getFieldWithDefault(msg, 2, ""),
    name: jspb.Message.getFieldWithDefault(msg, 3, ""),
    path: jspb.Message.getFieldWithDefault(msg, 4, ""),
    genmethod: jspb.Message.getFieldWithDefault(msg, 5, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL}
 */
proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL;
  return proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL}
 */
proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setScmd(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setGid(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setPath(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setGenmethod(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getScmd();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getGid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getPath();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getGenmethod();
  if (f !== 0) {
    writer.writeInt32(
      5,
      f
    );
  }
};


/**
 * optional string SCMD = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL.prototype.getScmd = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL.prototype.setScmd = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string GID = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL.prototype.getGid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL.prototype.setGid = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string NAME = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL.prototype.setName = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string PATH = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL.prototype.getPath = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL.prototype.setPath = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional int32 GENMETHOD = 5;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL.prototype.getGenmethod = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_CriticalFileAccessBL.prototype.setGenmethod = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL.displayName = 'proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL.toObject = function(includeInstance, msg) {
  var f, obj = {
    scmd: jspb.Message.getFieldWithDefault(msg, 1, ""),
    gid: jspb.Message.getFieldWithDefault(msg, 2, ""),
    name: jspb.Message.getFieldWithDefault(msg, 3, ""),
    path: jspb.Message.getFieldWithDefault(msg, 4, ""),
    timeGroup: jspb.Message.getFieldWithDefault(msg, 5, 0),
    count: jspb.Message.getFieldWithDefault(msg, 6, 0),
    genmethod: jspb.Message.getFieldWithDefault(msg, 7, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL}
 */
proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL;
  return proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL}
 */
proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setScmd(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setGid(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setPath(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setTimeGroup(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setCount(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setGenmethod(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getScmd();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getGid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getPath();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getTimeGroup();
  if (f !== 0) {
    writer.writeInt32(
      5,
      f
    );
  }
  f = message.getCount();
  if (f !== 0) {
    writer.writeInt32(
      6,
      f
    );
  }
  f = message.getGenmethod();
  if (f !== 0) {
    writer.writeInt32(
      7,
      f
    );
  }
};


/**
 * optional string SCMD = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL.prototype.getScmd = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL.prototype.setScmd = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string GID = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL.prototype.getGid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL.prototype.setGid = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string NAME = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL.prototype.setName = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string PATH = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL.prototype.getPath = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL.prototype.setPath = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional int32 TIME_GROUP = 5;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL.prototype.getTimeGroup = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL.prototype.setTimeGroup = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional int32 COUNT = 6;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL.prototype.getCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL.prototype.setCount = function(value) {
  jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional int32 GENMETHOD = 7;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL.prototype.getGenmethod = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_CriticalFileChangeBL.prototype.setGenmethod = function(value) {
  jspb.Message.setProto3IntField(this, 7, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_ProcessSyscallBL = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_ProcessSyscallBL, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_ProcessSyscallBL.displayName = 'proto.com.jy.network_monitor.proto.MSG_ProcessSyscallBL';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessSyscallBL.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_ProcessSyscallBL.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_ProcessSyscallBL} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ProcessSyscallBL.toObject = function(includeInstance, msg) {
  var f, obj = {
    scmd: jspb.Message.getFieldWithDefault(msg, 1, ""),
    gid: jspb.Message.getFieldWithDefault(msg, 2, ""),
    syscall: jspb.Message.getFieldWithDefault(msg, 3, ""),
    genmethod: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ProcessSyscallBL}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessSyscallBL.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_ProcessSyscallBL;
  return proto.com.jy.network_monitor.proto.MSG_ProcessSyscallBL.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ProcessSyscallBL} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ProcessSyscallBL}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessSyscallBL.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setScmd(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setGid(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setSyscall(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setGenmethod(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessSyscallBL.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_ProcessSyscallBL.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ProcessSyscallBL} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ProcessSyscallBL.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getScmd();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getGid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSyscall();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getGenmethod();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
};


/**
 * optional string SCMD = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessSyscallBL.prototype.getScmd = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ProcessSyscallBL.prototype.setScmd = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string GID = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessSyscallBL.prototype.getGid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ProcessSyscallBL.prototype.setGid = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string SYSCALL = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessSyscallBL.prototype.getSyscall = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ProcessSyscallBL.prototype.setSyscall = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional int32 GENMETHOD = 4;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ProcessSyscallBL.prototype.getGenmethod = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ProcessSyscallBL.prototype.setGenmethod = function(value) {
  jspb.Message.setProto3IntField(this, 4, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_ServiceBL = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_ServiceBL, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_ServiceBL.displayName = 'proto.com.jy.network_monitor.proto.MSG_ServiceBL';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_ServiceBL.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_ServiceBL.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_ServiceBL} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ServiceBL.toObject = function(includeInstance, msg) {
  var f, obj = {
    gid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    name: jspb.Message.getFieldWithDefault(msg, 2, ""),
    genmethod: jspb.Message.getFieldWithDefault(msg, 3, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ServiceBL}
 */
proto.com.jy.network_monitor.proto.MSG_ServiceBL.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_ServiceBL;
  return proto.com.jy.network_monitor.proto.MSG_ServiceBL.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ServiceBL} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ServiceBL}
 */
proto.com.jy.network_monitor.proto.MSG_ServiceBL.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setGid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setGenmethod(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_ServiceBL.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_ServiceBL.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ServiceBL} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ServiceBL.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getGid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getGenmethod();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
};


/**
 * optional string GID = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServiceBL.prototype.getGid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServiceBL.prototype.setGid = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string NAME = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServiceBL.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServiceBL.prototype.setName = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int32 GENMETHOD = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ServiceBL.prototype.getGenmethod = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ServiceBL.prototype.setGenmethod = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DriverBL = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DriverBL, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DriverBL.displayName = 'proto.com.jy.network_monitor.proto.MSG_DriverBL';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DriverBL.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DriverBL.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DriverBL} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DriverBL.toObject = function(includeInstance, msg) {
  var f, obj = {
    gid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    name: jspb.Message.getFieldWithDefault(msg, 2, ""),
    genmethod: jspb.Message.getFieldWithDefault(msg, 3, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DriverBL}
 */
proto.com.jy.network_monitor.proto.MSG_DriverBL.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DriverBL;
  return proto.com.jy.network_monitor.proto.MSG_DriverBL.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DriverBL} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DriverBL}
 */
proto.com.jy.network_monitor.proto.MSG_DriverBL.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setGid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setGenmethod(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DriverBL.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DriverBL.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DriverBL} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DriverBL.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getGid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getGenmethod();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
};


/**
 * optional string GID = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DriverBL.prototype.getGid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DriverBL.prototype.setGid = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string NAME = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DriverBL.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DriverBL.prototype.setName = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int32 GENMETHOD = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DriverBL.prototype.getGenmethod = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DriverBL.prototype.setGenmethod = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.BehaviorResultCode = {
  BEHAVIOR_RESULT_SUCCESS: 0,
  BEHAVIOR_RESULT_FAILURE: 1,
  BEHAVIOR_RESULT_UNKNOWN: 2
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.LabelType = {
  LABEL_TYPE_DUMMY: 0,
  LABEL_TYPE_BL: 1,
  LABEL_TYPE_RL: 2,
  LABEL_TYPE_AI: 4
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.BaselineLabel = {
  BASELINE_DUMMY: 0,
  BASELINE_SERVER_NET_ACCESS: 1,
  BASELINE_PROCESS_NET_ACCESS: 2,
  BASELINE_NET_PORT_LISTEN: 3,
  BASELINE_ARP_INFO: 4,
  BASELINE_HOST_PROCESS: 5,
  BASELINE_KEY_FILE_ACCESS: 6,
  BASELINE_KEY_FILE_CHANGE: 7,
  BASELINE_PROC_SYSCALL: 8,
  BASELINE_HOST_SERVICE: 9,
  BASELINE_HOST_DRIVER_LOAD: 10
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.BaselineGenMethod = {
  LEARNMETHOD_DUMMY: 0,
  AUTO_LEARN: 1,
  MANUAL_SET: 2
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.BaselineType = {
  BASELINEDUMMY: 0,
  BASELINESTATISTICSET: 1,
  BASELINEANALYZESET: 2
};

goog.object.extend(exports, proto.com.jy.network_monitor.proto);
