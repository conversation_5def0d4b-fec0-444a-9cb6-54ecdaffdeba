/**
 * @fileoverview
 * @enhanceable
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!

var jspb = require('google-protobuf');
var goog = jspb;
var global = Function('return this')();

var PublicDefine2_pb = require('./PublicDefine2_pb.js');
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_AssetSequence', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_Connection', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_NetworkAccess', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_NetworkReachable', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_ReachableSequence', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_WirelessConnect', null, global);

/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_Connection = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.com.jy.network_monitor.proto.MSG_Connection.repeatedFields_, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_Connection, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_Connection.displayName = 'proto.com.jy.network_monitor.proto.MSG_Connection';
}
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.com.jy.network_monitor.proto.MSG_Connection.repeatedFields_ = [1,2,3,4,5,6];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_Connection.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_Connection.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_Connection} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_Connection.toObject = function(includeInstance, msg) {
  var f, obj = {
    physicalConnectivityList: jspb.Message.toObjectList(msg.getPhysicalConnectivityList(),
    proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.toObject, includeInstance),
    wirelessConnectList: jspb.Message.toObjectList(msg.getWirelessConnectList(),
    proto.com.jy.network_monitor.proto.MSG_WirelessConnect.toObject, includeInstance),
    networkReachableList: jspb.Message.toObjectList(msg.getNetworkReachableList(),
    proto.com.jy.network_monitor.proto.MSG_NetworkReachable.toObject, includeInstance),
    reachableSequenceList: jspb.Message.toObjectList(msg.getReachableSequenceList(),
    proto.com.jy.network_monitor.proto.MSG_ReachableSequence.toObject, includeInstance),
    assetSequenceList: jspb.Message.toObjectList(msg.getAssetSequenceList(),
    proto.com.jy.network_monitor.proto.MSG_AssetSequence.toObject, includeInstance),
    networkAccessList: jspb.Message.toObjectList(msg.getNetworkAccessList(),
    proto.com.jy.network_monitor.proto.MSG_NetworkAccess.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_Connection}
 */
proto.com.jy.network_monitor.proto.MSG_Connection.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_Connection;
  return proto.com.jy.network_monitor.proto.MSG_Connection.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_Connection} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_Connection}
 */
proto.com.jy.network_monitor.proto.MSG_Connection.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity;
      reader.readMessage(value,proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.deserializeBinaryFromReader);
      msg.addPhysicalConnectivity(value);
      break;
    case 2:
      var value = new proto.com.jy.network_monitor.proto.MSG_WirelessConnect;
      reader.readMessage(value,proto.com.jy.network_monitor.proto.MSG_WirelessConnect.deserializeBinaryFromReader);
      msg.addWirelessConnect(value);
      break;
    case 3:
      var value = new proto.com.jy.network_monitor.proto.MSG_NetworkReachable;
      reader.readMessage(value,proto.com.jy.network_monitor.proto.MSG_NetworkReachable.deserializeBinaryFromReader);
      msg.addNetworkReachable(value);
      break;
    case 4:
      var value = new proto.com.jy.network_monitor.proto.MSG_ReachableSequence;
      reader.readMessage(value,proto.com.jy.network_monitor.proto.MSG_ReachableSequence.deserializeBinaryFromReader);
      msg.addReachableSequence(value);
      break;
    case 5:
      var value = new proto.com.jy.network_monitor.proto.MSG_AssetSequence;
      reader.readMessage(value,proto.com.jy.network_monitor.proto.MSG_AssetSequence.deserializeBinaryFromReader);
      msg.addAssetSequence(value);
      break;
    case 6:
      var value = new proto.com.jy.network_monitor.proto.MSG_NetworkAccess;
      reader.readMessage(value,proto.com.jy.network_monitor.proto.MSG_NetworkAccess.deserializeBinaryFromReader);
      msg.addNetworkAccess(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_Connection.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_Connection.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_Connection} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_Connection.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPhysicalConnectivityList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.serializeBinaryToWriter
    );
  }
  f = message.getWirelessConnectList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      2,
      f,
      proto.com.jy.network_monitor.proto.MSG_WirelessConnect.serializeBinaryToWriter
    );
  }
  f = message.getNetworkReachableList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      proto.com.jy.network_monitor.proto.MSG_NetworkReachable.serializeBinaryToWriter
    );
  }
  f = message.getReachableSequenceList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      4,
      f,
      proto.com.jy.network_monitor.proto.MSG_ReachableSequence.serializeBinaryToWriter
    );
  }
  f = message.getAssetSequenceList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      5,
      f,
      proto.com.jy.network_monitor.proto.MSG_AssetSequence.serializeBinaryToWriter
    );
  }
  f = message.getNetworkAccessList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      6,
      f,
      proto.com.jy.network_monitor.proto.MSG_NetworkAccess.serializeBinaryToWriter
    );
  }
};


/**
 * repeated MSG_PhysicalConnectivity physical_connectivity = 1;
 * @return {!Array<!proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity>}
 */
proto.com.jy.network_monitor.proto.MSG_Connection.prototype.getPhysicalConnectivityList = function() {
  return /** @type{!Array<!proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity, 1));
};


/** @param {!Array<!proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity>} value */
proto.com.jy.network_monitor.proto.MSG_Connection.prototype.setPhysicalConnectivityList = function(value) {
  jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity=} opt_value
 * @param {number=} opt_index
 * @return {!proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity}
 */
proto.com.jy.network_monitor.proto.MSG_Connection.prototype.addPhysicalConnectivity = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity, opt_index);
};


proto.com.jy.network_monitor.proto.MSG_Connection.prototype.clearPhysicalConnectivityList = function() {
  this.setPhysicalConnectivityList([]);
};


/**
 * repeated MSG_WirelessConnect wireless_connect = 2;
 * @return {!Array<!proto.com.jy.network_monitor.proto.MSG_WirelessConnect>}
 */
proto.com.jy.network_monitor.proto.MSG_Connection.prototype.getWirelessConnectList = function() {
  return /** @type{!Array<!proto.com.jy.network_monitor.proto.MSG_WirelessConnect>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.com.jy.network_monitor.proto.MSG_WirelessConnect, 2));
};


/** @param {!Array<!proto.com.jy.network_monitor.proto.MSG_WirelessConnect>} value */
proto.com.jy.network_monitor.proto.MSG_Connection.prototype.setWirelessConnectList = function(value) {
  jspb.Message.setRepeatedWrapperField(this, 2, value);
};


/**
 * @param {!proto.com.jy.network_monitor.proto.MSG_WirelessConnect=} opt_value
 * @param {number=} opt_index
 * @return {!proto.com.jy.network_monitor.proto.MSG_WirelessConnect}
 */
proto.com.jy.network_monitor.proto.MSG_Connection.prototype.addWirelessConnect = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 2, opt_value, proto.com.jy.network_monitor.proto.MSG_WirelessConnect, opt_index);
};


proto.com.jy.network_monitor.proto.MSG_Connection.prototype.clearWirelessConnectList = function() {
  this.setWirelessConnectList([]);
};


/**
 * repeated MSG_NetworkReachable network_reachable = 3;
 * @return {!Array<!proto.com.jy.network_monitor.proto.MSG_NetworkReachable>}
 */
proto.com.jy.network_monitor.proto.MSG_Connection.prototype.getNetworkReachableList = function() {
  return /** @type{!Array<!proto.com.jy.network_monitor.proto.MSG_NetworkReachable>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.com.jy.network_monitor.proto.MSG_NetworkReachable, 3));
};


/** @param {!Array<!proto.com.jy.network_monitor.proto.MSG_NetworkReachable>} value */
proto.com.jy.network_monitor.proto.MSG_Connection.prototype.setNetworkReachableList = function(value) {
  jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.com.jy.network_monitor.proto.MSG_NetworkReachable=} opt_value
 * @param {number=} opt_index
 * @return {!proto.com.jy.network_monitor.proto.MSG_NetworkReachable}
 */
proto.com.jy.network_monitor.proto.MSG_Connection.prototype.addNetworkReachable = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.com.jy.network_monitor.proto.MSG_NetworkReachable, opt_index);
};


proto.com.jy.network_monitor.proto.MSG_Connection.prototype.clearNetworkReachableList = function() {
  this.setNetworkReachableList([]);
};


/**
 * repeated MSG_ReachableSequence reachable_sequence = 4;
 * @return {!Array<!proto.com.jy.network_monitor.proto.MSG_ReachableSequence>}
 */
proto.com.jy.network_monitor.proto.MSG_Connection.prototype.getReachableSequenceList = function() {
  return /** @type{!Array<!proto.com.jy.network_monitor.proto.MSG_ReachableSequence>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.com.jy.network_monitor.proto.MSG_ReachableSequence, 4));
};


/** @param {!Array<!proto.com.jy.network_monitor.proto.MSG_ReachableSequence>} value */
proto.com.jy.network_monitor.proto.MSG_Connection.prototype.setReachableSequenceList = function(value) {
  jspb.Message.setRepeatedWrapperField(this, 4, value);
};


/**
 * @param {!proto.com.jy.network_monitor.proto.MSG_ReachableSequence=} opt_value
 * @param {number=} opt_index
 * @return {!proto.com.jy.network_monitor.proto.MSG_ReachableSequence}
 */
proto.com.jy.network_monitor.proto.MSG_Connection.prototype.addReachableSequence = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 4, opt_value, proto.com.jy.network_monitor.proto.MSG_ReachableSequence, opt_index);
};


proto.com.jy.network_monitor.proto.MSG_Connection.prototype.clearReachableSequenceList = function() {
  this.setReachableSequenceList([]);
};


/**
 * repeated MSG_AssetSequence asset_sequence = 5;
 * @return {!Array<!proto.com.jy.network_monitor.proto.MSG_AssetSequence>}
 */
proto.com.jy.network_monitor.proto.MSG_Connection.prototype.getAssetSequenceList = function() {
  return /** @type{!Array<!proto.com.jy.network_monitor.proto.MSG_AssetSequence>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.com.jy.network_monitor.proto.MSG_AssetSequence, 5));
};


/** @param {!Array<!proto.com.jy.network_monitor.proto.MSG_AssetSequence>} value */
proto.com.jy.network_monitor.proto.MSG_Connection.prototype.setAssetSequenceList = function(value) {
  jspb.Message.setRepeatedWrapperField(this, 5, value);
};


/**
 * @param {!proto.com.jy.network_monitor.proto.MSG_AssetSequence=} opt_value
 * @param {number=} opt_index
 * @return {!proto.com.jy.network_monitor.proto.MSG_AssetSequence}
 */
proto.com.jy.network_monitor.proto.MSG_Connection.prototype.addAssetSequence = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 5, opt_value, proto.com.jy.network_monitor.proto.MSG_AssetSequence, opt_index);
};


proto.com.jy.network_monitor.proto.MSG_Connection.prototype.clearAssetSequenceList = function() {
  this.setAssetSequenceList([]);
};


/**
 * repeated MSG_NetworkAccess network_access = 6;
 * @return {!Array<!proto.com.jy.network_monitor.proto.MSG_NetworkAccess>}
 */
proto.com.jy.network_monitor.proto.MSG_Connection.prototype.getNetworkAccessList = function() {
  return /** @type{!Array<!proto.com.jy.network_monitor.proto.MSG_NetworkAccess>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.com.jy.network_monitor.proto.MSG_NetworkAccess, 6));
};


/** @param {!Array<!proto.com.jy.network_monitor.proto.MSG_NetworkAccess>} value */
proto.com.jy.network_monitor.proto.MSG_Connection.prototype.setNetworkAccessList = function(value) {
  jspb.Message.setRepeatedWrapperField(this, 6, value);
};


/**
 * @param {!proto.com.jy.network_monitor.proto.MSG_NetworkAccess=} opt_value
 * @param {number=} opt_index
 * @return {!proto.com.jy.network_monitor.proto.MSG_NetworkAccess}
 */
proto.com.jy.network_monitor.proto.MSG_Connection.prototype.addNetworkAccess = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 6, opt_value, proto.com.jy.network_monitor.proto.MSG_NetworkAccess, opt_index);
};


proto.com.jy.network_monitor.proto.MSG_Connection.prototype.clearNetworkAccessList = function() {
  this.setNetworkAccessList([]);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.displayName = 'proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.toObject = function(includeInstance, msg) {
  var f, obj = {
    pconid: jspb.Message.getFieldWithDefault(msg, 1, 0),
    gida: jspb.Message.getFieldWithDefault(msg, 2, ""),
    ethida: jspb.Message.getFieldWithDefault(msg, 3, ""),
    gidb: jspb.Message.getFieldWithDefault(msg, 4, ""),
    ethidb: jspb.Message.getFieldWithDefault(msg, 5, ""),
    consta: jspb.Message.getFieldWithDefault(msg, 6, 0),
    createt: jspb.Message.getFieldWithDefault(msg, 7, ""),
    updatet: jspb.Message.getFieldWithDefault(msg, 8, ""),
    source: jspb.Message.getFieldWithDefault(msg, 9, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity}
 */
proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity;
  return proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity}
 */
proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setPconid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setGida(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setEthida(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setGidb(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setEthidb(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setConsta(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setCreatet(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setUpdatet(value);
      break;
    case 9:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSource(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPconid();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getGida();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getEthida();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getGidb();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getEthidb();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getConsta();
  if (f !== 0) {
    writer.writeInt32(
      6,
      f
    );
  }
  f = message.getCreatet();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getUpdatet();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getSource();
  if (f !== 0) {
    writer.writeInt32(
      9,
      f
    );
  }
};


/**
 * optional int32 PCONID = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.prototype.getPconid = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.prototype.setPconid = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string GIDA = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.prototype.getGida = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.prototype.setGida = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string ETHIDA = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.prototype.getEthida = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.prototype.setEthida = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string GIDB = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.prototype.getGidb = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.prototype.setGidb = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string ETHIDB = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.prototype.getEthidb = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.prototype.setEthidb = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional int32 CONSTA = 6;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.prototype.getConsta = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.prototype.setConsta = function(value) {
  jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional string CREATET = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.prototype.getCreatet = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.prototype.setCreatet = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string UPDATET = 8;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.prototype.getUpdatet = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.prototype.setUpdatet = function(value) {
  jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional int32 SOURCE = 9;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.prototype.getSource = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 9, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_PhysicalConnectivity.prototype.setSource = function(value) {
  jspb.Message.setProto3IntField(this, 9, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_WirelessConnect, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_WirelessConnect.displayName = 'proto.com.jy.network_monitor.proto.MSG_WirelessConnect';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_WirelessConnect.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_WirelessConnect} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.toObject = function(includeInstance, msg) {
  var f, obj = {
    ifid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    wfid: jspb.Message.getFieldWithDefault(msg, 2, ""),
    name: jspb.Message.getFieldWithDefault(msg, 3, ""),
    mac: jspb.Message.getFieldWithDefault(msg, 4, ""),
    chnl: jspb.Message.getFieldWithDefault(msg, 5, ""),
    freq: jspb.Message.getFieldWithDefault(msg, 6, ""),
    qual: jspb.Message.getFieldWithDefault(msg, 7, ""),
    siglev: jspb.Message.getFieldWithDefault(msg, 8, ""),
    enckey: jspb.Message.getFieldWithDefault(msg, 9, ""),
    mode: jspb.Message.getFieldWithDefault(msg, 10, ""),
    createt: jspb.Message.getFieldWithDefault(msg, 11, ""),
    updatet: jspb.Message.getFieldWithDefault(msg, 12, ""),
    source: jspb.Message.getFieldWithDefault(msg, 13, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_WirelessConnect}
 */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_WirelessConnect;
  return proto.com.jy.network_monitor.proto.MSG_WirelessConnect.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_WirelessConnect} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_WirelessConnect}
 */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setIfid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setWfid(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setMac(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setChnl(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setFreq(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setQual(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setSiglev(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setEnckey(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setMode(value);
      break;
    case 11:
      var value = /** @type {string} */ (reader.readString());
      msg.setCreatet(value);
      break;
    case 12:
      var value = /** @type {string} */ (reader.readString());
      msg.setUpdatet(value);
      break;
    case 13:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSource(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_WirelessConnect.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_WirelessConnect} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getIfid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getWfid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getMac();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getChnl();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getFreq();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getQual();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getSiglev();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getEnckey();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
  f = message.getMode();
  if (f.length > 0) {
    writer.writeString(
      10,
      f
    );
  }
  f = message.getCreatet();
  if (f.length > 0) {
    writer.writeString(
      11,
      f
    );
  }
  f = message.getUpdatet();
  if (f.length > 0) {
    writer.writeString(
      12,
      f
    );
  }
  f = message.getSource();
  if (f !== 0) {
    writer.writeInt32(
      13,
      f
    );
  }
};


/**
 * optional string IFID = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.prototype.getIfid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.prototype.setIfid = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string WFID = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.prototype.getWfid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.prototype.setWfid = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string NAME = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.prototype.setName = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string MAC = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.prototype.getMac = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.prototype.setMac = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string CHNL = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.prototype.getChnl = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.prototype.setChnl = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string FREQ = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.prototype.getFreq = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.prototype.setFreq = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string QUAL = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.prototype.getQual = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.prototype.setQual = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string SIGLEV = 8;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.prototype.getSiglev = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.prototype.setSiglev = function(value) {
  jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional string ENCKEY = 9;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.prototype.getEnckey = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.prototype.setEnckey = function(value) {
  jspb.Message.setProto3StringField(this, 9, value);
};


/**
 * optional string MODE = 10;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.prototype.getMode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.prototype.setMode = function(value) {
  jspb.Message.setProto3StringField(this, 10, value);
};


/**
 * optional string CREATET = 11;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.prototype.getCreatet = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 11, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.prototype.setCreatet = function(value) {
  jspb.Message.setProto3StringField(this, 11, value);
};


/**
 * optional string UPDATET = 12;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.prototype.getUpdatet = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 12, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.prototype.setUpdatet = function(value) {
  jspb.Message.setProto3StringField(this, 12, value);
};


/**
 * optional int32 SOURCE = 13;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.prototype.getSource = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 13, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_WirelessConnect.prototype.setSource = function(value) {
  jspb.Message.setProto3IntField(this, 13, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_NetworkReachable = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_NetworkReachable, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_NetworkReachable.displayName = 'proto.com.jy.network_monitor.proto.MSG_NetworkReachable';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkReachable.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_NetworkReachable.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_NetworkReachable} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_NetworkReachable.toObject = function(includeInstance, msg) {
  var f, obj = {
    raid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    srcifid: jspb.Message.getFieldWithDefault(msg, 2, ""),
    dstifid: jspb.Message.getFieldWithDefault(msg, 3, ""),
    rrol: jspb.Message.getFieldWithDefault(msg, 4, ""),
    portl: jspb.Message.getFieldWithDefault(msg, 5, ""),
    acond: jspb.Message.getFieldWithDefault(msg, 6, ""),
    createt: jspb.Message.getFieldWithDefault(msg, 7, ""),
    updatet: jspb.Message.getFieldWithDefault(msg, 8, ""),
    source: jspb.Message.getFieldWithDefault(msg, 9, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_NetworkReachable}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkReachable.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_NetworkReachable;
  return proto.com.jy.network_monitor.proto.MSG_NetworkReachable.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_NetworkReachable} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_NetworkReachable}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkReachable.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setRaid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSrcifid(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setDstifid(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setRrol(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setPortl(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setAcond(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setCreatet(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setUpdatet(value);
      break;
    case 9:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSource(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkReachable.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_NetworkReachable.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_NetworkReachable} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_NetworkReachable.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRaid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getSrcifid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getDstifid();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getRrol();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getPortl();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getAcond();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getCreatet();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getUpdatet();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getSource();
  if (f !== 0) {
    writer.writeInt32(
      9,
      f
    );
  }
};


/**
 * optional string RAID = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkReachable.prototype.getRaid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_NetworkReachable.prototype.setRaid = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string SRCIFID = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkReachable.prototype.getSrcifid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_NetworkReachable.prototype.setSrcifid = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string DSTIFID = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkReachable.prototype.getDstifid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_NetworkReachable.prototype.setDstifid = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string RROL = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkReachable.prototype.getRrol = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_NetworkReachable.prototype.setRrol = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string PORTL = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkReachable.prototype.getPortl = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_NetworkReachable.prototype.setPortl = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string ACOND = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkReachable.prototype.getAcond = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_NetworkReachable.prototype.setAcond = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string CREATET = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkReachable.prototype.getCreatet = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_NetworkReachable.prototype.setCreatet = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string UPDATET = 8;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkReachable.prototype.getUpdatet = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_NetworkReachable.prototype.setUpdatet = function(value) {
  jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional int32 SOURCE = 9;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkReachable.prototype.getSource = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 9, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_NetworkReachable.prototype.setSource = function(value) {
  jspb.Message.setProto3IntField(this, 9, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_ReachableSequence = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_ReachableSequence, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_ReachableSequence.displayName = 'proto.com.jy.network_monitor.proto.MSG_ReachableSequence';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_ReachableSequence.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_ReachableSequence.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_ReachableSequence} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ReachableSequence.toObject = function(includeInstance, msg) {
  var f, obj = {
    seqnum: jspb.Message.getFieldWithDefault(msg, 1, ""),
    raid: jspb.Message.getFieldWithDefault(msg, 2, ""),
    createt: jspb.Message.getFieldWithDefault(msg, 3, ""),
    updatet: jspb.Message.getFieldWithDefault(msg, 4, ""),
    source: jspb.Message.getFieldWithDefault(msg, 5, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ReachableSequence}
 */
proto.com.jy.network_monitor.proto.MSG_ReachableSequence.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_ReachableSequence;
  return proto.com.jy.network_monitor.proto.MSG_ReachableSequence.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ReachableSequence} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ReachableSequence}
 */
proto.com.jy.network_monitor.proto.MSG_ReachableSequence.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setSeqnum(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setRaid(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setCreatet(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setUpdatet(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSource(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_ReachableSequence.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_ReachableSequence.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ReachableSequence} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ReachableSequence.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSeqnum();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getRaid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getCreatet();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getUpdatet();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getSource();
  if (f !== 0) {
    writer.writeInt32(
      5,
      f
    );
  }
};


/**
 * optional string SEQNUM = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ReachableSequence.prototype.getSeqnum = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ReachableSequence.prototype.setSeqnum = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string RAID = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ReachableSequence.prototype.getRaid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ReachableSequence.prototype.setRaid = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string CREATET = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ReachableSequence.prototype.getCreatet = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ReachableSequence.prototype.setCreatet = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string UPDATET = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ReachableSequence.prototype.getUpdatet = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ReachableSequence.prototype.setUpdatet = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional int32 SOURCE = 5;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ReachableSequence.prototype.getSource = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ReachableSequence.prototype.setSource = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_AssetSequence = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_AssetSequence, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_AssetSequence.displayName = 'proto.com.jy.network_monitor.proto.MSG_AssetSequence';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_AssetSequence.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_AssetSequence.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_AssetSequence} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_AssetSequence.toObject = function(includeInstance, msg) {
  var f, obj = {
    sn: jspb.Message.getFieldWithDefault(msg, 1, 0),
    seqnum: jspb.Message.getFieldWithDefault(msg, 2, ""),
    pconid: jspb.Message.getFieldWithDefault(msg, 3, ""),
    createt: jspb.Message.getFieldWithDefault(msg, 4, ""),
    updatet: jspb.Message.getFieldWithDefault(msg, 5, ""),
    source: jspb.Message.getFieldWithDefault(msg, 6, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_AssetSequence}
 */
proto.com.jy.network_monitor.proto.MSG_AssetSequence.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_AssetSequence;
  return proto.com.jy.network_monitor.proto.MSG_AssetSequence.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_AssetSequence} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_AssetSequence}
 */
proto.com.jy.network_monitor.proto.MSG_AssetSequence.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSn(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSeqnum(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setPconid(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setCreatet(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setUpdatet(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSource(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_AssetSequence.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_AssetSequence.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_AssetSequence} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_AssetSequence.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSn();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getSeqnum();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getPconid();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getCreatet();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getUpdatet();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getSource();
  if (f !== 0) {
    writer.writeInt32(
      6,
      f
    );
  }
};


/**
 * optional int32 SN = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_AssetSequence.prototype.getSn = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_AssetSequence.prototype.setSn = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string SEQNUM = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_AssetSequence.prototype.getSeqnum = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_AssetSequence.prototype.setSeqnum = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string PCONID = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_AssetSequence.prototype.getPconid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_AssetSequence.prototype.setPconid = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string CREATET = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_AssetSequence.prototype.getCreatet = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_AssetSequence.prototype.setCreatet = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string UPDATET = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_AssetSequence.prototype.getUpdatet = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_AssetSequence.prototype.setUpdatet = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional int32 SOURCE = 6;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_AssetSequence.prototype.getSource = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_AssetSequence.prototype.setSource = function(value) {
  jspb.Message.setProto3IntField(this, 6, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_NetworkAccess, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_NetworkAccess.displayName = 'proto.com.jy.network_monitor.proto.MSG_NetworkAccess';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_NetworkAccess.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_NetworkAccess} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.toObject = function(includeInstance, msg) {
  var f, obj = {
    srcGid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    srcip: jspb.Message.getFieldWithDefault(msg, 2, ""),
    srcifid: jspb.Message.getFieldWithDefault(msg, 3, ""),
    dstGid: jspb.Message.getFieldWithDefault(msg, 4, ""),
    dstip: jspb.Message.getFieldWithDefault(msg, 5, ""),
    dstifid: jspb.Message.getFieldWithDefault(msg, 6, ""),
    srcp: jspb.Message.getFieldWithDefault(msg, 7, ""),
    dstp: jspb.Message.getFieldWithDefault(msg, 8, ""),
    proto: jspb.Message.getFieldWithDefault(msg, 9, ""),
    anum: jspb.Message.getFieldWithDefault(msg, 10, 0),
    raid: jspb.Message.getFieldWithDefault(msg, 11, 0),
    seqnum: jspb.Message.getFieldWithDefault(msg, 12, 0),
    createt: jspb.Message.getFieldWithDefault(msg, 13, ""),
    updatet: jspb.Message.getFieldWithDefault(msg, 14, ""),
    source: jspb.Message.getFieldWithDefault(msg, 15, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_NetworkAccess}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_NetworkAccess;
  return proto.com.jy.network_monitor.proto.MSG_NetworkAccess.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_NetworkAccess} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_NetworkAccess}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setSrcGid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSrcip(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setSrcifid(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setDstGid(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setDstip(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setDstifid(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setSrcp(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setDstp(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setProto(value);
      break;
    case 10:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setAnum(value);
      break;
    case 11:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRaid(value);
      break;
    case 12:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSeqnum(value);
      break;
    case 13:
      var value = /** @type {string} */ (reader.readString());
      msg.setCreatet(value);
      break;
    case 14:
      var value = /** @type {string} */ (reader.readString());
      msg.setUpdatet(value);
      break;
    case 15:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSource(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_NetworkAccess.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_NetworkAccess} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSrcGid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getSrcip();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSrcifid();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getDstGid();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getDstip();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getDstifid();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getSrcp();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getDstp();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getProto();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
  f = message.getAnum();
  if (f !== 0) {
    writer.writeInt32(
      10,
      f
    );
  }
  f = message.getRaid();
  if (f !== 0) {
    writer.writeInt32(
      11,
      f
    );
  }
  f = message.getSeqnum();
  if (f !== 0) {
    writer.writeInt32(
      12,
      f
    );
  }
  f = message.getCreatet();
  if (f.length > 0) {
    writer.writeString(
      13,
      f
    );
  }
  f = message.getUpdatet();
  if (f.length > 0) {
    writer.writeString(
      14,
      f
    );
  }
  f = message.getSource();
  if (f !== 0) {
    writer.writeInt32(
      15,
      f
    );
  }
};


/**
 * optional string SRC_GID = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.getSrcGid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.setSrcGid = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string SRCIP = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.getSrcip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.setSrcip = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string SRCIFID = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.getSrcifid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.setSrcifid = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string DST_GID = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.getDstGid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.setDstGid = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string DSTIP = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.getDstip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.setDstip = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string DSTIFID = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.getDstifid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.setDstifid = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string SRCP = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.getSrcp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.setSrcp = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string DSTP = 8;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.getDstp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.setDstp = function(value) {
  jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional string PROTO = 9;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.getProto = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.setProto = function(value) {
  jspb.Message.setProto3StringField(this, 9, value);
};


/**
 * optional int32 ANUM = 10;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.getAnum = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 10, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.setAnum = function(value) {
  jspb.Message.setProto3IntField(this, 10, value);
};


/**
 * optional int32 RAID = 11;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.getRaid = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 11, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.setRaid = function(value) {
  jspb.Message.setProto3IntField(this, 11, value);
};


/**
 * optional int32 SEQNUM = 12;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.getSeqnum = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 12, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.setSeqnum = function(value) {
  jspb.Message.setProto3IntField(this, 12, value);
};


/**
 * optional string CREATET = 13;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.getCreatet = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 13, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.setCreatet = function(value) {
  jspb.Message.setProto3StringField(this, 13, value);
};


/**
 * optional string UPDATET = 14;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.getUpdatet = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 14, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.setUpdatet = function(value) {
  jspb.Message.setProto3StringField(this, 14, value);
};


/**
 * optional int32 SOURCE = 15;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.getSource = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 15, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_NetworkAccess.prototype.setSource = function(value) {
  jspb.Message.setProto3IntField(this, 15, value);
};


goog.object.extend(exports, proto.com.jy.network_monitor.proto);
