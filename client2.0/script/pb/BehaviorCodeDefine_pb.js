/**
 * @fileoverview
 * @enhanceable
 * @suppress {messageConventions} <PERSON><PERSON> Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!

var jspb = require('google-protobuf');
var goog = jspb;
var global = Function('return this')();

goog.exportSymbol('proto.com.jy.network_monitor.proto.ConnectionBehaviorCode', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.HardwareBehaviorCode', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.NetworkBehaviorCode', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.SoftwareBehaviorCode', null, global);
/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.HardwareBehaviorCode = {
  HARDWAREBEHAVIORCODE_DUMMY: 0,
  DISK_ADD_DEL: 257,
  DIS<PERSON>_CHANGE: 258,
  NC_ADD_DEL: 259,
  WIRELESS_ADD_DEL: 260,
  PERI_INT_ADD_DEL: 261,
  PERI_INT_DISABLE_ENABLE: 262,
  PERI_CONNECT_UNPLUG: 263,
  NET_INT_CONN_DISCONN: 264
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.SoftwareBehaviorCode = {
  SOFTWAREBEHAVIORCODE_DUMMY: 0,
  SOFTWARE_CHANGE: 513,
  SOFTWARE_CONFIG_SWITCH: 514,
  USER_GRP_ADD_DEL: 515,
  USER_GRP_NAME_CHANGE: 516,
  USER_ADD_DEL: 517,
  USER_ATTR_CHANGE: 518,
  USER_LOCK_UNLOCK: 519,
  PART_ADD_DEL: 520,
  PART_FORMAT: 521,
  PART_MOUNT_UNM: 522,
  BOOT_TASK_ADD_DEL: 523,
  BOOT_TASK_CHANGE: 524,
  DRV_LOAD_UNL: 525,
  SOFTPAC_INSTALL_UNINST: 526,
  SOFTPAC_UPGRADE: 527,
  SERV_INSTALL_UNINST: 528,
  SERV_PARAM_MOD: 529,
  SERV_START_STOP: 530,
  FILE_ADD_DEL: 531,
  FILE_CHANGE: 532,
  CORE_FILE_CHANGE: 533,
  FILE_ACCESS: 534,
  EXEC_FILE_CHANGE: 535,
  PROC_START_STOP: 536,
  PROC_SYS_CALL: 537,
  SESSION_LOGIN: 538,
  SESSION_LOGOUT: 539,
  SESSION_OPER: 540,
  APP_START_STOP: 541,
  APP_OPER: 542,
  AGENT_SRCPLUG_CHANGE: 543
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.NetworkBehaviorCode = {
  NETWORKBEHAVIORCODE_DUMMY: 0,
  NET_CONFIG_SWITCH: 769,
  NET_INT_ADD_DEL: 770,
  NET_INT_CHANGE: 771,
  NET_INT_START_STOP: 772,
  ROUTE_ADD_DEL: 773,
  ACC_CTRL_POL_CHANGE: 774,
  ADDR_CHANGE: 775,
  LISTEN_PORT_SWITCH: 776
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.ConnectionBehaviorCode = {
  CONNECTIONBEHAVIORCODE_DUMMY: 0,
  NET_ACCESS: 1025,
  NET_FILE_TRANSFER: 1026
};

goog.object.extend(exports, proto.com.jy.network_monitor.proto);
