/**
 * @fileoverview
 * @enhanceable
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!

var jspb = require('google-protobuf');
var goog = jspb;
var global = Function('return this')();

goog.exportSymbol('proto.com.jy.network_monitor.proto.AttackFrameworkDic', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.BaselineLabel', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.BehaviorLabel', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.BehaviorResult', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.BehaviorResultCode', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.LabelType', null, global);

/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.BehaviorResult = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.com.jy.network_monitor.proto.BehaviorResult.repeatedFields_, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.BehaviorResult, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.BehaviorResult.displayName = 'proto.com.jy.network_monitor.proto.BehaviorResult';
}
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.com.jy.network_monitor.proto.BehaviorResult.repeatedFields_ = [2];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.BehaviorResult.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.BehaviorResult.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.BehaviorResult} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.BehaviorResult.toObject = function(includeInstance, msg) {
  var f, obj = {
    ret: jspb.Message.getFieldWithDefault(msg, 1, 0),
    lblList: jspb.Message.toObjectList(msg.getLblList(),
    proto.com.jy.network_monitor.proto.BehaviorLabel.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.BehaviorResult}
 */
proto.com.jy.network_monitor.proto.BehaviorResult.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.BehaviorResult;
  return proto.com.jy.network_monitor.proto.BehaviorResult.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.BehaviorResult} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.BehaviorResult}
 */
proto.com.jy.network_monitor.proto.BehaviorResult.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.com.jy.network_monitor.proto.BehaviorResultCode} */ (reader.readEnum());
      msg.setRet(value);
      break;
    case 2:
      var value = new proto.com.jy.network_monitor.proto.BehaviorLabel;
      reader.readMessage(value,proto.com.jy.network_monitor.proto.BehaviorLabel.deserializeBinaryFromReader);
      msg.addLbl(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.BehaviorResult.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.BehaviorResult.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.BehaviorResult} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.BehaviorResult.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRet();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getLblList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      2,
      f,
      proto.com.jy.network_monitor.proto.BehaviorLabel.serializeBinaryToWriter
    );
  }
};


/**
 * optional BehaviorResultCode RET = 1;
 * @return {!proto.com.jy.network_monitor.proto.BehaviorResultCode}
 */
proto.com.jy.network_monitor.proto.BehaviorResult.prototype.getRet = function() {
  return /** @type {!proto.com.jy.network_monitor.proto.BehaviorResultCode} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {!proto.com.jy.network_monitor.proto.BehaviorResultCode} value */
proto.com.jy.network_monitor.proto.BehaviorResult.prototype.setRet = function(value) {
  jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * repeated BehaviorLabel LBL = 2;
 * @return {!Array<!proto.com.jy.network_monitor.proto.BehaviorLabel>}
 */
proto.com.jy.network_monitor.proto.BehaviorResult.prototype.getLblList = function() {
  return /** @type{!Array<!proto.com.jy.network_monitor.proto.BehaviorLabel>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.com.jy.network_monitor.proto.BehaviorLabel, 2));
};


/** @param {!Array<!proto.com.jy.network_monitor.proto.BehaviorLabel>} value */
proto.com.jy.network_monitor.proto.BehaviorResult.prototype.setLblList = function(value) {
  jspb.Message.setRepeatedWrapperField(this, 2, value);
};


/**
 * @param {!proto.com.jy.network_monitor.proto.BehaviorLabel=} opt_value
 * @param {number=} opt_index
 * @return {!proto.com.jy.network_monitor.proto.BehaviorLabel}
 */
proto.com.jy.network_monitor.proto.BehaviorResult.prototype.addLbl = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 2, opt_value, proto.com.jy.network_monitor.proto.BehaviorLabel, opt_index);
};


proto.com.jy.network_monitor.proto.BehaviorResult.prototype.clearLblList = function() {
  this.setLblList([]);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.BehaviorLabel = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.BehaviorLabel, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.BehaviorLabel.displayName = 'proto.com.jy.network_monitor.proto.BehaviorLabel';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.BehaviorLabel.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.BehaviorLabel.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.BehaviorLabel} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.BehaviorLabel.toObject = function(includeInstance, msg) {
  var f, obj = {
    n: jspb.Message.getFieldWithDefault(msg, 1, 0),
    l: jspb.Message.getFieldWithDefault(msg, 2, ""),
    r: jspb.Message.getFieldWithDefault(msg, 3, ""),
    id: jspb.Message.getFieldWithDefault(msg, 4, ""),
    rds: jspb.Message.getFieldWithDefault(msg, 5, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.BehaviorLabel}
 */
proto.com.jy.network_monitor.proto.BehaviorLabel.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.BehaviorLabel;
  return proto.com.jy.network_monitor.proto.BehaviorLabel.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.BehaviorLabel} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.BehaviorLabel}
 */
proto.com.jy.network_monitor.proto.BehaviorLabel.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.com.jy.network_monitor.proto.LabelType} */ (reader.readEnum());
      msg.setN(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setL(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setR(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setId(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setRds(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.BehaviorLabel.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.BehaviorLabel.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.BehaviorLabel} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.BehaviorLabel.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getN();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getL();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getR();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getId();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getRds();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
};


/**
 * optional LabelType N = 1;
 * @return {!proto.com.jy.network_monitor.proto.LabelType}
 */
proto.com.jy.network_monitor.proto.BehaviorLabel.prototype.getN = function() {
  return /** @type {!proto.com.jy.network_monitor.proto.LabelType} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {!proto.com.jy.network_monitor.proto.LabelType} value */
proto.com.jy.network_monitor.proto.BehaviorLabel.prototype.setN = function(value) {
  jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional string L = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.BehaviorLabel.prototype.getL = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.BehaviorLabel.prototype.setL = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string R = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.BehaviorLabel.prototype.getR = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.BehaviorLabel.prototype.setR = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string ID = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.BehaviorLabel.prototype.getId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.BehaviorLabel.prototype.setId = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string RDS = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.BehaviorLabel.prototype.getRds = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.BehaviorLabel.prototype.setRds = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.AttackFrameworkDic = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.AttackFrameworkDic, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.AttackFrameworkDic.displayName = 'proto.com.jy.network_monitor.proto.AttackFrameworkDic';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.AttackFrameworkDic.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.AttackFrameworkDic} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.toObject = function(includeInstance, msg) {
  var f, obj = {
    tacticId: jspb.Message.getFieldWithDefault(msg, 1, ""),
    tacticName: jspb.Message.getFieldWithDefault(msg, 2, ""),
    techId: jspb.Message.getFieldWithDefault(msg, 3, ""),
    techName: jspb.Message.getFieldWithDefault(msg, 4, ""),
    subTechId: jspb.Message.getFieldWithDefault(msg, 5, ""),
    subTechName: jspb.Message.getFieldWithDefault(msg, 6, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.AttackFrameworkDic}
 */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.AttackFrameworkDic;
  return proto.com.jy.network_monitor.proto.AttackFrameworkDic.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.AttackFrameworkDic} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.AttackFrameworkDic}
 */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setTacticId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setTacticName(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setTechId(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setTechName(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setSubTechId(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setSubTechName(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.AttackFrameworkDic.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.AttackFrameworkDic} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTacticId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getTacticName();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getTechId();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getTechName();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getSubTechId();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getSubTechName();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
};


/**
 * optional string tactic_id = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.prototype.getTacticId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.prototype.setTacticId = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string tactic_name = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.prototype.getTacticName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.prototype.setTacticName = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string tech_id = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.prototype.getTechId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.prototype.setTechId = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string tech_name = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.prototype.getTechName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.prototype.setTechName = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string sub_tech_id = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.prototype.getSubTechId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.prototype.setSubTechId = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string sub_tech_name = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.prototype.getSubTechName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.AttackFrameworkDic.prototype.setSubTechName = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.BehaviorResultCode = {
  BEHAVIOR_RESULT_SUCCESS: 0,
  BEHAVIOR_RESULT_FAILURE: 1,
  BEHAVIOR_RESULT_UNKNOWN: 2
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.LabelType = {
  LABEL_TYPE_BL: 0,
  LABEL_TYPE_SUS: 1
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.BaselineLabel = {
  BASELINE_DUMMY: 0,
  BASELINE_SERVER_NET_ACCESS: 1,
  BASELINE_PROCESS_NET_ACCESS: 2,
  BASELINE_NET_PORT_LISTEN: 3,
  BASELINE_ARP_INFO: 4,
  BASELINE_HOST_PROCESS: 5,
  BASELINE_KEY_FILE_ACCESS: 6,
  BASELINE_KEY_FILE_CHANGE: 7,
  BASELINE_PROC_SYSCALL: 8,
  BASELINE_HOST_SERVICE: 9,
  BASELINE_HOST_DRIVER_LOAD: 10
};

goog.object.extend(exports, proto.com.jy.network_monitor.proto);
