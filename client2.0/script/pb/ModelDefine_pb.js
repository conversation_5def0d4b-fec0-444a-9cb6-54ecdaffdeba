/**
 * @fileoverview
 * @enhanceable
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!

var jspb = require('google-protobuf');
var goog = jspb;
var global = Function('return this')();

goog.exportSymbol('proto.com.jy.network_monitor.proto.TTCircle', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.TTRectangle', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.TTShape', null, global);

/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.TTShape = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, 3, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.TTShape, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.TTShape.displayName = 'proto.com.jy.network_monitor.proto.TTShape';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.TTShape.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.TTShape.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.TTShape} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.TTShape.toObject = function(includeInstance, msg) {
  var f, obj = {
    name: jspb.Message.getField(msg, 1),
    area: jspb.Message.getOptionalFloatingPointField(msg, 2)
  };

  jspb.Message.toObjectExtension(/** @type {!jspb.Message} */ (msg), obj,
      proto.com.jy.network_monitor.proto.TTShape.extensions, proto.com.jy.network_monitor.proto.TTShape.prototype.getExtension,
      includeInstance);
  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.TTShape}
 */
proto.com.jy.network_monitor.proto.TTShape.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.TTShape;
  return proto.com.jy.network_monitor.proto.TTShape.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.TTShape} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.TTShape}
 */
proto.com.jy.network_monitor.proto.TTShape.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readDouble());
      msg.setArea(value);
      break;
    default:
      jspb.Message.readBinaryExtension(msg, reader, proto.com.jy.network_monitor.proto.TTShape.extensionsBinary,
        proto.com.jy.network_monitor.proto.TTShape.prototype.getExtension,
        proto.com.jy.network_monitor.proto.TTShape.prototype.setExtension);
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.TTShape.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.TTShape.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.TTShape} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.TTShape.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = /** @type {string} */ (jspb.Message.getField(message, 1));
  if (f != null) {
    writer.writeString(
      1,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeDouble(
      2,
      f
    );
  }
  jspb.Message.serializeBinaryExtensions(message, writer,
    proto.com.jy.network_monitor.proto.TTShape.extensionsBinary, proto.com.jy.network_monitor.proto.TTShape.prototype.getExtension);
};


/**
 * required string name = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.TTShape.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.TTShape.prototype.setName = function(value) {
  jspb.Message.setField(this, 1, value);
};


proto.com.jy.network_monitor.proto.TTShape.prototype.clearName = function() {
  jspb.Message.setField(this, 1, undefined);
};


/**
 * Returns whether this field is set.
 * @return {!boolean}
 */
proto.com.jy.network_monitor.proto.TTShape.prototype.hasName = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional double area = 2;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.TTShape.prototype.getArea = function() {
  return /** @type {number} */ (+jspb.Message.getFieldWithDefault(this, 2, 0.0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.TTShape.prototype.setArea = function(value) {
  jspb.Message.setField(this, 2, value);
};


proto.com.jy.network_monitor.proto.TTShape.prototype.clearArea = function() {
  jspb.Message.setField(this, 2, undefined);
};


/**
 * Returns whether this field is set.
 * @return {!boolean}
 */
proto.com.jy.network_monitor.proto.TTShape.prototype.hasArea = function() {
  return jspb.Message.getField(this, 2) != null;
};



/**
 * The extensions registered with this message class. This is a map of
 * extension field number to fieldInfo object.
 *
 * For example:
 *     { 123: {fieldIndex: 123, fieldName: {my_field_name: 0}, ctor: proto.example.MyMessage} }
 *
 * fieldName contains the JsCompiler renamed field name property so that it
 * works in OPTIMIZED mode.
 *
 * @type {!Object<number, jspb.ExtensionFieldInfo>}
 */
proto.com.jy.network_monitor.proto.TTShape.extensions = {};


/**
 * The extensions registered with this message class. This is a map of
 * extension field number to fieldInfo object.
 *
 * For example:
 *     { 123: {fieldIndex: 123, fieldName: {my_field_name: 0}, ctor: proto.example.MyMessage} }
 *
 * fieldName contains the JsCompiler renamed field name property so that it
 * works in OPTIMIZED mode.
 *
 * @type {!Object<number, jspb.ExtensionFieldBinaryInfo>}
 */
proto.com.jy.network_monitor.proto.TTShape.extensionsBinary = {};


/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.TTCircle = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.TTCircle, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.TTCircle.displayName = 'proto.com.jy.network_monitor.proto.TTCircle';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.TTCircle.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.TTCircle.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.TTCircle} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.TTCircle.toObject = function(includeInstance, msg) {
  var f, obj = {
    radius: +jspb.Message.getField(msg, 1)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.TTCircle}
 */
proto.com.jy.network_monitor.proto.TTCircle.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.TTCircle;
  return proto.com.jy.network_monitor.proto.TTCircle.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.TTCircle} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.TTCircle}
 */
proto.com.jy.network_monitor.proto.TTCircle.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readDouble());
      msg.setRadius(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.TTCircle.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.TTCircle.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.TTCircle} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.TTCircle.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = /** @type {number} */ (jspb.Message.getField(message, 1));
  if (f != null) {
    writer.writeDouble(
      1,
      f
    );
  }
};



/**
 * A tuple of {field number, class constructor} for the extension
 * field named `circleExt`.
 * @type {!jspb.ExtensionFieldInfo<!proto.com.jy.network_monitor.proto.TTCircle>}
 */
proto.com.jy.network_monitor.proto.TTCircle.circleExt = new jspb.ExtensionFieldInfo(
    100,
    {circleExt: 0},
    proto.com.jy.network_monitor.proto.TTCircle,
     /** @type {?function((boolean|undefined),!jspb.Message=): !Object} */ (
         proto.com.jy.network_monitor.proto.TTCircle.toObject),
    0);

proto.com.jy.network_monitor.proto.TTShape.extensionsBinary[100] = new jspb.ExtensionFieldBinaryInfo(
    proto.com.jy.network_monitor.proto.TTCircle.circleExt,
    jspb.BinaryReader.prototype.readMessage,
    jspb.BinaryWriter.prototype.writeMessage,
    proto.com.jy.network_monitor.proto.TTCircle.serializeBinaryToWriter,
    proto.com.jy.network_monitor.proto.TTCircle.deserializeBinaryFromReader,
    false);
// This registers the extension field with the extended class, so that
// toObject() will function correctly.
proto.com.jy.network_monitor.proto.TTShape.extensions[100] = proto.com.jy.network_monitor.proto.TTCircle.circleExt;

/**
 * required double radius = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.TTCircle.prototype.getRadius = function() {
  return /** @type {number} */ (+jspb.Message.getFieldWithDefault(this, 1, 0.0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.TTCircle.prototype.setRadius = function(value) {
  jspb.Message.setField(this, 1, value);
};


proto.com.jy.network_monitor.proto.TTCircle.prototype.clearRadius = function() {
  jspb.Message.setField(this, 1, undefined);
};


/**
 * Returns whether this field is set.
 * @return {!boolean}
 */
proto.com.jy.network_monitor.proto.TTCircle.prototype.hasRadius = function() {
  return jspb.Message.getField(this, 1) != null;
};



/**
 * A tuple of {field number, class constructor} for the extension
 * field named `circleExt`.
 * @type {!jspb.ExtensionFieldInfo<!proto.com.jy.network_monitor.proto.TTCircle>}
 */
proto.com.jy.network_monitor.proto.TTCircle.circleExt = new jspb.ExtensionFieldInfo(
    100,
    {circleExt: 0},
    proto.com.jy.network_monitor.proto.TTCircle,
     /** @type {?function((boolean|undefined),!jspb.Message=): !Object} */ (
         proto.com.jy.network_monitor.proto.TTCircle.toObject),
    0);

proto.com.jy.network_monitor.proto.TTShape.extensionsBinary[100] = new jspb.ExtensionFieldBinaryInfo(
    proto.com.jy.network_monitor.proto.TTCircle.circleExt,
    jspb.BinaryReader.prototype.readMessage,
    jspb.BinaryWriter.prototype.writeMessage,
    proto.com.jy.network_monitor.proto.TTCircle.serializeBinaryToWriter,
    proto.com.jy.network_monitor.proto.TTCircle.deserializeBinaryFromReader,
    false);
// This registers the extension field with the extended class, so that
// toObject() will function correctly.
proto.com.jy.network_monitor.proto.TTShape.extensions[100] = proto.com.jy.network_monitor.proto.TTCircle.circleExt;


/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.TTRectangle = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.TTRectangle, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.TTRectangle.displayName = 'proto.com.jy.network_monitor.proto.TTRectangle';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.TTRectangle.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.TTRectangle.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.TTRectangle} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.TTRectangle.toObject = function(includeInstance, msg) {
  var f, obj = {
    width: +jspb.Message.getField(msg, 1),
    height: +jspb.Message.getField(msg, 2)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.TTRectangle}
 */
proto.com.jy.network_monitor.proto.TTRectangle.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.TTRectangle;
  return proto.com.jy.network_monitor.proto.TTRectangle.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.TTRectangle} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.TTRectangle}
 */
proto.com.jy.network_monitor.proto.TTRectangle.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readDouble());
      msg.setWidth(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readDouble());
      msg.setHeight(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.TTRectangle.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.TTRectangle.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.TTRectangle} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.TTRectangle.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = /** @type {number} */ (jspb.Message.getField(message, 1));
  if (f != null) {
    writer.writeDouble(
      1,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeDouble(
      2,
      f
    );
  }
};



/**
 * A tuple of {field number, class constructor} for the extension
 * field named `rectangleExt`.
 * @type {!jspb.ExtensionFieldInfo<!proto.com.jy.network_monitor.proto.TTRectangle>}
 */
proto.com.jy.network_monitor.proto.TTRectangle.rectangleExt = new jspb.ExtensionFieldInfo(
    101,
    {rectangleExt: 0},
    proto.com.jy.network_monitor.proto.TTRectangle,
     /** @type {?function((boolean|undefined),!jspb.Message=): !Object} */ (
         proto.com.jy.network_monitor.proto.TTRectangle.toObject),
    0);

proto.com.jy.network_monitor.proto.TTShape.extensionsBinary[101] = new jspb.ExtensionFieldBinaryInfo(
    proto.com.jy.network_monitor.proto.TTRectangle.rectangleExt,
    jspb.BinaryReader.prototype.readMessage,
    jspb.BinaryWriter.prototype.writeMessage,
    proto.com.jy.network_monitor.proto.TTRectangle.serializeBinaryToWriter,
    proto.com.jy.network_monitor.proto.TTRectangle.deserializeBinaryFromReader,
    false);
// This registers the extension field with the extended class, so that
// toObject() will function correctly.
proto.com.jy.network_monitor.proto.TTShape.extensions[101] = proto.com.jy.network_monitor.proto.TTRectangle.rectangleExt;

/**
 * required double width = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.TTRectangle.prototype.getWidth = function() {
  return /** @type {number} */ (+jspb.Message.getFieldWithDefault(this, 1, 0.0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.TTRectangle.prototype.setWidth = function(value) {
  jspb.Message.setField(this, 1, value);
};


proto.com.jy.network_monitor.proto.TTRectangle.prototype.clearWidth = function() {
  jspb.Message.setField(this, 1, undefined);
};


/**
 * Returns whether this field is set.
 * @return {!boolean}
 */
proto.com.jy.network_monitor.proto.TTRectangle.prototype.hasWidth = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * required double height = 2;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.TTRectangle.prototype.getHeight = function() {
  return /** @type {number} */ (+jspb.Message.getFieldWithDefault(this, 2, 0.0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.TTRectangle.prototype.setHeight = function(value) {
  jspb.Message.setField(this, 2, value);
};


proto.com.jy.network_monitor.proto.TTRectangle.prototype.clearHeight = function() {
  jspb.Message.setField(this, 2, undefined);
};


/**
 * Returns whether this field is set.
 * @return {!boolean}
 */
proto.com.jy.network_monitor.proto.TTRectangle.prototype.hasHeight = function() {
  return jspb.Message.getField(this, 2) != null;
};



/**
 * A tuple of {field number, class constructor} for the extension
 * field named `rectangleExt`.
 * @type {!jspb.ExtensionFieldInfo<!proto.com.jy.network_monitor.proto.TTRectangle>}
 */
proto.com.jy.network_monitor.proto.TTRectangle.rectangleExt = new jspb.ExtensionFieldInfo(
    101,
    {rectangleExt: 0},
    proto.com.jy.network_monitor.proto.TTRectangle,
     /** @type {?function((boolean|undefined),!jspb.Message=): !Object} */ (
         proto.com.jy.network_monitor.proto.TTRectangle.toObject),
    0);

proto.com.jy.network_monitor.proto.TTShape.extensionsBinary[101] = new jspb.ExtensionFieldBinaryInfo(
    proto.com.jy.network_monitor.proto.TTRectangle.rectangleExt,
    jspb.BinaryReader.prototype.readMessage,
    jspb.BinaryWriter.prototype.writeMessage,
    proto.com.jy.network_monitor.proto.TTRectangle.serializeBinaryToWriter,
    proto.com.jy.network_monitor.proto.TTRectangle.deserializeBinaryFromReader,
    false);
// This registers the extension field with the extended class, so that
// toObject() will function correctly.
proto.com.jy.network_monitor.proto.TTShape.extensions[101] = proto.com.jy.network_monitor.proto.TTRectangle.rectangleExt;

goog.object.extend(exports, proto.com.jy.network_monitor.proto);
