/**
 * @fileoverview
 * @enhanceable
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!

var jspb = require('google-protobuf');
var goog = jspb;
var global = Function('return this')();

goog.exportSymbol('proto.com.jy.network_monitor.proto.AppPackageType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.ErrorCode2', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.FileClass', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.FileType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_AssetAppendCallbackParam', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_AssetChangeCallbackParam', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_File2', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_LOG_INFO', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_PlatformCerts', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_ServerTask', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_ServerTaskResult', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_ServerTaskResultData', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.PrimaryStatus', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.ResultType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.ServerTaskParam', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.ServerTaskType', null, global);

/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.ServerTaskParam = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.ServerTaskParam, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.ServerTaskParam.displayName = 'proto.com.jy.network_monitor.proto.ServerTaskParam';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.ServerTaskParam.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.ServerTaskParam.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.ServerTaskParam} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.ServerTaskParam.toObject = function(includeInstance, msg) {
  var f, obj = {
    paramType: jspb.Message.getFieldWithDefault(msg, 1, 0),
    paramContent: msg.getParamContent_asB64()
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.ServerTaskParam}
 */
proto.com.jy.network_monitor.proto.ServerTaskParam.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.ServerTaskParam;
  return proto.com.jy.network_monitor.proto.ServerTaskParam.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.ServerTaskParam} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.ServerTaskParam}
 */
proto.com.jy.network_monitor.proto.ServerTaskParam.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setParamType(value);
      break;
    case 2:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setParamContent(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.ServerTaskParam.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.ServerTaskParam.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.ServerTaskParam} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.ServerTaskParam.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getParamType();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getParamContent_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      2,
      f
    );
  }
};


/**
 * optional int32 param_type = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.ServerTaskParam.prototype.getParamType = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.ServerTaskParam.prototype.setParamType = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional bytes param_content = 2;
 * @return {!(string|Uint8Array)}
 */
proto.com.jy.network_monitor.proto.ServerTaskParam.prototype.getParamContent = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * optional bytes param_content = 2;
 * This is a type-conversion wrapper around `getParamContent()`
 * @return {string}
 */
proto.com.jy.network_monitor.proto.ServerTaskParam.prototype.getParamContent_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getParamContent()));
};


/**
 * optional bytes param_content = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getParamContent()`
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.ServerTaskParam.prototype.getParamContent_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getParamContent()));
};


/** @param {!(string|Uint8Array)} value */
proto.com.jy.network_monitor.proto.ServerTaskParam.prototype.setParamContent = function(value) {
  jspb.Message.setProto3BytesField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_ServerTask = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.com.jy.network_monitor.proto.MSG_ServerTask.repeatedFields_, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_ServerTask, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_ServerTask.displayName = 'proto.com.jy.network_monitor.proto.MSG_ServerTask';
}
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.com.jy.network_monitor.proto.MSG_ServerTask.repeatedFields_ = [6];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTask.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_ServerTask.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_ServerTask} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ServerTask.toObject = function(includeInstance, msg) {
  var f, obj = {
    taskType: jspb.Message.getFieldWithDefault(msg, 1, 0),
    taskId: msg.getTaskId_asB64(),
    destIp: jspb.Message.getFieldWithDefault(msg, 3, ""),
    taskParam: (f = msg.getTaskParam()) && proto.com.jy.network_monitor.proto.ServerTaskParam.toObject(includeInstance, f),
    srcIp: jspb.Message.getFieldWithDefault(msg, 5, ""),
    gidList: jspb.Message.getRepeatedField(msg, 6)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ServerTask}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTask.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_ServerTask;
  return proto.com.jy.network_monitor.proto.MSG_ServerTask.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ServerTask} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ServerTask}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTask.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.com.jy.network_monitor.proto.ServerTaskType} */ (reader.readEnum());
      msg.setTaskType(value);
      break;
    case 2:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setTaskId(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setDestIp(value);
      break;
    case 4:
      var value = new proto.com.jy.network_monitor.proto.ServerTaskParam;
      reader.readMessage(value,proto.com.jy.network_monitor.proto.ServerTaskParam.deserializeBinaryFromReader);
      msg.setTaskParam(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setSrcIp(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.addGid(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTask.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_ServerTask.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ServerTask} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ServerTask.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTaskType();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getTaskId_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      2,
      f
    );
  }
  f = message.getDestIp();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getTaskParam();
  if (f != null) {
    writer.writeMessage(
      4,
      f,
      proto.com.jy.network_monitor.proto.ServerTaskParam.serializeBinaryToWriter
    );
  }
  f = message.getSrcIp();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getGidList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      6,
      f
    );
  }
};


/**
 * optional ServerTaskType task_type = 1;
 * @return {!proto.com.jy.network_monitor.proto.ServerTaskType}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTask.prototype.getTaskType = function() {
  return /** @type {!proto.com.jy.network_monitor.proto.ServerTaskType} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {!proto.com.jy.network_monitor.proto.ServerTaskType} value */
proto.com.jy.network_monitor.proto.MSG_ServerTask.prototype.setTaskType = function(value) {
  jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional bytes task_id = 2;
 * @return {!(string|Uint8Array)}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTask.prototype.getTaskId = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * optional bytes task_id = 2;
 * This is a type-conversion wrapper around `getTaskId()`
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTask.prototype.getTaskId_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getTaskId()));
};


/**
 * optional bytes task_id = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getTaskId()`
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTask.prototype.getTaskId_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getTaskId()));
};


/** @param {!(string|Uint8Array)} value */
proto.com.jy.network_monitor.proto.MSG_ServerTask.prototype.setTaskId = function(value) {
  jspb.Message.setProto3BytesField(this, 2, value);
};


/**
 * optional string dest_ip = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTask.prototype.getDestIp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServerTask.prototype.setDestIp = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional ServerTaskParam task_param = 4;
 * @return {?proto.com.jy.network_monitor.proto.ServerTaskParam}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTask.prototype.getTaskParam = function() {
  return /** @type{?proto.com.jy.network_monitor.proto.ServerTaskParam} */ (
    jspb.Message.getWrapperField(this, proto.com.jy.network_monitor.proto.ServerTaskParam, 4));
};


/** @param {?proto.com.jy.network_monitor.proto.ServerTaskParam|undefined} value */
proto.com.jy.network_monitor.proto.MSG_ServerTask.prototype.setTaskParam = function(value) {
  jspb.Message.setWrapperField(this, 4, value);
};


proto.com.jy.network_monitor.proto.MSG_ServerTask.prototype.clearTaskParam = function() {
  this.setTaskParam(undefined);
};


/**
 * Returns whether this field is set.
 * @return {!boolean}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTask.prototype.hasTaskParam = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional string src_ip = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTask.prototype.getSrcIp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServerTask.prototype.setSrcIp = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * repeated string GID = 6;
 * @return {!Array<string>}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTask.prototype.getGidList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 6));
};


/** @param {!Array<string>} value */
proto.com.jy.network_monitor.proto.MSG_ServerTask.prototype.setGidList = function(value) {
  jspb.Message.setField(this, 6, value || []);
};


/**
 * @param {!string} value
 * @param {number=} opt_index
 */
proto.com.jy.network_monitor.proto.MSG_ServerTask.prototype.addGid = function(value, opt_index) {
  jspb.Message.addToRepeatedField(this, 6, value, opt_index);
};


proto.com.jy.network_monitor.proto.MSG_ServerTask.prototype.clearGidList = function() {
  this.setGidList([]);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_ServerTaskResult = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_ServerTaskResult, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_ServerTaskResult.displayName = 'proto.com.jy.network_monitor.proto.MSG_ServerTaskResult';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTaskResult.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_ServerTaskResult.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_ServerTaskResult} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ServerTaskResult.toObject = function(includeInstance, msg) {
  var f, obj = {
    resultType: jspb.Message.getFieldWithDefault(msg, 1, 0),
    resultData: msg.getResultData_asB64(),
    timestamp: jspb.Message.getFieldWithDefault(msg, 3, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ServerTaskResult}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTaskResult.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_ServerTaskResult;
  return proto.com.jy.network_monitor.proto.MSG_ServerTaskResult.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ServerTaskResult} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ServerTaskResult}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTaskResult.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.com.jy.network_monitor.proto.ResultType} */ (reader.readEnum());
      msg.setResultType(value);
      break;
    case 2:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setResultData(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setTimestamp(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTaskResult.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_ServerTaskResult.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ServerTaskResult} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ServerTaskResult.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getResultType();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getResultData_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      2,
      f
    );
  }
  f = message.getTimestamp();
  if (f !== 0) {
    writer.writeInt64(
      3,
      f
    );
  }
};


/**
 * optional ResultType result_type = 1;
 * @return {!proto.com.jy.network_monitor.proto.ResultType}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTaskResult.prototype.getResultType = function() {
  return /** @type {!proto.com.jy.network_monitor.proto.ResultType} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {!proto.com.jy.network_monitor.proto.ResultType} value */
proto.com.jy.network_monitor.proto.MSG_ServerTaskResult.prototype.setResultType = function(value) {
  jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional bytes result_data = 2;
 * @return {!(string|Uint8Array)}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTaskResult.prototype.getResultData = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * optional bytes result_data = 2;
 * This is a type-conversion wrapper around `getResultData()`
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTaskResult.prototype.getResultData_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getResultData()));
};


/**
 * optional bytes result_data = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getResultData()`
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTaskResult.prototype.getResultData_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getResultData()));
};


/** @param {!(string|Uint8Array)} value */
proto.com.jy.network_monitor.proto.MSG_ServerTaskResult.prototype.setResultData = function(value) {
  jspb.Message.setProto3BytesField(this, 2, value);
};


/**
 * optional int64 timestamp = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTaskResult.prototype.getTimestamp = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ServerTaskResult.prototype.setTimestamp = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_ServerTaskResultData = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_ServerTaskResultData, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_ServerTaskResultData.displayName = 'proto.com.jy.network_monitor.proto.MSG_ServerTaskResultData';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTaskResultData.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_ServerTaskResultData.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_ServerTaskResultData} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ServerTaskResultData.toObject = function(includeInstance, msg) {
  var f, obj = {
    errorCode: jspb.Message.getFieldWithDefault(msg, 1, 0),
    resultContent: msg.getResultContent_asB64()
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ServerTaskResultData}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTaskResultData.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_ServerTaskResultData;
  return proto.com.jy.network_monitor.proto.MSG_ServerTaskResultData.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ServerTaskResultData} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ServerTaskResultData}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTaskResultData.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setErrorCode(value);
      break;
    case 2:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setResultContent(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTaskResultData.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_ServerTaskResultData.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ServerTaskResultData} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ServerTaskResultData.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getErrorCode();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getResultContent_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      2,
      f
    );
  }
};


/**
 * optional int32 error_code = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTaskResultData.prototype.getErrorCode = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ServerTaskResultData.prototype.setErrorCode = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional bytes result_content = 2;
 * @return {!(string|Uint8Array)}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTaskResultData.prototype.getResultContent = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * optional bytes result_content = 2;
 * This is a type-conversion wrapper around `getResultContent()`
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTaskResultData.prototype.getResultContent_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getResultContent()));
};


/**
 * optional bytes result_content = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getResultContent()`
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_ServerTaskResultData.prototype.getResultContent_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getResultContent()));
};


/** @param {!(string|Uint8Array)} value */
proto.com.jy.network_monitor.proto.MSG_ServerTaskResultData.prototype.setResultContent = function(value) {
  jspb.Message.setProto3BytesField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_LOG_INFO = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_LOG_INFO, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_LOG_INFO.displayName = 'proto.com.jy.network_monitor.proto.MSG_LOG_INFO';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_LOG_INFO.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_LOG_INFO.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_LOG_INFO} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_LOG_INFO.toObject = function(includeInstance, msg) {
  var f, obj = {
    gid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    logType: jspb.Message.getFieldWithDefault(msg, 2, ""),
    devType: jspb.Message.getFieldWithDefault(msg, 3, ""),
    eventLevel: jspb.Message.getFieldWithDefault(msg, 4, 0),
    eventTime: jspb.Message.getFieldWithDefault(msg, 5, 0),
    eventContent: jspb.Message.getFieldWithDefault(msg, 6, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_LOG_INFO}
 */
proto.com.jy.network_monitor.proto.MSG_LOG_INFO.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_LOG_INFO;
  return proto.com.jy.network_monitor.proto.MSG_LOG_INFO.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_LOG_INFO} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_LOG_INFO}
 */
proto.com.jy.network_monitor.proto.MSG_LOG_INFO.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setGid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setLogType(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setDevType(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setEventLevel(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setEventTime(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setEventContent(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_LOG_INFO.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_LOG_INFO.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_LOG_INFO} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_LOG_INFO.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getGid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getLogType();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getDevType();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getEventLevel();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
  f = message.getEventTime();
  if (f !== 0) {
    writer.writeInt64(
      5,
      f
    );
  }
  f = message.getEventContent();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
};


/**
 * optional string GID = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_LOG_INFO.prototype.getGid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_LOG_INFO.prototype.setGid = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string log_type = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_LOG_INFO.prototype.getLogType = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_LOG_INFO.prototype.setLogType = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string dev_type = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_LOG_INFO.prototype.getDevType = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_LOG_INFO.prototype.setDevType = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional int32 event_level = 4;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_LOG_INFO.prototype.getEventLevel = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_LOG_INFO.prototype.setEventLevel = function(value) {
  jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional int64 event_time = 5;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_LOG_INFO.prototype.getEventTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_LOG_INFO.prototype.setEventTime = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional string event_content = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_LOG_INFO.prototype.getEventContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_LOG_INFO.prototype.setEventContent = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_File2 = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.com.jy.network_monitor.proto.MSG_File2.repeatedFields_, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_File2, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_File2.displayName = 'proto.com.jy.network_monitor.proto.MSG_File2';
}
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.com.jy.network_monitor.proto.MSG_File2.repeatedFields_ = [17];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_File2.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_File2} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_File2.toObject = function(includeInstance, msg) {
  var f, obj = {
    fid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    softid: jspb.Message.getFieldWithDefault(msg, 2, ""),
    uid: jspb.Message.getFieldWithDefault(msg, 3, ""),
    grpid: jspb.Message.getFieldWithDefault(msg, 4, ""),
    name: jspb.Message.getFieldWithDefault(msg, 5, ""),
    type: jspb.Message.getFieldWithDefault(msg, 6, 0),
    path: jspb.Message.getFieldWithDefault(msg, 7, ""),
    perm: jspb.Message.getFieldWithDefault(msg, 8, ""),
    fhash: jspb.Message.getFieldWithDefault(msg, 9, ""),
    fsize: jspb.Message.getFieldWithDefault(msg, 10, 0),
    ltfile: jspb.Message.getFieldWithDefault(msg, 11, ""),
    ctime: jspb.Message.getFieldWithDefault(msg, 12, ""),
    latime: jspb.Message.getFieldWithDefault(msg, 13, ""),
    lmtime: jspb.Message.getFieldWithDefault(msg, 14, ""),
    lctime: jspb.Message.getFieldWithDefault(msg, 15, ""),
    ino: jspb.Message.getFieldWithDefault(msg, 16, ""),
    dllList: jspb.Message.toObjectList(msg.getDllList(),
    proto.com.jy.network_monitor.proto.MSG_File2.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_File2}
 */
proto.com.jy.network_monitor.proto.MSG_File2.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_File2;
  return proto.com.jy.network_monitor.proto.MSG_File2.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_File2} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_File2}
 */
proto.com.jy.network_monitor.proto.MSG_File2.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setFid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSoftid(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setUid(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setGrpid(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setType(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setPath(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setPerm(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setFhash(value);
      break;
    case 10:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setFsize(value);
      break;
    case 11:
      var value = /** @type {string} */ (reader.readString());
      msg.setLtfile(value);
      break;
    case 12:
      var value = /** @type {string} */ (reader.readString());
      msg.setCtime(value);
      break;
    case 13:
      var value = /** @type {string} */ (reader.readString());
      msg.setLatime(value);
      break;
    case 14:
      var value = /** @type {string} */ (reader.readString());
      msg.setLmtime(value);
      break;
    case 15:
      var value = /** @type {string} */ (reader.readString());
      msg.setLctime(value);
      break;
    case 16:
      var value = /** @type {string} */ (reader.readString());
      msg.setIno(value);
      break;
    case 17:
      var value = new proto.com.jy.network_monitor.proto.MSG_File2;
      reader.readMessage(value,proto.com.jy.network_monitor.proto.MSG_File2.deserializeBinaryFromReader);
      msg.addDll(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_File2.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_File2} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_File2.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getSoftid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getUid();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getGrpid();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getType();
  if (f !== 0) {
    writer.writeInt32(
      6,
      f
    );
  }
  f = message.getPath();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getPerm();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getFhash();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
  f = message.getFsize();
  if (f !== 0) {
    writer.writeInt64(
      10,
      f
    );
  }
  f = message.getLtfile();
  if (f.length > 0) {
    writer.writeString(
      11,
      f
    );
  }
  f = message.getCtime();
  if (f.length > 0) {
    writer.writeString(
      12,
      f
    );
  }
  f = message.getLatime();
  if (f.length > 0) {
    writer.writeString(
      13,
      f
    );
  }
  f = message.getLmtime();
  if (f.length > 0) {
    writer.writeString(
      14,
      f
    );
  }
  f = message.getLctime();
  if (f.length > 0) {
    writer.writeString(
      15,
      f
    );
  }
  f = message.getIno();
  if (f.length > 0) {
    writer.writeString(
      16,
      f
    );
  }
  f = message.getDllList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      17,
      f,
      proto.com.jy.network_monitor.proto.MSG_File2.serializeBinaryToWriter
    );
  }
};


/**
 * optional string FID = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.getFid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.setFid = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string SOFTID = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.getSoftid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.setSoftid = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string UID = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.getUid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.setUid = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string GRPID = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.getGrpid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.setGrpid = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string NAME = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.setName = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional int32 TYPE = 6;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.getType = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.setType = function(value) {
  jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional string PATH = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.getPath = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.setPath = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string PERM = 8;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.getPerm = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.setPerm = function(value) {
  jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional string FHASH = 9;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.getFhash = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.setFhash = function(value) {
  jspb.Message.setProto3StringField(this, 9, value);
};


/**
 * optional int64 FSIZE = 10;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.getFsize = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 10, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.setFsize = function(value) {
  jspb.Message.setProto3IntField(this, 10, value);
};


/**
 * optional string LTFILE = 11;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.getLtfile = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 11, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.setLtfile = function(value) {
  jspb.Message.setProto3StringField(this, 11, value);
};


/**
 * optional string CTIME = 12;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.getCtime = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 12, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.setCtime = function(value) {
  jspb.Message.setProto3StringField(this, 12, value);
};


/**
 * optional string LATIME = 13;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.getLatime = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 13, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.setLatime = function(value) {
  jspb.Message.setProto3StringField(this, 13, value);
};


/**
 * optional string LMTIME = 14;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.getLmtime = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 14, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.setLmtime = function(value) {
  jspb.Message.setProto3StringField(this, 14, value);
};


/**
 * optional string LCTIME = 15;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.getLctime = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 15, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.setLctime = function(value) {
  jspb.Message.setProto3StringField(this, 15, value);
};


/**
 * optional string INO = 16;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.getIno = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 16, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.setIno = function(value) {
  jspb.Message.setProto3StringField(this, 16, value);
};


/**
 * repeated MSG_File2 DLL = 17;
 * @return {!Array<!proto.com.jy.network_monitor.proto.MSG_File2>}
 */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.getDllList = function() {
  return /** @type{!Array<!proto.com.jy.network_monitor.proto.MSG_File2>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.com.jy.network_monitor.proto.MSG_File2, 17));
};


/** @param {!Array<!proto.com.jy.network_monitor.proto.MSG_File2>} value */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.setDllList = function(value) {
  jspb.Message.setRepeatedWrapperField(this, 17, value);
};


/**
 * @param {!proto.com.jy.network_monitor.proto.MSG_File2=} opt_value
 * @param {number=} opt_index
 * @return {!proto.com.jy.network_monitor.proto.MSG_File2}
 */
proto.com.jy.network_monitor.proto.MSG_File2.prototype.addDll = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 17, opt_value, proto.com.jy.network_monitor.proto.MSG_File2, opt_index);
};


proto.com.jy.network_monitor.proto.MSG_File2.prototype.clearDllList = function() {
  this.setDllList([]);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_AssetChangeCallbackParam = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.com.jy.network_monitor.proto.MSG_AssetChangeCallbackParam.repeatedFields_, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_AssetChangeCallbackParam, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_AssetChangeCallbackParam.displayName = 'proto.com.jy.network_monitor.proto.MSG_AssetChangeCallbackParam';
}
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.com.jy.network_monitor.proto.MSG_AssetChangeCallbackParam.repeatedFields_ = [2];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_AssetChangeCallbackParam.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_AssetChangeCallbackParam.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_AssetChangeCallbackParam} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_AssetChangeCallbackParam.toObject = function(includeInstance, msg) {
  var f, obj = {
    operateCode: jspb.Message.getFieldWithDefault(msg, 1, 0),
    gidListList: jspb.Message.getRepeatedField(msg, 2)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_AssetChangeCallbackParam}
 */
proto.com.jy.network_monitor.proto.MSG_AssetChangeCallbackParam.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_AssetChangeCallbackParam;
  return proto.com.jy.network_monitor.proto.MSG_AssetChangeCallbackParam.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_AssetChangeCallbackParam} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_AssetChangeCallbackParam}
 */
proto.com.jy.network_monitor.proto.MSG_AssetChangeCallbackParam.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setOperateCode(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.addGidList(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_AssetChangeCallbackParam.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_AssetChangeCallbackParam.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_AssetChangeCallbackParam} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_AssetChangeCallbackParam.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getOperateCode();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getGidListList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      2,
      f
    );
  }
};


/**
 * optional int32 operate_code = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_AssetChangeCallbackParam.prototype.getOperateCode = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_AssetChangeCallbackParam.prototype.setOperateCode = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * repeated string gid_list = 2;
 * @return {!Array<string>}
 */
proto.com.jy.network_monitor.proto.MSG_AssetChangeCallbackParam.prototype.getGidListList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 2));
};


/** @param {!Array<string>} value */
proto.com.jy.network_monitor.proto.MSG_AssetChangeCallbackParam.prototype.setGidListList = function(value) {
  jspb.Message.setField(this, 2, value || []);
};


/**
 * @param {!string} value
 * @param {number=} opt_index
 */
proto.com.jy.network_monitor.proto.MSG_AssetChangeCallbackParam.prototype.addGidList = function(value, opt_index) {
  jspb.Message.addToRepeatedField(this, 2, value, opt_index);
};


proto.com.jy.network_monitor.proto.MSG_AssetChangeCallbackParam.prototype.clearGidListList = function() {
  this.setGidListList([]);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_AssetAppendCallbackParam = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.com.jy.network_monitor.proto.MSG_AssetAppendCallbackParam.repeatedFields_, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_AssetAppendCallbackParam, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_AssetAppendCallbackParam.displayName = 'proto.com.jy.network_monitor.proto.MSG_AssetAppendCallbackParam';
}
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.com.jy.network_monitor.proto.MSG_AssetAppendCallbackParam.repeatedFields_ = [2];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_AssetAppendCallbackParam.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_AssetAppendCallbackParam.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_AssetAppendCallbackParam} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_AssetAppendCallbackParam.toObject = function(includeInstance, msg) {
  var f, obj = {
    gid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    atagListList: jspb.Message.getRepeatedField(msg, 2)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_AssetAppendCallbackParam}
 */
proto.com.jy.network_monitor.proto.MSG_AssetAppendCallbackParam.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_AssetAppendCallbackParam;
  return proto.com.jy.network_monitor.proto.MSG_AssetAppendCallbackParam.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_AssetAppendCallbackParam} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_AssetAppendCallbackParam}
 */
proto.com.jy.network_monitor.proto.MSG_AssetAppendCallbackParam.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setGid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.addAtagList(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_AssetAppendCallbackParam.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_AssetAppendCallbackParam.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_AssetAppendCallbackParam} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_AssetAppendCallbackParam.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getGid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getAtagListList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      2,
      f
    );
  }
};


/**
 * optional string gid = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_AssetAppendCallbackParam.prototype.getGid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_AssetAppendCallbackParam.prototype.setGid = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * repeated string atag_list = 2;
 * @return {!Array<string>}
 */
proto.com.jy.network_monitor.proto.MSG_AssetAppendCallbackParam.prototype.getAtagListList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 2));
};


/** @param {!Array<string>} value */
proto.com.jy.network_monitor.proto.MSG_AssetAppendCallbackParam.prototype.setAtagListList = function(value) {
  jspb.Message.setField(this, 2, value || []);
};


/**
 * @param {!string} value
 * @param {number=} opt_index
 */
proto.com.jy.network_monitor.proto.MSG_AssetAppendCallbackParam.prototype.addAtagList = function(value, opt_index) {
  jspb.Message.addToRepeatedField(this, 2, value, opt_index);
};


proto.com.jy.network_monitor.proto.MSG_AssetAppendCallbackParam.prototype.clearAtagListList = function() {
  this.setAtagListList([]);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCerts = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.com.jy.network_monitor.proto.MSG_PlatformCerts.repeatedFields_, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_PlatformCerts, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_PlatformCerts.displayName = 'proto.com.jy.network_monitor.proto.MSG_PlatformCerts';
}
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCerts.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCerts.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_PlatformCerts.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_PlatformCerts} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCerts.toObject = function(includeInstance, msg) {
  var f, obj = {
    addinfoList: jspb.Message.toObjectList(msg.getAddinfoList(),
    proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_PlatformCerts}
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCerts.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_PlatformCerts;
  return proto.com.jy.network_monitor.proto.MSG_PlatformCerts.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_PlatformCerts} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_PlatformCerts}
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCerts.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert;
      reader.readMessage(value,proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert.deserializeBinaryFromReader);
      msg.addAddinfo(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCerts.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_PlatformCerts.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_PlatformCerts} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCerts.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAddinfoList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert.serializeBinaryToWriter
    );
  }
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert.displayName = 'proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, 0),
    ip: jspb.Message.getFieldWithDefault(msg, 2, ""),
    name: jspb.Message.getFieldWithDefault(msg, 3, ""),
    fileSize: jspb.Message.getFieldWithDefault(msg, 4, 0),
    fileAbsolutePath: jspb.Message.getFieldWithDefault(msg, 5, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert}
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert;
  return proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert}
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setIp(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setFileSize(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setFileAbsolutePath(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getIp();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getFileSize();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
  f = message.getFileAbsolutePath();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
};


/**
 * optional int32 id = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert.prototype.getId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert.prototype.setId = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string ip = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert.prototype.getIp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert.prototype.setIp = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string name = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert.prototype.setName = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional int32 file_size = 4;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert.prototype.getFileSize = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert.prototype.setFileSize = function(value) {
  jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional string file_absolute_path = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert.prototype.getFileAbsolutePath = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert.prototype.setFileAbsolutePath = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * repeated PlatformCert addInfo = 1;
 * @return {!Array<!proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert>}
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCerts.prototype.getAddinfoList = function() {
  return /** @type{!Array<!proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert, 1));
};


/** @param {!Array<!proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert>} value */
proto.com.jy.network_monitor.proto.MSG_PlatformCerts.prototype.setAddinfoList = function(value) {
  jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert=} opt_value
 * @param {number=} opt_index
 * @return {!proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert}
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCerts.prototype.addAddinfo = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.com.jy.network_monitor.proto.MSG_PlatformCerts.PlatformCert, opt_index);
};


proto.com.jy.network_monitor.proto.MSG_PlatformCerts.prototype.clearAddinfoList = function() {
  this.setAddinfoList([]);
};


/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.ErrorCode2 = {
  ERRORCODE2_SUCCESS: 0,
  ERRORCODE2_INNERERROR: 1,
  ERRORCODE2_UNSUPPORTEDTYPE: 2,
  ERRORCODE2_WRONGPARAM: 3,
  ERRORCODE2_OBJECTNOTEXIST: 4,
  ERRORCODE2_OBJECTUNREACHABLE: 5,
  ERRORCODE2_INVALIDCONTENTFORMAT: 6,
  ERRORCODE2_TIMESTAMPMISMATCH: 7,
  ERRORCODE2_INVALIDSIGN: 8,
  ERRORCODE2_CANNOT_EXEC_CMD: 9,
  ERRORCODE2_DATAVERIFYFAILED: 10,
  ERRORCODE2_INVALIDCERTFORMAT: 11,
  ERRORCODE2_CAERROR: 12,
  ERRORCODE2_NOCACHAIN: 13,
  ERRORCODE2_CERTVALIDATEERROR: 14
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.ServerTaskType = {
  SERVER_TASK_TYPE_READ_PARAM: 0,
  SERVER_TASK_TYPE_WRITE_PARAM: 1,
  SERVER_TASK_TYPE_BASELINE: 2,
  SERVER_TASK_TYPE_CLOSE_NETWORK: 3,
  SERVER_TASK_TYPE_LEAK_SCAN: 4,
  SERVER_TASK_TYPE_VERSION_MANAGE: 5,
  SERVER_TASK_TYPE_CHARACTER_UPDATE: 6,
  SERVER_TASK_TYPE_BLOCK_LINK: 7,
  SERVER_TASK_TYPE_RELOAD_DB: 100,
  SERVER_TASK_TYPE_NOTIFY_WARNING: 101,
  SERVER_TASK_TYPE_NOTIFY_LOG_INFO: 102,
  SERVER_TASK_TYPE_NOTIFY_MODEL_INFO: 103,
  SERVER_TASK_TYPE_NOTIFY_UNCONFIRMED_ASSET: 104,
  SERVER_TASK_TYPE_NOTIFY_SOFTWARE_HARDWARE_STATUS: 105,
  SERVER_TASK_TYPE_NOTIFY_SENSOR_UPDATE_LISTEN: 106,
  SERVER_TASK_TYPE_NOTIFY_ASSET_ONLINE: 107,
  SERVER_TASK_TYPE_NOTIFY_ASSET_OFFLINE: 108,
  SERVER_TASK_TYPE_NOTIFY_ASSET_CHANGE: 109,
  SERVER_TASK_TYPE_NOTIFY_ASSET_REGISTER: 110,
  SERVER_TASK_TYPE_NOTIFY_ASSET_APPEND: 111,
  SERVER_TASK_TYPE_RELOAD_PLATFORM_CERT: 112,
  SERVER_TASK_TYPE_NOTIFY_DETECT_NETWORK: 113,
  SERVER_TASK_TYPE_INTERACT_WITH_SVR: 200,
  SERVER_TASK_TYPE_INTERACT_WITH_FW_SW: 201,
  SERVER_TASK_TYPE_CHECK_BASELINE: 202,
  SERVER_TASK_TYPE_SCAN_VULN: 203,
  SERVER_TASK_TYPE_INTERACT_WITH_PLUGIN: 204,
  SERVER_TASK_TYPE_SOFTWARE_UPGRADE: 205
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.ResultType = {
  RESULTTYPE_DUMMY: 0,
  RESULTTYPE_BASELINE_START: 1,
  RESULTTYPE_BASELINE_RESULT: 2,
  RESULTTYPE_LEAKSCAN_START: 3,
  RESULTTYPE_LEAKSCAN_RESULT: 4,
  RESULTTYPE_VERSION_VERIFY_START: 5,
  RESULTTYPE_VERSION_VERIFY_RESULT: 6,
  RESULTTYPE_OTHER: 99
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.AppPackageType = {
  APPPACKAGETYPE_DUMMY: 0,
  APPPACKAGETYPE_RPM: 1,
  APPPACKAGETYPE_DEB: 2,
  APPPACKAGETYPE_EXE: 3,
  APPPACKAGETYPE_MSI: 4,
  APPPACKAGETYPE_OTHER: 99
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.FileType = {
  FILE_TYPE_UNSPECIFIED: 0,
  FILE_TYPE_REGULAR: 1,
  FILE_TYPE_DIRECTORY: 2,
  FILE_TYPE_LINK: 3
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.FileClass = {
  FILE_CLASS_UNSPECIFIED: 0,
  FILE_CLASS_REGULAR: 1,
  FILE_CLASS_DYNAMIC_LIB: 2,
  FILE_CLASS_EXECUTABLE: 3,
  FILE_CLASS_SCRIPT: 4,
  FILE_CLASS_OTHER: 5
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.PrimaryStatus = {
  PRIMARYSTATUS_DUMMY: 0,
  PRIMARYSTATUS_PRIMARY: 1,
  PRIMARYSTATUS_STANDBY: 2
};

goog.object.extend(exports, proto.com.jy.network_monitor.proto);
