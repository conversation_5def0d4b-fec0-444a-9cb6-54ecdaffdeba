/**
 * @fileoverview
 * @enhanceable
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!

var jspb = require('google-protobuf');
var goog = jspb;
var global = Function('return this')();

var google_protobuf_any_pb = require('google-protobuf/google/protobuf/any_pb.js');
goog.exportSymbol('proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.AddrChangeExt', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.AgentSrcplugChangeExt', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.AppOperExt', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.BootTaskChange', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.CoreFileChangeExt', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.ExecFileChangeExt', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.FileAddDelExt', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.FileChangeExt', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_Behavior', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_PROC_SYS_CALL_OBJ', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SecurityDeviceNetworkAccessDetail', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.DATA_DIREACTION', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.Role', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_TrafficDetail', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_Upload_Behavior', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.NetAccessExt', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.NetFileTransfer', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.NetIntChangeExt', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.PartMountUnmExt', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.ServParamModExt', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.SessionLoginExt', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.SessionOperExt', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.UserAttrChangeExt', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.UserGrpNameChangeExt', null, global);

/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_Upload_Behavior, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.displayName = 'proto.com.jy.network_monitor.proto.MSG_Upload_Behavior';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_Upload_Behavior} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.toObject = function(includeInstance, msg) {
  var f, obj = {
    code: jspb.Message.getFieldWithDefault(msg, 1, 0),
    uuid: jspb.Message.getFieldWithDefault(msg, 2, ""),
    gid: jspb.Message.getFieldWithDefault(msg, 3, ""),
    atag: jspb.Message.getFieldWithDefault(msg, 4, ""),
    time: jspb.Message.getFieldWithDefault(msg, 5, ""),
    sub: (f = msg.getSub()) && google_protobuf_any_pb.Any.toObject(includeInstance, f),
    subGid: jspb.Message.getFieldWithDefault(msg, 7, ""),
    subAtag: jspb.Message.getFieldWithDefault(msg, 8, ""),
    obj: (f = msg.getObj()) && google_protobuf_any_pb.Any.toObject(includeInstance, f),
    objGid: jspb.Message.getFieldWithDefault(msg, 10, ""),
    objAtag: jspb.Message.getFieldWithDefault(msg, 11, ""),
    type: jspb.Message.getFieldWithDefault(msg, 12, 0),
    ret: jspb.Message.getFieldWithDefault(msg, 13, ""),
    ext: (f = msg.getExt()) && google_protobuf_any_pb.Any.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_Upload_Behavior}
 */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_Upload_Behavior;
  return proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_Upload_Behavior} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_Upload_Behavior}
 */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setCode(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setUuid(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setGid(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setAtag(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setTime(value);
      break;
    case 6:
      var value = new google_protobuf_any_pb.Any;
      reader.readMessage(value,google_protobuf_any_pb.Any.deserializeBinaryFromReader);
      msg.setSub(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setSubGid(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setSubAtag(value);
      break;
    case 9:
      var value = new google_protobuf_any_pb.Any;
      reader.readMessage(value,google_protobuf_any_pb.Any.deserializeBinaryFromReader);
      msg.setObj(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setObjGid(value);
      break;
    case 11:
      var value = /** @type {string} */ (reader.readString());
      msg.setObjAtag(value);
      break;
    case 12:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setType(value);
      break;
    case 13:
      var value = /** @type {string} */ (reader.readString());
      msg.setRet(value);
      break;
    case 14:
      var value = new google_protobuf_any_pb.Any;
      reader.readMessage(value,google_protobuf_any_pb.Any.deserializeBinaryFromReader);
      msg.setExt(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_Upload_Behavior} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getCode();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getUuid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getGid();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getAtag();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getTime();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getSub();
  if (f != null) {
    writer.writeMessage(
      6,
      f,
      google_protobuf_any_pb.Any.serializeBinaryToWriter
    );
  }
  f = message.getSubGid();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getSubAtag();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getObj();
  if (f != null) {
    writer.writeMessage(
      9,
      f,
      google_protobuf_any_pb.Any.serializeBinaryToWriter
    );
  }
  f = message.getObjGid();
  if (f.length > 0) {
    writer.writeString(
      10,
      f
    );
  }
  f = message.getObjAtag();
  if (f.length > 0) {
    writer.writeString(
      11,
      f
    );
  }
  f = message.getType();
  if (f !== 0) {
    writer.writeInt32(
      12,
      f
    );
  }
  f = message.getRet();
  if (f.length > 0) {
    writer.writeString(
      13,
      f
    );
  }
  f = message.getExt();
  if (f != null) {
    writer.writeMessage(
      14,
      f,
      google_protobuf_any_pb.Any.serializeBinaryToWriter
    );
  }
};


/**
 * optional int32 CODE = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.getCode = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.setCode = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string UUID = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.getUuid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.setUuid = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string GID = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.getGid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.setGid = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string ATAG = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.getAtag = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.setAtag = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string TIME = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.getTime = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.setTime = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional google.protobuf.Any SUB = 6;
 * @return {?proto.google.protobuf.Any}
 */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.getSub = function() {
  return /** @type{?proto.google.protobuf.Any} */ (
    jspb.Message.getWrapperField(this, google_protobuf_any_pb.Any, 6));
};


/** @param {?proto.google.protobuf.Any|undefined} value */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.setSub = function(value) {
  jspb.Message.setWrapperField(this, 6, value);
};


proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.clearSub = function() {
  this.setSub(undefined);
};


/**
 * Returns whether this field is set.
 * @return {!boolean}
 */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.hasSub = function() {
  return jspb.Message.getField(this, 6) != null;
};


/**
 * optional string SUB_GID = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.getSubGid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.setSubGid = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string SUB_ATAG = 8;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.getSubAtag = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.setSubAtag = function(value) {
  jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional google.protobuf.Any OBJ = 9;
 * @return {?proto.google.protobuf.Any}
 */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.getObj = function() {
  return /** @type{?proto.google.protobuf.Any} */ (
    jspb.Message.getWrapperField(this, google_protobuf_any_pb.Any, 9));
};


/** @param {?proto.google.protobuf.Any|undefined} value */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.setObj = function(value) {
  jspb.Message.setWrapperField(this, 9, value);
};


proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.clearObj = function() {
  this.setObj(undefined);
};


/**
 * Returns whether this field is set.
 * @return {!boolean}
 */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.hasObj = function() {
  return jspb.Message.getField(this, 9) != null;
};


/**
 * optional string OBJ_GID = 10;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.getObjGid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.setObjGid = function(value) {
  jspb.Message.setProto3StringField(this, 10, value);
};


/**
 * optional string OBJ_ATAG = 11;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.getObjAtag = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 11, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.setObjAtag = function(value) {
  jspb.Message.setProto3StringField(this, 11, value);
};


/**
 * optional int32 TYPE = 12;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.getType = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 12, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.setType = function(value) {
  jspb.Message.setProto3IntField(this, 12, value);
};


/**
 * optional string RET = 13;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.getRet = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 13, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.setRet = function(value) {
  jspb.Message.setProto3StringField(this, 13, value);
};


/**
 * optional google.protobuf.Any EXT = 14;
 * @return {?proto.google.protobuf.Any}
 */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.getExt = function() {
  return /** @type{?proto.google.protobuf.Any} */ (
    jspb.Message.getWrapperField(this, google_protobuf_any_pb.Any, 14));
};


/** @param {?proto.google.protobuf.Any|undefined} value */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.setExt = function(value) {
  jspb.Message.setWrapperField(this, 14, value);
};


proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.clearExt = function() {
  this.setExt(undefined);
};


/**
 * Returns whether this field is set.
 * @return {!boolean}
 */
proto.com.jy.network_monitor.proto.MSG_Upload_Behavior.prototype.hasExt = function() {
  return jspb.Message.getField(this, 14) != null;
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_Behavior = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_Behavior, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_Behavior.displayName = 'proto.com.jy.network_monitor.proto.MSG_Behavior';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_Behavior.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_Behavior.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_Behavior} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_Behavior.toObject = function(includeInstance, msg) {
  var f, obj = {
    code: jspb.Message.getFieldWithDefault(msg, 1, 0),
    uuid: jspb.Message.getFieldWithDefault(msg, 2, ""),
    gid: jspb.Message.getFieldWithDefault(msg, 3, ""),
    atag: jspb.Message.getFieldWithDefault(msg, 4, ""),
    time: jspb.Message.getFieldWithDefault(msg, 5, ""),
    sub: jspb.Message.getFieldWithDefault(msg, 6, ""),
    obj: jspb.Message.getFieldWithDefault(msg, 7, ""),
    type: jspb.Message.getFieldWithDefault(msg, 8, 0),
    ret: jspb.Message.getFieldWithDefault(msg, 9, ""),
    ext: (f = msg.getExt()) && google_protobuf_any_pb.Any.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_Behavior}
 */
proto.com.jy.network_monitor.proto.MSG_Behavior.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_Behavior;
  return proto.com.jy.network_monitor.proto.MSG_Behavior.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_Behavior} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_Behavior}
 */
proto.com.jy.network_monitor.proto.MSG_Behavior.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setCode(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setUuid(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setGid(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setAtag(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setTime(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setSub(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setObj(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setType(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setRet(value);
      break;
    case 10:
      var value = new google_protobuf_any_pb.Any;
      reader.readMessage(value,google_protobuf_any_pb.Any.deserializeBinaryFromReader);
      msg.setExt(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_Behavior.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_Behavior.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_Behavior} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_Behavior.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getCode();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getUuid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getGid();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getAtag();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getTime();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getSub();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getObj();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getType();
  if (f !== 0) {
    writer.writeInt32(
      8,
      f
    );
  }
  f = message.getRet();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
  f = message.getExt();
  if (f != null) {
    writer.writeMessage(
      10,
      f,
      google_protobuf_any_pb.Any.serializeBinaryToWriter
    );
  }
};


/**
 * optional int32 CODE = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_Behavior.prototype.getCode = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_Behavior.prototype.setCode = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string UUID = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Behavior.prototype.getUuid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Behavior.prototype.setUuid = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string GID = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Behavior.prototype.getGid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Behavior.prototype.setGid = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string ATAG = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Behavior.prototype.getAtag = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Behavior.prototype.setAtag = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string TIME = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Behavior.prototype.getTime = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Behavior.prototype.setTime = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string SUB = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Behavior.prototype.getSub = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Behavior.prototype.setSub = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string OBJ = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Behavior.prototype.getObj = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Behavior.prototype.setObj = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional int32 TYPE = 8;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_Behavior.prototype.getType = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_Behavior.prototype.setType = function(value) {
  jspb.Message.setProto3IntField(this, 8, value);
};


/**
 * optional string RET = 9;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_Behavior.prototype.getRet = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_Behavior.prototype.setRet = function(value) {
  jspb.Message.setProto3StringField(this, 9, value);
};


/**
 * optional google.protobuf.Any EXT = 10;
 * @return {?proto.google.protobuf.Any}
 */
proto.com.jy.network_monitor.proto.MSG_Behavior.prototype.getExt = function() {
  return /** @type{?proto.google.protobuf.Any} */ (
    jspb.Message.getWrapperField(this, google_protobuf_any_pb.Any, 10));
};


/** @param {?proto.google.protobuf.Any|undefined} value */
proto.com.jy.network_monitor.proto.MSG_Behavior.prototype.setExt = function(value) {
  jspb.Message.setWrapperField(this, 10, value);
};


proto.com.jy.network_monitor.proto.MSG_Behavior.prototype.clearExt = function() {
  this.setExt(undefined);
};


/**
 * Returns whether this field is set.
 * @return {!boolean}
 */
proto.com.jy.network_monitor.proto.MSG_Behavior.prototype.hasExt = function() {
  return jspb.Message.getField(this, 10) != null;
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.UserGrpNameChangeExt = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.UserGrpNameChangeExt, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.UserGrpNameChangeExt.displayName = 'proto.com.jy.network_monitor.proto.UserGrpNameChangeExt';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.UserGrpNameChangeExt.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.UserGrpNameChangeExt.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.UserGrpNameChangeExt} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.UserGrpNameChangeExt.toObject = function(includeInstance, msg) {
  var f, obj = {
    preva: jspb.Message.getFieldWithDefault(msg, 7, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.UserGrpNameChangeExt}
 */
proto.com.jy.network_monitor.proto.UserGrpNameChangeExt.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.UserGrpNameChangeExt;
  return proto.com.jy.network_monitor.proto.UserGrpNameChangeExt.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.UserGrpNameChangeExt} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.UserGrpNameChangeExt}
 */
proto.com.jy.network_monitor.proto.UserGrpNameChangeExt.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setPreva(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.UserGrpNameChangeExt.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.UserGrpNameChangeExt.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.UserGrpNameChangeExt} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.UserGrpNameChangeExt.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPreva();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
};


/**
 * optional string PREVA = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.UserGrpNameChangeExt.prototype.getPreva = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.UserGrpNameChangeExt.prototype.setPreva = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.UserAttrChangeExt = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.UserAttrChangeExt, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.UserAttrChangeExt.displayName = 'proto.com.jy.network_monitor.proto.UserAttrChangeExt';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.UserAttrChangeExt.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.UserAttrChangeExt.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.UserAttrChangeExt} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.UserAttrChangeExt.toObject = function(includeInstance, msg) {
  var f, obj = {
    preva: jspb.Message.getFieldWithDefault(msg, 7, ""),
    chcmd: jspb.Message.getFieldWithDefault(msg, 8, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.UserAttrChangeExt}
 */
proto.com.jy.network_monitor.proto.UserAttrChangeExt.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.UserAttrChangeExt;
  return proto.com.jy.network_monitor.proto.UserAttrChangeExt.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.UserAttrChangeExt} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.UserAttrChangeExt}
 */
proto.com.jy.network_monitor.proto.UserAttrChangeExt.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setPreva(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setChcmd(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.UserAttrChangeExt.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.UserAttrChangeExt.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.UserAttrChangeExt} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.UserAttrChangeExt.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPreva();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getChcmd();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
};


/**
 * optional string PREVA = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.UserAttrChangeExt.prototype.getPreva = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.UserAttrChangeExt.prototype.setPreva = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string CHCMD = 8;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.UserAttrChangeExt.prototype.getChcmd = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.UserAttrChangeExt.prototype.setChcmd = function(value) {
  jspb.Message.setProto3StringField(this, 8, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.PartMountUnmExt = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.PartMountUnmExt, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.PartMountUnmExt.displayName = 'proto.com.jy.network_monitor.proto.PartMountUnmExt';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.PartMountUnmExt.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.PartMountUnmExt.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.PartMountUnmExt} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.PartMountUnmExt.toObject = function(includeInstance, msg) {
  var f, obj = {
    rable: jspb.Message.getFieldWithDefault(msg, 7, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.PartMountUnmExt}
 */
proto.com.jy.network_monitor.proto.PartMountUnmExt.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.PartMountUnmExt;
  return proto.com.jy.network_monitor.proto.PartMountUnmExt.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.PartMountUnmExt} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.PartMountUnmExt}
 */
proto.com.jy.network_monitor.proto.PartMountUnmExt.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 7:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setRable(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.PartMountUnmExt.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.PartMountUnmExt.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.PartMountUnmExt} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.PartMountUnmExt.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRable();
  if (f) {
    writer.writeBool(
      7,
      f
    );
  }
};


/**
 * optional bool RABLE = 7;
 * Note that Boolean fields may be set to 0/1 when serialized from a Java server.
 * You should avoid comparisons like {@code val === true/false} in those cases.
 * @return {boolean}
 */
proto.com.jy.network_monitor.proto.PartMountUnmExt.prototype.getRable = function() {
  return /** @type {boolean} */ (jspb.Message.getFieldWithDefault(this, 7, false));
};


/** @param {boolean} value */
proto.com.jy.network_monitor.proto.PartMountUnmExt.prototype.setRable = function(value) {
  jspb.Message.setProto3BooleanField(this, 7, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.BootTaskChange = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.BootTaskChange, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.BootTaskChange.displayName = 'proto.com.jy.network_monitor.proto.BootTaskChange';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.BootTaskChange.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.BootTaskChange.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.BootTaskChange} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.BootTaskChange.toObject = function(includeInstance, msg) {
  var f, obj = {
    preva: jspb.Message.getFieldWithDefault(msg, 7, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.BootTaskChange}
 */
proto.com.jy.network_monitor.proto.BootTaskChange.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.BootTaskChange;
  return proto.com.jy.network_monitor.proto.BootTaskChange.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.BootTaskChange} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.BootTaskChange}
 */
proto.com.jy.network_monitor.proto.BootTaskChange.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setPreva(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.BootTaskChange.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.BootTaskChange.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.BootTaskChange} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.BootTaskChange.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPreva();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
};


/**
 * optional string PREVA = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.BootTaskChange.prototype.getPreva = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.BootTaskChange.prototype.setPreva = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.ServParamModExt = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.ServParamModExt, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.ServParamModExt.displayName = 'proto.com.jy.network_monitor.proto.ServParamModExt';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.ServParamModExt.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.ServParamModExt.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.ServParamModExt} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.ServParamModExt.toObject = function(includeInstance, msg) {
  var f, obj = {
    preva: jspb.Message.getFieldWithDefault(msg, 7, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.ServParamModExt}
 */
proto.com.jy.network_monitor.proto.ServParamModExt.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.ServParamModExt;
  return proto.com.jy.network_monitor.proto.ServParamModExt.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.ServParamModExt} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.ServParamModExt}
 */
proto.com.jy.network_monitor.proto.ServParamModExt.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setPreva(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.ServParamModExt.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.ServParamModExt.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.ServParamModExt} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.ServParamModExt.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPreva();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
};


/**
 * optional string PREVA = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.ServParamModExt.prototype.getPreva = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.ServParamModExt.prototype.setPreva = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.FileAddDelExt = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.FileAddDelExt, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.FileAddDelExt.displayName = 'proto.com.jy.network_monitor.proto.FileAddDelExt';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.FileAddDelExt.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.FileAddDelExt.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.FileAddDelExt} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.FileAddDelExt.toObject = function(includeInstance, msg) {
  var f, obj = {
    sfname: jspb.Message.getFieldWithDefault(msg, 7, ""),
    sfdir: jspb.Message.getFieldWithDefault(msg, 8, ""),
    cpDir: jspb.Message.getFieldWithDefault(msg, 9, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.FileAddDelExt}
 */
proto.com.jy.network_monitor.proto.FileAddDelExt.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.FileAddDelExt;
  return proto.com.jy.network_monitor.proto.FileAddDelExt.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.FileAddDelExt} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.FileAddDelExt}
 */
proto.com.jy.network_monitor.proto.FileAddDelExt.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setSfname(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setSfdir(value);
      break;
    case 9:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setCpDir(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.FileAddDelExt.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.FileAddDelExt.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.FileAddDelExt} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.FileAddDelExt.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSfname();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getSfdir();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getCpDir();
  if (f !== 0) {
    writer.writeInt32(
      9,
      f
    );
  }
};


/**
 * optional string SFNAME = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.FileAddDelExt.prototype.getSfname = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.FileAddDelExt.prototype.setSfname = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string SFDIR = 8;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.FileAddDelExt.prototype.getSfdir = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.FileAddDelExt.prototype.setSfdir = function(value) {
  jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional int32 CP_DIR = 9;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.FileAddDelExt.prototype.getCpDir = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 9, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.FileAddDelExt.prototype.setCpDir = function(value) {
  jspb.Message.setProto3IntField(this, 9, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.FileChangeExt = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.FileChangeExt, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.FileChangeExt.displayName = 'proto.com.jy.network_monitor.proto.FileChangeExt';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.FileChangeExt.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.FileChangeExt.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.FileChangeExt} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.FileChangeExt.toObject = function(includeInstance, msg) {
  var f, obj = {
    preva: jspb.Message.getFieldWithDefault(msg, 7, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.FileChangeExt}
 */
proto.com.jy.network_monitor.proto.FileChangeExt.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.FileChangeExt;
  return proto.com.jy.network_monitor.proto.FileChangeExt.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.FileChangeExt} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.FileChangeExt}
 */
proto.com.jy.network_monitor.proto.FileChangeExt.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setPreva(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.FileChangeExt.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.FileChangeExt.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.FileChangeExt} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.FileChangeExt.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPreva();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
};


/**
 * optional string PREVA = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.FileChangeExt.prototype.getPreva = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.FileChangeExt.prototype.setPreva = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.CoreFileChangeExt = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.CoreFileChangeExt, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.CoreFileChangeExt.displayName = 'proto.com.jy.network_monitor.proto.CoreFileChangeExt';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.CoreFileChangeExt.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.CoreFileChangeExt.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.CoreFileChangeExt} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.CoreFileChangeExt.toObject = function(includeInstance, msg) {
  var f, obj = {
    prehash: jspb.Message.getFieldWithDefault(msg, 7, ""),
    diffva: jspb.Message.getFieldWithDefault(msg, 8, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.CoreFileChangeExt}
 */
proto.com.jy.network_monitor.proto.CoreFileChangeExt.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.CoreFileChangeExt;
  return proto.com.jy.network_monitor.proto.CoreFileChangeExt.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.CoreFileChangeExt} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.CoreFileChangeExt}
 */
proto.com.jy.network_monitor.proto.CoreFileChangeExt.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setPrehash(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setDiffva(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.CoreFileChangeExt.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.CoreFileChangeExt.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.CoreFileChangeExt} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.CoreFileChangeExt.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPrehash();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getDiffva();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
};


/**
 * optional string PREHASH = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.CoreFileChangeExt.prototype.getPrehash = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.CoreFileChangeExt.prototype.setPrehash = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string DIFFVA = 8;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.CoreFileChangeExt.prototype.getDiffva = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.CoreFileChangeExt.prototype.setDiffva = function(value) {
  jspb.Message.setProto3StringField(this, 8, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.ExecFileChangeExt = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.ExecFileChangeExt, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.ExecFileChangeExt.displayName = 'proto.com.jy.network_monitor.proto.ExecFileChangeExt';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.ExecFileChangeExt.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.ExecFileChangeExt.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.ExecFileChangeExt} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.ExecFileChangeExt.toObject = function(includeInstance, msg) {
  var f, obj = {
    preva: jspb.Message.getFieldWithDefault(msg, 7, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.ExecFileChangeExt}
 */
proto.com.jy.network_monitor.proto.ExecFileChangeExt.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.ExecFileChangeExt;
  return proto.com.jy.network_monitor.proto.ExecFileChangeExt.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.ExecFileChangeExt} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.ExecFileChangeExt}
 */
proto.com.jy.network_monitor.proto.ExecFileChangeExt.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setPreva(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.ExecFileChangeExt.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.ExecFileChangeExt.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.ExecFileChangeExt} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.ExecFileChangeExt.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPreva();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
};


/**
 * optional string PREVA = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.ExecFileChangeExt.prototype.getPreva = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.ExecFileChangeExt.prototype.setPreva = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.SessionLoginExt = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.SessionLoginExt, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.SessionLoginExt.displayName = 'proto.com.jy.network_monitor.proto.SessionLoginExt';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.SessionLoginExt.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.SessionLoginExt.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.SessionLoginExt} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.SessionLoginExt.toObject = function(includeInstance, msg) {
  var f, obj = {
    source: jspb.Message.getFieldWithDefault(msg, 7, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.SessionLoginExt}
 */
proto.com.jy.network_monitor.proto.SessionLoginExt.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.SessionLoginExt;
  return proto.com.jy.network_monitor.proto.SessionLoginExt.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.SessionLoginExt} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.SessionLoginExt}
 */
proto.com.jy.network_monitor.proto.SessionLoginExt.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 7:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSource(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.SessionLoginExt.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.SessionLoginExt.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.SessionLoginExt} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.SessionLoginExt.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSource();
  if (f !== 0) {
    writer.writeInt32(
      7,
      f
    );
  }
};


/**
 * optional int32 SOURCE = 7;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.SessionLoginExt.prototype.getSource = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.SessionLoginExt.prototype.setSource = function(value) {
  jspb.Message.setProto3IntField(this, 7, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.SessionOperExt = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.SessionOperExt, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.SessionOperExt.displayName = 'proto.com.jy.network_monitor.proto.SessionOperExt';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.SessionOperExt.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.SessionOperExt.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.SessionOperExt} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.SessionOperExt.toObject = function(includeInstance, msg) {
  var f, obj = {
    oppath: jspb.Message.getFieldWithDefault(msg, 6, ""),
    opcmd: jspb.Message.getFieldWithDefault(msg, 7, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.SessionOperExt}
 */
proto.com.jy.network_monitor.proto.SessionOperExt.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.SessionOperExt;
  return proto.com.jy.network_monitor.proto.SessionOperExt.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.SessionOperExt} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.SessionOperExt}
 */
proto.com.jy.network_monitor.proto.SessionOperExt.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setOppath(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setOpcmd(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.SessionOperExt.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.SessionOperExt.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.SessionOperExt} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.SessionOperExt.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getOppath();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getOpcmd();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
};


/**
 * optional string OPPATH = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.SessionOperExt.prototype.getOppath = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.SessionOperExt.prototype.setOppath = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string OPCMD = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.SessionOperExt.prototype.getOpcmd = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.SessionOperExt.prototype.setOpcmd = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.AppOperExt = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.AppOperExt, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.AppOperExt.displayName = 'proto.com.jy.network_monitor.proto.AppOperExt';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.AppOperExt.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.AppOperExt.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.AppOperExt} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.AppOperExt.toObject = function(includeInstance, msg) {
  var f, obj = {
    oapp: jspb.Message.getFieldWithDefault(msg, 6, 0),
    account: jspb.Message.getFieldWithDefault(msg, 7, ""),
    optype: jspb.Message.getFieldWithDefault(msg, 8, 0),
    opstype: jspb.Message.getFieldWithDefault(msg, 9, 0),
    desr: jspb.Message.getFieldWithDefault(msg, 10, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.AppOperExt}
 */
proto.com.jy.network_monitor.proto.AppOperExt.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.AppOperExt;
  return proto.com.jy.network_monitor.proto.AppOperExt.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.AppOperExt} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.AppOperExt}
 */
proto.com.jy.network_monitor.proto.AppOperExt.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 6:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setOapp(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setAccount(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setOptype(value);
      break;
    case 9:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setOpstype(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setDesr(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.AppOperExt.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.AppOperExt.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.AppOperExt} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.AppOperExt.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getOapp();
  if (f !== 0) {
    writer.writeInt32(
      6,
      f
    );
  }
  f = message.getAccount();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getOptype();
  if (f !== 0) {
    writer.writeInt32(
      8,
      f
    );
  }
  f = message.getOpstype();
  if (f !== 0) {
    writer.writeInt32(
      9,
      f
    );
  }
  f = message.getDesr();
  if (f.length > 0) {
    writer.writeString(
      10,
      f
    );
  }
};


/**
 * optional int32 OAPP = 6;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.AppOperExt.prototype.getOapp = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.AppOperExt.prototype.setOapp = function(value) {
  jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional string ACCOUNT = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.AppOperExt.prototype.getAccount = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.AppOperExt.prototype.setAccount = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional int32 OPTYPE = 8;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.AppOperExt.prototype.getOptype = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.AppOperExt.prototype.setOptype = function(value) {
  jspb.Message.setProto3IntField(this, 8, value);
};


/**
 * optional int32 OPSTYPE = 9;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.AppOperExt.prototype.getOpstype = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 9, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.AppOperExt.prototype.setOpstype = function(value) {
  jspb.Message.setProto3IntField(this, 9, value);
};


/**
 * optional string DESR = 10;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.AppOperExt.prototype.getDesr = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.AppOperExt.prototype.setDesr = function(value) {
  jspb.Message.setProto3StringField(this, 10, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.AgentSrcplugChangeExt = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.AgentSrcplugChangeExt, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.AgentSrcplugChangeExt.displayName = 'proto.com.jy.network_monitor.proto.AgentSrcplugChangeExt';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.AgentSrcplugChangeExt.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.AgentSrcplugChangeExt.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.AgentSrcplugChangeExt} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.AgentSrcplugChangeExt.toObject = function(includeInstance, msg) {
  var f, obj = {
    ereason: jspb.Message.getFieldWithDefault(msg, 7, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.AgentSrcplugChangeExt}
 */
proto.com.jy.network_monitor.proto.AgentSrcplugChangeExt.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.AgentSrcplugChangeExt;
  return proto.com.jy.network_monitor.proto.AgentSrcplugChangeExt.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.AgentSrcplugChangeExt} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.AgentSrcplugChangeExt}
 */
proto.com.jy.network_monitor.proto.AgentSrcplugChangeExt.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setEreason(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.AgentSrcplugChangeExt.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.AgentSrcplugChangeExt.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.AgentSrcplugChangeExt} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.AgentSrcplugChangeExt.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getEreason();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
};


/**
 * optional string EREASON = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.AgentSrcplugChangeExt.prototype.getEreason = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.AgentSrcplugChangeExt.prototype.setEreason = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.NetIntChangeExt = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.NetIntChangeExt, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.NetIntChangeExt.displayName = 'proto.com.jy.network_monitor.proto.NetIntChangeExt';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.NetIntChangeExt.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.NetIntChangeExt.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.NetIntChangeExt} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.NetIntChangeExt.toObject = function(includeInstance, msg) {
  var f, obj = {
    preva: jspb.Message.getFieldWithDefault(msg, 7, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.NetIntChangeExt}
 */
proto.com.jy.network_monitor.proto.NetIntChangeExt.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.NetIntChangeExt;
  return proto.com.jy.network_monitor.proto.NetIntChangeExt.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.NetIntChangeExt} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.NetIntChangeExt}
 */
proto.com.jy.network_monitor.proto.NetIntChangeExt.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setPreva(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.NetIntChangeExt.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.NetIntChangeExt.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.NetIntChangeExt} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.NetIntChangeExt.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPreva();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
};


/**
 * optional string PREVA = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.NetIntChangeExt.prototype.getPreva = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.NetIntChangeExt.prototype.setPreva = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt.displayName = 'proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt.toObject = function(includeInstance, msg) {
  var f, obj = {
    oapp: jspb.Message.getFieldWithDefault(msg, 6, 0),
    account: jspb.Message.getFieldWithDefault(msg, 7, ""),
    optype: jspb.Message.getFieldWithDefault(msg, 8, 0),
    opstype: jspb.Message.getFieldWithDefault(msg, 9, 0),
    desr: jspb.Message.getFieldWithDefault(msg, 10, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt}
 */
proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt;
  return proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt}
 */
proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 6:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setOapp(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setAccount(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setOptype(value);
      break;
    case 9:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setOpstype(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setDesr(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getOapp();
  if (f !== 0) {
    writer.writeInt32(
      6,
      f
    );
  }
  f = message.getAccount();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getOptype();
  if (f !== 0) {
    writer.writeInt32(
      8,
      f
    );
  }
  f = message.getOpstype();
  if (f !== 0) {
    writer.writeInt32(
      9,
      f
    );
  }
  f = message.getDesr();
  if (f.length > 0) {
    writer.writeString(
      10,
      f
    );
  }
};


/**
 * optional int32 OAPP = 6;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt.prototype.getOapp = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt.prototype.setOapp = function(value) {
  jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional string ACCOUNT = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt.prototype.getAccount = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt.prototype.setAccount = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional int32 OPTYPE = 8;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt.prototype.getOptype = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt.prototype.setOptype = function(value) {
  jspb.Message.setProto3IntField(this, 8, value);
};


/**
 * optional int32 OPSTYPE = 9;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt.prototype.getOpstype = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 9, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt.prototype.setOpstype = function(value) {
  jspb.Message.setProto3IntField(this, 9, value);
};


/**
 * optional string DESR = 10;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt.prototype.getDesr = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.AccCtrlPolStartStopExt.prototype.setDesr = function(value) {
  jspb.Message.setProto3StringField(this, 10, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.AddrChangeExt = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.AddrChangeExt, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.AddrChangeExt.displayName = 'proto.com.jy.network_monitor.proto.AddrChangeExt';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.AddrChangeExt.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.AddrChangeExt.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.AddrChangeExt} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.AddrChangeExt.toObject = function(includeInstance, msg) {
  var f, obj = {
    preva: jspb.Message.getFieldWithDefault(msg, 7, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.AddrChangeExt}
 */
proto.com.jy.network_monitor.proto.AddrChangeExt.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.AddrChangeExt;
  return proto.com.jy.network_monitor.proto.AddrChangeExt.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.AddrChangeExt} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.AddrChangeExt}
 */
proto.com.jy.network_monitor.proto.AddrChangeExt.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setPreva(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.AddrChangeExt.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.AddrChangeExt.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.AddrChangeExt} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.AddrChangeExt.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPreva();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
};


/**
 * optional string PREVA = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.AddrChangeExt.prototype.getPreva = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.AddrChangeExt.prototype.setPreva = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.NetAccessExt = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.NetAccessExt, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.NetAccessExt.displayName = 'proto.com.jy.network_monitor.proto.NetAccessExt';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.NetAccessExt.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.NetAccessExt.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.NetAccessExt} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.NetAccessExt.toObject = function(includeInstance, msg) {
  var f, obj = {
    source: jspb.Message.getFieldWithDefault(msg, 7, 0),
    details: jspb.Message.getFieldWithDefault(msg, 8, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.NetAccessExt}
 */
proto.com.jy.network_monitor.proto.NetAccessExt.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.NetAccessExt;
  return proto.com.jy.network_monitor.proto.NetAccessExt.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.NetAccessExt} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.NetAccessExt}
 */
proto.com.jy.network_monitor.proto.NetAccessExt.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 7:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSource(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setDetails(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.NetAccessExt.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.NetAccessExt.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.NetAccessExt} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.NetAccessExt.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSource();
  if (f !== 0) {
    writer.writeInt32(
      7,
      f
    );
  }
  f = message.getDetails();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
};


/**
 * optional int32 SOURCE = 7;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.NetAccessExt.prototype.getSource = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.NetAccessExt.prototype.setSource = function(value) {
  jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional string DETAILS = 8;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.NetAccessExt.prototype.getDetails = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.NetAccessExt.prototype.setDetails = function(value) {
  jspb.Message.setProto3StringField(this, 8, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.displayName = 'proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.toObject = function(includeInstance, msg) {
  var f, obj = {
    sesseq: jspb.Message.getFieldWithDefault(msg, 1, ""),
    sprid: jspb.Message.getFieldWithDefault(msg, 2, ""),
    spid: jspb.Message.getFieldWithDefault(msg, 3, ""),
    seid: jspb.Message.getFieldWithDefault(msg, 4, ""),
    sexenm: jspb.Message.getFieldWithDefault(msg, 5, ""),
    sexedir: jspb.Message.getFieldWithDefault(msg, 6, ""),
    ssparam: jspb.Message.getFieldWithDefault(msg, 7, ""),
    sstime: jspb.Message.getFieldWithDefault(msg, 8, ""),
    dprid: jspb.Message.getFieldWithDefault(msg, 9, ""),
    dpid: jspb.Message.getFieldWithDefault(msg, 10, ""),
    deid: jspb.Message.getFieldWithDefault(msg, 11, ""),
    dexenm: jspb.Message.getFieldWithDefault(msg, 12, ""),
    dexedir: jspb.Message.getFieldWithDefault(msg, 13, ""),
    dsparam: jspb.Message.getFieldWithDefault(msg, 14, ""),
    dstime: jspb.Message.getFieldWithDefault(msg, 15, ""),
    lproto: jspb.Message.getFieldWithDefault(msg, 16, ""),
    nproto: jspb.Message.getFieldWithDefault(msg, 17, ""),
    tproto: jspb.Message.getFieldWithDefault(msg, 18, ""),
    aproto: jspb.Message.getFieldWithDefault(msg, 19, ""),
    datadr: jspb.Message.getFieldWithDefault(msg, 20, 0),
    role: jspb.Message.getFieldWithDefault(msg, 21, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail}
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail;
  return proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail}
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setSesseq(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSprid(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setSpid(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setSeid(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setSexenm(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setSexedir(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setSsparam(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setSstime(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setDprid(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setDpid(value);
      break;
    case 11:
      var value = /** @type {string} */ (reader.readString());
      msg.setDeid(value);
      break;
    case 12:
      var value = /** @type {string} */ (reader.readString());
      msg.setDexenm(value);
      break;
    case 13:
      var value = /** @type {string} */ (reader.readString());
      msg.setDexedir(value);
      break;
    case 14:
      var value = /** @type {string} */ (reader.readString());
      msg.setDsparam(value);
      break;
    case 15:
      var value = /** @type {string} */ (reader.readString());
      msg.setDstime(value);
      break;
    case 16:
      var value = /** @type {string} */ (reader.readString());
      msg.setLproto(value);
      break;
    case 17:
      var value = /** @type {string} */ (reader.readString());
      msg.setNproto(value);
      break;
    case 18:
      var value = /** @type {string} */ (reader.readString());
      msg.setTproto(value);
      break;
    case 19:
      var value = /** @type {string} */ (reader.readString());
      msg.setAproto(value);
      break;
    case 20:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setDatadr(value);
      break;
    case 21:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRole(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSesseq();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getSprid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSpid();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getSeid();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getSexenm();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getSexedir();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getSsparam();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getSstime();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getDprid();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
  f = message.getDpid();
  if (f.length > 0) {
    writer.writeString(
      10,
      f
    );
  }
  f = message.getDeid();
  if (f.length > 0) {
    writer.writeString(
      11,
      f
    );
  }
  f = message.getDexenm();
  if (f.length > 0) {
    writer.writeString(
      12,
      f
    );
  }
  f = message.getDexedir();
  if (f.length > 0) {
    writer.writeString(
      13,
      f
    );
  }
  f = message.getDsparam();
  if (f.length > 0) {
    writer.writeString(
      14,
      f
    );
  }
  f = message.getDstime();
  if (f.length > 0) {
    writer.writeString(
      15,
      f
    );
  }
  f = message.getLproto();
  if (f.length > 0) {
    writer.writeString(
      16,
      f
    );
  }
  f = message.getNproto();
  if (f.length > 0) {
    writer.writeString(
      17,
      f
    );
  }
  f = message.getTproto();
  if (f.length > 0) {
    writer.writeString(
      18,
      f
    );
  }
  f = message.getAproto();
  if (f.length > 0) {
    writer.writeString(
      19,
      f
    );
  }
  f = message.getDatadr();
  if (f !== 0) {
    writer.writeInt32(
      20,
      f
    );
  }
  f = message.getRole();
  if (f !== 0) {
    writer.writeInt32(
      21,
      f
    );
  }
};


/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.DATA_DIREACTION = {
  OUT: 0,
  IN: 1,
  BOTH: 3
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.Role = {
  CLIENT: 0,
  SERVER: 1
};

/**
 * optional string SESSEQ = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.getSesseq = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.setSesseq = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string SPRID = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.getSprid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.setSprid = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string SPID = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.getSpid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.setSpid = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string SEID = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.getSeid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.setSeid = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string SEXENM = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.getSexenm = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.setSexenm = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string SEXEDIR = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.getSexedir = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.setSexedir = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string SSPARAM = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.getSsparam = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.setSsparam = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string SSTIME = 8;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.getSstime = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.setSstime = function(value) {
  jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional string DPRID = 9;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.getDprid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.setDprid = function(value) {
  jspb.Message.setProto3StringField(this, 9, value);
};


/**
 * optional string DPID = 10;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.getDpid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.setDpid = function(value) {
  jspb.Message.setProto3StringField(this, 10, value);
};


/**
 * optional string DEID = 11;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.getDeid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 11, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.setDeid = function(value) {
  jspb.Message.setProto3StringField(this, 11, value);
};


/**
 * optional string DEXENM = 12;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.getDexenm = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 12, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.setDexenm = function(value) {
  jspb.Message.setProto3StringField(this, 12, value);
};


/**
 * optional string DEXEDIR = 13;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.getDexedir = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 13, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.setDexedir = function(value) {
  jspb.Message.setProto3StringField(this, 13, value);
};


/**
 * optional string DSPARAM = 14;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.getDsparam = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 14, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.setDsparam = function(value) {
  jspb.Message.setProto3StringField(this, 14, value);
};


/**
 * optional string DSTIME = 15;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.getDstime = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 15, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.setDstime = function(value) {
  jspb.Message.setProto3StringField(this, 15, value);
};


/**
 * optional string LPROTO = 16;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.getLproto = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 16, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.setLproto = function(value) {
  jspb.Message.setProto3StringField(this, 16, value);
};


/**
 * optional string NPROTO = 17;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.getNproto = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 17, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.setNproto = function(value) {
  jspb.Message.setProto3StringField(this, 17, value);
};


/**
 * optional string TPROTO = 18;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.getTproto = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 18, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.setTproto = function(value) {
  jspb.Message.setProto3StringField(this, 18, value);
};


/**
 * optional string APROTO = 19;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.getAproto = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 19, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.setAproto = function(value) {
  jspb.Message.setProto3StringField(this, 19, value);
};


/**
 * optional int32 DATADR = 20;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.getDatadr = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 20, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.setDatadr = function(value) {
  jspb.Message.setProto3IntField(this, 20, value);
};


/**
 * optional int32 ROLE = 21;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.getRole = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 21, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_ServerNetworkAccessDetail.prototype.setRole = function(value) {
  jspb.Message.setProto3IntField(this, 21, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_TrafficDetail = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_TrafficDetail, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_TrafficDetail.displayName = 'proto.com.jy.network_monitor.proto.MSG_TrafficDetail';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_TrafficDetail.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_TrafficDetail.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_TrafficDetail} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_TrafficDetail.toObject = function(includeInstance, msg) {
  var f, obj = {
    sesseq: jspb.Message.getFieldWithDefault(msg, 1, ""),
    lproto: jspb.Message.getFieldWithDefault(msg, 2, ""),
    nproto: jspb.Message.getFieldWithDefault(msg, 3, ""),
    tproto: jspb.Message.getFieldWithDefault(msg, 4, ""),
    aproto: jspb.Message.getFieldWithDefault(msg, 5, ""),
    datadr: jspb.Message.getFieldWithDefault(msg, 6, 0),
    repack: jspb.Message.getFieldWithDefault(msg, 7, 0),
    snpack: jspb.Message.getFieldWithDefault(msg, 8, 0),
    rebyte: jspb.Message.getFieldWithDefault(msg, 9, 0),
    snbyte: jspb.Message.getFieldWithDefault(msg, 10, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_TrafficDetail}
 */
proto.com.jy.network_monitor.proto.MSG_TrafficDetail.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_TrafficDetail;
  return proto.com.jy.network_monitor.proto.MSG_TrafficDetail.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_TrafficDetail} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_TrafficDetail}
 */
proto.com.jy.network_monitor.proto.MSG_TrafficDetail.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setSesseq(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setLproto(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setNproto(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setTproto(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setAproto(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setDatadr(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRepack(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSnpack(value);
      break;
    case 9:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRebyte(value);
      break;
    case 10:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSnbyte(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_TrafficDetail.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_TrafficDetail.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_TrafficDetail} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_TrafficDetail.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSesseq();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getLproto();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getNproto();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getTproto();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getAproto();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getDatadr();
  if (f !== 0) {
    writer.writeInt32(
      6,
      f
    );
  }
  f = message.getRepack();
  if (f !== 0) {
    writer.writeInt32(
      7,
      f
    );
  }
  f = message.getSnpack();
  if (f !== 0) {
    writer.writeInt32(
      8,
      f
    );
  }
  f = message.getRebyte();
  if (f !== 0) {
    writer.writeInt32(
      9,
      f
    );
  }
  f = message.getSnbyte();
  if (f !== 0) {
    writer.writeInt32(
      10,
      f
    );
  }
};


/**
 * optional string SESSEQ = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_TrafficDetail.prototype.getSesseq = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_TrafficDetail.prototype.setSesseq = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string LPROTO = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_TrafficDetail.prototype.getLproto = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_TrafficDetail.prototype.setLproto = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string NPROTO = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_TrafficDetail.prototype.getNproto = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_TrafficDetail.prototype.setNproto = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string TPROTO = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_TrafficDetail.prototype.getTproto = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_TrafficDetail.prototype.setTproto = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string APROTO = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_TrafficDetail.prototype.getAproto = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_TrafficDetail.prototype.setAproto = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional int32 DATADR = 6;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_TrafficDetail.prototype.getDatadr = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_TrafficDetail.prototype.setDatadr = function(value) {
  jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional int32 REPACK = 7;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_TrafficDetail.prototype.getRepack = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_TrafficDetail.prototype.setRepack = function(value) {
  jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional int32 SNPACK = 8;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_TrafficDetail.prototype.getSnpack = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_TrafficDetail.prototype.setSnpack = function(value) {
  jspb.Message.setProto3IntField(this, 8, value);
};


/**
 * optional int32 REBYTE = 9;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_TrafficDetail.prototype.getRebyte = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 9, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_TrafficDetail.prototype.setRebyte = function(value) {
  jspb.Message.setProto3IntField(this, 9, value);
};


/**
 * optional int32 SNBYTE = 10;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_TrafficDetail.prototype.getSnbyte = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 10, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_TrafficDetail.prototype.setSnbyte = function(value) {
  jspb.Message.setProto3IntField(this, 10, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SecurityDeviceNetworkAccessDetail = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SecurityDeviceNetworkAccessDetail, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SecurityDeviceNetworkAccessDetail.displayName = 'proto.com.jy.network_monitor.proto.MSG_SecurityDeviceNetworkAccessDetail';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SecurityDeviceNetworkAccessDetail.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SecurityDeviceNetworkAccessDetail.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SecurityDeviceNetworkAccessDetail} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SecurityDeviceNetworkAccessDetail.toObject = function(includeInstance, msg) {
  var f, obj = {
    tproto: jspb.Message.getFieldWithDefault(msg, 1, ""),
    dser: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SecurityDeviceNetworkAccessDetail}
 */
proto.com.jy.network_monitor.proto.MSG_SecurityDeviceNetworkAccessDetail.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SecurityDeviceNetworkAccessDetail;
  return proto.com.jy.network_monitor.proto.MSG_SecurityDeviceNetworkAccessDetail.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SecurityDeviceNetworkAccessDetail} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SecurityDeviceNetworkAccessDetail}
 */
proto.com.jy.network_monitor.proto.MSG_SecurityDeviceNetworkAccessDetail.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setTproto(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setDser(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SecurityDeviceNetworkAccessDetail.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SecurityDeviceNetworkAccessDetail.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SecurityDeviceNetworkAccessDetail} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SecurityDeviceNetworkAccessDetail.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTproto();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getDser();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string TPROTO = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SecurityDeviceNetworkAccessDetail.prototype.getTproto = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SecurityDeviceNetworkAccessDetail.prototype.setTproto = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string DSER = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SecurityDeviceNetworkAccessDetail.prototype.getDser = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SecurityDeviceNetworkAccessDetail.prototype.setDser = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.NetFileTransfer = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.NetFileTransfer, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.NetFileTransfer.displayName = 'proto.com.jy.network_monitor.proto.NetFileTransfer';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.NetFileTransfer.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.NetFileTransfer.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.NetFileTransfer} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.NetFileTransfer.toObject = function(includeInstance, msg) {
  var f, obj = {
    sesseq: jspb.Message.getFieldWithDefault(msg, 7, ""),
    fileproto: jspb.Message.getFieldWithDefault(msg, 8, ""),
    srcport: jspb.Message.getFieldWithDefault(msg, 9, 0),
    dstport: jspb.Message.getFieldWithDefault(msg, 10, 0),
    transaccout: jspb.Message.getFieldWithDefault(msg, 11, ""),
    fname: jspb.Message.getFieldWithDefault(msg, 12, ""),
    hash: jspb.Message.getFieldWithDefault(msg, 13, ""),
    tssize: jspb.Message.getFieldWithDefault(msg, 14, 0),
    unit: jspb.Message.getFieldWithDefault(msg, 15, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.NetFileTransfer}
 */
proto.com.jy.network_monitor.proto.NetFileTransfer.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.NetFileTransfer;
  return proto.com.jy.network_monitor.proto.NetFileTransfer.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.NetFileTransfer} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.NetFileTransfer}
 */
proto.com.jy.network_monitor.proto.NetFileTransfer.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setSesseq(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setFileproto(value);
      break;
    case 9:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSrcport(value);
      break;
    case 10:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setDstport(value);
      break;
    case 11:
      var value = /** @type {string} */ (reader.readString());
      msg.setTransaccout(value);
      break;
    case 12:
      var value = /** @type {string} */ (reader.readString());
      msg.setFname(value);
      break;
    case 13:
      var value = /** @type {string} */ (reader.readString());
      msg.setHash(value);
      break;
    case 14:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setTssize(value);
      break;
    case 15:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setUnit(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.NetFileTransfer.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.NetFileTransfer.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.NetFileTransfer} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.NetFileTransfer.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSesseq();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getFileproto();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getSrcport();
  if (f !== 0) {
    writer.writeInt32(
      9,
      f
    );
  }
  f = message.getDstport();
  if (f !== 0) {
    writer.writeInt32(
      10,
      f
    );
  }
  f = message.getTransaccout();
  if (f.length > 0) {
    writer.writeString(
      11,
      f
    );
  }
  f = message.getFname();
  if (f.length > 0) {
    writer.writeString(
      12,
      f
    );
  }
  f = message.getHash();
  if (f.length > 0) {
    writer.writeString(
      13,
      f
    );
  }
  f = message.getTssize();
  if (f !== 0) {
    writer.writeInt32(
      14,
      f
    );
  }
  f = message.getUnit();
  if (f !== 0) {
    writer.writeInt32(
      15,
      f
    );
  }
};


/**
 * optional string SESSEQ = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.NetFileTransfer.prototype.getSesseq = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.NetFileTransfer.prototype.setSesseq = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string FILEPROTO = 8;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.NetFileTransfer.prototype.getFileproto = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.NetFileTransfer.prototype.setFileproto = function(value) {
  jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional int32 SRCPORT = 9;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.NetFileTransfer.prototype.getSrcport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 9, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.NetFileTransfer.prototype.setSrcport = function(value) {
  jspb.Message.setProto3IntField(this, 9, value);
};


/**
 * optional int32 DSTPORT = 10;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.NetFileTransfer.prototype.getDstport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 10, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.NetFileTransfer.prototype.setDstport = function(value) {
  jspb.Message.setProto3IntField(this, 10, value);
};


/**
 * optional string TRANSACCOUT = 11;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.NetFileTransfer.prototype.getTransaccout = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 11, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.NetFileTransfer.prototype.setTransaccout = function(value) {
  jspb.Message.setProto3StringField(this, 11, value);
};


/**
 * optional string FNAME = 12;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.NetFileTransfer.prototype.getFname = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 12, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.NetFileTransfer.prototype.setFname = function(value) {
  jspb.Message.setProto3StringField(this, 12, value);
};


/**
 * optional string HASH = 13;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.NetFileTransfer.prototype.getHash = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 13, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.NetFileTransfer.prototype.setHash = function(value) {
  jspb.Message.setProto3StringField(this, 13, value);
};


/**
 * optional int32 TSSIZE = 14;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.NetFileTransfer.prototype.getTssize = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 14, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.NetFileTransfer.prototype.setTssize = function(value) {
  jspb.Message.setProto3IntField(this, 14, value);
};


/**
 * optional int32 UNIT = 15;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.NetFileTransfer.prototype.getUnit = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 15, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.NetFileTransfer.prototype.setUnit = function(value) {
  jspb.Message.setProto3IntField(this, 15, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_PROC_SYS_CALL_OBJ = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_PROC_SYS_CALL_OBJ, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_PROC_SYS_CALL_OBJ.displayName = 'proto.com.jy.network_monitor.proto.MSG_PROC_SYS_CALL_OBJ';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_PROC_SYS_CALL_OBJ.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_PROC_SYS_CALL_OBJ.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_PROC_SYS_CALL_OBJ} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_PROC_SYS_CALL_OBJ.toObject = function(includeInstance, msg) {
  var f, obj = {
    gid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    syscall: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_PROC_SYS_CALL_OBJ}
 */
proto.com.jy.network_monitor.proto.MSG_PROC_SYS_CALL_OBJ.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_PROC_SYS_CALL_OBJ;
  return proto.com.jy.network_monitor.proto.MSG_PROC_SYS_CALL_OBJ.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_PROC_SYS_CALL_OBJ} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_PROC_SYS_CALL_OBJ}
 */
proto.com.jy.network_monitor.proto.MSG_PROC_SYS_CALL_OBJ.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setGid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSyscall(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_PROC_SYS_CALL_OBJ.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_PROC_SYS_CALL_OBJ.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_PROC_SYS_CALL_OBJ} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_PROC_SYS_CALL_OBJ.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getGid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getSyscall();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string GID = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_PROC_SYS_CALL_OBJ.prototype.getGid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_PROC_SYS_CALL_OBJ.prototype.setGid = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string SYSCALL = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_PROC_SYS_CALL_OBJ.prototype.getSyscall = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_PROC_SYS_CALL_OBJ.prototype.setSyscall = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


goog.object.extend(exports, proto.com.jy.network_monitor.proto);
