/**
 * @fileoverview
 * @enhanceable
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!

var jspb = require('google-protobuf');
var goog = jspb;
var global = Function('return this')();

var PublicDefine2_pb = require('./PublicDefine2_pb.js');
goog.exportSymbol('proto.com.jy.network_monitor.proto.AgentAuthSubType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.AgentEncryptMode', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.AgentErrorCode', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.AgentServerPacketType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.AssetHandlingParam', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.CommandControlSubType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.EmergHandlingParam', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_AgentRegister', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSAppInstallPathQuery', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSAssetQuery', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSDirQuery', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSDisableNICByTimerOfEmerg', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSDisableNICForeverOfEmerg', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfAsset', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfEmerg', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfAsset', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfEmerg', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSFileQuery', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSLockUserOfAsset', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSLockUserOfEmerg', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSRestoreX11', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfAsset', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfAsset', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfEmerg', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_DSVersionQuery', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_HeartbeatRequest', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_HeartbeatRequest.Status', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_HeartbeatResponse', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_MSG_DSPlugin', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_MSG_SDPlugin', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_PlatformCert', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_PlatformCertSync', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDAddACPOfAsset', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDAddACPOfEmerg', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDAssetQuery', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDBlockSSHByTimer', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDBlockSSHForever', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDBlockSSHOnce', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDBlockX11ByTimer', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDBlockX11Forever', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDBlockX11Once', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfAsset', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfEmerg', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDDirQuery', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDDisableNICByTimerOfEmerg', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDDisableNICForeverOfEmerg', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfAsset', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfEmerg', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfAsset', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfEmerg', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDFileQuery', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDFoundPlugin', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDLockUserOfAsset', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDLockUserOfEmerg', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDRestoreSSH', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDRestoreX11', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfAsset', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfEmerg', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDThirdPartyPluginMsgPush', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfAsset', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfEmerg', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MSG_SDVersionQuery', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.MessagePushSubType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.ModelQuerySubType', null, global);
goog.exportSymbol('proto.com.jy.network_monitor.proto.ModelUploadSubType', null, global);

/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDFoundPlugin = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDFoundPlugin, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDFoundPlugin.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDFoundPlugin';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDFoundPlugin.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDFoundPlugin.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDFoundPlugin} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDFoundPlugin.toObject = function(includeInstance, msg) {
  var f, obj = {
    sign: jspb.Message.getFieldWithDefault(msg, 1, ""),
    pdir: jspb.Message.getFieldWithDefault(msg, 2, ""),
    fhash: jspb.Message.getFieldWithDefault(msg, 3, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDFoundPlugin}
 */
proto.com.jy.network_monitor.proto.MSG_SDFoundPlugin.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDFoundPlugin;
  return proto.com.jy.network_monitor.proto.MSG_SDFoundPlugin.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDFoundPlugin} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDFoundPlugin}
 */
proto.com.jy.network_monitor.proto.MSG_SDFoundPlugin.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setSign(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setPdir(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setFhash(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDFoundPlugin.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDFoundPlugin.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDFoundPlugin} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDFoundPlugin.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSign();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getPdir();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getFhash();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
};


/**
 * optional string SIGN = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDFoundPlugin.prototype.getSign = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDFoundPlugin.prototype.setSign = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string PDIR = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDFoundPlugin.prototype.getPdir = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDFoundPlugin.prototype.setPdir = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string FHASH = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDFoundPlugin.prototype.getFhash = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDFoundPlugin.prototype.setFhash = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDThirdPartyPluginMsgPush = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDThirdPartyPluginMsgPush, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDThirdPartyPluginMsgPush.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDThirdPartyPluginMsgPush';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDThirdPartyPluginMsgPush.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDThirdPartyPluginMsgPush.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDThirdPartyPluginMsgPush} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDThirdPartyPluginMsgPush.toObject = function(includeInstance, msg) {
  var f, obj = {
    apid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    stype: jspb.Message.getFieldWithDefault(msg, 2, ""),
    content: jspb.Message.getFieldWithDefault(msg, 3, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDThirdPartyPluginMsgPush}
 */
proto.com.jy.network_monitor.proto.MSG_SDThirdPartyPluginMsgPush.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDThirdPartyPluginMsgPush;
  return proto.com.jy.network_monitor.proto.MSG_SDThirdPartyPluginMsgPush.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDThirdPartyPluginMsgPush} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDThirdPartyPluginMsgPush}
 */
proto.com.jy.network_monitor.proto.MSG_SDThirdPartyPluginMsgPush.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setApid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setStype(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setContent(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDThirdPartyPluginMsgPush.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDThirdPartyPluginMsgPush.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDThirdPartyPluginMsgPush} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDThirdPartyPluginMsgPush.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getApid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getStype();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getContent();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
};


/**
 * optional string APID = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDThirdPartyPluginMsgPush.prototype.getApid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDThirdPartyPluginMsgPush.prototype.setApid = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string STYPE = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDThirdPartyPluginMsgPush.prototype.getStype = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDThirdPartyPluginMsgPush.prototype.setStype = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string CONTENT = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDThirdPartyPluginMsgPush.prototype.getContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDThirdPartyPluginMsgPush.prototype.setContent = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSAssetQuery = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSAssetQuery, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSAssetQuery.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSAssetQuery';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSAssetQuery.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSAssetQuery.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSAssetQuery} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSAssetQuery.toObject = function(includeInstance, msg) {
  var f, obj = {
    mark: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSAssetQuery}
 */
proto.com.jy.network_monitor.proto.MSG_DSAssetQuery.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSAssetQuery;
  return proto.com.jy.network_monitor.proto.MSG_DSAssetQuery.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSAssetQuery} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSAssetQuery}
 */
proto.com.jy.network_monitor.proto.MSG_DSAssetQuery.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setMark(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSAssetQuery.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSAssetQuery.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSAssetQuery} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSAssetQuery.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMark();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
};


/**
 * optional int32 MARK = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSAssetQuery.prototype.getMark = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSAssetQuery.prototype.setMark = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDAssetQuery = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDAssetQuery, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDAssetQuery.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDAssetQuery';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDAssetQuery.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDAssetQuery.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDAssetQuery} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDAssetQuery.toObject = function(includeInstance, msg) {
  var f, obj = {
    mark: jspb.Message.getFieldWithDefault(msg, 1, 0),
    data: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDAssetQuery}
 */
proto.com.jy.network_monitor.proto.MSG_SDAssetQuery.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDAssetQuery;
  return proto.com.jy.network_monitor.proto.MSG_SDAssetQuery.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDAssetQuery} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDAssetQuery}
 */
proto.com.jy.network_monitor.proto.MSG_SDAssetQuery.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setMark(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setData(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDAssetQuery.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDAssetQuery.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDAssetQuery} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDAssetQuery.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMark();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getData();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional int32 mark = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDAssetQuery.prototype.getMark = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDAssetQuery.prototype.setMark = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string data = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDAssetQuery.prototype.getData = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDAssetQuery.prototype.setData = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSFileQuery = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSFileQuery, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSFileQuery.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSFileQuery';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSFileQuery.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSFileQuery.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSFileQuery} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSFileQuery.toObject = function(includeInstance, msg) {
  var f, obj = {
    path: jspb.Message.getFieldWithDefault(msg, 1, ""),
    fname: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSFileQuery}
 */
proto.com.jy.network_monitor.proto.MSG_DSFileQuery.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSFileQuery;
  return proto.com.jy.network_monitor.proto.MSG_DSFileQuery.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSFileQuery} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSFileQuery}
 */
proto.com.jy.network_monitor.proto.MSG_DSFileQuery.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setPath(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setFname(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSFileQuery.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSFileQuery.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSFileQuery} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSFileQuery.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPath();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getFname();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string PATH = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSFileQuery.prototype.getPath = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSFileQuery.prototype.setPath = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string FNAME = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSFileQuery.prototype.getFname = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSFileQuery.prototype.setFname = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDFileQuery, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDFileQuery.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDFileQuery';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDFileQuery.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDFileQuery} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.toObject = function(includeInstance, msg) {
  var f, obj = {
    fid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    softid: jspb.Message.getFieldWithDefault(msg, 2, ""),
    uid: jspb.Message.getFieldWithDefault(msg, 3, ""),
    grpid: jspb.Message.getFieldWithDefault(msg, 4, ""),
    name: jspb.Message.getFieldWithDefault(msg, 5, ""),
    type: jspb.Message.getFieldWithDefault(msg, 6, 0),
    path: jspb.Message.getFieldWithDefault(msg, 7, ""),
    perm: jspb.Message.getFieldWithDefault(msg, 8, ""),
    fhash: jspb.Message.getFieldWithDefault(msg, 9, ""),
    fsize: jspb.Message.getFieldWithDefault(msg, 10, 0),
    ltfile: jspb.Message.getFieldWithDefault(msg, 11, ""),
    ctime: jspb.Message.getFieldWithDefault(msg, 12, ""),
    latime: jspb.Message.getFieldWithDefault(msg, 13, ""),
    lmtime: jspb.Message.getFieldWithDefault(msg, 14, ""),
    lctime: jspb.Message.getFieldWithDefault(msg, 15, ""),
    ino: jspb.Message.getFieldWithDefault(msg, 16, ""),
    content: jspb.Message.getFieldWithDefault(msg, 17, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDFileQuery}
 */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDFileQuery;
  return proto.com.jy.network_monitor.proto.MSG_SDFileQuery.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDFileQuery} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDFileQuery}
 */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setFid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSoftid(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setUid(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setGrpid(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setType(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setPath(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setPerm(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setFhash(value);
      break;
    case 10:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setFsize(value);
      break;
    case 11:
      var value = /** @type {string} */ (reader.readString());
      msg.setLtfile(value);
      break;
    case 12:
      var value = /** @type {string} */ (reader.readString());
      msg.setCtime(value);
      break;
    case 13:
      var value = /** @type {string} */ (reader.readString());
      msg.setLatime(value);
      break;
    case 14:
      var value = /** @type {string} */ (reader.readString());
      msg.setLmtime(value);
      break;
    case 15:
      var value = /** @type {string} */ (reader.readString());
      msg.setLctime(value);
      break;
    case 16:
      var value = /** @type {string} */ (reader.readString());
      msg.setIno(value);
      break;
    case 17:
      var value = /** @type {string} */ (reader.readString());
      msg.setContent(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDFileQuery.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDFileQuery} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getSoftid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getUid();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getGrpid();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getType();
  if (f !== 0) {
    writer.writeInt32(
      6,
      f
    );
  }
  f = message.getPath();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getPerm();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getFhash();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
  f = message.getFsize();
  if (f !== 0) {
    writer.writeInt64(
      10,
      f
    );
  }
  f = message.getLtfile();
  if (f.length > 0) {
    writer.writeString(
      11,
      f
    );
  }
  f = message.getCtime();
  if (f.length > 0) {
    writer.writeString(
      12,
      f
    );
  }
  f = message.getLatime();
  if (f.length > 0) {
    writer.writeString(
      13,
      f
    );
  }
  f = message.getLmtime();
  if (f.length > 0) {
    writer.writeString(
      14,
      f
    );
  }
  f = message.getLctime();
  if (f.length > 0) {
    writer.writeString(
      15,
      f
    );
  }
  f = message.getIno();
  if (f.length > 0) {
    writer.writeString(
      16,
      f
    );
  }
  f = message.getContent();
  if (f.length > 0) {
    writer.writeString(
      17,
      f
    );
  }
};


/**
 * optional string FID = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.getFid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.setFid = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string SOFTID = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.getSoftid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.setSoftid = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string UID = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.getUid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.setUid = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string GRPID = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.getGrpid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.setGrpid = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string NAME = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.setName = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional int32 TYPE = 6;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.getType = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.setType = function(value) {
  jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional string PATH = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.getPath = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.setPath = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string PERM = 8;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.getPerm = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.setPerm = function(value) {
  jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional string FHASH = 9;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.getFhash = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.setFhash = function(value) {
  jspb.Message.setProto3StringField(this, 9, value);
};


/**
 * optional int64 FSIZE = 10;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.getFsize = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 10, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.setFsize = function(value) {
  jspb.Message.setProto3IntField(this, 10, value);
};


/**
 * optional string LTFILE = 11;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.getLtfile = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 11, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.setLtfile = function(value) {
  jspb.Message.setProto3StringField(this, 11, value);
};


/**
 * optional string CTIME = 12;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.getCtime = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 12, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.setCtime = function(value) {
  jspb.Message.setProto3StringField(this, 12, value);
};


/**
 * optional string LATIME = 13;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.getLatime = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 13, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.setLatime = function(value) {
  jspb.Message.setProto3StringField(this, 13, value);
};


/**
 * optional string LMTIME = 14;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.getLmtime = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 14, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.setLmtime = function(value) {
  jspb.Message.setProto3StringField(this, 14, value);
};


/**
 * optional string LCTIME = 15;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.getLctime = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 15, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.setLctime = function(value) {
  jspb.Message.setProto3StringField(this, 15, value);
};


/**
 * optional string INO = 16;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.getIno = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 16, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.setIno = function(value) {
  jspb.Message.setProto3StringField(this, 16, value);
};


/**
 * optional string CONTENT = 17;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.getContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 17, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDFileQuery.prototype.setContent = function(value) {
  jspb.Message.setProto3StringField(this, 17, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSDirQuery = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSDirQuery, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSDirQuery.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSDirQuery';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSDirQuery.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSDirQuery.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSDirQuery} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSDirQuery.toObject = function(includeInstance, msg) {
  var f, obj = {
    path: jspb.Message.getFieldWithDefault(msg, 1, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSDirQuery}
 */
proto.com.jy.network_monitor.proto.MSG_DSDirQuery.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSDirQuery;
  return proto.com.jy.network_monitor.proto.MSG_DSDirQuery.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSDirQuery} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSDirQuery}
 */
proto.com.jy.network_monitor.proto.MSG_DSDirQuery.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setPath(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSDirQuery.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSDirQuery.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSDirQuery} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSDirQuery.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPath();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
};


/**
 * optional string PATH = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSDirQuery.prototype.getPath = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSDirQuery.prototype.setPath = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDDirQuery, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDDirQuery.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDDirQuery';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDDirQuery.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDDirQuery} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.toObject = function(includeInstance, msg) {
  var f, obj = {
    fid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    softid: jspb.Message.getFieldWithDefault(msg, 2, ""),
    uid: jspb.Message.getFieldWithDefault(msg, 3, ""),
    grpid: jspb.Message.getFieldWithDefault(msg, 4, ""),
    name: jspb.Message.getFieldWithDefault(msg, 5, ""),
    type: jspb.Message.getFieldWithDefault(msg, 6, 0),
    path: jspb.Message.getFieldWithDefault(msg, 7, ""),
    perm: jspb.Message.getFieldWithDefault(msg, 8, ""),
    fhash: jspb.Message.getFieldWithDefault(msg, 9, ""),
    fsize: jspb.Message.getFieldWithDefault(msg, 10, 0),
    ltfile: jspb.Message.getFieldWithDefault(msg, 11, ""),
    ctime: jspb.Message.getFieldWithDefault(msg, 12, ""),
    latime: jspb.Message.getFieldWithDefault(msg, 13, ""),
    lmtime: jspb.Message.getFieldWithDefault(msg, 14, ""),
    lctime: jspb.Message.getFieldWithDefault(msg, 15, ""),
    ino: jspb.Message.getFieldWithDefault(msg, 16, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDDirQuery}
 */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDDirQuery;
  return proto.com.jy.network_monitor.proto.MSG_SDDirQuery.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDDirQuery} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDDirQuery}
 */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setFid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSoftid(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setUid(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setGrpid(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setType(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setPath(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setPerm(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setFhash(value);
      break;
    case 10:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setFsize(value);
      break;
    case 11:
      var value = /** @type {string} */ (reader.readString());
      msg.setLtfile(value);
      break;
    case 12:
      var value = /** @type {string} */ (reader.readString());
      msg.setCtime(value);
      break;
    case 13:
      var value = /** @type {string} */ (reader.readString());
      msg.setLatime(value);
      break;
    case 14:
      var value = /** @type {string} */ (reader.readString());
      msg.setLmtime(value);
      break;
    case 15:
      var value = /** @type {string} */ (reader.readString());
      msg.setLctime(value);
      break;
    case 16:
      var value = /** @type {string} */ (reader.readString());
      msg.setIno(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDDirQuery.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDDirQuery} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getSoftid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getUid();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getGrpid();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getType();
  if (f !== 0) {
    writer.writeInt32(
      6,
      f
    );
  }
  f = message.getPath();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getPerm();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getFhash();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
  f = message.getFsize();
  if (f !== 0) {
    writer.writeInt64(
      10,
      f
    );
  }
  f = message.getLtfile();
  if (f.length > 0) {
    writer.writeString(
      11,
      f
    );
  }
  f = message.getCtime();
  if (f.length > 0) {
    writer.writeString(
      12,
      f
    );
  }
  f = message.getLatime();
  if (f.length > 0) {
    writer.writeString(
      13,
      f
    );
  }
  f = message.getLmtime();
  if (f.length > 0) {
    writer.writeString(
      14,
      f
    );
  }
  f = message.getLctime();
  if (f.length > 0) {
    writer.writeString(
      15,
      f
    );
  }
  f = message.getIno();
  if (f.length > 0) {
    writer.writeString(
      16,
      f
    );
  }
};


/**
 * optional string FID = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.getFid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.setFid = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string SOFTID = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.getSoftid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.setSoftid = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string UID = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.getUid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.setUid = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string GRPID = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.getGrpid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.setGrpid = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string NAME = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.setName = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional int32 TYPE = 6;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.getType = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.setType = function(value) {
  jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional string PATH = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.getPath = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.setPath = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string PERM = 8;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.getPerm = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.setPerm = function(value) {
  jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional string FHASH = 9;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.getFhash = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.setFhash = function(value) {
  jspb.Message.setProto3StringField(this, 9, value);
};


/**
 * optional int64 FSIZE = 10;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.getFsize = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 10, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.setFsize = function(value) {
  jspb.Message.setProto3IntField(this, 10, value);
};


/**
 * optional string LTFILE = 11;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.getLtfile = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 11, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.setLtfile = function(value) {
  jspb.Message.setProto3StringField(this, 11, value);
};


/**
 * optional string CTIME = 12;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.getCtime = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 12, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.setCtime = function(value) {
  jspb.Message.setProto3StringField(this, 12, value);
};


/**
 * optional string LATIME = 13;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.getLatime = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 13, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.setLatime = function(value) {
  jspb.Message.setProto3StringField(this, 13, value);
};


/**
 * optional string LMTIME = 14;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.getLmtime = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 14, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.setLmtime = function(value) {
  jspb.Message.setProto3StringField(this, 14, value);
};


/**
 * optional string LCTIME = 15;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.getLctime = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 15, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.setLctime = function(value) {
  jspb.Message.setProto3StringField(this, 15, value);
};


/**
 * optional string INO = 16;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.getIno = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 16, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDDirQuery.prototype.setIno = function(value) {
  jspb.Message.setProto3StringField(this, 16, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSAppInstallPathQuery = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSAppInstallPathQuery, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSAppInstallPathQuery.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSAppInstallPathQuery';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSAppInstallPathQuery.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSAppInstallPathQuery.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSAppInstallPathQuery} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSAppInstallPathQuery.toObject = function(includeInstance, msg) {
  var f, obj = {
    pname: jspb.Message.getFieldWithDefault(msg, 1, ""),
    ptype: jspb.Message.getFieldWithDefault(msg, 2, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSAppInstallPathQuery}
 */
proto.com.jy.network_monitor.proto.MSG_DSAppInstallPathQuery.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSAppInstallPathQuery;
  return proto.com.jy.network_monitor.proto.MSG_DSAppInstallPathQuery.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSAppInstallPathQuery} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSAppInstallPathQuery}
 */
proto.com.jy.network_monitor.proto.MSG_DSAppInstallPathQuery.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setPname(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setPtype(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSAppInstallPathQuery.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSAppInstallPathQuery.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSAppInstallPathQuery} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSAppInstallPathQuery.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPname();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getPtype();
  if (f !== 0) {
    writer.writeInt32(
      2,
      f
    );
  }
};


/**
 * optional string PNAME = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSAppInstallPathQuery.prototype.getPname = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSAppInstallPathQuery.prototype.setPname = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional int32 PTYPE = 2;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSAppInstallPathQuery.prototype.getPtype = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSAppInstallPathQuery.prototype.setPtype = function(value) {
  jspb.Message.setProto3IntField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.toObject = function(includeInstance, msg) {
  var f, obj = {
    fid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    softid: jspb.Message.getFieldWithDefault(msg, 2, ""),
    uid: jspb.Message.getFieldWithDefault(msg, 3, ""),
    grpid: jspb.Message.getFieldWithDefault(msg, 4, ""),
    name: jspb.Message.getFieldWithDefault(msg, 5, ""),
    type: jspb.Message.getFieldWithDefault(msg, 6, 0),
    path: jspb.Message.getFieldWithDefault(msg, 7, ""),
    perm: jspb.Message.getFieldWithDefault(msg, 8, ""),
    fhash: jspb.Message.getFieldWithDefault(msg, 9, ""),
    fsize: jspb.Message.getFieldWithDefault(msg, 10, 0),
    ltfile: jspb.Message.getFieldWithDefault(msg, 11, ""),
    ctime: jspb.Message.getFieldWithDefault(msg, 12, ""),
    latime: jspb.Message.getFieldWithDefault(msg, 13, ""),
    lmtime: jspb.Message.getFieldWithDefault(msg, 14, ""),
    lctime: jspb.Message.getFieldWithDefault(msg, 15, ""),
    ino: jspb.Message.getFieldWithDefault(msg, 16, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery}
 */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery;
  return proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery}
 */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setFid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSoftid(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setUid(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setGrpid(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setType(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setPath(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setPerm(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setFhash(value);
      break;
    case 10:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setFsize(value);
      break;
    case 11:
      var value = /** @type {string} */ (reader.readString());
      msg.setLtfile(value);
      break;
    case 12:
      var value = /** @type {string} */ (reader.readString());
      msg.setCtime(value);
      break;
    case 13:
      var value = /** @type {string} */ (reader.readString());
      msg.setLatime(value);
      break;
    case 14:
      var value = /** @type {string} */ (reader.readString());
      msg.setLmtime(value);
      break;
    case 15:
      var value = /** @type {string} */ (reader.readString());
      msg.setLctime(value);
      break;
    case 16:
      var value = /** @type {string} */ (reader.readString());
      msg.setIno(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getSoftid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getUid();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getGrpid();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getType();
  if (f !== 0) {
    writer.writeInt32(
      6,
      f
    );
  }
  f = message.getPath();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getPerm();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getFhash();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
  f = message.getFsize();
  if (f !== 0) {
    writer.writeInt64(
      10,
      f
    );
  }
  f = message.getLtfile();
  if (f.length > 0) {
    writer.writeString(
      11,
      f
    );
  }
  f = message.getCtime();
  if (f.length > 0) {
    writer.writeString(
      12,
      f
    );
  }
  f = message.getLatime();
  if (f.length > 0) {
    writer.writeString(
      13,
      f
    );
  }
  f = message.getLmtime();
  if (f.length > 0) {
    writer.writeString(
      14,
      f
    );
  }
  f = message.getLctime();
  if (f.length > 0) {
    writer.writeString(
      15,
      f
    );
  }
  f = message.getIno();
  if (f.length > 0) {
    writer.writeString(
      16,
      f
    );
  }
};


/**
 * optional string FID = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.getFid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.setFid = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string SOFTID = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.getSoftid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.setSoftid = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string UID = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.getUid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.setUid = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string GRPID = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.getGrpid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.setGrpid = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string NAME = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.setName = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional int32 TYPE = 6;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.getType = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.setType = function(value) {
  jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional string PATH = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.getPath = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.setPath = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string PERM = 8;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.getPerm = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.setPerm = function(value) {
  jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional string FHASH = 9;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.getFhash = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.setFhash = function(value) {
  jspb.Message.setProto3StringField(this, 9, value);
};


/**
 * optional int64 FSIZE = 10;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.getFsize = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 10, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.setFsize = function(value) {
  jspb.Message.setProto3IntField(this, 10, value);
};


/**
 * optional string LTFILE = 11;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.getLtfile = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 11, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.setLtfile = function(value) {
  jspb.Message.setProto3StringField(this, 11, value);
};


/**
 * optional string CTIME = 12;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.getCtime = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 12, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.setCtime = function(value) {
  jspb.Message.setProto3StringField(this, 12, value);
};


/**
 * optional string LATIME = 13;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.getLatime = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 13, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.setLatime = function(value) {
  jspb.Message.setProto3StringField(this, 13, value);
};


/**
 * optional string LMTIME = 14;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.getLmtime = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 14, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.setLmtime = function(value) {
  jspb.Message.setProto3StringField(this, 14, value);
};


/**
 * optional string LCTIME = 15;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.getLctime = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 15, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.setLctime = function(value) {
  jspb.Message.setProto3StringField(this, 15, value);
};


/**
 * optional string INO = 16;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.getIno = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 16, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDAppInstallPathQuery.prototype.setIno = function(value) {
  jspb.Message.setProto3StringField(this, 16, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSVersionQuery = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSVersionQuery, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSVersionQuery.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSVersionQuery';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSVersionQuery.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSVersionQuery.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSVersionQuery} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSVersionQuery.toObject = function(includeInstance, msg) {
  var f, obj = {
    name: jspb.Message.getFieldWithDefault(msg, 1, ""),
    type: jspb.Message.getFieldWithDefault(msg, 2, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSVersionQuery}
 */
proto.com.jy.network_monitor.proto.MSG_DSVersionQuery.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSVersionQuery;
  return proto.com.jy.network_monitor.proto.MSG_DSVersionQuery.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSVersionQuery} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSVersionQuery}
 */
proto.com.jy.network_monitor.proto.MSG_DSVersionQuery.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setType(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSVersionQuery.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSVersionQuery.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSVersionQuery} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSVersionQuery.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getType();
  if (f !== 0) {
    writer.writeInt32(
      2,
      f
    );
  }
};


/**
 * optional string NAME = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSVersionQuery.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSVersionQuery.prototype.setName = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional int32 TYPE = 2;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSVersionQuery.prototype.getType = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSVersionQuery.prototype.setType = function(value) {
  jspb.Message.setProto3IntField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDVersionQuery = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.com.jy.network_monitor.proto.MSG_SDVersionQuery.repeatedFields_, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDVersionQuery, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDVersionQuery.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDVersionQuery';
}
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.com.jy.network_monitor.proto.MSG_SDVersionQuery.repeatedFields_ = [1,2];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDVersionQuery.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDVersionQuery.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDVersionQuery} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDVersionQuery.toObject = function(includeInstance, msg) {
  var f, obj = {
    execfileList: jspb.Message.toObjectList(msg.getExecfileList(),
    PublicDefine2_pb.MSG_File2.toObject, includeInstance),
    driverList: jspb.Message.toObjectList(msg.getDriverList(),
    PublicDefine2_pb.MSG_File2.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDVersionQuery}
 */
proto.com.jy.network_monitor.proto.MSG_SDVersionQuery.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDVersionQuery;
  return proto.com.jy.network_monitor.proto.MSG_SDVersionQuery.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDVersionQuery} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDVersionQuery}
 */
proto.com.jy.network_monitor.proto.MSG_SDVersionQuery.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new PublicDefine2_pb.MSG_File2;
      reader.readMessage(value,PublicDefine2_pb.MSG_File2.deserializeBinaryFromReader);
      msg.addExecfile(value);
      break;
    case 2:
      var value = new PublicDefine2_pb.MSG_File2;
      reader.readMessage(value,PublicDefine2_pb.MSG_File2.deserializeBinaryFromReader);
      msg.addDriver(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDVersionQuery.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDVersionQuery.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDVersionQuery} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDVersionQuery.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getExecfileList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      PublicDefine2_pb.MSG_File2.serializeBinaryToWriter
    );
  }
  f = message.getDriverList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      2,
      f,
      PublicDefine2_pb.MSG_File2.serializeBinaryToWriter
    );
  }
};


/**
 * repeated MSG_File2 EXECFILE = 1;
 * @return {!Array<!proto.com.jy.network_monitor.proto.MSG_File2>}
 */
proto.com.jy.network_monitor.proto.MSG_SDVersionQuery.prototype.getExecfileList = function() {
  return /** @type{!Array<!proto.com.jy.network_monitor.proto.MSG_File2>} */ (
    jspb.Message.getRepeatedWrapperField(this, PublicDefine2_pb.MSG_File2, 1));
};


/** @param {!Array<!proto.com.jy.network_monitor.proto.MSG_File2>} value */
proto.com.jy.network_monitor.proto.MSG_SDVersionQuery.prototype.setExecfileList = function(value) {
  jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.com.jy.network_monitor.proto.MSG_File2=} opt_value
 * @param {number=} opt_index
 * @return {!proto.com.jy.network_monitor.proto.MSG_File2}
 */
proto.com.jy.network_monitor.proto.MSG_SDVersionQuery.prototype.addExecfile = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.com.jy.network_monitor.proto.MSG_File2, opt_index);
};


proto.com.jy.network_monitor.proto.MSG_SDVersionQuery.prototype.clearExecfileList = function() {
  this.setExecfileList([]);
};


/**
 * repeated MSG_File2 DRIVER = 2;
 * @return {!Array<!proto.com.jy.network_monitor.proto.MSG_File2>}
 */
proto.com.jy.network_monitor.proto.MSG_SDVersionQuery.prototype.getDriverList = function() {
  return /** @type{!Array<!proto.com.jy.network_monitor.proto.MSG_File2>} */ (
    jspb.Message.getRepeatedWrapperField(this, PublicDefine2_pb.MSG_File2, 2));
};


/** @param {!Array<!proto.com.jy.network_monitor.proto.MSG_File2>} value */
proto.com.jy.network_monitor.proto.MSG_SDVersionQuery.prototype.setDriverList = function(value) {
  jspb.Message.setRepeatedWrapperField(this, 2, value);
};


/**
 * @param {!proto.com.jy.network_monitor.proto.MSG_File2=} opt_value
 * @param {number=} opt_index
 * @return {!proto.com.jy.network_monitor.proto.MSG_File2}
 */
proto.com.jy.network_monitor.proto.MSG_SDVersionQuery.prototype.addDriver = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 2, opt_value, proto.com.jy.network_monitor.proto.MSG_File2, opt_index);
};


proto.com.jy.network_monitor.proto.MSG_SDVersionQuery.prototype.clearDriverList = function() {
  this.setDriverList([]);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfAsset = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfAsset, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfAsset.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfAsset';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfAsset.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfAsset.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfAsset} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfAsset.toObject = function(includeInstance, msg) {
  var f, obj = {
    pid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    fname: jspb.Message.getFieldWithDefault(msg, 2, ""),
    stime: jspb.Message.getFieldWithDefault(msg, 3, ""),
    prid: jspb.Message.getFieldWithDefault(msg, 4, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfAsset}
 */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfAsset.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfAsset;
  return proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfAsset.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfAsset} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfAsset}
 */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfAsset.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setPid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setFname(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setStime(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setPrid(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfAsset.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfAsset.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfAsset} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfAsset.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getFname();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getStime();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getPrid();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
};


/**
 * optional string PID = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfAsset.prototype.getPid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfAsset.prototype.setPid = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string FNAME = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfAsset.prototype.getFname = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfAsset.prototype.setFname = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string STIME = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfAsset.prototype.getStime = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfAsset.prototype.setStime = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string PRID = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfAsset.prototype.getPrid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfAsset.prototype.setPrid = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfAsset = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfAsset, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfAsset.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfAsset';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfAsset.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfAsset.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfAsset} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfAsset.toObject = function(includeInstance, msg) {
  var f, obj = {
    ret: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msg: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfAsset}
 */
proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfAsset.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfAsset;
  return proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfAsset.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfAsset} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfAsset}
 */
proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfAsset.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRet(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsg(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfAsset.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfAsset.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfAsset} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfAsset.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRet();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getMsg();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional int32 ret = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfAsset.prototype.getRet = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfAsset.prototype.setRet = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string msg = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfAsset.prototype.getMsg = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfAsset.prototype.setMsg = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfAsset = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfAsset, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfAsset.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfAsset';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfAsset.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfAsset.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfAsset} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfAsset.toObject = function(includeInstance, msg) {
  var f, obj = {
    ifnm: jspb.Message.getFieldWithDefault(msg, 1, ""),
    ifid: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfAsset}
 */
proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfAsset.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfAsset;
  return proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfAsset.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfAsset} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfAsset}
 */
proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfAsset.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setIfnm(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setIfid(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfAsset.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfAsset.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfAsset} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfAsset.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getIfnm();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getIfid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string IFNM = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfAsset.prototype.getIfnm = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfAsset.prototype.setIfnm = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string IFID = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfAsset.prototype.getIfid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfAsset.prototype.setIfid = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfAsset = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfAsset, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfAsset.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfAsset';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfAsset.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfAsset.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfAsset} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfAsset.toObject = function(includeInstance, msg) {
  var f, obj = {
    ret: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msg: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfAsset}
 */
proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfAsset.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfAsset;
  return proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfAsset.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfAsset} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfAsset}
 */
proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfAsset.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRet(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsg(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfAsset.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfAsset.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfAsset} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfAsset.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRet();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getMsg();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional int32 ret = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfAsset.prototype.getRet = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfAsset.prototype.setRet = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string msg = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfAsset.prototype.getMsg = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfAsset.prototype.setMsg = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfAsset = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfAsset, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfAsset.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfAsset';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfAsset.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfAsset.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfAsset} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfAsset.toObject = function(includeInstance, msg) {
  var f, obj = {
    ifnm: jspb.Message.getFieldWithDefault(msg, 1, ""),
    ifid: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfAsset}
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfAsset.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfAsset;
  return proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfAsset.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfAsset} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfAsset}
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfAsset.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setIfnm(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setIfid(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfAsset.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfAsset.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfAsset} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfAsset.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getIfnm();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getIfid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string IFNM = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfAsset.prototype.getIfnm = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfAsset.prototype.setIfnm = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string IFID = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfAsset.prototype.getIfid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfAsset.prototype.setIfid = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfAsset = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfAsset, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfAsset.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfAsset';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfAsset.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfAsset.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfAsset} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfAsset.toObject = function(includeInstance, msg) {
  var f, obj = {
    ret: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msg: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfAsset}
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfAsset.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfAsset;
  return proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfAsset.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfAsset} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfAsset}
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfAsset.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRet(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsg(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfAsset.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfAsset.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfAsset} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfAsset.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRet();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getMsg();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional int32 ret = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfAsset.prototype.getRet = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfAsset.prototype.setRet = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string msg = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfAsset.prototype.getMsg = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfAsset.prototype.setMsg = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSLockUserOfAsset = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSLockUserOfAsset, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSLockUserOfAsset.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSLockUserOfAsset';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSLockUserOfAsset.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSLockUserOfAsset.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSLockUserOfAsset} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSLockUserOfAsset.toObject = function(includeInstance, msg) {
  var f, obj = {
    name: jspb.Message.getFieldWithDefault(msg, 1, ""),
    uid: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSLockUserOfAsset}
 */
proto.com.jy.network_monitor.proto.MSG_DSLockUserOfAsset.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSLockUserOfAsset;
  return proto.com.jy.network_monitor.proto.MSG_DSLockUserOfAsset.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSLockUserOfAsset} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSLockUserOfAsset}
 */
proto.com.jy.network_monitor.proto.MSG_DSLockUserOfAsset.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setUid(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSLockUserOfAsset.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSLockUserOfAsset.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSLockUserOfAsset} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSLockUserOfAsset.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getUid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string NAME = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSLockUserOfAsset.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSLockUserOfAsset.prototype.setName = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string UID = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSLockUserOfAsset.prototype.getUid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSLockUserOfAsset.prototype.setUid = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDLockUserOfAsset = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDLockUserOfAsset, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDLockUserOfAsset.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDLockUserOfAsset';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDLockUserOfAsset.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDLockUserOfAsset.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDLockUserOfAsset} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDLockUserOfAsset.toObject = function(includeInstance, msg) {
  var f, obj = {
    ret: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msg: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDLockUserOfAsset}
 */
proto.com.jy.network_monitor.proto.MSG_SDLockUserOfAsset.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDLockUserOfAsset;
  return proto.com.jy.network_monitor.proto.MSG_SDLockUserOfAsset.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDLockUserOfAsset} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDLockUserOfAsset}
 */
proto.com.jy.network_monitor.proto.MSG_SDLockUserOfAsset.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRet(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsg(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDLockUserOfAsset.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDLockUserOfAsset.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDLockUserOfAsset} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDLockUserOfAsset.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRet();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getMsg();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional int32 ret = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDLockUserOfAsset.prototype.getRet = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDLockUserOfAsset.prototype.setRet = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string msg = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDLockUserOfAsset.prototype.getMsg = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDLockUserOfAsset.prototype.setMsg = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfAsset = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfAsset, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfAsset.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfAsset';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfAsset.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfAsset.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfAsset} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfAsset.toObject = function(includeInstance, msg) {
  var f, obj = {
    name: jspb.Message.getFieldWithDefault(msg, 1, ""),
    uid: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfAsset}
 */
proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfAsset.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfAsset;
  return proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfAsset.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfAsset} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfAsset}
 */
proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfAsset.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setUid(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfAsset.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfAsset.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfAsset} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfAsset.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getUid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string NAME = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfAsset.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfAsset.prototype.setName = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string UID = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfAsset.prototype.getUid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfAsset.prototype.setUid = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfAsset = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfAsset, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfAsset.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfAsset';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfAsset.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfAsset.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfAsset} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfAsset.toObject = function(includeInstance, msg) {
  var f, obj = {
    ret: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msg: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfAsset}
 */
proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfAsset.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfAsset;
  return proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfAsset.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfAsset} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfAsset}
 */
proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfAsset.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRet(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsg(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfAsset.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfAsset.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfAsset} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfAsset.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRet();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getMsg();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional int32 ret = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfAsset.prototype.getRet = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfAsset.prototype.setRet = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string msg = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfAsset.prototype.getMsg = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfAsset.prototype.setMsg = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.toObject = function(includeInstance, msg) {
  var f, obj = {
    srcbip: jspb.Message.getFieldWithDefault(msg, 1, ""),
    srceip: jspb.Message.getFieldWithDefault(msg, 2, ""),
    srcbport: jspb.Message.getFieldWithDefault(msg, 3, 0),
    srceport: jspb.Message.getFieldWithDefault(msg, 4, 0),
    dstbip: jspb.Message.getFieldWithDefault(msg, 5, ""),
    dsteip: jspb.Message.getFieldWithDefault(msg, 6, ""),
    dstbport: jspb.Message.getFieldWithDefault(msg, 7, 0),
    dsteport: jspb.Message.getFieldWithDefault(msg, 8, 0),
    proto: jspb.Message.getFieldWithDefault(msg, 9, ""),
    ifnm: jspb.Message.getFieldWithDefault(msg, 10, ""),
    action: jspb.Message.getFieldWithDefault(msg, 11, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset;
  return proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setSrcbip(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSrceip(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSrcbport(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSrceport(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setDstbip(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setDsteip(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setDstbport(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setDsteport(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setProto(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setIfnm(value);
      break;
    case 11:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setAction(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSrcbip();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getSrceip();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSrcbport();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getSrceport();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
  f = message.getDstbip();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getDsteip();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getDstbport();
  if (f !== 0) {
    writer.writeInt32(
      7,
      f
    );
  }
  f = message.getDsteport();
  if (f !== 0) {
    writer.writeInt32(
      8,
      f
    );
  }
  f = message.getProto();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
  f = message.getIfnm();
  if (f.length > 0) {
    writer.writeString(
      10,
      f
    );
  }
  f = message.getAction();
  if (f !== 0) {
    writer.writeInt32(
      11,
      f
    );
  }
};


/**
 * optional string SRCBIP = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.prototype.getSrcbip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.prototype.setSrcbip = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string SRCEIP = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.prototype.getSrceip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.prototype.setSrceip = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int32 SRCBPORT = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.prototype.getSrcbport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.prototype.setSrcbport = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional int32 SRCEPORT = 4;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.prototype.getSrceport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.prototype.setSrceport = function(value) {
  jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional string DSTBIP = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.prototype.getDstbip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.prototype.setDstbip = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string DSTEIP = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.prototype.getDsteip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.prototype.setDsteip = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional int32 DSTBPORT = 7;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.prototype.getDstbport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.prototype.setDstbport = function(value) {
  jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional int32 DSTEPORT = 8;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.prototype.getDsteport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.prototype.setDsteport = function(value) {
  jspb.Message.setProto3IntField(this, 8, value);
};


/**
 * optional string PROTO = 9;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.prototype.getProto = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.prototype.setProto = function(value) {
  jspb.Message.setProto3StringField(this, 9, value);
};


/**
 * optional string IFNM = 10;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.prototype.getIfnm = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.prototype.setIfnm = function(value) {
  jspb.Message.setProto3StringField(this, 10, value);
};


/**
 * optional int32 ACTION = 11;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.prototype.getAction = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 11, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfAsset.prototype.setAction = function(value) {
  jspb.Message.setProto3IntField(this, 11, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDAddACPOfAsset = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDAddACPOfAsset, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDAddACPOfAsset.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDAddACPOfAsset';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDAddACPOfAsset.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDAddACPOfAsset.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDAddACPOfAsset} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDAddACPOfAsset.toObject = function(includeInstance, msg) {
  var f, obj = {
    ret: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msg: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDAddACPOfAsset}
 */
proto.com.jy.network_monitor.proto.MSG_SDAddACPOfAsset.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDAddACPOfAsset;
  return proto.com.jy.network_monitor.proto.MSG_SDAddACPOfAsset.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDAddACPOfAsset} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDAddACPOfAsset}
 */
proto.com.jy.network_monitor.proto.MSG_SDAddACPOfAsset.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRet(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsg(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDAddACPOfAsset.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDAddACPOfAsset.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDAddACPOfAsset} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDAddACPOfAsset.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRet();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getMsg();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional int32 ret = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDAddACPOfAsset.prototype.getRet = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDAddACPOfAsset.prototype.setRet = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string msg = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDAddACPOfAsset.prototype.getMsg = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDAddACPOfAsset.prototype.setMsg = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.toObject = function(includeInstance, msg) {
  var f, obj = {
    srcbip: jspb.Message.getFieldWithDefault(msg, 1, ""),
    srceip: jspb.Message.getFieldWithDefault(msg, 2, ""),
    srcbport: jspb.Message.getFieldWithDefault(msg, 3, 0),
    srceport: jspb.Message.getFieldWithDefault(msg, 4, 0),
    dstbip: jspb.Message.getFieldWithDefault(msg, 5, ""),
    dsteip: jspb.Message.getFieldWithDefault(msg, 6, ""),
    dstbport: jspb.Message.getFieldWithDefault(msg, 7, 0),
    dsteport: jspb.Message.getFieldWithDefault(msg, 8, 0),
    proto: jspb.Message.getFieldWithDefault(msg, 9, ""),
    ifnm: jspb.Message.getFieldWithDefault(msg, 10, ""),
    action: jspb.Message.getFieldWithDefault(msg, 11, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset;
  return proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setSrcbip(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSrceip(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSrcbport(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSrceport(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setDstbip(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setDsteip(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setDstbport(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setDsteport(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setProto(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setIfnm(value);
      break;
    case 11:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setAction(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSrcbip();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getSrceip();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSrcbport();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getSrceport();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
  f = message.getDstbip();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getDsteip();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getDstbport();
  if (f !== 0) {
    writer.writeInt32(
      7,
      f
    );
  }
  f = message.getDsteport();
  if (f !== 0) {
    writer.writeInt32(
      8,
      f
    );
  }
  f = message.getProto();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
  f = message.getIfnm();
  if (f.length > 0) {
    writer.writeString(
      10,
      f
    );
  }
  f = message.getAction();
  if (f !== 0) {
    writer.writeInt32(
      11,
      f
    );
  }
};


/**
 * optional string SRCBIP = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.prototype.getSrcbip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.prototype.setSrcbip = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string SRCEIP = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.prototype.getSrceip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.prototype.setSrceip = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int32 SRCBPORT = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.prototype.getSrcbport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.prototype.setSrcbport = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional int32 SRCEPORT = 4;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.prototype.getSrceport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.prototype.setSrceport = function(value) {
  jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional string DSTBIP = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.prototype.getDstbip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.prototype.setDstbip = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string DSTEIP = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.prototype.getDsteip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.prototype.setDsteip = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional int32 DSTBPORT = 7;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.prototype.getDstbport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.prototype.setDstbport = function(value) {
  jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional int32 DSTEPORT = 8;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.prototype.getDsteport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.prototype.setDsteport = function(value) {
  jspb.Message.setProto3IntField(this, 8, value);
};


/**
 * optional string PROTO = 9;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.prototype.getProto = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.prototype.setProto = function(value) {
  jspb.Message.setProto3StringField(this, 9, value);
};


/**
 * optional string IFNM = 10;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.prototype.getIfnm = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.prototype.setIfnm = function(value) {
  jspb.Message.setProto3StringField(this, 10, value);
};


/**
 * optional int32 ACTION = 11;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.prototype.getAction = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 11, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfAsset.prototype.setAction = function(value) {
  jspb.Message.setProto3IntField(this, 11, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfAsset = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfAsset, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfAsset.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfAsset';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfAsset.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfAsset.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfAsset} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfAsset.toObject = function(includeInstance, msg) {
  var f, obj = {
    ret: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msg: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfAsset}
 */
proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfAsset.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfAsset;
  return proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfAsset.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfAsset} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfAsset}
 */
proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfAsset.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRet(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsg(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfAsset.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfAsset.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfAsset} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfAsset.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRet();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getMsg();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional int32 ret = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfAsset.prototype.getRet = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfAsset.prototype.setRet = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string msg = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfAsset.prototype.getMsg = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfAsset.prototype.setMsg = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce.toObject = function(includeInstance, msg) {
  var f, obj = {
    pid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    sip: jspb.Message.getFieldWithDefault(msg, 2, ""),
    sport: jspb.Message.getFieldWithDefault(msg, 3, 0),
    dip: jspb.Message.getFieldWithDefault(msg, 4, ""),
    dport: jspb.Message.getFieldWithDefault(msg, 5, 0),
    name: jspb.Message.getFieldWithDefault(msg, 6, ""),
    sessid: jspb.Message.getFieldWithDefault(msg, 7, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce;
  return proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setPid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSip(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSport(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setDip(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setDport(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setSessid(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getSip();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSport();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getDip();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getDport();
  if (f !== 0) {
    writer.writeInt32(
      5,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getSessid();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
};


/**
 * optional string PID = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce.prototype.getPid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce.prototype.setPid = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string SIP = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce.prototype.getSip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce.prototype.setSip = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int32 SPORT = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce.prototype.getSport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce.prototype.setSport = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional string DIP = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce.prototype.getDip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce.prototype.setDip = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional int32 DPORT = 5;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce.prototype.getDport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce.prototype.setDport = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional string NAME = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce.prototype.setName = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string SESSID = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce.prototype.getSessid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHOnce.prototype.setSessid = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHOnce = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDBlockSSHOnce, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDBlockSSHOnce.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDBlockSSHOnce';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHOnce.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDBlockSSHOnce.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDBlockSSHOnce} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHOnce.toObject = function(includeInstance, msg) {
  var f, obj = {
    ret: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msg: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDBlockSSHOnce}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHOnce.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDBlockSSHOnce;
  return proto.com.jy.network_monitor.proto.MSG_SDBlockSSHOnce.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDBlockSSHOnce} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDBlockSSHOnce}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHOnce.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRet(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsg(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHOnce.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDBlockSSHOnce.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDBlockSSHOnce} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHOnce.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRet();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getMsg();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional int32 ret = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHOnce.prototype.getRet = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHOnce.prototype.setRet = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string msg = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHOnce.prototype.getMsg = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHOnce.prototype.setMsg = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer.toObject = function(includeInstance, msg) {
  var f, obj = {
    pid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    sip: jspb.Message.getFieldWithDefault(msg, 2, ""),
    sport: jspb.Message.getFieldWithDefault(msg, 3, 0),
    dip: jspb.Message.getFieldWithDefault(msg, 4, ""),
    dport: jspb.Message.getFieldWithDefault(msg, 5, 0),
    name: jspb.Message.getFieldWithDefault(msg, 6, ""),
    len: jspb.Message.getFieldWithDefault(msg, 7, 0),
    sessid: jspb.Message.getFieldWithDefault(msg, 8, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer;
  return proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setPid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSip(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSport(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setDip(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setDport(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setLen(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setSessid(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getSip();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSport();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getDip();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getDport();
  if (f !== 0) {
    writer.writeInt32(
      5,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getLen();
  if (f !== 0) {
    writer.writeInt32(
      7,
      f
    );
  }
  f = message.getSessid();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
};


/**
 * optional string PID = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer.prototype.getPid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer.prototype.setPid = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string SIP = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer.prototype.getSip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer.prototype.setSip = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int32 SPORT = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer.prototype.getSport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer.prototype.setSport = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional string DIP = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer.prototype.getDip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer.prototype.setDip = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional int32 DPORT = 5;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer.prototype.getDport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer.prototype.setDport = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional string NAME = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer.prototype.setName = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional int32 LEN = 7;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer.prototype.getLen = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer.prototype.setLen = function(value) {
  jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional string SESSID = 8;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer.prototype.getSessid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHByTimer.prototype.setSessid = function(value) {
  jspb.Message.setProto3StringField(this, 8, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHByTimer = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDBlockSSHByTimer, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDBlockSSHByTimer.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDBlockSSHByTimer';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHByTimer.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDBlockSSHByTimer.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDBlockSSHByTimer} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHByTimer.toObject = function(includeInstance, msg) {
  var f, obj = {
    ret: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msg: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDBlockSSHByTimer}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHByTimer.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDBlockSSHByTimer;
  return proto.com.jy.network_monitor.proto.MSG_SDBlockSSHByTimer.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDBlockSSHByTimer} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDBlockSSHByTimer}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHByTimer.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRet(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsg(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHByTimer.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDBlockSSHByTimer.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDBlockSSHByTimer} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHByTimer.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRet();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getMsg();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional int32 ret = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHByTimer.prototype.getRet = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHByTimer.prototype.setRet = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string msg = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHByTimer.prototype.getMsg = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHByTimer.prototype.setMsg = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever.toObject = function(includeInstance, msg) {
  var f, obj = {
    pid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    sip: jspb.Message.getFieldWithDefault(msg, 2, ""),
    sport: jspb.Message.getFieldWithDefault(msg, 3, 0),
    dip: jspb.Message.getFieldWithDefault(msg, 4, ""),
    dport: jspb.Message.getFieldWithDefault(msg, 5, 0),
    name: jspb.Message.getFieldWithDefault(msg, 6, ""),
    sessid: jspb.Message.getFieldWithDefault(msg, 7, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever;
  return proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setPid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSip(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSport(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setDip(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setDport(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setSessid(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getSip();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSport();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getDip();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getDport();
  if (f !== 0) {
    writer.writeInt32(
      5,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getSessid();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
};


/**
 * optional string PID = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever.prototype.getPid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever.prototype.setPid = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string SIP = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever.prototype.getSip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever.prototype.setSip = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int32 SPORT = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever.prototype.getSport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever.prototype.setSport = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional string DIP = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever.prototype.getDip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever.prototype.setDip = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional int32 DPORT = 5;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever.prototype.getDport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever.prototype.setDport = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional string NAME = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever.prototype.setName = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string SESSID = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever.prototype.getSessid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockSSHForever.prototype.setSessid = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHForever = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDBlockSSHForever, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDBlockSSHForever.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDBlockSSHForever';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHForever.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDBlockSSHForever.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDBlockSSHForever} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHForever.toObject = function(includeInstance, msg) {
  var f, obj = {
    ret: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msg: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDBlockSSHForever}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHForever.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDBlockSSHForever;
  return proto.com.jy.network_monitor.proto.MSG_SDBlockSSHForever.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDBlockSSHForever} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDBlockSSHForever}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHForever.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRet(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsg(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHForever.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDBlockSSHForever.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDBlockSSHForever} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHForever.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRet();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getMsg();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional int32 ret = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHForever.prototype.getRet = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHForever.prototype.setRet = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string msg = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHForever.prototype.getMsg = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDBlockSSHForever.prototype.setMsg = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once.toObject = function(includeInstance, msg) {
  var f, obj = {
    pid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    sip: jspb.Message.getFieldWithDefault(msg, 2, ""),
    sport: jspb.Message.getFieldWithDefault(msg, 3, 0),
    dip: jspb.Message.getFieldWithDefault(msg, 4, ""),
    dport: jspb.Message.getFieldWithDefault(msg, 5, 0),
    name: jspb.Message.getFieldWithDefault(msg, 6, ""),
    sessid: jspb.Message.getFieldWithDefault(msg, 7, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once;
  return proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setPid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSip(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSport(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setDip(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setDport(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setSessid(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getSip();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSport();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getDip();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getDport();
  if (f !== 0) {
    writer.writeInt32(
      5,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getSessid();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
};


/**
 * optional string PID = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once.prototype.getPid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once.prototype.setPid = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string SIP = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once.prototype.getSip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once.prototype.setSip = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int32 SPORT = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once.prototype.getSport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once.prototype.setSport = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional string DIP = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once.prototype.getDip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once.prototype.setDip = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional int32 DPORT = 5;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once.prototype.getDport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once.prototype.setDport = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional string NAME = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once.prototype.setName = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string SESSID = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once.prototype.getSessid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Once.prototype.setSessid = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11Once = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDBlockX11Once, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDBlockX11Once.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDBlockX11Once';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11Once.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDBlockX11Once.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDBlockX11Once} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11Once.toObject = function(includeInstance, msg) {
  var f, obj = {
    ret: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msg: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDBlockX11Once}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11Once.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDBlockX11Once;
  return proto.com.jy.network_monitor.proto.MSG_SDBlockX11Once.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDBlockX11Once} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDBlockX11Once}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11Once.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRet(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsg(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11Once.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDBlockX11Once.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDBlockX11Once} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11Once.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRet();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getMsg();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional int32 ret = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11Once.prototype.getRet = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11Once.prototype.setRet = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string msg = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11Once.prototype.getMsg = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11Once.prototype.setMsg = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer.toObject = function(includeInstance, msg) {
  var f, obj = {
    pid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    sip: jspb.Message.getFieldWithDefault(msg, 2, ""),
    sport: jspb.Message.getFieldWithDefault(msg, 3, 0),
    dip: jspb.Message.getFieldWithDefault(msg, 4, ""),
    dport: jspb.Message.getFieldWithDefault(msg, 5, 0),
    name: jspb.Message.getFieldWithDefault(msg, 6, ""),
    len: jspb.Message.getFieldWithDefault(msg, 7, 0),
    sessid: jspb.Message.getFieldWithDefault(msg, 8, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer;
  return proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setPid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSip(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSport(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setDip(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setDport(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setLen(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setSessid(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getSip();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSport();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getDip();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getDport();
  if (f !== 0) {
    writer.writeInt32(
      5,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getLen();
  if (f !== 0) {
    writer.writeInt32(
      7,
      f
    );
  }
  f = message.getSessid();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
};


/**
 * optional string PID = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer.prototype.getPid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer.prototype.setPid = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string SIP = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer.prototype.getSip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer.prototype.setSip = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int32 SPORT = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer.prototype.getSport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer.prototype.setSport = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional string DIP = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer.prototype.getDip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer.prototype.setDip = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional int32 DPORT = 5;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer.prototype.getDport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer.prototype.setDport = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional string NAME = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer.prototype.setName = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional int32 LEN = 7;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer.prototype.getLen = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer.prototype.setLen = function(value) {
  jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional string SESSID = 8;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer.prototype.getSessid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11ByTimer.prototype.setSessid = function(value) {
  jspb.Message.setProto3StringField(this, 8, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11ByTimer = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDBlockX11ByTimer, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDBlockX11ByTimer.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDBlockX11ByTimer';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11ByTimer.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDBlockX11ByTimer.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDBlockX11ByTimer} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11ByTimer.toObject = function(includeInstance, msg) {
  var f, obj = {
    ret: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msg: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDBlockX11ByTimer}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11ByTimer.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDBlockX11ByTimer;
  return proto.com.jy.network_monitor.proto.MSG_SDBlockX11ByTimer.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDBlockX11ByTimer} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDBlockX11ByTimer}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11ByTimer.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRet(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsg(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11ByTimer.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDBlockX11ByTimer.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDBlockX11ByTimer} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11ByTimer.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRet();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getMsg();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional int32 ret = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11ByTimer.prototype.getRet = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11ByTimer.prototype.setRet = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string msg = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11ByTimer.prototype.getMsg = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11ByTimer.prototype.setMsg = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever.toObject = function(includeInstance, msg) {
  var f, obj = {
    pid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    sip: jspb.Message.getFieldWithDefault(msg, 2, ""),
    sport: jspb.Message.getFieldWithDefault(msg, 3, 0),
    dip: jspb.Message.getFieldWithDefault(msg, 4, ""),
    dport: jspb.Message.getFieldWithDefault(msg, 5, 0),
    name: jspb.Message.getFieldWithDefault(msg, 6, ""),
    sessid: jspb.Message.getFieldWithDefault(msg, 7, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever;
  return proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setPid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSip(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSport(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setDip(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setDport(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setSessid(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getSip();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSport();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getDip();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getDport();
  if (f !== 0) {
    writer.writeInt32(
      5,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getSessid();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
};


/**
 * optional string PID = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever.prototype.getPid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever.prototype.setPid = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string SIP = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever.prototype.getSip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever.prototype.setSip = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int32 SPORT = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever.prototype.getSport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever.prototype.setSport = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional string DIP = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever.prototype.getDip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever.prototype.setDip = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional int32 DPORT = 5;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever.prototype.getDport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever.prototype.setDport = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional string NAME = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever.prototype.setName = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string SESSID = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever.prototype.getSessid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSBlockX11Forever.prototype.setSessid = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11Forever = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDBlockX11Forever, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDBlockX11Forever.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDBlockX11Forever';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11Forever.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDBlockX11Forever.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDBlockX11Forever} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11Forever.toObject = function(includeInstance, msg) {
  var f, obj = {
    ret: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msg: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDBlockX11Forever}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11Forever.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDBlockX11Forever;
  return proto.com.jy.network_monitor.proto.MSG_SDBlockX11Forever.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDBlockX11Forever} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDBlockX11Forever}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11Forever.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRet(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsg(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11Forever.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDBlockX11Forever.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDBlockX11Forever} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11Forever.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRet();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getMsg();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional int32 ret = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11Forever.prototype.getRet = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11Forever.prototype.setRet = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string msg = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11Forever.prototype.getMsg = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDBlockX11Forever.prototype.setMsg = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH.toObject = function(includeInstance, msg) {
  var f, obj = {
    pid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    sip: jspb.Message.getFieldWithDefault(msg, 2, ""),
    sport: jspb.Message.getFieldWithDefault(msg, 3, 0),
    dip: jspb.Message.getFieldWithDefault(msg, 4, ""),
    dport: jspb.Message.getFieldWithDefault(msg, 5, 0),
    name: jspb.Message.getFieldWithDefault(msg, 6, ""),
    sessid: jspb.Message.getFieldWithDefault(msg, 7, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH}
 */
proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH;
  return proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH}
 */
proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setPid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSip(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSport(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setDip(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setDport(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setSessid(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getSip();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSport();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getDip();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getDport();
  if (f !== 0) {
    writer.writeInt32(
      5,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getSessid();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
};


/**
 * optional string PID = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH.prototype.getPid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH.prototype.setPid = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string SIP = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH.prototype.getSip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH.prototype.setSip = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int32 SPORT = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH.prototype.getSport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH.prototype.setSport = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional string DIP = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH.prototype.getDip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH.prototype.setDip = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional int32 DPORT = 5;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH.prototype.getDport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH.prototype.setDport = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional string NAME = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH.prototype.setName = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string SESSID = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH.prototype.getSessid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSRestoreSSH.prototype.setSessid = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDRestoreSSH = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDRestoreSSH, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDRestoreSSH.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDRestoreSSH';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDRestoreSSH.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDRestoreSSH.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDRestoreSSH} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDRestoreSSH.toObject = function(includeInstance, msg) {
  var f, obj = {
    ret: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msg: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDRestoreSSH}
 */
proto.com.jy.network_monitor.proto.MSG_SDRestoreSSH.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDRestoreSSH;
  return proto.com.jy.network_monitor.proto.MSG_SDRestoreSSH.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDRestoreSSH} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDRestoreSSH}
 */
proto.com.jy.network_monitor.proto.MSG_SDRestoreSSH.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRet(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsg(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDRestoreSSH.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDRestoreSSH.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDRestoreSSH} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDRestoreSSH.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRet();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getMsg();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional int32 ret = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDRestoreSSH.prototype.getRet = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDRestoreSSH.prototype.setRet = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string msg = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDRestoreSSH.prototype.getMsg = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDRestoreSSH.prototype.setMsg = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSRestoreX11 = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSRestoreX11, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSRestoreX11.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSRestoreX11';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSRestoreX11.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSRestoreX11.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSRestoreX11} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSRestoreX11.toObject = function(includeInstance, msg) {
  var f, obj = {
    pid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    sip: jspb.Message.getFieldWithDefault(msg, 2, ""),
    sport: jspb.Message.getFieldWithDefault(msg, 3, 0),
    dip: jspb.Message.getFieldWithDefault(msg, 4, ""),
    dport: jspb.Message.getFieldWithDefault(msg, 5, 0),
    name: jspb.Message.getFieldWithDefault(msg, 6, ""),
    sessid: jspb.Message.getFieldWithDefault(msg, 7, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSRestoreX11}
 */
proto.com.jy.network_monitor.proto.MSG_DSRestoreX11.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSRestoreX11;
  return proto.com.jy.network_monitor.proto.MSG_DSRestoreX11.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSRestoreX11} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSRestoreX11}
 */
proto.com.jy.network_monitor.proto.MSG_DSRestoreX11.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setPid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSip(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSport(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setDip(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setDport(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setSessid(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSRestoreX11.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSRestoreX11.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSRestoreX11} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSRestoreX11.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getSip();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSport();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getDip();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getDport();
  if (f !== 0) {
    writer.writeInt32(
      5,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getSessid();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
};


/**
 * optional string PID = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSRestoreX11.prototype.getPid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSRestoreX11.prototype.setPid = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string SIP = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSRestoreX11.prototype.getSip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSRestoreX11.prototype.setSip = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int32 SPORT = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSRestoreX11.prototype.getSport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSRestoreX11.prototype.setSport = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional string DIP = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSRestoreX11.prototype.getDip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSRestoreX11.prototype.setDip = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional int32 DPORT = 5;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSRestoreX11.prototype.getDport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSRestoreX11.prototype.setDport = function(value) {
  jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional string NAME = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSRestoreX11.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSRestoreX11.prototype.setName = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string SESSID = 7;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSRestoreX11.prototype.getSessid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSRestoreX11.prototype.setSessid = function(value) {
  jspb.Message.setProto3StringField(this, 7, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDRestoreX11 = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDRestoreX11, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDRestoreX11.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDRestoreX11';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDRestoreX11.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDRestoreX11.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDRestoreX11} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDRestoreX11.toObject = function(includeInstance, msg) {
  var f, obj = {
    ret: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msg: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDRestoreX11}
 */
proto.com.jy.network_monitor.proto.MSG_SDRestoreX11.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDRestoreX11;
  return proto.com.jy.network_monitor.proto.MSG_SDRestoreX11.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDRestoreX11} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDRestoreX11}
 */
proto.com.jy.network_monitor.proto.MSG_SDRestoreX11.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRet(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsg(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDRestoreX11.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDRestoreX11.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDRestoreX11} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDRestoreX11.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRet();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getMsg();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional int32 ret = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDRestoreX11.prototype.getRet = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDRestoreX11.prototype.setRet = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string msg = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDRestoreX11.prototype.getMsg = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDRestoreX11.prototype.setMsg = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg.toObject = function(includeInstance, msg) {
  var f, obj = {
    pid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    fname: jspb.Message.getFieldWithDefault(msg, 2, ""),
    name: jspb.Message.getFieldWithDefault(msg, 3, ""),
    stime: jspb.Message.getFieldWithDefault(msg, 4, ""),
    prid: jspb.Message.getFieldWithDefault(msg, 5, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg;
  return proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setPid(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setFname(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setStime(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setPrid(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getFname();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getStime();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getPrid();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
};


/**
 * optional string PID = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg.prototype.getPid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg.prototype.setPid = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string FNAME = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg.prototype.getFname = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg.prototype.setFname = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string NAME = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg.prototype.setName = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string STIME = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg.prototype.getStime = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg.prototype.setStime = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string PRID = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg.prototype.getPrid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSTerminateProcessOfEmerg.prototype.setPrid = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfEmerg = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfEmerg, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfEmerg.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfEmerg';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfEmerg.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfEmerg.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfEmerg} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfEmerg.toObject = function(includeInstance, msg) {
  var f, obj = {
    ret: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msg: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfEmerg.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfEmerg;
  return proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfEmerg.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfEmerg} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfEmerg.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRet(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsg(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfEmerg.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfEmerg.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfEmerg} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfEmerg.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRet();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getMsg();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional int32 ret = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfEmerg.prototype.getRet = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfEmerg.prototype.setRet = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string msg = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfEmerg.prototype.getMsg = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDTerminateProcessOfEmerg.prototype.setMsg = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfEmerg = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfEmerg, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfEmerg.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfEmerg';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfEmerg.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfEmerg.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfEmerg} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfEmerg.toObject = function(includeInstance, msg) {
  var f, obj = {
    ifnm: jspb.Message.getFieldWithDefault(msg, 1, ""),
    ip: jspb.Message.getFieldWithDefault(msg, 2, ""),
    ifid: jspb.Message.getFieldWithDefault(msg, 3, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfEmerg.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfEmerg;
  return proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfEmerg.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfEmerg} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfEmerg.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setIfnm(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setIp(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setIfid(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfEmerg.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfEmerg.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfEmerg} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfEmerg.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getIfnm();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getIp();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getIfid();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
};


/**
 * optional string IFNM = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfEmerg.prototype.getIfnm = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfEmerg.prototype.setIfnm = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string IP = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfEmerg.prototype.getIp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfEmerg.prototype.setIp = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string IFID = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfEmerg.prototype.getIfid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICOfEmerg.prototype.setIfid = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfEmerg = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfEmerg, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfEmerg.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfEmerg';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfEmerg.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfEmerg.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfEmerg} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfEmerg.toObject = function(includeInstance, msg) {
  var f, obj = {
    ret: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msg: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfEmerg.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfEmerg;
  return proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfEmerg.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfEmerg} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfEmerg.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRet(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsg(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfEmerg.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfEmerg.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfEmerg} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfEmerg.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRet();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getMsg();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional int32 ret = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfEmerg.prototype.getRet = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfEmerg.prototype.setRet = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string msg = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfEmerg.prototype.getMsg = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICOfEmerg.prototype.setMsg = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICByTimerOfEmerg = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSDisableNICByTimerOfEmerg, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSDisableNICByTimerOfEmerg.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSDisableNICByTimerOfEmerg';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICByTimerOfEmerg.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSDisableNICByTimerOfEmerg.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSDisableNICByTimerOfEmerg} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICByTimerOfEmerg.toObject = function(includeInstance, msg) {
  var f, obj = {
    ifnm: jspb.Message.getFieldWithDefault(msg, 1, ""),
    ip: jspb.Message.getFieldWithDefault(msg, 2, ""),
    ifid: jspb.Message.getFieldWithDefault(msg, 3, ""),
    len: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSDisableNICByTimerOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICByTimerOfEmerg.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSDisableNICByTimerOfEmerg;
  return proto.com.jy.network_monitor.proto.MSG_DSDisableNICByTimerOfEmerg.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSDisableNICByTimerOfEmerg} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSDisableNICByTimerOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICByTimerOfEmerg.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setIfnm(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setIp(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setIfid(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setLen(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICByTimerOfEmerg.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSDisableNICByTimerOfEmerg.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSDisableNICByTimerOfEmerg} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICByTimerOfEmerg.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getIfnm();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getIp();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getIfid();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getLen();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
};


/**
 * optional string IFNM = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICByTimerOfEmerg.prototype.getIfnm = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICByTimerOfEmerg.prototype.setIfnm = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string IP = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICByTimerOfEmerg.prototype.getIp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICByTimerOfEmerg.prototype.setIp = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string IFID = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICByTimerOfEmerg.prototype.getIfid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICByTimerOfEmerg.prototype.setIfid = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional int32 LEN = 4;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICByTimerOfEmerg.prototype.getLen = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICByTimerOfEmerg.prototype.setLen = function(value) {
  jspb.Message.setProto3IntField(this, 4, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICByTimerOfEmerg = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDDisableNICByTimerOfEmerg, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDDisableNICByTimerOfEmerg.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDDisableNICByTimerOfEmerg';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICByTimerOfEmerg.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDDisableNICByTimerOfEmerg.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDDisableNICByTimerOfEmerg} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICByTimerOfEmerg.toObject = function(includeInstance, msg) {
  var f, obj = {
    ret: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msg: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDDisableNICByTimerOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICByTimerOfEmerg.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDDisableNICByTimerOfEmerg;
  return proto.com.jy.network_monitor.proto.MSG_SDDisableNICByTimerOfEmerg.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDDisableNICByTimerOfEmerg} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDDisableNICByTimerOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICByTimerOfEmerg.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRet(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsg(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICByTimerOfEmerg.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDDisableNICByTimerOfEmerg.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDDisableNICByTimerOfEmerg} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICByTimerOfEmerg.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRet();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getMsg();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional int32 ret = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICByTimerOfEmerg.prototype.getRet = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICByTimerOfEmerg.prototype.setRet = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string msg = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICByTimerOfEmerg.prototype.getMsg = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICByTimerOfEmerg.prototype.setMsg = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICForeverOfEmerg = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSDisableNICForeverOfEmerg, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSDisableNICForeverOfEmerg.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSDisableNICForeverOfEmerg';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICForeverOfEmerg.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSDisableNICForeverOfEmerg.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSDisableNICForeverOfEmerg} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICForeverOfEmerg.toObject = function(includeInstance, msg) {
  var f, obj = {
    ifnm: jspb.Message.getFieldWithDefault(msg, 1, ""),
    ip: jspb.Message.getFieldWithDefault(msg, 2, ""),
    ifid: jspb.Message.getFieldWithDefault(msg, 3, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSDisableNICForeverOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICForeverOfEmerg.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSDisableNICForeverOfEmerg;
  return proto.com.jy.network_monitor.proto.MSG_DSDisableNICForeverOfEmerg.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSDisableNICForeverOfEmerg} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSDisableNICForeverOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICForeverOfEmerg.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setIfnm(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setIp(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setIfid(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICForeverOfEmerg.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSDisableNICForeverOfEmerg.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSDisableNICForeverOfEmerg} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICForeverOfEmerg.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getIfnm();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getIp();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getIfid();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
};


/**
 * optional string IFNM = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICForeverOfEmerg.prototype.getIfnm = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICForeverOfEmerg.prototype.setIfnm = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string IP = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICForeverOfEmerg.prototype.getIp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICForeverOfEmerg.prototype.setIp = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string IFID = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICForeverOfEmerg.prototype.getIfid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSDisableNICForeverOfEmerg.prototype.setIfid = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICForeverOfEmerg = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDDisableNICForeverOfEmerg, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDDisableNICForeverOfEmerg.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDDisableNICForeverOfEmerg';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICForeverOfEmerg.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDDisableNICForeverOfEmerg.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDDisableNICForeverOfEmerg} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICForeverOfEmerg.toObject = function(includeInstance, msg) {
  var f, obj = {
    ret: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msg: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDDisableNICForeverOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICForeverOfEmerg.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDDisableNICForeverOfEmerg;
  return proto.com.jy.network_monitor.proto.MSG_SDDisableNICForeverOfEmerg.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDDisableNICForeverOfEmerg} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDDisableNICForeverOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICForeverOfEmerg.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRet(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsg(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICForeverOfEmerg.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDDisableNICForeverOfEmerg.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDDisableNICForeverOfEmerg} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICForeverOfEmerg.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRet();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getMsg();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional int32 ret = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICForeverOfEmerg.prototype.getRet = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICForeverOfEmerg.prototype.setRet = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string msg = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICForeverOfEmerg.prototype.getMsg = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDDisableNICForeverOfEmerg.prototype.setMsg = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfEmerg = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfEmerg, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfEmerg.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfEmerg';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfEmerg.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfEmerg.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfEmerg} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfEmerg.toObject = function(includeInstance, msg) {
  var f, obj = {
    ifnm: jspb.Message.getFieldWithDefault(msg, 1, ""),
    ifid: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfEmerg.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfEmerg;
  return proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfEmerg.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfEmerg} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfEmerg.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setIfnm(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setIfid(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfEmerg.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfEmerg.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfEmerg} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfEmerg.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getIfnm();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getIfid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string IFNM = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfEmerg.prototype.getIfnm = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfEmerg.prototype.setIfnm = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string IFID = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfEmerg.prototype.getIfid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSEnableNICOfEmerg.prototype.setIfid = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfEmerg = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfEmerg, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfEmerg.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfEmerg';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfEmerg.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfEmerg.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfEmerg} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfEmerg.toObject = function(includeInstance, msg) {
  var f, obj = {
    ret: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msg: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfEmerg.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfEmerg;
  return proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfEmerg.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfEmerg} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfEmerg.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRet(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsg(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfEmerg.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfEmerg.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfEmerg} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfEmerg.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRet();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getMsg();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional int32 ret = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfEmerg.prototype.getRet = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfEmerg.prototype.setRet = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string msg = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfEmerg.prototype.getMsg = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDEnableNICOfEmerg.prototype.setMsg = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSLockUserOfEmerg = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSLockUserOfEmerg, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSLockUserOfEmerg.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSLockUserOfEmerg';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSLockUserOfEmerg.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSLockUserOfEmerg.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSLockUserOfEmerg} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSLockUserOfEmerg.toObject = function(includeInstance, msg) {
  var f, obj = {
    name: jspb.Message.getFieldWithDefault(msg, 1, ""),
    uid: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSLockUserOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_DSLockUserOfEmerg.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSLockUserOfEmerg;
  return proto.com.jy.network_monitor.proto.MSG_DSLockUserOfEmerg.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSLockUserOfEmerg} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSLockUserOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_DSLockUserOfEmerg.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setUid(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSLockUserOfEmerg.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSLockUserOfEmerg.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSLockUserOfEmerg} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSLockUserOfEmerg.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getUid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string NAME = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSLockUserOfEmerg.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSLockUserOfEmerg.prototype.setName = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string UID = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSLockUserOfEmerg.prototype.getUid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSLockUserOfEmerg.prototype.setUid = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDLockUserOfEmerg = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDLockUserOfEmerg, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDLockUserOfEmerg.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDLockUserOfEmerg';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDLockUserOfEmerg.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDLockUserOfEmerg.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDLockUserOfEmerg} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDLockUserOfEmerg.toObject = function(includeInstance, msg) {
  var f, obj = {
    ret: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msg: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDLockUserOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_SDLockUserOfEmerg.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDLockUserOfEmerg;
  return proto.com.jy.network_monitor.proto.MSG_SDLockUserOfEmerg.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDLockUserOfEmerg} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDLockUserOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_SDLockUserOfEmerg.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRet(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsg(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDLockUserOfEmerg.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDLockUserOfEmerg.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDLockUserOfEmerg} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDLockUserOfEmerg.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRet();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getMsg();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional int32 ret = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDLockUserOfEmerg.prototype.getRet = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDLockUserOfEmerg.prototype.setRet = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string msg = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDLockUserOfEmerg.prototype.getMsg = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDLockUserOfEmerg.prototype.setMsg = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfEmerg = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfEmerg, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfEmerg.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfEmerg';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfEmerg.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfEmerg.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfEmerg} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfEmerg.toObject = function(includeInstance, msg) {
  var f, obj = {
    name: jspb.Message.getFieldWithDefault(msg, 1, ""),
    uid: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfEmerg.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfEmerg;
  return proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfEmerg.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfEmerg} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfEmerg.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setUid(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfEmerg.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfEmerg.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfEmerg} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfEmerg.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getUid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string NAME = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfEmerg.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfEmerg.prototype.setName = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string UID = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfEmerg.prototype.getUid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSUnlockUserOfEmerg.prototype.setUid = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfEmerg = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfEmerg, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfEmerg.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfEmerg';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfEmerg.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfEmerg.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfEmerg} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfEmerg.toObject = function(includeInstance, msg) {
  var f, obj = {
    ret: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msg: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfEmerg.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfEmerg;
  return proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfEmerg.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfEmerg} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfEmerg.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRet(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsg(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfEmerg.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfEmerg.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfEmerg} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfEmerg.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRet();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getMsg();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional int32 ret = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfEmerg.prototype.getRet = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfEmerg.prototype.setRet = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string msg = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfEmerg.prototype.getMsg = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDUnlockUserOfEmerg.prototype.setMsg = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.toObject = function(includeInstance, msg) {
  var f, obj = {
    srcbip: jspb.Message.getFieldWithDefault(msg, 1, ""),
    srceip: jspb.Message.getFieldWithDefault(msg, 2, ""),
    srcbport: jspb.Message.getFieldWithDefault(msg, 3, 0),
    srceport: jspb.Message.getFieldWithDefault(msg, 4, 0),
    dstbip: jspb.Message.getFieldWithDefault(msg, 5, ""),
    dsteip: jspb.Message.getFieldWithDefault(msg, 6, ""),
    dstbport: jspb.Message.getFieldWithDefault(msg, 7, 0),
    dsteport: jspb.Message.getFieldWithDefault(msg, 8, 0),
    proto: jspb.Message.getFieldWithDefault(msg, 9, ""),
    ifnm: jspb.Message.getFieldWithDefault(msg, 10, ""),
    action: jspb.Message.getFieldWithDefault(msg, 11, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg;
  return proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setSrcbip(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSrceip(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSrcbport(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSrceport(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setDstbip(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setDsteip(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setDstbport(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setDsteport(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setProto(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setIfnm(value);
      break;
    case 11:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setAction(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSrcbip();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getSrceip();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSrcbport();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getSrceport();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
  f = message.getDstbip();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getDsteip();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getDstbport();
  if (f !== 0) {
    writer.writeInt32(
      7,
      f
    );
  }
  f = message.getDsteport();
  if (f !== 0) {
    writer.writeInt32(
      8,
      f
    );
  }
  f = message.getProto();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
  f = message.getIfnm();
  if (f.length > 0) {
    writer.writeString(
      10,
      f
    );
  }
  f = message.getAction();
  if (f !== 0) {
    writer.writeInt32(
      11,
      f
    );
  }
};


/**
 * optional string SRCBIP = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.prototype.getSrcbip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.prototype.setSrcbip = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string SRCEIP = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.prototype.getSrceip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.prototype.setSrceip = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int32 SRCBPORT = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.prototype.getSrcbport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.prototype.setSrcbport = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional int32 SRCEPORT = 4;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.prototype.getSrceport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.prototype.setSrceport = function(value) {
  jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional string DSTBIP = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.prototype.getDstbip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.prototype.setDstbip = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string DSTEIP = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.prototype.getDsteip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.prototype.setDsteip = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional int32 DSTBPORT = 7;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.prototype.getDstbport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.prototype.setDstbport = function(value) {
  jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional int32 DSTEPORT = 8;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.prototype.getDsteport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.prototype.setDsteport = function(value) {
  jspb.Message.setProto3IntField(this, 8, value);
};


/**
 * optional string PROTO = 9;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.prototype.getProto = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.prototype.setProto = function(value) {
  jspb.Message.setProto3StringField(this, 9, value);
};


/**
 * optional string IFNM = 10;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.prototype.getIfnm = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.prototype.setIfnm = function(value) {
  jspb.Message.setProto3StringField(this, 10, value);
};


/**
 * optional int32 ACTION = 11;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.prototype.getAction = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 11, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSAddACPOfEmerg.prototype.setAction = function(value) {
  jspb.Message.setProto3IntField(this, 11, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDAddACPOfEmerg = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDAddACPOfEmerg, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDAddACPOfEmerg.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDAddACPOfEmerg';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDAddACPOfEmerg.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDAddACPOfEmerg.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDAddACPOfEmerg} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDAddACPOfEmerg.toObject = function(includeInstance, msg) {
  var f, obj = {
    ret: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msg: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDAddACPOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_SDAddACPOfEmerg.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDAddACPOfEmerg;
  return proto.com.jy.network_monitor.proto.MSG_SDAddACPOfEmerg.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDAddACPOfEmerg} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDAddACPOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_SDAddACPOfEmerg.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRet(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsg(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDAddACPOfEmerg.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDAddACPOfEmerg.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDAddACPOfEmerg} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDAddACPOfEmerg.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRet();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getMsg();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional int32 ret = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDAddACPOfEmerg.prototype.getRet = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDAddACPOfEmerg.prototype.setRet = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string msg = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDAddACPOfEmerg.prototype.getMsg = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDAddACPOfEmerg.prototype.setMsg = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.displayName = 'proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.toObject = function(includeInstance, msg) {
  var f, obj = {
    srcbip: jspb.Message.getFieldWithDefault(msg, 1, ""),
    srceip: jspb.Message.getFieldWithDefault(msg, 2, ""),
    srcbport: jspb.Message.getFieldWithDefault(msg, 3, 0),
    srceport: jspb.Message.getFieldWithDefault(msg, 4, 0),
    dstbip: jspb.Message.getFieldWithDefault(msg, 5, ""),
    dsteip: jspb.Message.getFieldWithDefault(msg, 6, ""),
    dstbport: jspb.Message.getFieldWithDefault(msg, 7, 0),
    dsteport: jspb.Message.getFieldWithDefault(msg, 8, 0),
    proto: jspb.Message.getFieldWithDefault(msg, 9, ""),
    ifnm: jspb.Message.getFieldWithDefault(msg, 10, ""),
    action: jspb.Message.getFieldWithDefault(msg, 11, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg;
  return proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setSrcbip(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSrceip(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSrcbport(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSrceport(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setDstbip(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setDsteip(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setDstbport(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setDsteport(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setProto(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setIfnm(value);
      break;
    case 11:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setAction(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSrcbip();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getSrceip();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSrcbport();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getSrceport();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
  f = message.getDstbip();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getDsteip();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getDstbport();
  if (f !== 0) {
    writer.writeInt32(
      7,
      f
    );
  }
  f = message.getDsteport();
  if (f !== 0) {
    writer.writeInt32(
      8,
      f
    );
  }
  f = message.getProto();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
  f = message.getIfnm();
  if (f.length > 0) {
    writer.writeString(
      10,
      f
    );
  }
  f = message.getAction();
  if (f !== 0) {
    writer.writeInt32(
      11,
      f
    );
  }
};


/**
 * optional string SRCBIP = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.prototype.getSrcbip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.prototype.setSrcbip = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string SRCEIP = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.prototype.getSrceip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.prototype.setSrceip = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int32 SRCBPORT = 3;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.prototype.getSrcbport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.prototype.setSrcbport = function(value) {
  jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional int32 SRCEPORT = 4;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.prototype.getSrceport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.prototype.setSrceport = function(value) {
  jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional string DSTBIP = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.prototype.getDstbip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.prototype.setDstbip = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string DSTEIP = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.prototype.getDsteip = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.prototype.setDsteip = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional int32 DSTBPORT = 7;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.prototype.getDstbport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.prototype.setDstbport = function(value) {
  jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional int32 DSTEPORT = 8;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.prototype.getDsteport = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.prototype.setDsteport = function(value) {
  jspb.Message.setProto3IntField(this, 8, value);
};


/**
 * optional string PROTO = 9;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.prototype.getProto = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.prototype.setProto = function(value) {
  jspb.Message.setProto3StringField(this, 9, value);
};


/**
 * optional string IFNM = 10;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.prototype.getIfnm = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.prototype.setIfnm = function(value) {
  jspb.Message.setProto3StringField(this, 10, value);
};


/**
 * optional int32 ACTION = 11;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.prototype.getAction = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 11, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_DSDeleteACPOfEmerg.prototype.setAction = function(value) {
  jspb.Message.setProto3IntField(this, 11, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfEmerg = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfEmerg, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfEmerg.displayName = 'proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfEmerg';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfEmerg.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfEmerg.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfEmerg} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfEmerg.toObject = function(includeInstance, msg) {
  var f, obj = {
    ret: jspb.Message.getFieldWithDefault(msg, 1, 0),
    msg: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfEmerg.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfEmerg;
  return proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfEmerg.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfEmerg} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfEmerg}
 */
proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfEmerg.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRet(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMsg(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfEmerg.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfEmerg.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfEmerg} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfEmerg.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRet();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getMsg();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional int32 ret = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfEmerg.prototype.getRet = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfEmerg.prototype.setRet = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string msg = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfEmerg.prototype.getMsg = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_SDDeleteACPOfEmerg.prototype.setMsg = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_MSG_DSPlugin = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_MSG_DSPlugin, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_MSG_DSPlugin.displayName = 'proto.com.jy.network_monitor.proto.MSG_MSG_DSPlugin';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_MSG_DSPlugin.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_MSG_DSPlugin.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_MSG_DSPlugin} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_MSG_DSPlugin.toObject = function(includeInstance, msg) {
  var f, obj = {

  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_MSG_DSPlugin}
 */
proto.com.jy.network_monitor.proto.MSG_MSG_DSPlugin.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_MSG_DSPlugin;
  return proto.com.jy.network_monitor.proto.MSG_MSG_DSPlugin.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_MSG_DSPlugin} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_MSG_DSPlugin}
 */
proto.com.jy.network_monitor.proto.MSG_MSG_DSPlugin.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_MSG_DSPlugin.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_MSG_DSPlugin.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_MSG_DSPlugin} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_MSG_DSPlugin.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_MSG_SDPlugin = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_MSG_SDPlugin, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_MSG_SDPlugin.displayName = 'proto.com.jy.network_monitor.proto.MSG_MSG_SDPlugin';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_MSG_SDPlugin.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_MSG_SDPlugin.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_MSG_SDPlugin} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_MSG_SDPlugin.toObject = function(includeInstance, msg) {
  var f, obj = {
    plnm: jspb.Message.getFieldWithDefault(msg, 1, ""),
    plver: jspb.Message.getFieldWithDefault(msg, 2, ""),
    brand: jspb.Message.getFieldWithDefault(msg, 3, ""),
    desc: jspb.Message.getFieldWithDefault(msg, 4, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_MSG_SDPlugin}
 */
proto.com.jy.network_monitor.proto.MSG_MSG_SDPlugin.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_MSG_SDPlugin;
  return proto.com.jy.network_monitor.proto.MSG_MSG_SDPlugin.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_MSG_SDPlugin} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_MSG_SDPlugin}
 */
proto.com.jy.network_monitor.proto.MSG_MSG_SDPlugin.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setPlnm(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setPlver(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setBrand(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setDesc(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_MSG_SDPlugin.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_MSG_SDPlugin.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_MSG_SDPlugin} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_MSG_SDPlugin.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPlnm();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getPlver();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getBrand();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getDesc();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
};


/**
 * optional string plnm = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_MSG_SDPlugin.prototype.getPlnm = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_MSG_SDPlugin.prototype.setPlnm = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string plver = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_MSG_SDPlugin.prototype.getPlver = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_MSG_SDPlugin.prototype.setPlver = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string brand = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_MSG_SDPlugin.prototype.getBrand = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_MSG_SDPlugin.prototype.setBrand = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string desc = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_MSG_SDPlugin.prototype.getDesc = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_MSG_SDPlugin.prototype.setDesc = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_AgentRegister = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.com.jy.network_monitor.proto.MSG_AgentRegister.repeatedFields_, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_AgentRegister, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_AgentRegister.displayName = 'proto.com.jy.network_monitor.proto.MSG_AgentRegister';
}
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.com.jy.network_monitor.proto.MSG_AgentRegister.repeatedFields_ = [3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_AgentRegister.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_AgentRegister.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_AgentRegister} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_AgentRegister.toObject = function(includeInstance, msg) {
  var f, obj = {
    hostname: jspb.Message.getFieldWithDefault(msg, 1, ""),
    devType: jspb.Message.getFieldWithDefault(msg, 2, ""),
    netList: jspb.Message.toObjectList(msg.getNetList(),
    proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet.toObject, includeInstance),
    ver: jspb.Message.getFieldWithDefault(msg, 4, ""),
    cert: jspb.Message.getFieldWithDefault(msg, 5, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_AgentRegister}
 */
proto.com.jy.network_monitor.proto.MSG_AgentRegister.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_AgentRegister;
  return proto.com.jy.network_monitor.proto.MSG_AgentRegister.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_AgentRegister} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_AgentRegister}
 */
proto.com.jy.network_monitor.proto.MSG_AgentRegister.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setHostname(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setDevType(value);
      break;
    case 3:
      var value = new proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet;
      reader.readMessage(value,proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet.deserializeBinaryFromReader);
      msg.addNet(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setVer(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setCert(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_AgentRegister.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_AgentRegister.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_AgentRegister} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_AgentRegister.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getHostname();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getDevType();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getNetList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet.serializeBinaryToWriter
    );
  }
  f = message.getVer();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getCert();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
};


/**
 * optional string hostname = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_AgentRegister.prototype.getHostname = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_AgentRegister.prototype.setHostname = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string dev_type = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_AgentRegister.prototype.getDevType = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_AgentRegister.prototype.setDevType = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * repeated MSG_AgentRegisterNet net = 3;
 * @return {!Array<!proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet>}
 */
proto.com.jy.network_monitor.proto.MSG_AgentRegister.prototype.getNetList = function() {
  return /** @type{!Array<!proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet, 3));
};


/** @param {!Array<!proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet>} value */
proto.com.jy.network_monitor.proto.MSG_AgentRegister.prototype.setNetList = function(value) {
  jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet=} opt_value
 * @param {number=} opt_index
 * @return {!proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet}
 */
proto.com.jy.network_monitor.proto.MSG_AgentRegister.prototype.addNet = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet, opt_index);
};


proto.com.jy.network_monitor.proto.MSG_AgentRegister.prototype.clearNetList = function() {
  this.setNetList([]);
};


/**
 * optional string ver = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_AgentRegister.prototype.getVer = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_AgentRegister.prototype.setVer = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string cert = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_AgentRegister.prototype.getCert = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_AgentRegister.prototype.setCert = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet.displayName = 'proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet.toObject = function(includeInstance, msg) {
  var f, obj = {
    ip: jspb.Message.getFieldWithDefault(msg, 1, ""),
    mac: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet}
 */
proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet;
  return proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet}
 */
proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setIp(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMac(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getIp();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getMac();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string ip = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet.prototype.getIp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet.prototype.setIp = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string mac = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet.prototype.getMac = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_AgentRegisterNet.prototype.setMac = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_HeartbeatRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_HeartbeatRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_HeartbeatRequest.displayName = 'proto.com.jy.network_monitor.proto.MSG_HeartbeatRequest';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_HeartbeatRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_HeartbeatRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_HeartbeatRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_HeartbeatRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    ver: jspb.Message.getFieldWithDefault(msg, 1, ""),
    status: jspb.Message.getFieldWithDefault(msg, 2, ""),
    abnormal: jspb.Message.getFieldWithDefault(msg, 3, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_HeartbeatRequest}
 */
proto.com.jy.network_monitor.proto.MSG_HeartbeatRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_HeartbeatRequest;
  return proto.com.jy.network_monitor.proto.MSG_HeartbeatRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_HeartbeatRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_HeartbeatRequest}
 */
proto.com.jy.network_monitor.proto.MSG_HeartbeatRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setVer(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setStatus(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setAbnormal(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_HeartbeatRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_HeartbeatRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_HeartbeatRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_HeartbeatRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getVer();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getStatus();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getAbnormal();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
};


/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.MSG_HeartbeatRequest.Status = {
  DUMMY: 0,
  NORMAL: 1,
  ABNORMAL: 2
};

/**
 * optional string ver = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_HeartbeatRequest.prototype.getVer = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_HeartbeatRequest.prototype.setVer = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string status = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_HeartbeatRequest.prototype.getStatus = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_HeartbeatRequest.prototype.setStatus = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string abnormal = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_HeartbeatRequest.prototype.getAbnormal = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_HeartbeatRequest.prototype.setAbnormal = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_HeartbeatResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_HeartbeatResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_HeartbeatResponse.displayName = 'proto.com.jy.network_monitor.proto.MSG_HeartbeatResponse';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_HeartbeatResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_HeartbeatResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_HeartbeatResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_HeartbeatResponse.toObject = function(includeInstance, msg) {
  var f, obj = {

  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_HeartbeatResponse}
 */
proto.com.jy.network_monitor.proto.MSG_HeartbeatResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_HeartbeatResponse;
  return proto.com.jy.network_monitor.proto.MSG_HeartbeatResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_HeartbeatResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_HeartbeatResponse}
 */
proto.com.jy.network_monitor.proto.MSG_HeartbeatResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_HeartbeatResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_HeartbeatResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_HeartbeatResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_HeartbeatResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent.displayName = 'proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent.toObject = function(includeInstance, msg) {
  var f, obj = {
    conmode: jspb.Message.getFieldWithDefault(msg, 1, 0),
    asset: jspb.Message.getFieldWithDefault(msg, 2, ""),
    behavior: jspb.Message.getFieldWithDefault(msg, 3, ""),
    review: jspb.Message.getFieldWithDefault(msg, 4, ""),
    control: jspb.Message.getFieldWithDefault(msg, 5, ""),
    confinter: jspb.Message.getFieldWithDefault(msg, 6, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent}
 */
proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent;
  return proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent}
 */
proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setConmode(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setAsset(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setBehavior(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setReview(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setControl(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setConfinter(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getConmode();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getAsset();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getBehavior();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getReview();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getControl();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getConfinter();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
};


/**
 * optional int32 conmode = 1;
 * @return {number}
 */
proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent.prototype.getConmode = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/** @param {number} value */
proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent.prototype.setConmode = function(value) {
  jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string asset = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent.prototype.getAsset = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent.prototype.setAsset = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string behavior = 3;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent.prototype.getBehavior = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent.prototype.setBehavior = function(value) {
  jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string review = 4;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent.prototype.getReview = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent.prototype.setReview = function(value) {
  jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string control = 5;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent.prototype.getControl = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent.prototype.setControl = function(value) {
  jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string confinter = 6;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent.prototype.getConfinter = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_AgentOnlineDataContent.prototype.setConfinter = function(value) {
  jspb.Message.setProto3StringField(this, 6, value);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCertSync = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.com.jy.network_monitor.proto.MSG_PlatformCertSync.repeatedFields_, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_PlatformCertSync, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_PlatformCertSync.displayName = 'proto.com.jy.network_monitor.proto.MSG_PlatformCertSync';
}
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCertSync.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCertSync.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_PlatformCertSync.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_PlatformCertSync} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCertSync.toObject = function(includeInstance, msg) {
  var f, obj = {
    platformcertList: jspb.Message.toObjectList(msg.getPlatformcertList(),
    proto.com.jy.network_monitor.proto.MSG_PlatformCert.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_PlatformCertSync}
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCertSync.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_PlatformCertSync;
  return proto.com.jy.network_monitor.proto.MSG_PlatformCertSync.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_PlatformCertSync} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_PlatformCertSync}
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCertSync.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.com.jy.network_monitor.proto.MSG_PlatformCert;
      reader.readMessage(value,proto.com.jy.network_monitor.proto.MSG_PlatformCert.deserializeBinaryFromReader);
      msg.addPlatformcert(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCertSync.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_PlatformCertSync.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_PlatformCertSync} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCertSync.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPlatformcertList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.com.jy.network_monitor.proto.MSG_PlatformCert.serializeBinaryToWriter
    );
  }
};


/**
 * repeated MSG_PlatformCert platformCert = 1;
 * @return {!Array<!proto.com.jy.network_monitor.proto.MSG_PlatformCert>}
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCertSync.prototype.getPlatformcertList = function() {
  return /** @type{!Array<!proto.com.jy.network_monitor.proto.MSG_PlatformCert>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.com.jy.network_monitor.proto.MSG_PlatformCert, 1));
};


/** @param {!Array<!proto.com.jy.network_monitor.proto.MSG_PlatformCert>} value */
proto.com.jy.network_monitor.proto.MSG_PlatformCertSync.prototype.setPlatformcertList = function(value) {
  jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.com.jy.network_monitor.proto.MSG_PlatformCert=} opt_value
 * @param {number=} opt_index
 * @return {!proto.com.jy.network_monitor.proto.MSG_PlatformCert}
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCertSync.prototype.addPlatformcert = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.com.jy.network_monitor.proto.MSG_PlatformCert, opt_index);
};


proto.com.jy.network_monitor.proto.MSG_PlatformCertSync.prototype.clearPlatformcertList = function() {
  this.setPlatformcertList([]);
};



/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCert = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.com.jy.network_monitor.proto.MSG_PlatformCert, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  proto.com.jy.network_monitor.proto.MSG_PlatformCert.displayName = 'proto.com.jy.network_monitor.proto.MSG_PlatformCert';
}


if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto suitable for use in Soy templates.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     com.google.apps.jspb.JsClassTemplate.JS_RESERVED_WORDS.
 * @param {boolean=} opt_includeInstance Whether to include the JSPB instance
 *     for transitional soy proto support: http://goto/soy-param-migration
 * @return {!Object}
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCert.prototype.toObject = function(opt_includeInstance) {
  return proto.com.jy.network_monitor.proto.MSG_PlatformCert.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Whether to include the JSPB
 *     instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.com.jy.network_monitor.proto.MSG_PlatformCert} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCert.toObject = function(includeInstance, msg) {
  var f, obj = {
    ip: jspb.Message.getFieldWithDefault(msg, 1, ""),
    cert: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.com.jy.network_monitor.proto.MSG_PlatformCert}
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCert.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.com.jy.network_monitor.proto.MSG_PlatformCert;
  return proto.com.jy.network_monitor.proto.MSG_PlatformCert.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.com.jy.network_monitor.proto.MSG_PlatformCert} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.com.jy.network_monitor.proto.MSG_PlatformCert}
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCert.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setIp(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setCert(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCert.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.com.jy.network_monitor.proto.MSG_PlatformCert.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.com.jy.network_monitor.proto.MSG_PlatformCert} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCert.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getIp();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getCert();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string ip = 1;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCert.prototype.getIp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_PlatformCert.prototype.setIp = function(value) {
  jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string cert = 2;
 * @return {string}
 */
proto.com.jy.network_monitor.proto.MSG_PlatformCert.prototype.getCert = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/** @param {string} value */
proto.com.jy.network_monitor.proto.MSG_PlatformCert.prototype.setCert = function(value) {
  jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.AgentEncryptMode = {
  AGENTENCRYPTMODE_BOTH: 0,
  AGENTENCRYPTMODE_SM4: 1,
  AGENTENCRYPTMODE_SM2: 2,
  AGENTENCRYPTMODE_NOENCRYPT: 10
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.AgentErrorCode = {
  AGENTERRORCODE_SUCCESS: 0,
  AGENTERRORCODE_INNERERROR: 1,
  AGENTERRORCODE_UNSUPPORTEDTYPE: 2,
  AGENTERRORCODE_WRONGPARAM: 3,
  AGENTERRORCODE_OBJECTNOTEXIST: 4,
  AGENTERRORCODE_OBJECTUNREACHABLE: 5,
  AGENTERRORCODE_INVALIDCONTENTFORMAT: 6,
  AGENTERRORCODE_TIMESTAMPMISMATCH: 7,
  AGENTERRORCODE_INVALIDSIGN: 8,
  AGENTERRORCODE_CANNOT_EXEC_CMD: 9,
  AGENTERRORCODE_DATAVERIFYFAILED: 10,
  AGENTERRORCODE_FILEEXIST: 11,
  AGENTERRORCODE_VERIFYDEVICEIDERROR: 12,
  AGENTERRORCODE_PACKETLENGTHERROR: 13,
  AGENTERRORCODE_ENCRYPTERROR: 14,
  AGENTERRORCODE_DEVICETYPEERROR: 15,
  AGENTERRORCODE_PACKETCHECKSUMERROR: 16,
  AGENTERRORCODE_BLOCKFAILED: 17
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.AgentServerPacketType = {
  AGENTSERVERPACKETTYPE_DUMMY: 0,
  AGENTSERVERPACKETTYPE_MODEL_UPLOAD: 1,
  AGENTSERVERPACKETTYPE_MESSAGE_NOTIFICATION: 2,
  AGENTSERVERPACKETTYPE_MODEL_QUERY: 3,
  AGENTSERVERPACKETTYPE_COMMAND_CONTROL: 4,
  AGENTSERVERPACKETTYPE_SVR_REGISTRATION: 5,
  AGENTSERVERPACKETTYPE_CONFIG_INTERACTION: 6
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.ModelUploadSubType = {
  MODELUPLOADSUBTYPE_DUMMY: 0,
  MODELUPLOADSUBTYPE_ASSET: 1,
  MODELUPLOADSUBTYPE_BEHAVIOR: 2
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.MessagePushSubType = {
  MESSAGEPUSHSUBTYPE_DUMMY: 0,
  MESSAGEPUSHSUBTYPE_FOUNDPLUGIN: 1,
  MESSAGEPUSHSUBTYPE_THIRDPARTYPLUGINMSGPUSH: 2
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.ModelQuerySubType = {
  MODELQUERYSUBTYPE_DUMMY: 0,
  MODELQUERYSUBTYPE_ASSET: 1,
  MODELQUERYSUBTYPE_FILE: 2,
  MODELQUERYSUBTYPE_DIR: 3,
  MODELQUERYSUBTYPE_APPINSTALLDIR: 4,
  MODELQUERYSUBTYPE_VERSIONQUERY: 5
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.CommandControlSubType = {
  COMMANDCONTROLSUBTYPE_DUMMY: 0,
  COMMANDCONTROLSUBTYPE_ASSETHANDLING: 1,
  COMMANDCONTROLSUBTYPE_EMERGHANDLING: 2,
  COMMANDCONTROLSUBTYPE_BASELINE: 3,
  COMMANDCONTROLSUBTYPE_ILLEGALACCESS: 4
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.AssetHandlingParam = {
  ASSETHANDLINGPARAM_DUMMY: 0,
  ASSETHANDLINGPARAM_TERMINATEPROCESS: 1,
  ASSETHANDLINGPARAM_ENABLENIC: 2,
  ASSETHANDLINGPARAM_DISABLENIC: 3,
  ASSETHANDLINGPARAM_LOCKUSER: 4,
  ASSETHANDLINGPARAM_UNLOCKUSER: 5,
  ASSETHANDLINGPARAM_ADDACP: 6,
  ASSETHANDLINGPARAM_DELETEACP: 7
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.EmergHandlingParam = {
  EMERGHANDLINGPARAM_DUMMY: 0,
  EMERGHANDLINGPARAM_BLOCKSSHONCE: 1,
  EMERGHANDLINGPARAM_BLOCKSSHBYTIMER: 2,
  EMERGHANDLINGPARAM_BLOCKSSHFOREVER: 3,
  EMERGHANDLINGPARAM_BLOCKX11ONCE: 4,
  EMERGHANDLINGPARAM_BLOCKX11BYTIMER: 5,
  EMERGHANDLINGPARAM_BLOCKX11FOREVER: 6,
  EMERGHANDLINGPARAM_RESTORESSH: 7,
  EMERGHANDLINGPARAM_RESTOREX11: 8,
  EMERGHANDLINGPARAM_TERMINATEPROCESS: 9,
  EMERGHANDLINGPARAM_DISABLENICONCE: 10,
  EMERGHANDLINGPARAM_DISABLENICBYTIMER: 11,
  EMERGHANDLINGPARAM_DISABLENICFOREVER: 12,
  EMERGHANDLINGPARAM_ENABLENIC: 13,
  EMERGHANDLINGPARAM_LOCKUSER: 14,
  EMERGHANDLINGPARAM_UNLOCKUSER: 15,
  EMERGHANDLINGPARAM_ADDACP: 16,
  EMERGHANDLINGPARAM_DELETEACP: 17
};

/**
 * @enum {number}
 */
proto.com.jy.network_monitor.proto.AgentAuthSubType = {
  AGENTAUTHSUBTYPE_DUMMY: 0,
  AGENTAUTHSUBTYPE_SDCONNECTMODEREQUEST: 1,
  AGENTAUTHSUBTYPE_DSCONNECTMODEREQUEST: 2,
  AGENTAUTHSUBTYPE_SDCONNECTMODEREQUESTCOMPLETE: 3,
  AGENTAUTHSUBTYPE_SDREQUESTCERT: 4,
  AGENTAUTHSUBTYPE_DSREQUESTCERT: 5,
  AGENTAUTHSUBTYPE_SDREQUESTCERTCOMPLETE: 6,
  AGENTAUTHSUBTYPE_SDREGISTER: 7,
  AGENTAUTHSUBTYPE_DSREGISTER: 8,
  AGENTAUTHSUBTYPE_SDREGISTERCOMPLETE: 9,
  AGENTAUTHSUBTYPE_SDONLINEREQUEST: 10,
  AGENTAUTHSUBTYPE_DSONLINEREQUEST: 11,
  AGENTAUTHSUBTYPE_SDVERIFYCRYPTKEY: 12,
  AGENTAUTHSUBTYPE_DSVERIFYCRYPTKEY: 13,
  AGENTAUTHSUBTYPE_SDONLINEREQUESTCOMPLETE: 14,
  AGENTAUTHSUBTYPE_HEARTBEATREQUEST: 15,
  AGENTAUTHSUBTYPE_HEARTBEATRESPONSE: 16,
  AGENTAUTHSUBTYPE_DSUPDATEDEVICEID: 17,
  AGENTAUTHSUBTYPE_SDUPDATEDEVICEID: 18,
  AGENTAUTHSUBTYPE_DSPLATFORMCERTUPDATE: 19,
  AGENTAUTHSUBTYPE_SDPLATFORMCERTUPDATE: 20
};

goog.object.extend(exports, proto.com.jy.network_monitor.proto);
