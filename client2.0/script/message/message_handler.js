/**
 * 与服务器通信入口文件，只允许被渲染进程require一次
 */

"use strict";
const ProxyServer_pb = require("../pb/ProxyServer_pb");
const tcp_client = require('../../lib/tcp_client');
const util = require('../../lib/util');
let viewEmitters = {};

// 与服务器建立连接
tcp_client.connect(onGuiMsg, '192.168.104.190', 8801);

/**
 * GUI消息处理入口
 * @param msgId 消息id
 * @param pb 原始pb消息
 */
function onGuiMsg(msgId, pb) {
    console.log(Date(), 'onMsg', msgId, pb);
    switch (msgId) {
        case ProxyServer_pb.PSMessageId.PSMESSAGEID_PCLOGIN:
            emitUIEvent(msgId, ProxyServer_pb.MSG_PCLogin.deserializeBinary(pb));
            break;
        case ProxyServer_pb.PSMessageId.PSMESSAGEID_PCADDAPPUSER:
            emitUIEvent(msgId, ProxyServer_pb.MSG_PCAddAppUser.deserializeBinary(pb));
            break;
        case ProxyServer_pb.PSMessageId.PSMESSAGEID_PCREMOVEAPPUSER:
            emitUIEvent(msgId, ProxyServer_pb.MSG_PCRemoveAppUser.deserializeBinary(pb));
            break;
        case ProxyServer_pb.PSMessageId.PSMESSAGEID_PCCHANGEAPPUSER:
            emitUIEvent(msgId, ProxyServer_pb.MSG_PCChangeAppUser.deserializeBinary(pb));
            break;
        case ProxyServer_pb.PSMessageId.PSMESSAGEID_PCLOADAPPUSERS:
            emitUIEvent(msgId, ProxyServer_pb.MSG_PCLoadAppUsers.deserializeBinary(pb));
            break;
        default:
            break;
    }
}

function onPlatformMsg(msg) {
    console.log(Date(), 'onPlatformMsg', msg);
}

/**
 * 注册界面发射器
 * @param msgId 消息id
 * @param viewEmitter 发射器
 */
function registerViewEmitter(msgId, viewEmitter) {
    viewEmitters[msgId] = viewEmitter;
}

/**
 * 将收到服务端的消息发送到对应界面
 * @param msgId 消息id，用于区分具体属于哪条消息
 * @param msg   反序列化后的pb消息
 */
function emitUIEvent(msgId, msg) {
    if (viewEmitters[msgId] !== undefined) {
        viewEmitters[msgId].emit('ui_event', msgId, msg);
    }
}

/**
 * 发送自定义消息到服务代理
 * @param msgId 消息id
 * @param sendMsg pb结构体
 * @param user_sign 用户认证信息
 */
function sendGuiMsg(msgId, sendMsg, user_sign) {
    tcp_client.send(util.packGuiMsg(msgId, sendMsg, user_sign));
}

/**
 * 发送平台消息，跟主站平台消息格式完全一致
 * @param buffer
 */
function sendPlatformMsg(buffer) {
    tcp_client.send(buffer);
}

module.exports = {
    registerViewEmitter: registerViewEmitter,
    sendGuiMsg: sendGuiMsg,
    sendPlatformMsg: sendPlatformMsg,
};
