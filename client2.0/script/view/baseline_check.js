'use strict';

const ProxyServer_pb = require('../pb/ProxyServer_pb');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const util = require('../../lib/util');
const ui_util = require('./ui_util');
const tcp_client = require('../../lib/tcp_client');
const config = require("../../config.json");
const device_codec = require('../platform_codec/device_codec');
const baseline_codec = require('../platform_codec/baseline_codec');
// 相关变量定义
const user_sign = $("body").attr("user_sign_info");
const user_name = $("body").attr("user_name_info");
console.log("user_sign:", user_sign + user_name);

let validateDlgId,validateDlgIp;
//定时刷新页面.
let timer=null;
loadBaselinesGenerator();
// timer=setInterval(function(){
  // console.log('baseline_check')
//   loadBaselinesGenerator();
// }, 10000);
// ui_util.stopTimeOut(timer)

// 点击刷新基线核查状态
$('#add_check').on('click', function () {
  $('#validateUserDlg').modal('show');
  $('#validateUserForm').get(0).reset()
  $('#opraType').val(1)
})

// 点击创建新基线核查
$('#refresh').on('click', function () {
  loadBaselinesGenerator();
})

// 启动单行设备基线核查
$('#start_device_btn').on('click', function () {
  $('#addCheckDlg').modal('hide');
  startBaseline(util.genTaskId(),$('#deviceIp').val());// 启动时针对每条设备生成新id
});

//查看基线核查状态&结果
$('#seeStateDlg').on('shown.bs.modal', function () {
  if($('#seeStateTitle').attr('wa-name')=='see_result'){
    getBaselineResult($('#baselineId').val(),$('#baselineIp').val());
  }else{
    // 考虑点击查看状态操作时.
    // getBaselineState($('#baselineId').val(),$('#baselineIp').val());
  }
})

// 确认取消&移除基线核查
$('#stop_btn').on('click', function () {
  if($('#stopTitle').attr('wa-name')=='_stop'){
    stopBaseline($('#baselineId2').val(),$('#baselineIp2').val());
  }else{
    removeBaselineGenerator($('#baselineId2').val());
  }
});

$('#validateuser_btn').on('click',function (e) {
  if ($("#validateuser_btn").hasClass("disabled")) {
    return;
  }
  $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  let username=user_name
  let psd=$('#validateUserDlg #password').val()
   validateDlgId=$('#opraId').val() ,validateDlgIp=$('#opraIp').val()
  // console.log(validateDlgId,validateDlgIp)
  validateUserGenerator(username,psd);
})

/**
 * 初始化基线表格&设备(状态上线)表格
 */
let baselineData = new Array();
let deviceData = new Array();

function loadBaselineTb() {
  $("#baselines-tb").jqGrid(
    {
      datatype: "local",
      height: "90%",
      autowidth: true,
      shrinkToFit: true,
      pager: '#baselines-pager',
      viewrecords: true,
      rowNum: 10,
      rowList: [5, 10, 20, 50, 100],
      recordtext: "{0} - {1}\u3000共 {2} 条",
      emptyrecords: "当前没有基线核查记录",
      loadtext: "读取中...",
      pgtext: "{0} 共 {1} 页",
      colNames: ['编号','核查状态','监控对象Ip','启动时间','取消时间','完成时间','操作'],
      colModel: [
        {name: 'taskid', index: 'taskid', sortable: false},
        {name: 'baselineState', index: 'baselineState', sortable: false,formatter:displayStates},
        {name: 'destIp', index: 'destIp', sortable: false},
        {name: 'startTime', index: 'startTime',},
        {name: 'stopTime', index: 'stopTime',},
        {name: 'completeTime', index: 'completeTime',},
        {name: 'operation', index: 'operation', sortable: false, formatter: displayButtons}
      ],
      sortable:true,
      sortname: 'startTime',
      sortorder:'desc',
    });

  // 清空和填充表格数据
  $("#baselines-tb").jqGrid('clearGridData');
  for (let i = 0; i <= baselineData.length; i++) {
    $("#baselines-tb").jqGrid('addRowData', i + 1, baselineData[i]);
  }
  // 数组数据分页.
  $('.ui-pg-table.ui-pager-table #first_baselines-pager span')
    .addClass('ace-icon fa fa-angle-double-left bigger-100')
  $('.ui-pg-table.ui-pager-table #prev_baselines-pager span')
    .addClass('ace-icon fa fa-angle-left bigger-100')
  $('.ui-pg-table.ui-pager-table #next_baselines-pager span')
    .addClass('ace-icon fa fa-angle-right bigger-100')
  $('.ui-pg-table.ui-pager-table #last_baselines-pager span')
    .addClass('ace-icon fa fa-angle-double-right bigger-100')
  const localData = {page: 1, total: 2, records: "2", rows: []};
  localData.rows = baselineData;
  localData.records = baselineData.length;
  localData.total = (baselineData.length % 2 == 0) ? (baselineData.length / 2) : (Math.floor(baselineData.length / 2) + 1);
  const reader = {
    root: function (obj) {return localData.rows;},
    page: function (obj) {return localData.page;},
    total: function (obj) {return localData.total;},
    records: function (obj) {return localData.records;},
    repeatitems: false
  };
  $("#baselines-tb").setGridParam({
    data: localData.rows,
    reader: reader
  }).trigger('reloadGrid');

  /**\
   * 创造行末尾按钮.
   */
  function displayButtons(cellvalue, options, rowObject) {
    let html="<div class='hidden-sm hidden-xs btn-group'>"
    let html2="<button class='btn btn-minier btn-success wa-mlr5' id='btn_see_result' " +
      "row-id='" + rowObject.taskid + "'" +
      "row-ip='" + rowObject.destIp + "'" +
      "onclick=\"$('#validateUserDlg').modal('show');\n" +
      "$('#validateUserForm').get(0).reset()\n" +
      "$('#opraType').val(2)\n" +
      "$('#opraId').val($(this).attr('row-id'))\n" +
      "$('#opraIp').val($(this).attr('row-ip'))\">" +
      "<i class='ace-icon fa fa-eye bigger-80'>&nbsp;查看结果</i>" +
      "</button>";
    let html3="<button class='btn btn-minier btn-success wa-mlr5 disabled' id='btn_see_result' " +
      "row-id='" + rowObject.taskid + "'" +
      "row-ip='" + rowObject.destIp + "'>" +
      "<i class='ace-icon fa fa-eye bigger-80'>&nbsp;查看结果</i>" +
      "</button>";
    if(cellvalue ==0){
      html +=html2+
        "<button class='btn btn-minier btn-inverse'" +
        "row-id='" + rowObject.taskid + "'" +
        "row-ip='" + rowObject.destIp + "'" +
        "onclick=\"$('#validateUserDlg').modal('show');\n" +
        "$('#validateUserForm').get(0).reset()\n" +
        "$('#opraType').val(3)\n" +
        "$('#opraId').val($(this).attr('row-id'))\n" +
        "$('#opraIp').val($(this).attr('row-ip'))\">" +
        "<i class='ace-icon fa fa-ban bigger-80'>&nbsp;取消</i>" +
        "</button>" +
        "</div>"
    }else if(cellvalue==1){
      html += html3+
        "<button class='btn btn-minier btn-danger'" +
        "row-id='" + rowObject.taskid + "'" +
        "row-ip='" + rowObject.destIp + "'" +
        "onclick=\"$('#validateUserDlg').modal('show');\n" +
        "$('#validateUserForm').get(0).reset()\n" +
        "$('#opraType').val(4)\n" +
        "$('#opraId').val($(this).attr('row-id'))\n" +
        "$('#opraIp').val($(this).attr('row-ip'))\">" +
        "<i class='ace-icon fa fa-trash bigger-80'>&nbsp;删除</i>" +
        "</button>" +
        "</div>"
    }else{
      html +=html2+
        "<button class='btn btn-minier btn-danger'" +
        "row-id='" + rowObject.taskid + "'" +
        "row-ip='" + rowObject.destIp + "'" +
        "onclick=\"$('#validateUserDlg').modal('show');\n" +
        "$('#validateUserForm').get(0).reset()\n" +
        "$('#opraType').val(4)\n" +
        "$('#opraId').val($(this).attr('row-id'))\n" +
        "$('#opraIp').val($(this).attr('row-ip'))\">" +
        "<i class='ace-icon fa fa-trash bigger-80'>&nbsp;删除</i>" +
        "</button>" +
        "</div>"
    }
    return html;
  }
  /**
   * 设置核查状态字段
   */
  function displayStates(cellvalue, options, rowObject) {
    if(cellvalue==0) return '正在执行中'
    else if(cellvalue==1) return '已取消'
    else if(cellvalue==2) return '启动失败'
    else if(cellvalue==3) return '启动成功'
  }

  jqgridColResize()
  tableResize();
}

function loadDeviceTb() {
  $("#device-tb").jqGrid(
    {
      datatype: "local",
      height: "90%",
      autowidth: true,
      shrinkToFit: true,
      pager: '#device-pager',
      viewrecords: true,
      sortname: 'id',
      sortorder: 'desc',
      rowNum: 10,
      rowList: [5, 10, 20, 50, 100],
      recordtext: "{0} - {1}\u3000共 {2} 条",
      emptyrecords: "当前没有在线服务器设备",
      loadtext: "读取中...",
      pgtext: "{0} 共 {1} 页",
      colNames: ['序号', '核查主机', 'Ip', 'MAC', '操作'],
      colModel: [
        {name: 'id', index: 'id',sorttype:'integer'},
        {name: 'deviceName', index: 'deviceName'},
        {name: 'deviceIp', index: 'deviceIp'},
        {name: 'mac', index: 'mac',},
        {name: 'operation', index: 'operation', sortable: false, formatter: displayButtons}
      ]
    });
  // 清空和填充表格数据
  $("#device-tb").jqGrid('clearGridData');
  for (let i = 0; i <= deviceData.length; i++) {
    $("#device-tb").jqGrid('addRowData', i + 1, deviceData[i]);
  }
  // 表格自适应modal窗口
  $(window).on('resize.jqGrid', function () {
    $("#device-tb").jqGrid('setGridWidth', $("#addCheckDlg .modal-content>.modal-body .col-xs-12").width());
  });
  const parent_column = $("#device-tb").closest('[class*="col-"]');
  $(document).on('settings.ace.jqGrid', function (ev, event_name, collapsed) {
    if (event_name == 'sidebar_collapsed' || event_name == 'main_container_fixed') {
      setTimeout(function () {
        $("#device-tb").jqGrid('setGridWidth', parent_column.width());
      }, 0);
    }
  })
  // 数组数据分页.
  $('.ui-pg-table.ui-pager-table #first_device-pager span')
    .addClass('ace-icon fa fa-angle-double-left bigger-100')
  $('.ui-pg-table.ui-pager-table #prev_device-pager span')
    .addClass('ace-icon fa fa-angle-left bigger-100')
  $('.ui-pg-table.ui-pager-table #next_device-pager span')
    .addClass('ace-icon fa fa-angle-right bigger-100')
  $('.ui-pg-table.ui-pager-table #last_device-pager span')
    .addClass('ace-icon fa fa-angle-double-right bigger-100')
  const localData = {page: 1, total: 2, records: "2", rows: []};
  localData.rows = deviceData;
  localData.records = deviceData.length;
  localData.total = (deviceData.length % 2 == 0) ? (deviceData.length / 2) : (Math.floor(deviceData.length / 2) + 1);
  const reader = {
    root: function (obj) {return localData.rows;},
    page: function (obj) {return localData.page;},
    total: function (obj) {return localData.total;},
    records: function (obj) {return localData.records;},
    repeatitems: false
  };
  $("#device-tb").setGridParam({
    data: localData.rows,
    reader: reader
  }).trigger('reloadGrid');

  /**\
   * 创造行末尾按钮操作方法.
   */
  function displayButtons(cellvalue, options, rowObject) {
    return "<div class='hidden-sm hidden-xs btn-group'>" +
      "<button class='btn btn-minier btn-success'" +
      // "onclick=\"removeDevice("+rowObject.id+");\">" +
      "row-id='" + rowObject.id + "'" +
      "row-ip='" + rowObject.deviceIp + "'" +
      "onclick=\"$('#confirmStartDlg').modal('show')\n" +
      "$('#deviceId').val($(this).attr('row-id'))\n" +
      "$('#deviceIp').val($(this).attr('row-ip'))\">" +
      "<i class='ace-icon fa fa-play-circle bigger-80'>&nbsp;启动</i>" +
      "</button>" +
      "</div>";
  }
}

/**
 * 重新调整jqgrid每列的宽度
 */
function jqgridColResize(){
  let td=$('#tdCompute')//获取计算实际列长度的容器
    ,tds//临时保存列
    ,arr=[];//用于保存最大的列宽
  //遍历每行获得每行中的最大列宽
  $('.ui-jqgrid-htable tr,.ui-jqgrid-btable tr:gt(0)').each(function(){
    $(this).find('td,th').each(function(idx){
      arr[idx]=Math.max(arr[idx]?arr[idx]:0,td.html($(this).text())[0].offsetWidth);
    })
  });
  //设置页头单元格宽度
  $('.ui-jqgrid-labels th').each(function(idx){this.style.width=arr[idx]+'px'});
  //设置内容表格中控制单元格宽度的单元格，在第一行
  $('.ui-jqgrid-btable tr:eq(0) td').each(function(idx){this.style.width=arr[idx]+'px'});
  // 设置操作栏固定宽度
  $('.ui-jqgrid .ui-jqgrid-htable th:last-child').css("width", "89");
  $('.ui-jqgrid-btable>tbody>tr>td:last-child').css("width","89");
}
/**
 * 表格自适应窗口
 */
function tableResize(){
  $(window).on('resize.jqGrid', function () {
    $("#baselines-tb").jqGrid('setGridWidth', $(".page-content").width());
    jqgridColResize();
  });
  const parent_column = $("#baselines-tb").closest('[class*="col-"]');
  $(document).on('settings.ace.jqGrid', function (ev, event_name, collapsed) {
    if (event_name == 'sidebar_collapsed' || event_name == 'main_container_fixed') {
      setTimeout(function () {
        $("#baselines-tb").jqGrid('setGridWidth', parent_column.width());
        jqgridColResize();
      }, 0);
    }
  })
}

/**
 * 获取初始化基线表格数据
 */
function loadBaselinesGenerator() {
  // 连接服务器
  tcp_client.connect(loadBaselinesHandler, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPLoadBaselines();
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADBASELINES, sendMsg, user_sign);
}

function loadBaselinesHandler(msgId, pb) {
  if (msgId !== ProxyServer_pb.PSMessageId.PSMESSAGEID_PCLOADBASELINES) {
    console.error('invalid msg', msgId);
    return;
  }
  const msg = ProxyServer_pb.MSG_PCLoadBaselines.deserializeBinary(pb);
  const baselines = msg.getBaselinesList();
  baselineData = [];
  for (const baseline of baselines) {
    // console.log('baseline', baseline.getTaskId(), baseline.getResult(), baseline.getStartTime(), baseline.getStopTime(), baseline.getCompleteTime(), baseline.getError(), baseline.getDestIp())
    let _state=0;
    if(baseline.getStopTime()==0&&baseline.getCompleteTime()==0) _state=0
    else if(baseline.getStopTime()>0) _state=1
    else if(baseline.getCompleteTime()>0){
      if(baseline.getError()>0) _state=2
      else _state=3
    }
    const baselineObj = {
      taskid: $.trim(baseline.getTaskId()),
      baselineState:_state,
      destIp: baseline.getDestIp(),
      startTime: baseline.getStartTime()==0?'-':util.getThisDateMs(baseline.getStartTime()),
      stopTime: baseline.getStopTime()==0?'-':util.getThisDateMs(baseline.getStopTime()),
      completeTime: baseline.getCompleteTime()==0?'-':util.getThisDateMs(baseline.getCompleteTime()),
      operation:_state
    }
    baselineData.push(baselineObj)
  }
  // console.log('baselineData',baselineData)
  loadBaselineTb();
}

/**
 * 发送验证密码参数
 * @param psd
 */
function validateUserGenerator(username,psd){
// 连接服务器
  tcp_client.connect(function(msgId, pb) {
    const msg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(pb);
    if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
      //ui_util.showFadeTip('验证用户通过!');
      if($('#opraType').val()==1){
        $('#addCheckDlg').modal('show');
        loadDeviceidsGenerator();
      }else if($('#opraType').val()==2){
        $('#seeStateDlg').modal('show')
        $('#seeStateTitle').attr('wa-name','see_result')
        $('#seeStateTitle').text('查看该基线核查结果')
        console.log(validateDlgId,validateDlgIp)
        $('#baselineId').val(validateDlgId)
        $('#baselineIp').val(validateDlgIp)
      }else if($('#opraType').val()==3){
        $('#confirmStopDlg').modal('show')
        $('#stopTitle').attr('wa-name','_stop')
        $('#stopTitle').text('确认取消')
        $('#confirmStopDlg .modal-body p').text('确定取消该设备基线核查？')
        $('#baselineId2').val(validateDlgId)
        $('#baselineIp2').val(validateDlgIp)
      }else if($('#opraType').val()==4){
        $('#confirmStopDlg').modal('show')
        $('#stopTitle').attr('wa-name','_remove')
        $('#stopTitle').text('确认删除')
        $('#confirmStopDlg .modal-body p').text('确定删除该设备基线核查？')
        $('#baselineId2').val(validateDlgId)
      }

    } else {
      ui_util.getErrorTips(msg.getErrorCode(), '验证用户身份')
    }
  }, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
  sendMsg.setUserName(username)
  sendMsg.setPassword(psd)
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg, user_sign);
}

/**
 * 获取初始化设备(状态上线)表格数据
 */
function loadDeviceidsGenerator() {
  // 连接服务器
  tcp_client.connect(loadDeviceidsHandler, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPQueryOnlineDeviceIds();
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPQUERYONLINEDEVICEIDS, sendMsg, user_sign);
}
function loadDeviceidsHandler(msgId, pb) {
  if (msgId !== ProxyServer_pb.PSMessageId.PSMESSAGEID_PCQUERYONLINEDEVICEIDS) {
    console.error('invalid msg', msgId);
    return;
  }
  const msg = ProxyServer_pb.MSG_PCQueryOnlineDeviceIds.deserializeBinary(pb);
  const deviceids = msg.getDeviceIdsList();
  // console.log("deviceids",deviceids)

  loadDevicesGenerator(deviceids);
}

function loadDevicesGenerator(deviceids) {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      const devices = device_codec.unpackLoadDevice(dp_packet.content);
      deviceData = [];
      for (let i = 0; i < devices.length; i++) {
        if($.inArray(devices[i].getId(),deviceids)!==-1&&devices[i].getDeviceType()=="SVR"){
          const deviceObj = {
            id: devices[i].getId(),
            deviceName: devices[i].getDeviceName(),
            deviceIp: util.int2ip(devices[i].getIp1()),
            mac: util.str2mac(devices[i].getMac1())
          }
          deviceData.push(deviceObj)
        }
      }
      loadDeviceTb();
    }
    // 消息处理完成关闭连接
    tcp_client.close();
  }, config.host, config.port);

  // 发送消息
  tcp_client.send(device_codec.packLoadDevice());
}

/**
 * 启动基线核查
 * @param task_id
 * @param dest_ip
 */
function startBaseline(task_id, dest_ip) {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      ui_util.showFadeTip('启动基线核查成功!');
    } else {
      ui_util.getErrorTips(dp_packet.return_value,'启动基线核查')
    }
    // 消息处理完成关闭连接
    tcp_client.close();
    loadBaselinesGenerator();
  }, config.host, config.port);

  // 发送消息
  tcp_client.send(baseline_codec.packStartBaseline(task_id, util.ip2int(dest_ip)));
}

/**
 * 查看基线核查结果
 */
function getBaselineResult(task_id,dest_ip) {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      const baseline = baseline_codec.unpackBaselineGetResult(dp_packet.content);
      // console.log('baseline', baseline.getTaskId(), baseline.getResult());
      const _result=baseline.getResult()==""?"暂无结果":baseline.getResult();
      $('#seeStateDlg .modal-body>#baseStateCont').text(_result)
    }
    // 消息处理完成关闭连接
    tcp_client.close();
  }, config.host, config.port);

  // 发送消息
  tcp_client.send(baseline_codec.packBaselineGetResult(task_id, util.ip2int(dest_ip)));
}

/**
 * 取消基线核查
 * @param task_id
 * @param dest_ip
 */
function stopBaseline(task_id,dest_ip) {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      // const baseline = baseline_codec.unpackStopBaseline(dp_packet.content);
      // console.log('baseline', baseline.getTaskId(), baseline.getResult());
      ui_util.showFadeTip('取消基线核查成功!');
    } else {
      ui_util.getErrorTips(dp_packet.return_value,'取消基线核查')
    }
    // 消息处理完成关闭连接
    tcp_client.close();
    loadBaselinesGenerator();
  }, config.host, config.port);

  // 发送消息
  tcp_client.send(baseline_codec.packStopBaseline(task_id, util.ip2int(dest_ip)));
}

/**
 * 移除已完成或者已取消的基线核查
 * @param taskId
 */
function removeBaselineGenerator(taskId) {
    // 连接服务器
    tcp_client.connect(removeBaselineHandler, config.host, config.port);

    // 发送消息
    let sendMsg = new ProxyServer_pb.MSG_CPRemoveBaseline();
    sendMsg.setTaskId(taskId);
    tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPREMOVEBASELINE, sendMsg, user_sign);
}

function removeBaselineHandler(msgId, pb) {
    if (msgId !== ProxyServer_pb.PSMessageId.PSMESSAGEID_PCREMOVEBASELINE) {
        console.error('invalid msg', msgId);
        return;
    }
  //   const msg = ProxyServer_pb.MSG_PCRemoveBaseline.deserializeBinary(pb);
  //   console.log('removeBaselineHandler', msg.getTaskId(), msg.getResult());
  ui_util.showFadeTip('删除基线核查成功!');
    loadBaselinesGenerator()
}
