"use strict";

const {dialog} = require('electron');
const ProxyServer_pb = require('../pb/ProxyServer_pb');
const PublicDefine_pb = require('../pb/PublicDefine_pb');

/**
 * 根据返回值分类错误提示信息
 * @param value
 * @param text
 */
function getErrorTips(value, text, subText = '') {
    let showText = text
    switch (value) {
        // 1
        case PublicDefine_pb.ErrorCode.ERRORCODE_INNERERROR:
            showText = showText + '失败，内部错误！'
            break;
        // 2
        case PublicDefine_pb.ErrorCode.ERRORCODE_UNSUPPORTEDTYPE:
            showText = showText + '失败，不支持的类型！';
            break;
        // 3
        case PublicDefine_pb.ErrorCode.ERRORCODE_WRONGPARAM:
            showText = showText + '失败，参数错误！';
            break;
        // 4
        case PublicDefine_pb.ErrorCode.ERRORCODE_OBJECTNOTEXIST:
            showText = showText + '失败，对象不存在！';
            break;
        // 5
        case PublicDefine_pb.ErrorCode.ERRORCODE_OBJECTUNREACHABLE:
            showText = showText + '失败，对象不可达！';
            break;
        // 6
        case PublicDefine_pb.ErrorCode.ERRORCODE_INVALIDCONTENTFORMAT:
            showText = showText + '失败，内容格式错误（报文的格式错误）！';
            break;
        // 7
        case PublicDefine_pb.ErrorCode.ERRORCODE_TIMESTAMPMISMATCH:
            showText = showText + '失败，时间戳比较失败！';
            break;
        // 8
        case PublicDefine_pb.ErrorCode.ERRORCODE_INVALIDSIGN:
            showText = showText + '失败，验证签名出错（报文的签名错误）！';
            break;
        // 9
        case PublicDefine_pb.ErrorCode.ERRORCODE_CANNOT_EXEC_CMD:
            showText = showText + '失败，操作执行失败（其他不是此表所列问题导致的失败）！';
            break;
        // 10
        case PublicDefine_pb.ErrorCode.ERRORCODE_DATAVERIFYFAILED:
            showText = showText + '失败，数据校验出错（设置、升级、恢复、更新的数据校验出错）！';
            break;
        // 11
        case PublicDefine_pb.ErrorCode.ERRORCODE_INVALIDCERTFORMAT:
            showText = showText + '失败，错误的证书格式（不是一个正确的 PEM/DER 证书格式）（针对各级 CA 根证书、平台证书）！';
            break;
        // 12
        case PublicDefine_pb.ErrorCode.ERRORCODE_CAERROR:
            showText = showText + '失败，CA 根证书错误（仅针对各级 CA 根证书）！';
            break;
        // 13
        case PublicDefine_pb.ErrorCode.ERRORCODE_NOCACHAIN:
            showText = showText + '失败，没有可用的 CA 根证书链（针对各级 CA 根证书、平台证书）！';
            break;
        // 14
        case PublicDefine_pb.ErrorCode.ERRORCODE_CERTVALIDATEERROR:
            showText = showText + '失败，证书验证未通过（针对各级 CA 根证书、平台证书）！';
            break;
        // 15
        case PublicDefine_pb.ErrorCode.ERRORCODE_DEVICENOAUTHORITY:
            showText = showText + '失败，装置无注册权限！';
            break;
        // 16
        case PublicDefine_pb.ErrorCode.ERRORCODE_ASSETNOTEXIST:
            showText = showText + '失败，待注册资产不存在！';
            break;
        // 17
        case PublicDefine_pb.ErrorCode.ERRORCODE_PACKETCONTENTNOTENOUGH:
            showText = showText + '失败，插件所需权限冲突！';
            break;
        case PublicDefine_pb.ErrorCode.ERRORCODE_INVALIDUSER:
            showText = showText + '失败，用户不存在！';
            break;
        case PublicDefine_pb.ErrorCode.ERRORCODE_BASELINEISRUNNING:
            showText = showText + '失败，基线核查正在运行中，不允许删除！';
            break;
        case PublicDefine_pb.ErrorCode.ERRORCODE_LOGINTRIEDTIMESLIMIT:
            showText = showText + '失败，登录失败次数限制！';
            break;
        case PublicDefine_pb.ErrorCode.ERRORCODE_VERSIONMATCHING:
            showText = showText + '失败，版本匹配中！';
            break;
        case PublicDefine_pb.ErrorCode.ERRORCODE_VERSIONCONFIGUPDATING:
            showText = showText + '失败，版本配置更新中！';
            break;
        case PublicDefine_pb.ErrorCode.ERRORCODE_LEAKSCANISRUNNING:
            showText = showText + '失败，漏洞扫描正在运行中，不允许删除！';
            break;
        case PublicDefine_pb.ErrorCode.ERRORCODE_VERSIONVERIFYISRUNNING:
            showText = showText + '失败，版本校验正在运行中，不允许删除！';
            break;
        case PublicDefine_pb.ErrorCode.ERRORCODE_PASSWORDEXPIRED:
            showText = showText + '失败，密码已过期！';
            break;
        case PublicDefine_pb.ErrorCode.ERRORCODE_NEEDUPDATEPASSWORD:
            showText = showText + '首次登录，需要修改密码！';
            break;
        case PublicDefine_pb.ErrorCode.ERRORCODE_PASSWORDCONTAINUSERNAME:
            showText = showText + '失败，密码包含用户名！';
            break;
        case PublicDefine_pb.ErrorCode.ERRORCODE_PASSWORDREPEATED:
            showText = showText + '失败，与之前的密码重复了！';
            break;
        case PublicDefine_pb.ErrorCode.ERRORCODE_INVALIDLOGINIP:
            showText = showText + '失败，非法登录Ip！';
            break;
        case PublicDefine_pb.ErrorCode.ERRORCODE_UKEYNOTFOUND:
            showText = showText + '失败，未插入UKEY或未勾选UKEY！';
            break;
        case PublicDefine_pb.ErrorCode.ERRORCODE_UKEYMISMATCH:
            showText = showText + '失败，UKey验证错误！';
            break;
        case PublicDefine_pb.ErrorCode.ERRORCODE_INVALIDPASSWORD:
            showText = showText + '失败，密码或者UKey错误！';
            break;
        case PublicDefine_pb.ErrorCode.ERRORCODE_USEREXIST:
            showText = showText + '失败，用户名已存在(包括已删除的用户名)！';
            break;
        case PublicDefine_pb.ErrorCode.ERRORCODE_UKEYEXIST:
            showText = showText + '失败，UKEY已使用！';
            break;
        default:
            showText = showText + '失败，错误码：' + value;
            break;
    }
    if (subText) showText = showText + ' ' + subText;
    showFadeTip(showText)
}

/**
 * 显示淡入淡出的提示框，要求每个html内都有个id为alertDanger的div
 * @param content
 * @param timeout 淡出时间，毫秒
 */
function showFadeTip(content, timeout = 3000) {
    let tip = $("#alertDanger");
    let _top = document.documentElement.scrollTop;
    if (tip.length === 0) {
        let $tip = $(`
        <div id="alertDanger" class="alert alert-danger"
             style="position:absolute;top:50%;left:30%;width:500px;display:none;z-index:99999!important">
          <strong></strong>
        </div>
        `);
        // $('#alertDanger').css('top',_top)
        $('body').append($tip);
        tip = $tip;
    }

    tip.find("strong").html("");
    tip.find("strong").html(content);
    tip.fadeIn();
    if (timeout > 0) {
        setTimeout(function () {
            tip.fadeOut();
        }, timeout);
    }
}

/**
 * 登录失败展示的滞留提示框，需提供一个容器id.
 * @param content 提示内容
 * @param node 容器id
 */
function showRetentionTip(content, node) {
    let tip = $("#retentionDanger");
    let _top = document.documentElement.scrollTop;
    if (tip.length === 0) {
        let $tip = $(`
       <div class="alert alert-danger" id="retentionDanger" style="padding: 3px 15px;font-size: 12px;margin-bottom: 1px;">
				<button type="button" class="close" data-dismiss="alert">
						<i class="ace-icon fa fa-times"></i>
				</button>
<strong><i class="ace-icon fa fa-exclamation-circle"></i><span></span></strong><br>
				</div>
        `);
        $('#'+node).prepend($tip);
        tip = $tip;
    }
    tip.find("strong").find("span").html("");
    tip.find("strong").find("span").html(content);
    $("#retentionDanger [data-dismiss='alert']").on("click",function () {
        $("#retentionDanger").remove()
    })
}

/**
 * 获取url中的参数值，例如：admin_index.html?user_sign=123
 * @param key 参数名称
 * @returns 参数值
 */
function getUrlParam(key) {
    let reg = new RegExp("(^|&)" + key + "=([^&]*)(&|$)");
    let r = window.location.search.substr(1).match(reg);
    if (r != null) {
        return unescape(r[2]);
    }
    return "";
}

/**
 * 切换左侧导航时触发跳出timeout
 * @param timer
 */
function stopTimeOut(timer) {
    $(".submenu>li").on('click', function () {
        clearTimeout(timer)
    })
    $(".nav.nav-list >li.li_fst_class").on('click', function () {
        clearTimeout(timer)
    })
}

function messageBox(browserWindow, title, content) {
    const options = {
        type: 'warning',
        title: title,
        message: content,
        buttons: ['确定']
    };
    dialog.showMessageBoxSync(browserWindow, options);// dialog.showMessageBoxSync(browserWindow, options, callback);
}

/**
 * 显示loading动画
 * @param appendDiv 被附加的div
 * @param timeout loading动画消失时间，秒，默认10秒
 */
let g_loading_timer = null;

function showLoading(timeout = 10) {
    $('.parent_content').busyLoad("show",{
        background: "rgba(0, 0, 0, 0.59)",
        fontawesome: "fa fa-spinner fa-spin fa-3x fa-fw",
        text:"正在应用参数"
    })
    if (timeout > 0) {
        g_loading_timer = setTimeout(function () {
            $('.parent_content').busyLoad("hide");
        }, timeout * 1000);
    }
}

module.exports = {
    showFadeTip: showFadeTip,
    showRetentionTip: showRetentionTip,
    getUrlParam: getUrlParam,
    getErrorTips: getErrorTips,
    stopTimeOut: stopTimeOut,
    showLoading:showLoading,
    messageBox
};
