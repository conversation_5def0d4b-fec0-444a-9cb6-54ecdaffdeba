'use strict';

const ProxyServer_pb = require('../pb/ProxyServer_pb');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const util = require('../../lib/util');
const ui_util = require('./ui_util');
const tcp_client = require('../../lib/tcp_client');
const config = require("../../config.json");
const device_codec = require('../platform_codec/device_codec');
const monitored_object_codec = require('../platform_codec/monitored_object_codec');

// 相关变量定义
const user_sign = $("body").attr("user_sign_info");
const user_name = $("body").attr("user_name_info");
console.log("user_sign:", user_sign + user_name);

let validateDlgId,validateDlgIp,validateDlgName,validateDlgType;
$('#validateuser_btn').on('click',function (e) {
  if ($("#validateuser_btn").hasClass("disabled")) {
    return;
  }
  let username=user_name;
  let psd=$('#validateUserDlg #password').val();
  validateDlgId=$('#opraId').val()
  validateDlgIp=$('#opraIp').val()
    validateDlgName=$('#opraName').val()
  validateDlgType=$('#opraTp').val()
  $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  validateUserGenerator(username,psd);
})

$("#seeDeviceDlg").on("shown.bs.modal",function () {
  $('#sub_container textarea').text()
  $('#sub_container textarea').addClass('hidden')
  $('#sub_container input').addClass('hidden')
  $('#sub_container textarea').removeClass('hidden')
    .attr('placeholder','请输入格式为\'tcp/udp(协议号),0.0.0.0~***************(ip),0~65535(端口号)\',可多条,按回车换行,例如:tcp,0.0.0.0,0')
  loadMonitoredObjParam($('#see_device_id').val(),$('#see_device_ip').val(),"1")
})
// 监控对象参数选中事件
$('#sub_type').change(function () {
  let options_val = $("#sub_type option:selected").val();
  $('#sub_container textarea').text()
  $('#sub_container input').val()
  $('#sub_container textarea').addClass('hidden')
  $('#sub_container input').addClass('hidden')
  if(options_val=="1"){
    $('#sub_container textarea').removeClass('hidden')
      .attr('placeholder','请输入格式为\'tcp/udp(协议号),0.0.0.0~***************(ip),0~65535(端口号)\',可多条,按回车换行,例如:tcp,0.0.0.0,0')
     loadMonitoredObjParam($('#see_device_id').val(),$('#see_device_ip').val(),options_val)
    $('#sub_type').attr("disabled","disabled")
  }else if(options_val=="2"){
    $('#sub_container textarea').removeClass('hidden')
      .attr('placeholder','请输入格式为\'0~65535(端口号),服务名称\',可多条,按回车换行,例如:0,name1')
     loadMonitoredObjParam($('#see_device_id').val(),$('#see_device_ip').val(),options_val)
    $('#sub_type').attr("disabled","disabled")
  }else if(options_val=="3"){
    $('#sub_container textarea').removeClass('hidden')
      .attr('placeholder','请输入格式为\'文件/目录的路径\',可多条,按回车换行,例如:/root/1.sh')
     loadMonitoredObjParam($('#see_device_id').val(),$('#see_device_ip').val(),options_val)
    $('#sub_type').attr("disabled","disabled")
  }else if(options_val=="4"||options_val=="5"){
    $('#sub_container input').removeClass('hidden')
      .attr('placeholder','请输入>0整数,如\'60\',单位秒')
     loadMonitoredObjParam($('#see_device_id').val(),$('#see_device_ip').val(),options_val)
    $('#sub_type').attr("disabled","disabled")
  }else if(options_val=="6"){
    $('#sub_container textarea').removeClass('hidden')
      .attr('placeholder','请输入格式为\'危险命令\',可多条,按回车换行')
     loadMonitoredObjParam($('#see_device_id').val(),$('#see_device_ip').val(),options_val)
    $('#sub_type').attr("disabled","disabled")
  }
})
// 点击设置监控对象参数
$('#set_sub_type').on('click',function () {
  if ($("#set_sub_type").hasClass("disabled")) {
    return false;
  }
  if($("#sub_container .sub_val").not(".hidden").val() ==""){
    ui_util.showFadeTip("参数值不能为空!")
    return false;
  }
  let options_val = $("#sub_type option:selected").val();
  let sub_val=$("#sub_container .sub_val").not(".hidden").val();
  // console.log(sub_val)
  setMonitoredObjParam($('#see_device_id').val(),$('#see_device_ip').val(),options_val,sub_val)
})

/**
 * 初始化刷新表格.
 */
let deviceData = new Array();
// 设映射，key：设备id，value：设备信息，MSG_Device
// let deviceMap = new Map();

function loadDeviceTb() {
  $("#device-tb").jqGrid(
    {
      datatype: "local",
      height: "90%",
      autowidth: true,
      shrinkToFit: true,
      pager: '#device-pager',
      viewrecords: true,
      sortname: 'id',
      sortorder: 'desc',
      rowNum: 10,
      rowList: [5, 10, 20, 50, 100],
      recordtext: "{0} - {1}\u3000共 {2} 条",
      emptyrecords: "当前没有在线服务器或网络安全监测装置",
      loadtext: "读取中...",
      pgtext: "{0} 共 {1} 页",
      colNames: ['序号', '设备名称', '设备Ip', '设备Ip2', 'MAC地址', 'MAC地址2', '设备类型','厂商', '序列号', '版本', '操作'],
      colModel: [
        {name: 'id', index: 'id',sorttype:'integer'},
        {name: 'deviceName', index: 'deviceName'},
        {name: 'deviceIp', index: 'deviceIp'},
        {name: 'deviceIp2', index: 'deviceIp2', hidden: true},
        {name: 'mac', index: 'mac',},
        {name: 'mac2', index: 'mac2', hidden: true},
        {name: 'deviceType', index: 'deviceType', formatter: transformType},
        {name: 'manufacturer', index: 'manufacturer', hidden: true},
        {name: 'sequence', index: 'sequence', hidden: true},
        {name: 'sysVersion', index: 'sysVersion', hidden: true},
        {name: 'operation', index: 'operation', sortable: false, formatter: displayButtons}
      ]
    });

  // 清空和填充表格数据
  $("#device-tb").jqGrid('clearGridData');
  for (let i = 0; i <= deviceData.length; i++) {
    $("#device-tb").jqGrid('addRowData', i + 1, deviceData[i]);
  }
  // 数组数据分页.本地数据分页.
  $('.ui-pg-table.ui-pager-table #first_device-pager span')
    .addClass('ace-icon fa fa-angle-double-left bigger-100')
  $('.ui-pg-table.ui-pager-table #prev_device-pager span')
    .addClass('ace-icon fa fa-angle-left bigger-100')
  $('.ui-pg-table.ui-pager-table #next_device-pager span')
    .addClass('ace-icon fa fa-angle-right bigger-100')
  $('.ui-pg-table.ui-pager-table #last_device-pager span')
    .addClass('ace-icon fa fa-angle-double-right bigger-100')
  const localData = {page: 1, total: 2, records: "2", rows: []};
  localData.rows = deviceData;
  localData.records = deviceData.length;
  localData.total = (deviceData.length % 2 == 0) ? (deviceData.length / 2) : (Math.floor(deviceData.length / 2) + 1);
  const reader = {
    root: function (obj) {return localData.rows;},
    page: function (obj) {return localData.page;},
    total: function (obj) {return localData.total;},
    records: function (obj) {return localData.records;},
    repeatitems: false
  };
  $("#device-tb").setGridParam({
    data: localData.rows,
    reader: reader
  }).trigger('reloadGrid');
  /**\
   * 创造行末尾按钮点击方法.
   */
  function displayButtons(cellvalue, options, rowObject) {
    return "<div class='hidden-sm hidden-xs btn-group'>" +
      "<button class='btn btn-minier btn-success wa-mlr5'" +
      "row-id='" + rowObject.id + "'" +
      "row-ip='" + rowObject.deviceIp + "'" +
      "row-name='" + rowObject.deviceName + "'" +
      "row-type='" + rowObject.deviceType + "'" +
      "onclick=\"$('#validateUserDlg').modal('show');\n" +
      "$('#validateUserForm').get(0).reset()\n" +
      "$('#opraType').val(1)\n" +
      "$('#opraId').val($(this).attr('row-id'))\n" +
      "$('#opraIp').val($(this).attr('row-ip'))\n" +
      "$('#opraName').val($(this).attr('row-name'))\n" +
      "$('#opraTp').val($(this).attr('row-type'))\">" +
      "<i class='ace-icon fa fa-eye bigger-80'>&nbsp;查看</i>" +
      "</button>" +
      "<button class='btn btn-minier btn-inverse wa-mlr5'" +
      "row-id='" + rowObject.id + "'" +
      "row-ip='" + rowObject.deviceIp + "'" +
      "row-name='" + rowObject.deviceName + "'" +
      "row-type='" + rowObject.deviceType + "'" +
      "onclick=\"$('#validateUserDlg').modal('show');\n" +
      "$('#validateUserForm').get(0).reset()\n" +
      "$('#opraType').val(2)\n" +
      "$('#opraId').val($(this).attr('row-id'))\n" +
      "$('#opraIp').val($(this).attr('row-ip'))\n" +
      "$('#opraName').val($(this).attr('row-name'))\n" +
      "$('#opraTp').val($(this).attr('row-type'))\">" +
      "<i class='ace-icon fa fa-cog bigger-80'>&nbsp;设置</i>" +
      "</button>" +
      "</div>";
  }
  /**
   * 设备类型value转换文本
   */
  function transformType(cellvalue, options, rowObject) {
    if (cellvalue == "FW") return "防火墙";
    else if (cellvalue == "FID") return "横向正向隔离装置";
    else if (cellvalue == "BID") return "横向反向隔离装置";
    else if (cellvalue == "SVR") return "服务器";
    else if (cellvalue == "SW") return "交换机";
    else if (cellvalue == "DCD") return "网络安全监测装置";
    else return "";
  }
  jqgridColResize();
  tableResize();
}

/**
 * 重新调整jqgrid每列的宽度
 */
function jqgridColResize(){
  let td=$('#tdCompute')//获取计算实际列长度的容器
    ,tds//临时保存列
    ,arr=[];//用于保存最大的列宽
  //遍历每行获得每行中的最大列宽
  $('.ui-jqgrid-htable tr,.ui-jqgrid-btable tr:gt(0)').each(function(){
    $(this).find('td,th').each(function(idx){
      arr[idx]=Math.max(arr[idx]?arr[idx]:0,td.html($(this).text())[0].offsetWidth);
    })
  });
  //设置页头单元格宽度
  $('.ui-jqgrid-labels th').each(function(idx){this.style.width=arr[idx]+'px'});
  //设置内容表格中控制单元格宽度的单元格，在第一行
  $('.ui-jqgrid-btable tr:eq(0) td').each(function(idx){this.style.width=arr[idx]+'px'});
  // 设置操作栏固定宽度
  $('.ui-jqgrid .ui-jqgrid-htable th:last-child').css("width", "80");
  $('.ui-jqgrid-btable>tbody>tr>td:last-child').css("width","80");
}
/**
 * 表格自适应窗口
 */
function tableResize() {
  $(window).on('resize.jqGrid', function () {
    $("#device-tb").jqGrid('setGridWidth', $(".page-content").width());
    jqgridColResize();
  });
  const parent_column = $("#device-tb").closest('[class*="col-"]');
  $(document).on('settings.ace.jqGrid', function (ev, event_name, collapsed) {
    if (event_name == 'sidebar_collapsed' || event_name == 'main_container_fixed') {
      setTimeout(function () {
        $("#device-tb").jqGrid('setGridWidth', parent_column.width());
        jqgridColResize();
      }, 0);
    }
  })
}

/**
 * 初始化页面.填充表格数组数据.
 */
function loadDeviceidsGenerator() {
  // 连接服务器
  tcp_client.connect(loadDeviceidsHandler, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPQueryOnlineDeviceIds();
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPQUERYONLINEDEVICEIDS, sendMsg, user_sign);
}
function loadDeviceidsHandler(msgId, pb) {
  if (msgId !== ProxyServer_pb.PSMessageId.PSMESSAGEID_PCQUERYONLINEDEVICEIDS) {
    console.error('invalid msg', msgId);
    return;
  }
  const msg = ProxyServer_pb.MSG_PCQueryOnlineDeviceIds.deserializeBinary(pb);
  const deviceids = msg.getDeviceIdsList();
  // console.log("deviceids",deviceids)

  pageInit(deviceids);
}
function pageInit(deviceids) {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      const devices = device_codec.unpackLoadDevice(dp_packet.content);
      // console.log('device', devices[0].getDeviceName(),devices[0].getManufacturer(),
      //   devices[0].getSerialNo(),devices[0].getSysVersion(), devices[0].getReserved());
      deviceData = [];
      for (let i = 0; i < devices.length; i++) {
        if($.inArray(devices[i].getId(),deviceids)!==-1){
          if(devices[i].getDeviceType()=="SVR"||devices[i].getDeviceType()=="DCD"){
            const deviceObj = {
              id: devices[i].getId(),
              deviceName: devices[i].getDeviceName(),
              deviceIp: util.int2ip(devices[i].getIp1()),
              deviceIp2: util.int2ip(devices[i].getIp2()),
              mac: util.str2mac(devices[i].getMac1()),
              mac2: util.str2mac(devices[i].getMac2()),
              deviceType: devices[i].getDeviceType(),
              manufacturer: devices[i].getManufacturer(),
              sequence: devices[i].getSerialNo(),
              sysVersion: devices[i].getSysVersion(),
              snmpVersion: devices[i].getSnmpVersion(),
              snmpUsername: devices[i].getSnmpUsername(),
              snmpAuth: devices[i].getSnmpAuth(),
              snmpEncryp: devices[i].getSnmpEncrypt(),
              snmpReadCommunity: devices[i].getSnmpReadCommunity(),
              snmpWriteCommunity: devices[i].getSnmpWriteCommunity(),
            }
            deviceData.push(deviceObj);
            // deviceMap.set(devices[i].getId(), devices[i]);
          }
        }
      }
      // console.log('deviceData:', deviceMap)
      loadDeviceTb();
    }
    // 消息处理完成关闭连接
    tcp_client.close();
  }, config.host, config.port);

  // 发送消息
  tcp_client.send(device_codec.packLoadDevice());
}

loadDeviceidsGenerator();

/**
 * 发送验证密码参数
 * @param psd
 */
function validateUserGenerator(username,psd){
// 连接服务器
  tcp_client.connect(function(msgId, pb) {
    const msg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(pb);
    if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
      //ui_util.showFadeTip('验证用户通过!');
      if($('#opraType').val()==1){
        $('#seeDeviceDlg #seeDeviceForm').get(0).reset();
        $('#seeDeviceDlg #seeDeviceTitle').attr('wa-name','see_param')
        $('#seeDeviceDlg #seeDeviceTitle').text('查看监控对象参数')
        $('#seeDeviceDlg #see_device_id').val(validateDlgId)
        $('#seeDeviceDlg #see_device_ip').val(validateDlgIp)
        $('#seeDeviceDlg #device_name').val(validateDlgName)
        $('#seeDeviceDlg #device_type').val(validateDlgType)
        $('#sub_container').children('.form-control').attr('disabled','disabled')
        $('#set_sub_type').hide()
        $('#seeDeviceDlg').modal('show')
      }else if($('#opraType').val()==2){
        $('#seeDeviceDlg #seeDeviceForm').get(0).reset()
        $('#seeDeviceDlg #seeDeviceTitle').attr('wa-name','set_param')
        $('#seeDeviceDlg #seeDeviceTitle').text('设置监控对象参数')
        $('#seeDeviceDlg #see_device_id').val(validateDlgId)
        $('#seeDeviceDlg #see_device_ip').val(validateDlgIp)
        $('#seeDeviceDlg #device_name').val(validateDlgName)
        $('#seeDeviceDlg #device_type').val(validateDlgType)
        $('#sub_container').children('.form-control').removeAttr('disabled')
        $('#set_sub_type').show()
        $('#seeDeviceDlg').modal('show')
      }
    } else {
      ui_util.getErrorTips(msg.getErrorCode(), '验证用户身份')
    }
  }, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
  sendMsg.setUserName(username)
  sendMsg.setPassword(psd)
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg, user_sign);
}

/**
 * 发送查看该设备监控对象参数
 * @param task_id 设备id
 * @param dest_ip 设备ip
 * @param sub_type 监控对象参数类型
 */
function loadMonitoredObjParam(task_id,dest_ip,sub_type) {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    const text="查看监控对象参数"
    if (dp_packet.return_value === 0) {
      $("#sub_type").removeAttr("disabled");
      const monitored_object = monitored_object_codec.unpackLoadMonitoredObject(dp_packet.content);
      console.log('monitored_object', monitored_object.getContent());
      $('#sub_container .sub_val').not(".hidden").val(monitored_object.getContent())
    }else {
      ui_util.getErrorTips(dp_packet.return_value,text)
    }
    // 消息处理完成关闭连接
    tcp_client.close();
  }, config.host, config.port);

  // 发送消息
  if(sub_type=="1"){
    tcp_client.send(monitored_object_codec.packLoadMonitoredObject(task_id, util.ip2int(dest_ip),
      PublicDefine_pb.MonitoredObjectSubType.MONITORED_OBJECT_SUBTYPE_NETWORK_WHITE_LIST));
  }else if(sub_type=="2"){
    tcp_client.send(monitored_object_codec.packLoadMonitoredObject(task_id, util.ip2int(dest_ip),
    PublicDefine_pb.MonitoredObjectSubType.MONITORED_OBJECT_SUBTYPE_OPENED_PORT_LIST));
  }else if(sub_type=="3"){
    tcp_client.send(monitored_object_codec.packLoadMonitoredObject(task_id, util.ip2int(dest_ip),
    PublicDefine_pb.MonitoredObjectSubType.MONITORED_OBJECT_SUBTYPE_KEY_FILE_LIST));
  }else if(sub_type=="4"){
    tcp_client.send(monitored_object_codec.packLoadMonitoredObject(task_id, util.ip2int(dest_ip),
    PublicDefine_pb.MonitoredObjectSubType.MONITORED_OBJECT_SUBTYPE_CD_DETECT_CYCLE));
  }else if(sub_type=="5"){
    tcp_client.send(monitored_object_codec.packLoadMonitoredObject(task_id, util.ip2int(dest_ip),
    PublicDefine_pb.MonitoredObjectSubType.MONITORED_OBJECT_SUBTYPE_INVALID_PORT_DETECT_CYCLE));
  }else if(sub_type=="6"){
    tcp_client.send(monitored_object_codec.packLoadMonitoredObject(task_id, util.ip2int(dest_ip),
    PublicDefine_pb.MonitoredObjectSubType.MONITORED_OBJECT_SUBTYPE_DANGEROUS_CMD_LIST));
  }
}

/**
 * 发送设置该设备监控对象参数
 * @param task_id
 * @param dest_ip
 * @param sub_type
 */
function setMonitoredObjParam(task_id,dest_ip,sub_type,sub_val) {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      // const monitored_object = monitored_object_codec.unpackSetNetworkWhiteList(dp_packet.content);
      ui_util.showFadeTip('设置监控对象参数成功!');
    }else {
      ui_util.getErrorTips(dp_packet.return_value,'设置监控对象参数')
    }
    // 消息处理完成关闭连接
    tcp_client.close();
  }, config.host, config.port);

  // 发送消息
  if(sub_type=="1"){
    tcp_client.send(monitored_object_codec.packSetNetworkWhiteList(task_id, util.ip2int(dest_ip),sub_val));
  }else if(sub_type=="2"){
    tcp_client.send(monitored_object_codec.packSetOpenedPortList(task_id, util.ip2int(dest_ip),sub_val));
  }else if(sub_type=="3"){
    tcp_client.send(monitored_object_codec.packSetKeyFileList(task_id, util.ip2int(dest_ip),sub_val));
  }else if(sub_type=="4"){
    tcp_client.send(monitored_object_codec.packSetCdDetectCycle(task_id, util.ip2int(dest_ip),sub_val));
  }else if(sub_type=="5"){
    tcp_client.send(monitored_object_codec.packSetInvalidPortDetectCycle(task_id, util.ip2int(dest_ip),sub_val));
  }else if(sub_type=="6"){
    tcp_client.send(monitored_object_codec.packSetDangerousCMDList(task_id, util.ip2int(dest_ip),sub_val));
  }
}
