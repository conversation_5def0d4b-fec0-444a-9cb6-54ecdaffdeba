'use strict';

const ProxyServer_pb = require('../pb/ProxyServer_pb');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const util = require('../../lib/util');
const ui_util = require('./ui_util');
const tcp_client = require('../../lib/tcp_client');
const config = require("../../config.json");

// 相关变量定义
const user_sign = $("body").attr("user_sign_info");
const user_name = $("body").attr("user_name_info");
console.log("user_sign:", user_sign + user_name);

//定时刷新页面.
let timer=null;
pageInit();
timer=setInterval(function(){
  pageInit();
}, 5000);
ui_util.stopTimeOut(timer)

let $real_time_monitor = document.getElementById('realtimemonitor')
$real_time_monitor.style.width = (window.innerWidth - 138) + 'px';
let real_time_monitor = echarts.init($real_time_monitor,'shine');
// let $hour_alarm = document.getElementById('1houralarm')
// $hour_alarm.style.width = (window.innerWidth - 138) + 'px';
// let hour_alarm = echarts.init($hour_alarm);

let data=new Array(),data2=new Array(),dataAxis=new Array(), datay=new Array()
/**
 * 系统实时运行状态监控图
 */
function realTimeMonitor(data,data2){
  let dataAxis = ['CPU','物理内存','swap','磁盘'];
  let labelOption = {
    normal: {
      show: true, position: 'top', distance: 0, align: 'center', verticalAlign: 'bottom', rotate: 0, formatter: '{c}%', fontSize: 16,
      rich: {name: {textBorderColor: '#fff'}}
    }
  };
  let option = {
    title:{text:'系统实时运行状态监控', left: '5'},
    color: ['#0098d9', '#a0a0a0'],
    tooltip: {
      trigger: 'axis',
      axisPointer: {type: 'shadow'},
      formatter: '<h5>{b1}</h5>' +
        '<span style="display:inline-block;margin-right:5px;' +
        'width:10px;height:10px;background-color:#0098d9;border-radius:5px"></span>{a0}:{c0}%<br>'+
        '<span style="display:inline-block;margin-right:5px;' +
      'width:10px;height:10px;background-color:#a0a0a0;border-radius:5px"></span>{a1}:{c1}%<br>',
      backgroundColor:'rgba(255,255,255,.8)',
      borderRadius:0,
      // borderColor:'#e5323e',
      borderWidth:1,
      textStyle:{fontSize:13,fontFamily:'SimHei',color:'#333333'},
      },
    legend: {icon: 'circle', data: ['已用', '空闲'], bottom:'0'},
    calculable: true,
    grid: {x:39,x2:330,y:65,y2:80},
    xAxis: [{type: 'category', axisTick: {show: false}, data: dataAxis,boundaryGap: true}],
    yAxis: [{name:'百分比(%)',type: 'value',max:100}],
    series: [
      {name: '已用', type: 'bar', label: labelOption, data: data,barWidth: 35},
      {name: '空闲', type: 'bar',label: labelOption, data: data2,barWidth: 35}
    ]
  };
  real_time_monitor.clear();
  real_time_monitor.setOption(option);
  chartsResize($real_time_monitor,real_time_monitor)
}
/**
 * 最近1小时告警统计图
 */
function hourAlarm(dataAxis,data){
  let labelOption = {
    normal: {
      show: false, position: 'bottom', distance: 10, align: 'center', verticalAlign: 'bottom', rotate: 20, formatter: '{c}', fontSize: 16,
      rich: {name: {textBorderColor: '#fff'}}
    }
  };
  let option = {
    title:{text:'最近1小时告警事件趋势统计', left: '5'},
    color:'#0098d9',
    tooltip: {trigger: 'axis', axisPointer: {type: 'shadow'},},
    legend: {icon: 'circle',data: '告警事件',bottom:'0'},
    calculable: true,
    grid: {x:39,x2:330,y:65,y2:80},
    xAxis: [{type: 'category', axisTick: {show: false}, data: dataAxis,boundaryGap: true}],
    yAxis: [{name:'记录/条',type: 'value'}],
    series: [
      {name: '告警事件', type: 'line', label: labelOption, data: data}
    ]
  };
  // hour_alarm.clear();
  // hour_alarm.setOption(option);
  // chartsResize($hour_alarm,hour_alarm)
}
realTimeMonitor(data,data2);
// hourAlarm(dataAxis,datay);
/**
 * 图表自适应窗口宽度实现
 * @param $chart_val
 * @param chart_val
 */
function chartsResize($chart_val,chart_val) {
  window.addEventListener("resize", function () {
      $chart_val.style.width = window.innerWidth - 138 + 'px';
    chart_val.resize();
  });
}

/**
 * 初始化查看检测状态
 */
function pageInit() {
  tcp_client.connect(onMsg, config.host, config.port);
  loadGenerator();
}
function loadGenerator() {
  let sendMsg = new ProxyServer_pb.MSG_CPLoadRunStatus();
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADRUNSTATUS, sendMsg, user_sign);
}

/**
 * 消息处理函数
 * @param msgId 消息id
 * @param pb 原始pb字节
 */
function onMsg(msgId, pb) {
      const msg = ProxyServer_pb.MSG_PCLoadRunStatus.deserializeBinary(pb);
      // console.log('msg',msg)
      // console.log('msg',msg.getWarningCountMap())
   let cpu_usage=(msg.getCpuUsage()*100), memory_usage=(msg.getMemoryUsage()*100),
   swap_usage=(msg.getSwapUsage()*100), disk_usage=(msg.getDiskUsage()*100);
  let _data=[cpu_usage.toFixed(2),memory_usage.toFixed(2)
      ,swap_usage.toFixed(2),disk_usage.toFixed(2)],
    _data2=[(100-cpu_usage).toFixed(2),(100-memory_usage).toFixed(2)
      ,(100-swap_usage).toFixed(2),(100-disk_usage).toFixed(2)]
      let hourMap = msg.getWarningCountMap().toArray();
      console.log('hourMap',hourMap)
  // let _dataAxis=["2019/02/19 14:26:19", "2019/02/19 14:26:21", "2019/02/19 14:26:22", "2019/02/19 14:26:23", "2019/02/19 14:26:25", "2019/02/19 14:26:26", "2019/02/19 14:26:28", "2019/02/19 14:26:29"],
  let _dataAxis=new Array(),
    // _datay=[3, 5, 4, 4, 4, 5, 5, 3]
    _datay=new Array()
      for (let i = 0; i < hourMap.length; ++i) {
        _dataAxis.push(util.getThisDate(hourMap[i][0]))
_datay.push(hourMap[i][1])
      }
      console.log(_dataAxis,_datay)
  refreshData(_data,_data2,_dataAxis,_datay)
}

/**
 *  刷新图表数据
 * @param data
 */
function refreshData(data,data2,dataAxis,datay){
  if(!real_time_monitor){
    return;
  }
  //更新数据
  let option = real_time_monitor.getOption();
  option.series[0].data = data;
  option.series[1].data = data2;
  real_time_monitor.setOption(option);
  // let option2 = hour_alarm.getOption();
  // option2.xAxis[0].data = dataAxis;
  // option2.series[0].data = datay;
  // hour_alarm.setOption(option2);
}
