'use strict';

const {ipc<PERSON><PERSON><PERSON>} = require('electron');
const ProxyServer_pb = require('../pb/ProxyServer_pb');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const tcpInstance = require('../../lib/v2/tls_client.js');
const util = require('../../lib/util');
const ui_util = require('./ui_util');
// const tcp_client = require('../../lib/tcp_client');
const config = require("../../config.json");
// 相关变量定义
const user_sign = $("body").attr("user_sign_info");
const user_name = $("body").attr("user_name_info");
const user_id = $("body").attr("user_id_info");
console.log("user_sign:", user_sign + user_name+user_id);

// 解决dropdown-toggle失效问题.
$('a.dropdown-toggle').dropdown()

// 初始化页面表单-用户名
// $('#modifyPsdForm').validator();
$('#modifyPsdForm').get(0).reset();
$('#modifyPsdForm #app_user_id').val(user_id);
$('#modifyPsdForm #app_user_name').val(user_name);

// 返回首页
$('#isToHome').on('click', function (e) {
  // if($("body").attr("source")=="main"){
  //
  // }else if($("body").attr("source")=="login"){
  //   top.location = "./login.html";
  // }
})

//点击密码更新
$('#btn_modifypsd_save').on('click', function (e) {
  if ($("#btn_modifypsd_save").hasClass("disabled")) {
    return;
  }
  modifyPsdFormSave();
  e.preventDefault();//阻止bootstrap button点击自动刷新页面默认行为。
});

/**
 * 侧栏导航切换-含一二级菜单
 */
function initSidebarSwitch() {}
/**
 * 初始化进入第一个功能页
 */
function showFstMenu(){}

tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCCHANGEOWNPASSWORD, (pb) => {
  modifyPsdFormSaveHandler(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCCHANGEOWNPASSWORD, pb)
})

/**
 *
 * 发送密码更新参数
 */
function modifyPsdFormSave() {
  // 连接服务器
  // tcp_client.connect(modifyPsdFormSaveHandler, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPChangeOwnPassword();
  sendMsg.setAppUserId($('#app_user_id').val());
  sendMsg.setPassword($('#password').val());
  sendMsg.setChangeKey($('#ukeySwitch').val());
    if ($("#ukeySwitch").length > 0) {
        if ($('[name="switch-field-1"]:checked').length > 0) {
            sendMsg.setChangeKey(true);
        } else {
            sendMsg.setChangeKey(false);
        }
    }
  tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPCHANGEOWNPASSWORD, sendMsg).then();
  // tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPCHANGEOWNPASSWORD, sendMsg, user_sign);
}

function modifyPsdFormSaveHandler(msgId, pb) {
  if (msgId !== ProxyServer_pb.PSMessageId.PSMESSAGEID_PCCHANGEOWNPASSWORD) {
    console.error('invalid msg', msgId);
    return;
  }
  const msg = ProxyServer_pb.MSG_PCChangeOwnPassword.deserializeBinary(pb);
  const error_code = msg.getErrorCode();
  console.log('code', msg.getErrorCode())
  const text="密码更新"
  if (error_code ===  PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
    ui_util.showFadeTip('密码更新成功!请重新登录');
    const t=setTimeout( top.location = "./login.html",25000)
  } else {
    try {
      if(error_code== PublicDefine_pb.ErrorCode.ERRORCODE_PASSWORDCONTAINUSERNAME){
        console.log('xxx', ui_util)
        ui_util.showFadeTip('新密码不能包含用户名!');
        console.log('xxx3', ui_util)

      }else if(error_code== PublicDefine_pb.ErrorCode.ERRORCODE_PASSWORDREPEATED){
        ui_util.showFadeTip('新密码与之前密码重复!');
      }else{
        ui_util.getErrorTips(error_code,text)
      }
    } catch (e) {
      console.log('erre', e)
    }

  }

  // 消息处理完成关闭连接
  // tcp_client.close();
}
