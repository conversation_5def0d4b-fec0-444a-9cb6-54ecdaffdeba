'use strict';

const ProxyServer_pb = require('../pb/ProxyServer_pb');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const util = require('../../lib/util');
const ui_util = require('./ui_util');
const tcp_client = require('../../lib/tcp_client');
const config = require("../../config.json");
const device_codec = require('../platform_codec/device_codec');
// 相关变量定义
const user_sign = $("body").attr("user_sign_info");
const user_name = $("body").attr("user_name_info");
console.log("user_sign:", user_sign + user_name);

$("#editSwParam").on("Shown.bs.modal",function (e) {
  $('#addDeviceForm').validator()
})
// 修改交换机参数
$('#save_sw_btn').on('click', function (e) {
  if ($("#save_sw_btn").hasClass("disabled")) {
    return;
  }
  $('#editSwParam').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  editSwParamData();
})

// 导入进度条动画加载&导出
$('#sw_import').on('click', function () {
  importSwRules()
})
$('#sw_export').on('click', function () {
  exportSwRules()
})

/**
 * 初始化交换机表格.
 */
let switchData = new Array();
function loadSwitchTb(type, statis, search) {
  let id="tdCompute2",table="switch-tb"
  $("#switch-tb").jqGrid(
    {
      datatype: "local",
      height: "90%",
      autowidth: true,
      shrinkToFit: true,
      pager: '#switch-pager',
      viewrecords: true,
      rowNum: 10,
      rowList: [5, 10, 20, 50, 100],
      recordtext: "{0} - {1}\u3000共 {2} 条",
      emptyrecords: "无数据",
      loadtext: "读取中...",
      pgtext: "{0} 共 {1} 页",
      colNames: ['编码','参数', '操作'],
      colModel: [
        {name: 'sw_code', index: 'sw_code'},
        {name: 'sw_param', index: 'sw_param'},
        {name: 'operation', index: 'operation', sortable: false, formatter: displayButtons}
      ]
    });
  // 清空和填充表格数据
  $("#switch-tb").jqGrid('clearGridData');
  for (let i = 0; i <= switchData.length; i++) {
    $("#switch-tb").jqGrid('addRowData', i + 1, switchData[i]);
  }
// 数组数据分页.本地数据分页.
  $('.ui-pg-table.ui-pager-table #first_switch-pager span')
    .addClass('ace-icon fa fa-angle-double-left bigger-100')
  $('.ui-pg-table.ui-pager-table #prev_switch-pager span')
    .addClass('ace-icon fa fa-angle-left bigger-100')
  $('.ui-pg-table.ui-pager-table #next_switch-pager span')
    .addClass('ace-icon fa fa-angle-right bigger-100')
  $('.ui-pg-table.ui-pager-table #last_switch-pager span')
    .addClass('ace-icon fa fa-angle-double-right bigger-100')
  const localData = {page: 1, total: 2, records: "2", rows: []};
  localData.rows = switchData;
  localData.records = switchData.length;
  localData.total = (switchData.length % 2 == 0) ? (switchData.length / 2) : (Math.floor(switchData.length / 2) + 1);
  const reader = {
    root: function (obj) {return localData.rows;},
    page: function (obj) {return localData.page;},
    total: function (obj) {return localData.total;},
    records: function (obj) {return localData.records;},
    repeatitems: false
  };
  $("#switch-tb").setGridParam({
    data: localData.rows,
    reader: reader
  }).trigger('reloadGrid');
  /**\
   * 创造行末尾按钮并修改删除点击方法.
   * @param cellvalue
   * @param options
   * @param rowObject
   * @returns {string}
   */
  function displayButtons(cellvalue, options, rowObject) {
    return "<div class='hidden-sm hidden-xs btn-group'>" +
      "<button class='btn btn-minier btn-inverse wa-mlr5' " +
      "row-obj-str='" + JSON.stringify(rowObject) + "'" +
      "onclick=\"console.log($(this).attr('row-obj-str'))\n" +
      "$('#editSwParam').modal('show')\n" +
      // "$('#addDeviceForm').get(0).reset()\n" +
      "$('#sw_code').val(JSON.parse($(this).attr('row-obj-str')).sw_code)\n" +
      "$('#sw_param').val(JSON.parse($(this).attr('row-obj-str')).sw_param)\">" +
      "<i class='ace-icon fa fa-edit bigger-80'>&nbsp;修改</i>" +
      "</button>" +
      "</div>";
  }
  jqgridColResize()
  tableResize(table);
}

/**
 * 重新调整jqgrid每列的宽度
 */
function jqgridColResize(){
  let td=$('#tdCompute')//获取计算实际列长度的容器
    ,tds//临时保存列
    ,arr=[];//用于保存最大的列宽
  //遍历每行获得每行中的最大列宽
  $('.ui-jqgrid-htable tr,.ui-jqgrid-btable tr:gt(0)').each(function(){
    $(this).find('td,th').each(function(idx){
      arr[idx]=Math.max(arr[idx]?arr[idx]:0,td.html($(this).text())[0].offsetWidth);
    })
  });
  //设置页头单元格宽度
  $('.ui-jqgrid-labels th').each(function(idx){this.style.width=arr[idx]+'px'});
  //设置内容表格中控制单元格宽度的单元格，在第一行
  $('.ui-jqgrid-btable tr:eq(0) td').each(function(idx){this.style.width=arr[idx]+'px'});
  // 设置操作栏固定宽度
  $('.ui-jqgrid .ui-jqgrid-htable th:last-child').css("width", "50");
  $('.ui-jqgrid .ui-jqgrid-htable th:nth-child(2)').css("width", "200");
  $('.ui-jqgrid-btable>tbody>tr>td:last-child').css("width", "50");
  $('.ui-jqgrid-btable>tbody>tr>td:nth-child(2)').css("width", "200");
}
/**
 * 表格自适应窗口
 */
function tableResize(table) {
  $(window).on('resize.jqGrid', function () {
    $("#"+table).jqGrid('setGridWidth', $(".page-content").width());
    jqgridColResize()
  });
  const parent_column = $("#"+table).closest('[class*="col-"]');
  $(document).on('settings.ace.jqGrid', function (ev, event_name, collapsed) {
    if (event_name == 'sidebar_collapsed' || event_name == 'main_container_fixed') {
      setTimeout(function () {
        $("#"+table).jqGrid('setGridWidth', parent_column.width());
        jqgridColResize()
      }, 0);
    }
  })
}

pageInitSw()
/**
 * 初始化交换机规则数据表格
 */
function pageInitSw() {
  tcp_client.connect(onMsg, config.host, config.port);
  loadSwRuleGenerator();
}
function loadSwRuleGenerator() {
  let sendMsg = new ProxyServer_pb.MSG_CPLoadSwitchRules();
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADSWITCHRULES, sendMsg, user_sign);
}

/**
 * 发送修改交换机规则参数
 */
function editSwParamData() {
  tcp_client.connect(onMsg, config.host, config.port);
  let sendMsg = new ProxyServer_pb.MSG_CPConfigSwitchRule();
  sendMsg.setSwitchType($("#sw_code").val())
  sendMsg.setRule($("#sw_param").val())
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPCONFIGSWITCHRULE, sendMsg, user_sign);
}

/**
 * 消息处理函数
 * @param msgId 消息id
 * @param pb 原始pb字节
 */
function onMsg(msgId, pb) {
  switch (msgId) {
    case ProxyServer_pb.PSMessageId.PSMESSAGEID_PCLOADSWITCHRULES: {
      const msg = ProxyServer_pb.MSG_PCLoadSwitchRules.deserializeBinary(pb).getRulemapMap();
      let ruleMap = msg.toArray();
      console.log('ruleMap',ruleMap)
      switchData=[];
      for (let i = 0; i < ruleMap.length; ++i) {
        let row = {
          sw_code: ruleMap[i][0],
          sw_param: ruleMap[i][1],
        };
        switchData.push(row);
      }
      console.log('switchData',switchData)
      loadSwitchTb()
    }
      break;
    case ProxyServer_pb.PSMessageId.PSMESSAGEID_PCCONFIGSWITCHRULE: {
      const msg = ProxyServer_pb.MSG_PCConfigSwitchRule.deserializeBinary(pb);
      if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
        ui_util.showFadeTip('修改用户交换机参数成功!');
        // 重新加载用户数据
        pageInitSw();
      } else {
        ui_util.getErrorTips(dp_packet.return_value,'修改用户交换机参数')
      }
    }
      break;
    default:
      break;
  }
}

/**
 * 发送导出交换机规则配置
 */
function exportSwRules() {

}
/**
 * 发送导入交换机规则配置
 */
function importSwRules() {

}

