'use strict';

const ProxyServer_pb = require('../pb/ProxyServer_pb');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const util = require('../../lib/util');
const ui_util = require('./ui_util');
const tcp_client = require('../../lib/tcp_client');
const config = require("../../config.json");
const device_codec = require('../platform_codec/device_codec');
const x_scan_codec = require('../platform_codec/X-scan_codec');
// 相关变量定义
const user_sign = $("body").attr("user_sign_info");
const user_name = $("body").attr("user_name_info");
console.log("user_sign:", user_sign + user_name);

let validateDlgId,validateDlgIp;
//定时刷新页面.
let timer=null;
loadXscansGenerator();
timer=setInterval(function(){
  // console.log('X-scan')
  loadXscansGenerator();
}, 10000);
ui_util.stopTimeOut(timer)

// 点击创建新漏洞扫描
$('#add_Xscan').on('click', function () {
  $('#validateUserDlg').modal('show');
  $('#validateUserForm').get(0).reset()
  $('#opraType').val(1)
})

// 启动单行设备漏洞扫描
$('#start_device_btn').on('click', function () {
  $('#addXscanDlg').modal('hide');
  startXscan(util.genTaskId(),$('#deviceIp').val());// 启动时针对每条设备生成新id
});

//查看漏洞扫描状态&结果
$('#seeStateDlg').on('shown.bs.modal', function () {
  if($('#seeStateTitle').attr('wa-name')=='see_result'){
    getXscanResult($('#xscanId').val(),$('#xscanIp').val());
  }else{
    // 考虑点击查看状态操作时.
    // getBaselineState($('#baselineId').val(),$('#baselineIp').val());
  }
})

// 确认取消&移除漏洞扫描
$('#stop_btn').on('click', function () {
  if($('#stopTitle').attr('wa-name')=='_stop'){
    stopXscan($('#xscanId2').val(),$('#xscanIp2').val());
  }else{
    removeXscanGenerator($('#xscanId2').val());
  }
});

$('#validateuser_btn').on('click',function (e) {
  if ($("#validateuser_btn").hasClass("disabled")) {
    return;
  }
  $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  let username=user_name
  let psd=$('#validateUserDlg #password').val()
  validateDlgId=$('#opraId').val() ,validateDlgIp=$('#opraIp').val()
  validateUserGenerator(username,psd);
})

/**
 * 初始化基线表格&设备(状态上线)表格
 */
let leakScanData = new Array();
let deviceData = new Array();

function loadXscanTb() {
  $("#xscans-tb").jqGrid(
    {
      datatype: "local",
      height: "90%",
      autowidth: true,
      shrinkToFit: true,
      pager: '#xscans-pager',
      viewrecords: true,
      rowNum: 10,
      rowList: [5, 10, 20, 50, 100],
      recordtext: "{0} - {1}\u3000共 {2} 条",
      emptyrecords: "当前没有漏洞扫描记录",
      loadtext: "读取中...",
      pgtext: "{0} 共 {1} 页",
      colNames: ['编号','漏洞扫描状态','监控对象Ip','启动时间','取消时间','完成时间','操作'],
      colModel: [
        {name: 'taskid', index: 'taskid', sortable: false},
        {name: 'leakScanState', index: 'leakScanState', sortable: false,formatter:displayStates},
        {name: 'destIp', index: 'destIp', sortable: false},
        {name: 'startTime', index: 'startTime',},
        {name: 'stopTime', index: 'stopTime',},
        {name: 'completeTime', index: 'completeTime',},
        {name: 'operation', index: 'operation', sortable: false, formatter: displayButtons}
      ],
      sortable:true,
      sortname: 'startTime',
      sortorder:'desc',
    });

  // 清空和填充表格数据
  $("#xscans-tb").jqGrid('clearGridData');
  for (let i = 0; i <= leakScanData.length; i++) {
    $("#xscans-tb").jqGrid('addRowData', i + 1, leakScanData[i]);
  }
  // 数组数据分页.
  $('.ui-pg-table.ui-pager-table #first_xscans-pager span')
    .addClass('ace-icon fa fa-angle-double-left bigger-100')
  $('.ui-pg-table.ui-pager-table #prev_xscans-pager span')
    .addClass('ace-icon fa fa-angle-left bigger-100')
  $('.ui-pg-table.ui-pager-table #next_xscans-pager span')
    .addClass('ace-icon fa fa-angle-right bigger-100')
  $('.ui-pg-table.ui-pager-table #last_xscans-pager span')
    .addClass('ace-icon fa fa-angle-double-right bigger-100')
  const localData = {page: 1, total: 2, records: "2", rows: []};
  localData.rows = leakScanData;
  localData.records = leakScanData.length;
  localData.total = (leakScanData.length % 2 == 0) ? (leakScanData.length / 2) : (Math.floor(leakScanData.length / 2) + 1);
  const reader = {
    root: function (obj) {return localData.rows;},
    page: function (obj) {return localData.page;},
    total: function (obj) {return localData.total;},
    records: function (obj) {return localData.records;},
    repeatitems: false
  };
  $("#xscans-tb").setGridParam({
    data: localData.rows,
    reader: reader
  }).trigger('reloadGrid');

  /**\
   * 创造行末尾按钮.
   */
  function displayButtons(cellvalue, options, rowObject) {
    let html="<div class='hidden-sm hidden-xs btn-group'>";
    let html2="<button class='btn btn-minier btn-success wa-mlr5'" +
      "row-id='" + rowObject.taskid + "'" +
      "row-ip='" + rowObject.destIp + "'" +
      "onclick=\"$('#validateUserDlg').modal('show');\n" +
      "$('#validateUserForm').get(0).reset()\n" +
      "$('#opraType').val(2)\n" +
      "$('#opraId').val($(this).attr('row-id'))\n" +
      "$('#opraIp').val($(this).attr('row-ip'))\">" +
      "<i class='ace-icon fa fa-eye bigger-80'>&nbsp;查看结果</i>" +
      "</button>";
    let html3="<button class='btn btn-minier btn-success disabled wa-mlr5'" +
      "row-id='" + rowObject.taskid + "'" +
      "row-ip='" + rowObject.destIp + "'>" +
      "<i class='ace-icon fa fa-eye bigger-80'>&nbsp;查看结果</i>" +
      "</button>";
    if(cellvalue == 0){
      html +=html2+
      "<button class='btn btn-minier btn-inverse'" +
        "row-id='" + rowObject.taskid + "'" +
        "row-ip='" + rowObject.destIp + "'" +
        "onclick=\"$('#validateUserDlg').modal('show');\n" +
        "$('#validateUserForm').get(0).reset()\n" +
        "$('#opraType').val(3)\n" +
        "$('#opraId').val($(this).attr('row-id'))\n" +
        "$('#opraIp').val($(this).attr('row-ip'))\">" +
        "<i class='ace-icon fa fa-ban bigger-80'>&nbsp;取消</i>" +
        "</button>" +
        "</div>"
    }else if(cellvalue==1){
      html += html3+
      "<button class='btn btn-minier btn-danger'" +
        "row-id='" + rowObject.taskid + "'" +
        "row-ip='" + rowObject.destIp + "'" +
        "onclick=\"$('#validateUserDlg').modal('show');\n" +
        "$('#validateUserForm').get(0).reset()\n" +
        "$('#opraType').val(4)\n" +
        "$('#opraId').val($(this).attr('row-id'))\n" +
        "$('#opraIp').val($(this).attr('row-ip'))\">" +
        "<i class='ace-icon fa fa-trash bigger-80'>&nbsp;删除</i>" +
        "</button>" +
        "</div>"
    }else{
      html +=html2+
        "<button class='btn btn-minier btn-danger'" +
        "row-id='" + rowObject.taskid + "'" +
        "row-ip='" + rowObject.destIp + "'" +
        "onclick=\"$('#validateUserDlg').modal('show');\n" +
        "$('#validateUserForm').get(0).reset()\n" +
        "$('#opraType').val(4)\n" +
        "$('#opraId').val($(this).attr('row-id'))\n" +
        "$('#opraIp').val($(this).attr('row-ip'))\">" +
        "<i class='ace-icon fa fa-trash bigger-80'>&nbsp;删除</i>" +
        "</button>" +
        "</div>"
    }
    return html;
  }
  /**
   * 设置核查状态字段
   */
  function displayStates(cellvalue, options, rowObject) {
    if(cellvalue==0) return '正在执行中'
    else if(cellvalue==1) return '已取消'
    else if(cellvalue==2) return '启动失败'
    else if(cellvalue==3) return '启动成功'

  }
  jqgridColResize()
  tableResize();
}

function loadDeviceTb() {
  $("#device-tb").jqGrid(
    {
      datatype: "local",
      height: "90%",
      autowidth: true,
      shrinkToFit: true,
      pager: '#device-pager',
      viewrecords: true,
      rowNum: 10,
      rowList: [5, 10, 20, 50, 100],
      recordtext: "{0} - {1}\u3000共 {2} 条",
      emptyrecords: "当前没有在线服务器设备",
      loadtext: "读取中...",
      pgtext: "{0} 共 {1} 页",
      colNames: ['序号', '漏洞扫描主机', 'Ip', 'MAC', '操作'],
      colModel: [
        {name: 'id', index: 'id'},
        {name: 'deviceName', index: 'deviceName'},
        {name: 'deviceIp', index: 'deviceIp'},
        {name: 'mac', index: 'mac',},
        {name: 'operation', index: 'operation', sortable: false, formatter: displayButtons}
      ]
    });

  // 清空和填充表格数据
  $("#device-tb").jqGrid('clearGridData');
  for (let i = 0; i <= deviceData.length; i++) {
    $("#device-tb").jqGrid('addRowData', i + 1, deviceData[i]);
  }
  // 表格自适应modal窗口
  $(window).on('resize.jqGrid', function () {
    $("#device-tb").jqGrid('setGridWidth', $("#addXscanDlg .modal-content>.modal-body .col-xs-12").width());
  });
  const parent_column = $("#device-tb").closest('[class*="col-"]');
  $(document).on('settings.ace.jqGrid', function (ev, event_name, collapsed) {
    if (event_name == 'sidebar_collapsed' || event_name == 'main_container_fixed') {
      setTimeout(function () {
        $("#device-tb").jqGrid('setGridWidth', parent_column.width());
      }, 0);
    }
  })
  // 数组数据分页.
  $('.ui-pg-table.ui-pager-table #first_device-pager span')
    .addClass('ace-icon fa fa-angle-double-left bigger-100')
  $('.ui-pg-table.ui-pager-table #prev_device-pager span')
    .addClass('ace-icon fa fa-angle-left bigger-100')
  $('.ui-pg-table.ui-pager-table #next_device-pager span')
    .addClass('ace-icon fa fa-angle-right bigger-100')
  $('.ui-pg-table.ui-pager-table #last_device-pager span')
    .addClass('ace-icon fa fa-angle-double-right bigger-100')
  const localData = {page: 1, total: 2, records: "2", rows: []};
  localData.rows = deviceData;
  localData.records = deviceData.length;
  localData.total = (deviceData.length % 2 == 0) ? (deviceData.length / 2) : (Math.floor(deviceData.length / 2) + 1);
  const reader = {
    root: function (obj) {return localData.rows;},
    page: function (obj) {return localData.page;},
    total: function (obj) {return localData.total;},
    records: function (obj) {return localData.records;},
    repeatitems: false
  };
  $("#device-tb").setGridParam({
    data: localData.rows,
    reader: reader
  }).trigger('reloadGrid');

  /**\
   * 创造行末尾按钮操作方法.
   */
  function displayButtons(cellvalue, options, rowObject) {
    return "<div class='hidden-sm hidden-xs btn-group'>" +
      "<button class='btn btn-minier btn-success'" +
      "row-id='" + rowObject.id + "'" +
      "row-ip='" + rowObject.deviceIp + "'" +
      "onclick=\"$('#confirmStartDlg').modal('show')\n" +
      "$('#deviceId').val($(this).attr('row-id'))\n" +
      "$('#deviceIp').val($(this).attr('row-ip'))\">" +
      "<i class='ace-icon fa fa-play-circle bigger-80'>&nbsp;启动</i>" +
      "</button>" +
      "</div>";
  }
}

/**
 * 重新调整jqgrid每列的宽度
 */
function jqgridColResize(){
  let td=$('#tdCompute')//获取计算实际列长度的容器
    ,tds//临时保存列
    ,arr=[];//用于保存最大的列宽
  //遍历每行获得每行中的最大列宽
  $('.ui-jqgrid-htable tr,.ui-jqgrid-btable tr:gt(0)').each(function(){
    $(this).find('td,th').each(function(idx){
      arr[idx]=Math.max(arr[idx]?arr[idx]:0,td.html($(this).text())[0].offsetWidth);
    })
  });
  //设置页头单元格宽度
  $('.ui-jqgrid-labels th').each(function(idx){this.style.width=arr[idx]+'px'});
  //设置内容表格中控制单元格宽度的单元格，在第一行
  $('.ui-jqgrid-btable tr:eq(0) td').each(function(idx){this.style.width=arr[idx]+'px'});
  // 设置操作栏固定宽度
  $('.ui-jqgrid .ui-jqgrid-htable th:last-child').css("width", "89");
  $('.ui-jqgrid-btable>tbody>tr>td:last-child').css("width","89");
}
/**
 * 表格自适应窗口
 */
function tableResize(){
  $(window).on('resize.jqGrid', function () {
    $("#xscans-tb").jqGrid('setGridWidth', $(".page-content").width());
    jqgridColResize();
  });
  const parent_column = $("#xscans-tb").closest('[class*="col-"]');
  $(document).on('settings.ace.jqGrid', function (ev, event_name, collapsed) {
    if (event_name == 'sidebar_collapsed' || event_name == 'main_container_fixed') {
      setTimeout(function () {
        $("#xscans-tb").jqGrid('setGridWidth', parent_column.width());
        jqgridColResize();
      }, 0);
    }
  })
}

/**
 * 获取初始化漏洞扫描表格数据
 */
function loadXscansGenerator() {
  // 连接服务器
  tcp_client.connect(loadXscansHandler, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPLoadLeakScans();
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADLEAKSCANS, sendMsg, user_sign);
}

function loadXscansHandler(msgId, pb) {
  if (msgId !== ProxyServer_pb.PSMessageId.PSMESSAGEID_PCLOADLEAKSCANS) {
    console.error('invalid msg', msgId);
    return;
  }
  const msg = ProxyServer_pb.MSG_PCLoadLeakScans.deserializeBinary(pb);
  const leakscans = msg.getLeakScansList();
  leakScanData = [];
  for (const leakscan of leakscans) {
    // console.log('leakscan', leakscan.getTaskId(), leakscan.getResult(), leakscan.getStartTime(), leakscan.getStopTime(), leakscan.getCompleteTime(), leakscan.getError(), leakscan.getDestIp())
    let _state=0;
    if(leakscan.getStopTime()==0&&leakscan.getCompleteTime()==0) _state=0
    else if(leakscan.getStopTime()>0) _state=1
    else if(leakscan.getCompleteTime()>0){
      if(leakscan.getError()>0) _state=2
      else _state=3
    }
    const leakScanObj = {
      taskid: $.trim(leakscan.getTaskId()),
      leakScanState:_state,
      destIp: leakscan.getDestIp(),
      startTime: leakscan.getStartTime()==0?'-':util.getThisDateMs(leakscan.getStartTime()),
      stopTime: leakscan.getStopTime()==0?'-':util.getThisDateMs(leakscan.getStopTime()),
      completeTime: leakscan.getCompleteTime()==0?'-':util.getThisDateMs(leakscan.getCompleteTime()),
      operation:_state
    }
    leakScanData.push(leakScanObj)
  }
  loadXscanTb();
}

/**
 * 发送验证密码参数
 * @param psd
 */
function validateUserGenerator(username,psd){
// 连接服务器
  tcp_client.connect(function(msgId, pb) {
    const msg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(pb);
    if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
      //ui_util.showFadeTip('验证用户通过!');
      if($('#opraType').val()==1){
        $('#addXscanDlg').modal('show');
        loadDeviceidsGenerator();
      }else if($('#opraType').val()==2){
        $('#seeStateDlg').modal('show')
        $('#seeStateTitle').attr('wa-name','see_result')
        $('#seeStateTitle').text('查看该漏洞扫描结果')
        $('#xscanId').val(validateDlgId)
        $('#xscanIp').val(validateDlgIp)
      }else if($('#opraType').val()==3){
        $('#confirmStopDlg').modal('show')
        $('#stopTitle').attr('wa-name','_stop')
        $('#stopTitle').text('确认取消')
        $('#confirmStopDlg .modal-body p').text('确定取消该设备漏洞扫描？')
        $('#xscanId2').val(validateDlgId)
        $('#xscanIp2').val(validateDlgIp)
      }else if($('#opraType').val()==4){
        $('#confirmStopDlg').modal('show')
        $('#stopTitle').attr('wa-name','_remove')
        $('#stopTitle').text('确认删除')
        $('#confirmStopDlg .modal-body p').text('确定删除该设备漏洞扫描？')
        $('#xscanId2').val(validateDlgId)
      }

    } else {
      ui_util.showFadeTip('密码输入不正确!');
    }
  }, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
  sendMsg.setUserName(username)
  sendMsg.setPassword(psd)
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg, user_sign);
}

/**
 * 获取初始化设备(状态上线)表格数据
 */
function loadDeviceidsGenerator() {
  // 连接服务器
  tcp_client.connect(loadDeviceidsHandler, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPQueryOnlineDeviceIds();
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPQUERYONLINEDEVICEIDS, sendMsg, user_sign);
}
function loadDeviceidsHandler(msgId, pb) {
  if (msgId !== ProxyServer_pb.PSMessageId.PSMESSAGEID_PCQUERYONLINEDEVICEIDS) {
    console.error('invalid msg', msgId);
    return;
  }
  const msg = ProxyServer_pb.MSG_PCQueryOnlineDeviceIds.deserializeBinary(pb);
  const deviceids = msg.getDeviceIdsList();
  // console.log("deviceids",deviceids)

  loadDevicesGenerator(deviceids);
}

function loadDevicesGenerator(deviceids) {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      const devices = device_codec.unpackLoadDevice(dp_packet.content);
      deviceData = [];
      for (let i = 0; i < devices.length; i++) {
        if($.inArray(devices[i].getId(),deviceids)!==-1&&devices[i].getDeviceType()=="SVR"){
          const deviceObj = {
            id: devices[i].getId(),
            deviceName: devices[i].getDeviceName(),
            deviceIp: util.int2ip(devices[i].getIp1()),
            mac: util.str2mac(devices[i].getMac1())
          }
          deviceData.push(deviceObj)
        }
      }
      loadDeviceTb();
    }
    // 消息处理完成关闭连接
    tcp_client.close();
  }, config.host, config.port);

  // 发送消息
  tcp_client.send(device_codec.packLoadDevice());
}

/**
 * 启动漏洞扫描
 * @param task_id
 * @param dest_ip
 */
function startXscan(task_id, dest_ip) {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    const text="启动漏洞扫描"
    if (dp_packet.return_value === 0) {
      ui_util.showFadeTip('启动漏洞扫描成功!');
    } else {
      ui_util.getErrorTips(dp_packet.return_value,text)
    }
    // 消息处理完成关闭连接
    tcp_client.close();
    loadXscansGenerator();
  }, config.host, config.port);

  // 发送消息
  tcp_client.send(x_scan_codec.packStartXscan(task_id, util.ip2int(dest_ip)));
}

/**
 * 查看漏洞扫描结果
 */
function getXscanResult(task_id,dest_ip) {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      const x_scan = x_scan_codec.unpackXscanGetResult(dp_packet.content);
      // console.log('x_scan', x_scan.getTaskId(), x_scan.getResult());
      const _result=x_scan.getResult()==""?"暂无结果":x_scan.getResult();
      $('#seeStateDlg .modal-body>#scanStateCont').text(_result)
    }
    // 消息处理完成关闭连接
    tcp_client.close();
  }, config.host, config.port);

  // 发送消息
  tcp_client.send(x_scan_codec.packXscanGetResult(task_id, util.ip2int(dest_ip)));
}

/**
 * 取消漏洞扫描
 * @param task_id
 * @param dest_ip
 */
function stopXscan(task_id,dest_ip) {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    const text="取消漏洞扫描"
    if (dp_packet.return_value === 0) {
      // const x_scan = x_scan_codec.unpackStopXscan(dp_packet.content);
      // console.log('x_scan', x_scan.getTaskId(), x_scan.getResult());
      ui_util.showFadeTip('取消漏洞扫描成功!');
    } else {
      ui_util.getErrorTips(dp_packet.return_value)
    }
    // 消息处理完成关闭连接
    tcp_client.close();
    loadXscansGenerator();
  }, config.host, config.port);

  // 发送消息
  tcp_client.send(x_scan_codec.packStopXscan(task_id, util.ip2int(dest_ip)));
}

/**
 * 移除已完成或者已取消的漏洞扫描
 * @param taskId
 */
function removeXscanGenerator(taskId) {
    // 连接服务器
    tcp_client.connect(removeXscanHandler, config.host, config.port);

    // 发送消息
    let sendMsg = new ProxyServer_pb.MSG_CPRemoveLeakScan();
    sendMsg.setTaskId(taskId);
    tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPREMOVELEAKSCAN, sendMsg, user_sign);
}

function removeXscanHandler(msgId, pb) {
    if (msgId !== ProxyServer_pb.PSMessageId.PSMESSAGEID_PCREMOVELEAKSCAN) {
        console.error('invalid msg', msgId);
        return;
    }
  //   const msg = ProxyServer_pb.MSG_PCRemoveLeakScan.deserializeBinary(pb);
  //   console.log('removeXscanHandler', msg.getTaskId(), msg.getResult());
  ui_util.showFadeTip('删除漏洞扫成功!');
  loadXscansGenerator()
}
