'use strict';

const ProxyServer_pb = require('../pb/ProxyServer_pb');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const ui_util = require('./ui_util');
const util = require('../../lib/util');
const tcp_client = require('../../lib/tcp_client');
const config = require("../../config.json");
const collect_event_codec = require('../platform_codec/collect_event_codec');
// 相关变量定义
const user_sign = $("body").attr("user_sign_info");
const user_name = $("body").attr("user_name_info");
console.log("user_sign:", user_sign + user_name);

$('.main-content').busyLoad("hide");
// 初始化日期控件
let beginTimeStore = '';
let endTimeStore = '';
$('#newTimeRange').daterangepicker({
  "timePicker": true,
  "timePicker24Hour": true,
  "linkedCalendars": false,
  "autoUpdateInput": true,
  "startDate": moment().startOf('day'),
  "endDate": moment().endOf('day'),
  // maxDate: moment(new Date()), //设置最大日期
  "locale": {
    format: 'YYYY/MM/DD HH:mm:ss',
    separator: ' - ',
    applyLabel: "确认",
    cancelLabel: "取消",
    fromLabel: "开始时间",
    toLabel: "结束时间",
    customRangeLabel: "自定义",
    daysOfWeek: ["日", "一", "二", "三", "四", "五", "六"],
    monthNames: ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"]
  },
  ranges: {
    '今天': [moment(), moment().endOf('day')],
    '昨天': [moment().subtract(1, 'days'), moment().endOf('day').subtract(1, 'days')],
    '近7天': [moment().subtract(7, 'days'), moment().endOf('day')],
    '本月': [moment().startOf('month'), moment().endOf('month').endOf('day')],
    '上月': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month').endOf('day')]
  }
}, function (start, end, label) {
  beginTimeStore = start;
  endTimeStore = end;
  if (!this.startDate) {
    this.element.val('');
  } else {
    this.element.val(this.startDate.format(this.locale.format) + this.locale.separator + this.endDate.format(this.locale.format));
  }
});

$('#deviceTypeSelect [name="all"]').click(function () {
  if ($('#deviceTypeSelect [name="all"]:checked').length == 0) {
    $('#deviceTypeSelect [name="form-field-checkbox"]').prop("checked", false)
  } else {
    $('#deviceTypeSelect [name="form-field-checkbox"]').prop("checked", "checked")
  }
})
$('#eventLevelSlt [name="all"]').click(function () {
  if ($('#eventLevelSlt [name="all"]:checked').length == 0) {
    $('#eventLevelSlt [name="form-field-checkbox"]').prop("checked", false)
  } else {
    $('#eventLevelSlt [name="form-field-checkbox"]').prop("checked", "checked")
  }
})

// 条件筛选刷新表格
// let num = parseInt($('#recordCount').val()),
let num = 10,
  pageNo = 1,
  type = 0,
  grade = 0,
  beginDate = util.getThisTimestamp($('#newTimeRange').val().substring(0, 19)),
  endDate = util.getThisTimestamp($('#newTimeRange').val().substring(22, 41));
$('#queryAll').on('click', function (e) {
  num = 10;
  pageNo = 1;
  type = 0;
  grade = 0;
  $('#deviceTypeSelect input').prop('checked',true);
  $('#eventLevelSlt input').prop('checked',true);
  let new_begin="1900/01/01 00:00:00",
    _end = moment(new Date()).format('YYYY/MM/DD HH:mm:ss')
  // $('#newTimeRange').val(new_begin+" - "+_end)
  beginDate = util.getThisTimestamp(new_begin);
  endDate = util.getThisTimestamp(_end);
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  pageInit(num, type, grade, beginDate, endDate, pageNo);
})
$('#query').on('click', function (e) {
  // num = $('#recordCount').val()==""?1000:parseInt($('#recordCount').val());
  num = 10;
  pageNo = 1;
  type = "";
  grade = "";
  let tArray = $('#deviceTypeSelect [name="form-field-checkbox"]:checked')
  let gArray = $('#eventLevelSlt [name="form-field-checkbox"]:checked')
  if ($(tArray).length === 0) {
    type = 0
  } else {
    for (let i = 0; i < $(tArray).length; i++) {
      type |= parseInt($(tArray)[i].value)
    }
  }
  if ($(gArray).length === 0) {
    grade = 0
  } else {
    for (let i = 0; i < $(gArray).length; i++) {
      grade |= parseInt($(gArray)[i].value)
    }
  }
  let _begin = $('#newTimeRange').val().substring(0, 19), _end = $('#newTimeRange').val().substring(22, 41);
  beginDate = util.getThisTimestamp(_begin);
  endDate = util.getThisTimestamp(_end);
  let selectNum = $('#selectNum').val();
  if(selectNum != "" && selectNum != undefined){
      num = selectNum;
  }
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  pageInit(num, type, grade, beginDate, endDate, pageNo);
})

// 统计图弹窗
$('#statis_chart').on('click', function (e) {
  $('#seeChartDlg').modal('show');
  type = "";
  grade = "";
  let tArray = $('#deviceTypeSelect [name="form-field-checkbox"]:checked')
  let gArray = $('#eventLevelSlt [name="form-field-checkbox"]:checked')
  if ($(tArray).length === 0) {
    type = 0
  } else {
    for (let i = 0; i < $(tArray).length; i++) {
      type |= parseInt($(tArray)[i].value)
    }
  }
  if ($(gArray).length === 0) {
    grade = 0
  } else {
    for (let i = 0; i < $(gArray).length; i++) {
      grade |= parseInt($(gArray)[i].value)
    }
  }
  let _begin = $('#newTimeRange').val().substring(0, 19),
    _end = $('#newTimeRange').val().substring(22, 41);
  beginDate = util.getThisTimestamp(_begin);
  endDate = util.getThisTimestamp(_end);
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  echartsInit(type, grade, beginDate, endDate, true);
})

/**
 * 初始化刷新表格.
 */
let data = new Object();

function loadCollectEvtTb() {
  $("#collect-evt-tb").bootstrapTable({
    method: 'post',
    data: data,
    toolbar: '#toolbar',
    cache: false, // 设置为 false 禁用 AJAX 数据缓存， 默认为true
    striped: true,  //表格显示条纹，默认为false
    pagination: true, // 在表格底部显示分页组件，默认false
    pageList: [10, 20, 50, 100], // 设置页面可以显示的数据条数
    pageNumber: pageNo, //初始化加载第一页，默认第一页
    pageSize: num, // 页面数据条数
    queryParamsType: 'limit',//查询参数组织方式
    // queryParams:queryParams,//请求服务器时所传的参数
    sidePagination: 'server',//指定服务器端分页
    paginationShowPageGo: true,//跳转到
    columns: [
      {field: 'id', title: '编号', width: '8%', align: "center"},
      {field: 'event_date', title: '事件时间', width: '8%', align: "center",},
      {field: 'collect_date', title: '采集时间', width: '8%', align: "center",},
      {field: 'name', title: '设备名称或系统', width: '11%', align: "center"},
      {
        field: 'type', title: '设备类型', width: '10%', align: "center", formatter: function (value, row, pageNo) {
          if (value == "SVR") return "服务器工作站类";
          else if (value == "DB") return "数据库";
          else if (value == "SW") return "网络设备";//交换机?
          else if (value == "FID") return "横向正向隔离装置";
          else if (value == "BID") return "横向反向隔离装置";
          else if (value == "VEAD") return "纵向加密装置";
          else if (value == "FW") return "防火墙";
          else if (value == "IDS") return "入侵检测系统";
          else if (value == "AV") return "防病毒系统";
          else if (value == "DCD") return "网络安全监测装置";
          else return "";
        }
      },
      {
        field: 'evt_level', title: '事件等级', width: '8%', align: "center", formatter: function (value, row, pageNo) {
          if (value == "1") return "紧急";
          else if (value == "2") return "重要";
          else if (value == "3") return "次要";
          else if (value == "4") return "一般";
          else if (value == "5") return "告知";
          else return "";
        }
      },
      {field: 'evt_type', title: '事件类型', align: "center", width: '8%'},
      {field: 'child_type', title: '子类型', align: "center", width: '8%'},
      {field: 'describe', title: '内容描述', align: "center", width: '33%'}
    ],
    rowStyle: function (row, pageNo) {
      //这里有5个取值代表5中颜色['active', 'success', 'info', 'warning', 'danger'];
      let strclass = "";
      let style;
      if (row.evt_level == 0) {
        style = {css: {'background': '#f2dede'}};
        return style
        // strclass = 'danger'
      } else if (row.evt_level == 1) {
        style = {css: {'background': '#fcf8e3'}};
        return style
      } else if (row.evt_level == 2) {
        style = {css: {'background': '#d9edf7'}};
        return style
      } else if (row.evt_level == 3) {
        style = {css: {'background': '#dff0d8'}};
        return style
      } else if (row.evt_level == 4) {
        style = {css: {'background': '#f5f5f5'}};
        return style
      } else return {};
      return {classes: strclass}
    },
    sortable: true, //是否启用排序
    sortName: 'time', // 要排序的字段
    sortOrder: 'decs', // 排序规则,
    showLoading: true,
    onPageChange: function (number, size) {
      pageNo = number;
      num = size;
      pageInit(num, type, grade, beginDate, endDate, pageNo);
    }
  });
// 表格自适应窗口
  $(window).resize(function () {
    $("#collect-evt-tb").bootstrapTable('resetView', {
      width: $(".page-content").width()
    });
  });
}

loadCollectEvtTb()

let level_pert_data_xaxis=['紧急','重要','次要','一般','告知'],
  level_val_list=["1","2","3","4","5"],
  level_percent_data=[{value:0,name:'紧急'},{value:0,name:'重要'},{value:0,name:'次要'},{value:0,name:'一般'},{value:0,name:'告知'}],
  evt_classific_data_xaxis=['防火墙','横向正向隔离装置','横向反向隔离装置','服务器','交换机','纵向加密装置','防病毒系统','入侵检测系统','数据库','网络安全监测装置'],
  evt_val_list=["0","1","2","3","4","5","6","7","8","9"],
  evt_classific_data=[0,0,0,0,0,0,0,0,0,0];
/**
 * 事件采集等级&设备类型统计图
 */
function levelPercent() {
  let $level_percent = document.getElementById('level_percent')
  $level_percent.style.width = ($("#seeChartDlg .modal-dialog").width())/2 + 'px';
  let _level_percent = echarts.init($level_percent, 'shine');
  let option = {
    title:{text:'事件级别占比', left: '50'},
    tooltip : {
      trigger: 'item',
      axisPointer: {type: 'shadow'},
      formatter: '<h5>{a}</h5>' +
        '<span style="display:inline-block;margin-right:5px;' +
        'width:10px;height:10px;background-color:#0098d9;border-radius:5px"></span>{b}:{c}({d}%)<br>',
      backgroundColor:'rgba(255,255,255,.8)',
      borderRadius:0,
      // borderColor:'#006699',
      borderWidth:1,
      textStyle:{fontSize:13,fontFamily:'SimHei',color:'#333'},
    },
    legend: {show:false, orient: 'vertical', top: '69', left: '60', data:level_pert_data_xaxis},
    series : [
      {name: '等级占比', type: 'pie',  radius : '35%',
        center: ['50%', '50%'], data:level_percent_data,
        itemStyle: {
          normal:{
            label:{show: true, formatter:'{b}:{c}'},
            labelLine:{show:true}
          },
          emphasis: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }},
    ]
  };
  _level_percent.clear();
  _level_percent.setOption(option);
  chartsResize($level_percent, _level_percent)
}

function evtClassificStatis() {
  let $evt_classific_statis = document.getElementById('evt_classific_statis')
  $evt_classific_statis.style.width = ($("#seeChartDlg .modal-dialog").width())/2 + 'px';
  let _evt_classific_statis = echarts.init($evt_classific_statis, 'shine');
  let labelOption = {
    normal: {
      show: true,
      position: 'top',
      distance: 0,
      align: 'center',
      verticalAlign: 'bottom',
      rotate: 0,
      formatter: '{c}',
      fontSize: 16,
      rich: {
        name: {textBorderColor: '#fff'}
      }
    }
  };
  let option = {
    title:{text:'设备类型统计图', left: '5'},
    color: '#0098d9',
    tooltip: {
      trigger: 'axis',
      axisPointer: {type: 'shadow'},
      formatter: '<h5>{b0}</h5>' +
        '<span style="display:inline-block;margin-right:5px;' +
        'width:10px;height:10px;background-color:#0098d9;border-radius:5px"></span>{a0}:{c0}<br>',
      backgroundColor:'rgba(255,255,255,.8)',
      borderRadius:0,
      // borderColor:'#006699',
      borderWidth:1,
      textStyle:{fontSize:13,fontFamily:'SimHei',color:'#333'},
    },
    legend: {icon: 'circle', data: '设备类型', bottom:'0'},
    calculable: true,
    grid: {x: 30, x2: 66, y: 80, y2: 110},
    xAxis: [{type: 'category',axisTick: {show: false}, data: evt_classific_data_xaxis, axisLabel: {rotate: -60},boundaryGap: true}],
    yAxis: [{name:'数量/条',type: 'value'}],
    series: [{name: '设备条数', type: 'bar', label: labelOption, data:evt_classific_data,barWidth:19},]
  };
  _evt_classific_statis.clear();
  _evt_classific_statis.setOption(option);
  chartsResize($evt_classific_statis, _evt_classific_statis)
}

/**
 * 图表自适应窗口宽度实现
 * @param $chart_val
 * @param chart_val
 */
function chartsResize($chart_val, chart_val) {
  window.addEventListener("resize", function () {
    $chart_val.style.width = ($("#seeChartDlg .modal-dialog").width()) / 2 + 'px';
    chart_val.resize();
  });
}

pageInit(num, type, grade, beginDate, endDate, pageNo);
/**
 * 初始化页面.填充表格数组数据.
 */
let _totalCount;

function pageInit(num, type, grade, beginDate, endDate, pageNo) {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    data = {"total": 0, "rows": []};
    if (dp_packet.return_value === 0) {
      $('.main-content').busyLoad("hide");
      const collect_events = collect_event_codec.unpackLoadCollectEvent(dp_packet.content);
      if (collect_events.join('').startsWith('{totalCount:')) {
        _totalCount = getCaption(collect_events[0])
      }
      data.total = parseInt(_totalCount);
      let jdata = new Array();
      $.ajax({
        url: "evt_acquire.json",
        type: "get",
        dataType: "json",
        success: function (_data) {
          jdata = _data.evt_acquire;
          console.log("jdata", jdata)
          for (let i = 3; i < collect_events.length - 1; i++) {
            const _arr = collect_events[i].split(" ");
            if (_arr.length > 1) {
              let _evt_type = _arr[8], _child_type = _arr[9];
              $.map(jdata, function (value, key) {
                if (_arr[7] === value.device_type && parseInt(_arr[8]) === value.evt_type_val && parseInt(_arr[9]) === value.child_type_val) {
                  _evt_type = value.evt_type
                  _child_type = value.child_type
                }
              })
              let _cont = "", _newArr = new Array;
              _arr.forEach(function (val, index, _arr) {
                if (index > 9) {
                  _newArr.push(val)
                }
              })
              _cont = _newArr.join(" ");
              const _obj = {
                id: _arr[1],
                event_date: _arr[3] + " " + _arr[4],
                collect_date: _arr[3] + " " + _arr[4],
                name: _arr[5],
                type: _arr[7],
                evt_level: _arr[2].substr(1, _arr[2].length - 2),
                evt_type: _evt_type,
                child_type: _child_type,
                describe: _cont,
              }
              data.rows.push(_obj);
            }
          }
          console.log(data)
          $("#collect-evt-tb").removeClass("hidden")
          $("#collect-evt-tb-nodata").addClass("hidden")
          $('#collect-evt-tb').bootstrapTable('removeAll');
          $("#collect-evt-tb").bootstrapTable("load", data);
          // 设置表格宽度问题
//   let _width=$(".page-content").width()
//   $("#collect-evt-tb>tbody>tr.no-records-found>td").css("width",_width-40+"px")
          if (data) {
            if (data.rows !== undefined) {
              if (data.rows.length == 0) {
                $("#collect-evt-tb").addClass("hidden")
                $("#collect-evt-tb-nodata").removeClass("hidden")
              }
            }
          }
        }
      })
    } else if (dp_packet.return_value === 3) {
      $('.main-content').busyLoad("hide");
      ui_util.showFadeTip("没有可查询的数据")
      $("#collect-evt-tb").removeClass("hidden")
      $("#collect-evt-tb-nodata").addClass("hidden")
      $('#collect-evt-tb').bootstrapTable('removeAll');
      $("#collect-evt-tb").bootstrapTable("load", data);
    } else {
      $('.main-content').busyLoad("hide");
      ui_util.getErrorTips(dp_packet.return_value, "加载事件采集数据")
    }
    // 消息处理完成关闭连接
    tcp_client.close();
  }, config.host, config.port);

  // 发送消息
  tcp_client.send(collect_event_codec.packLoadCollectEvent(num, type, grade, beginDate, endDate, pageNo));
  $('.main-content').busyLoad("show", {
    background: "rgba(0, 0, 0, 0.55)",
    fontawesome: "fa fa-spinner fa-spin fa-3x fa-fw"
  })
}

/* 截取字符串-获取指定字符串内容*/
function getCaption(obj) {
  let index1 = obj.lastIndexOf("\:");
  let index2 = obj.lastIndexOf("\}");
  obj = obj.substring(index1 + 1, index2);
  return obj;
}

/**
 * 根据条件刷新事件采集图表数据.
 * @param type
 * @param grade
 * @param beginDate
 * @param endDate
 * @param is_collect
 */
function echartsInit(type, grade, beginDate, endDate, is_collect) {
  // 连接服务器
  tcp_client.connect(function (msgId, pb) {
    let msg = ProxyServer_pb.MSG_PCLoadEventStatics.deserializeBinary(pb)
    const countByDeviceTypeMap = msg.getCountByDeviceTypeMap().map_;
    const countByLevelMap = msg.getCountByLevelMap().map_;
    level_percent_data = [{value: 0, name: '紧急'}, {value: 0, name: '重要'}, {value: 0, name: '次要'}, {
      value: 0,
      name: '一般'
    }, {value: 0, name: '告知'}];
    evt_classific_data = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
    let new_arr = new Array();
    let new_arr2 = new Array();
    if (countByLevelMap) {
      for (const key in countByLevelMap) {
        new_arr.push(key)
      }
    }
    $.each(level_val_list, function (index, val) {
      if (new_arr.indexOf(level_val_list[index]) !== -1) {
        level_percent_data[index].value = countByLevelMap[level_val_list[index]].value;

      } else {
        level_percent_data[index].value = 0;
      }
    })
    if (countByDeviceTypeMap) {
      for (const key in countByDeviceTypeMap) {
        new_arr2.push(key)
      }
    }
    $.each(evt_val_list, function (index, val) {
      if (new_arr2.indexOf(evt_val_list[index]) !== -1) {
        evt_classific_data[index] = countByDeviceTypeMap[evt_val_list[index]].value;
      }
    })
    levelPercent()
    evtClassificStatis()
    // 消息处理完成关闭连接
    tcp_client.close();
  }, config.host, config.port);
  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPLoadEventStatics();
  sendMsg.setDeviceTypes(type);
  sendMsg.setEventLevels(grade);
  sendMsg.setBeginTime(beginDate);
  sendMsg.setEndTime(endDate);
  sendMsg.setIsCollect(is_collect);
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADEVENTSTATICS, sendMsg, user_sign);
}