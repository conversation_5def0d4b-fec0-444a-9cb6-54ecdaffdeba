'use strict';

const {ipc<PERSON>enderer} = require('electron');
const ProxyServer_pb = require('../../pb/ProxyServer_pb');
const PublicDefine_pb = require('../../pb/PublicDefine_pb');
const PlatformProxyServer_pb = require('../../pb/PlatformProxyServer_pb');
const util = require('../../../lib/util');
const ui_util = require('../ui_util');
const EventEmitter = require('events');
const tcp_client = require('../../../lib/tcp_client');
const config = require("../../../config.json");
const tcpInstance = require('../../../lib/v2/tls_client.js');
// 相关变量定义
const user_sign = $("body").attr("user_sign_info");
const user_name = $("body").attr("user_name_info");
const user_type = $("body").attr("roletype_info");
console.log("user_sign:", user_sign + user_name);

// 新增用户按钮点击事件
$('#btn_add').on('click', function () {
  $('#validateUserDlg').modal('show');
  $('#validateUserForm').get(0).reset()
  $('#opraType').val("add")
});
// 确认新增用户按钮点击事件
$('#sbbtn').on('click', function (e) {
  if ($("#sbbtn").hasClass("disabled")) {
    return;
  }
  if ($('#password').val() === $('#app_user_name').val()) {
    ui_util.showFadeTip('用户名和密码重复!');
    $('#password').focus();
    e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
    return;
  }
  if ($("#userDlgTitle").attr('wa-data') == 'edit') {
    changeUserGenerator();
  } else {
    // 用户名验重.
    let str = $('#app_user_name').val();
    if (str != "") {
      for (let i = 0; i < tableData.length; i++) {
        if (str == tableData[i].userName) {
          ui_util.showFadeTip('用户名已存在!');
          $('#app_user_name').focus();
          e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
          return;
        }
      }
    }
    addUserGenerator();
  }
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
});

// 点击确定删除用户
$('#removeUserBtn').on('click', function () {
  removeUserGenerator($('#remove_user_id').val());
});

let opraStr, opraUserId, opraGroupId, opraUserName, opraLoginIp, opraDesc, opraRemoveId
// 弹出密码校验框
$('#validateuser_btn').on('click', function (e) {
  if ($("#validateuser_btn").hasClass("disabled")) {
    return;
  }
  $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  let username = user_name
  let psd = $('#validateUserDlg #_password').val()
  opraStr = $('#opraStr').val()
  opraUserId = $('#opraUserId').val()
  opraGroupId = $('#opraGroupId').val()
  opraUserName = $('#opraUserName').val()
  opraLoginIp = $('#opraLoginIp').val()
  opraDesc = $('#opraDesc').val()
  opraRemoveId = $('#opraRemoveId').val()
  validateUserGenerator(username, psd);
})

function testIpc() {
  // 发送消息到主进程
  // console.log(ipcRenderer.sendSync('synchronous-message', 'ping')); // prints 'pong'
  ipcRenderer.on('asynchronous-reply', (event, arg) => {
    // console.log(arg); // prints 'pong'
  });
  ipcRenderer.send('asynchronous-message', 'ping');
}

/**
 * 初始化刷新用户管理表格.
 */
let tableData = new Array();

function createUserTable() {
  $("#user-table").jqGrid(
    {
      datatype: "local",
      height: "90%",
      autowidth: true,
      shrinkToFit: true,//按比例初始化列宽
      pager: '#user-pager',
      viewrecords: true,
      sortname: 'createDate',
      sortorder: 'desc',
      rowNum: 10,//每页显示数据个数
      rowList: [5, 10, 20, 50, 100],
      recordtext: "{0} - {1}\u3000共 {2} 条",
      emptyrecords: "当前没有人员信息记录",
      loadtext: "读取中...",
      pgtext: "{0} 共 {1} 页",
      colNames: ['序号', '角色', 'groupId', '用户名', '创建时间', '登录机ip', '说明', '权限', '操作'],
      colModel: [
        {name: 'id', index: 'id'},
        {name: 'groupName', index: 'groupName',},
        {name: 'groupId', index: 'groupId', hidden: true},
        {name: 'userName', index: 'userName'},
        {name: 'createDate', index: 'createDate', formatter: displayDate},
        {name: 'loginIp', index: 'loginIp',},
        {name: 'desc', index: 'desc', sortable: false},
        {name: 'authorities', index: 'authorities', sortable: false, formatter: displayAuthor},
        {
          name: 'operation', index: 'operation', sortable: false, formatter: displayButtons
        }]
    });

  // 清空和填充表格数据
  $("#user-table").jqGrid('clearGridData');
  for (let i = 0; i <= tableData.length; i++) {
    $("#user-table").jqGrid('addRowData', i + 1, tableData[i]);
  }
  // $(".main-content").getNiceScroll().resize();
// 数组数据分页.本地数据分页.
  $('.ui-pg-table.ui-pager-table #first_user-pager span')
    .addClass('ace-icon fa fa-angle-double-left bigger-100')
  $('.ui-pg-table.ui-pager-table #prev_user-pager span')
    .addClass('ace-icon fa fa-angle-left bigger-100')
  $('.ui-pg-table.ui-pager-table #next_user-pager span')
    .addClass('ace-icon fa fa-angle-right bigger-100')
  $('.ui-pg-table.ui-pager-table #last_user-pager span')
    .addClass('ace-icon fa fa-angle-double-right bigger-100')
  const localData = {page: 1, total: 2, records: "2", rows: []};
  localData.rows = tableData;
  localData.records = tableData.length;
  localData.total = (tableData.length % 2 == 0) ? (tableData.length / 2) : (Math.floor(tableData.length / 2) + 1);
  const reader = {
    root: function (obj) {
      return localData.rows;
    },
    page: function (obj) {
      return localData.page;
    },
    total: function (obj) {
      return localData.total;
    },
    records: function (obj) {
      return localData.records;
    },
    repeatitems: false
  };
  $("#user-table").setGridParam({
    data: localData.rows,
    reader: reader
  }).trigger('reloadGrid');

  /**\
   * 创造行末尾按钮并修改删除点击方法.
   */
  function displayDate(cellvalue, options, rowObject) {
    return new Date(cellvalue).toLocaleString()
  }

  function displayAuthor(cellvalue, options, rowObject) {
    let authorities = new Array()
    authorities = cellvalue;
    let author_lst_html = '<ul class="list-unstyled spaced">';
    if (authorities.length == 0) {
      author_lst_html += '没有权限</ul>'
    } else {
      for (let i = 0; i < authorities.length; i++) {
        if (i === authorities.length - 1) {
          author_lst_html += '<li style="margin:0;font-size:15px;"><i class="ace-icon fa fa-caret-right blue"></i>' + authorities[i].getAuthorityName() + '</li></ul>'
        } else {
          author_lst_html += '<li style="margin:0;font-size:15px;"><i class="ace-icon fa fa-caret-right blue"></i>' + authorities[i].getAuthorityName() + '</li>'
        }
      }
    }
    return "<div class='hidden-sm hidden-xs btn-group'>" +
      "<button class='btn btn-minier btn-success'" +
      "row-obj-str='" + author_lst_html + "'" +
      "onclick=\"$('#validateUserDlg').modal('show');\n" +
      "$('#validateUserForm').get(0).reset()\n" +
      "$('#opraType').val('see')\n" +
      "$('#opraStr').val($(this).attr('row-obj-str'))\">" +
      "<i class='ace-icon fa fa-eye bigger-80'>&nbsp;查看权限</i>" +
      "</button>" +
      "</div>";
  }

  function displayButtons(cellvalue, options, rowObject) {
      let role = rowObject.groupName;
      if(role=="操作员"){
          return "<div class='hidden-sm hidden-xs btn-group'>" +
              "<button class='btn btn-minier btn-inverse wa-mlr5'" +
              "row-obj-str='" + JSON.stringify(rowObject) + "'" +
              "onclick=\"$('#validateUserDlg').modal('show');\n" +
              "$('#validateUserForm').get(0).reset()\n" +
              "$('#opraType').val('edit')\n" +
              "$('#opraUserId').val(JSON.parse($(this).attr('row-obj-str')).id)\n" +
              "$('#opraGroupId').val(JSON.parse($(this).attr('row-obj-str')).groupId)\n" +
              "$('#opraUserName').val(JSON.parse($(this).attr('row-obj-str')).userName)\n" +
              "$('#opraLoginIp').val(JSON.parse($(this).attr('row-obj-str')).loginIp)\n" +
              "$('#opraDesc').val(JSON.parse($(this).attr('row-obj-str')).desc)\">" +
              "<i class='ace-icon fa fa-edit bigger-80'>&nbsp;修改</i>" +
              "</button>" +
              "<button class='btn btn-minier btn-danger'" +
              "row-id='" + rowObject.id + "'" +
              "onclick=\"$('#validateUserDlg').modal('show')\n" +
              "$('#validateUserForm').get(0).reset()\n" +
              "$('#opraType').val('del')\n" +
              "$('#opraRemoveId').val($(this).attr('row-id'))\">" +
              "<i class='ace-icon fa fa-trash-o bigger-80'>&nbsp;删除</i>" +
              "</button>" +
              "</div>";
      }else{
          return  "<div class='hidden-sm hidden-xs btn-group' style='display: none'>" +
              "<button class='btn btn-minier btn-inverse wa-mlr5'" +
              "row-obj-str='" + JSON.stringify(rowObject) + "'" +
              "onclick=\"$('#validateUserDlg').modal('show');\n" +
              "$('#validateUserForm').get(0).reset()\n" +
              "$('#opraType').val('edit')\n" +
              "$('#opraUserId').val(JSON.parse($(this).attr('row-obj-str')).id)\n" +
              "$('#opraGroupId').val(JSON.parse($(this).attr('row-obj-str')).groupId)\n" +
              "$('#opraUserName').val(JSON.parse($(this).attr('row-obj-str')).userName)\n" +
              "$('#opraLoginIp').val(JSON.parse($(this).attr('row-obj-str')).loginIp)\n" +
              "$('#opraDesc').val(JSON.parse($(this).attr('row-obj-str')).desc)\" >" +
              "</button>" +
              "<button class='btn btn-minier btn-danger'" +
              "row-id='" + rowObject.id + "'" +
              "onclick=\"$('#validateUserDlg').modal('show')\n" +
              "$('#validateUserForm').get(0).reset()\n" +
              "$('#opraType').val('del')\n" +
              "$('#opraRemoveId').val($(this).attr('row-id'))\">" +
              "</button>" +
              "</div>";
      }

  }

  jqgridColResize()
  tableResize();
}

/**
 * 重新调整jqgrid每列的宽度
 */
function jqgridColResize() {
  let td = $('#tdCompute')//获取计算实际列长度的容器
    , tds//临时保存列
    , arr = [];//用于保存最大的列宽
  //遍历每行获得每行中的最大列宽
  $('.ui-jqgrid-htable tr,.ui-jqgrid-btable tr:gt(0)').each(function () {
    $(this).find('td,th').each(function (idx) {
      arr[idx] = Math.max(arr[idx] ? arr[idx] : 0, td.html($(this).text())[0].offsetWidth);
    })
  });
  //设置页头单元格宽度
  $('.ui-jqgrid-labels th').each(function (idx) {
    this.style.width = arr[idx] + 'px'
  });
  //设置内容表格中控制单元格宽度的单元格，在第一行
  $('.ui-jqgrid-btable tr:eq(0) td').each(function (idx) {
    this.style.width = arr[idx] + 'px'
  });
  // 设置操作栏固定宽度
  $('.ui-jqgrid .ui-jqgrid-htable th:last-child').css("width", "99");
  $('.ui-jqgrid-btable>tbody>tr>td:last-child').css("width", "99");
}

/**
 * 表格自适应窗口
 */
function tableResize() {
  $(window).on('resize.jqGrid', function () {
    $("#user-table").jqGrid('setGridWidth', $(".page-content").width());
    jqgridColResize()
  });
  const parent_column = $("#user-table").closest('[class*="col-"]');
  $(document).on('settings.ace.jqGrid', function (ev, event_name, collapsed) {
    if (event_name == 'sidebar_collapsed' || event_name == 'main_container_fixed') {
      setTimeout(function () {
        $("#user-table").jqGrid('setGridWidth', parent_column.width());
        jqgridColResize()
      }, 0);
    }
  })
}

/**
 * 发送验证密码参数
 * @param username
 * @param psd
 */
function validateUserGenerator(username, psd) {
  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
  sendMsg.setUserName(username)
  sendMsg.setPassword(psd)
  tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg).then();
}


tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCVALIDATEUSER, (pb) => {
  const msg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(pb);
  if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
    if ($('#opraType').val() == "add") {
      $('#userDlg').modal('show');
      $('#userForm').get(0).reset();
      $('#userDlgTitle').text('新增用户');
      $('#userDlgTitle').attr('wa-data', 'add')
      // 清理原有数据
      $('#app_group_id').val(3);
      $('#app_user_name').val('');
      $('#password').val('');
      $('#confirmed_password').val('');
      $('#login_ip').val('');
      $('#desc').val('');
      // 启用相关输入框
      // $("#app_group_id").attr("readOnly", false);
      // $("#app_group_id").removeAttr("disabled");
      $("#app_user_name").attr("readOnly", false);
      $("#password").attr("readOnly", false);
      $("#confirmed_password").attr("readOnly", false);
      $("#ukeySwitch").addClass("hidden")
      $("#ukeyDesc").removeClass("hidden")
      $("#sbbtn").addClass("disabled")
    } else if ($('#opraType').val() == "see") {
      $('#authorityDlg').modal('show')
      $('#authorityDlg .modal-body').html(opraStr)
    } else if ($('#opraType').val() == "edit") {
      $('#userDlg').modal('show')
      $('#userForm').get(0).reset()
      $('#userDlgTitle').text('修改用户')
      $('#userDlgTitle').attr('wa-data', 'edit')
      $('#app_group_id').attr('readOnly', true)
      $('#app_group_id').attr('disabled', 'disabled')
      $('#app_user_name').attr('readOnly', true)
      $('#password').attr('readOnly', true)
      $('#confirmed_password').attr('readOnly', true)
      $('#app_user_id').val(opraUserId)
      $('#app_group_id').val(opraGroupId)
      $('#app_user_name').val(opraUserName)
      $('#password').val('********')
      $('#confirmed_password').val('********')
      $('#login_ip').val(opraLoginIp)
      $('#desc').val(opraDesc)
      $("#ukeyDesc").addClass("hidden")
      if (user_type === "1") {
        $("#ukeySwitch").removeClass("hidden")
      }
    } else if ($('#opraType').val() == "del") {
      $('#confirmDlg').modal('show')
      $('#remove_user_id').val(opraRemoveId)
    }
  } else {
    ui_util.getErrorTips(msg.getErrorCode(), '验证用户身份')
  }
})

/**
 * 页面定义的相关数据和初始化执行的相关操作
 */
function init() {
  loadUsersGenerator();
}

init();

/**
 * 发送加载用户请求
 * @param index 索引，从0开始
 * @param count 加载数量
 */
function loadUsersGenerator(index, count) {
  let sendMsg = new ProxyServer_pb.MSG_CPLoadAppUsers();
  sendMsg.setIndex(0);
  sendMsg.setCount(10);
  console.log(sendMsg)
  tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADAPPUSERS, sendMsg, user_sign)
}

/**
 * 发送添加用户请求
 */
function addUserGenerator() {
  let sendMsg = new ProxyServer_pb.MSG_CPAddAppUser();
  sendMsg.setAppGroupId($('#app_group_id').val());
  sendMsg.setAppUserName($('#app_user_name').val());
  sendMsg.setPassword(($('#password').val()));
  sendMsg.setLoginIp($('#login_ip').val());
  sendMsg.setDesc($('#desc').val());
  tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPADDAPPUSER, sendMsg, user_sign);
}

/**
 * 发送修改用户请求
 */
function changeUserGenerator() {
  let sendMsg = new ProxyServer_pb.MSG_CPChangeAppUser();
  sendMsg.setAppUserId($('#app_user_id').val());
  sendMsg.setLoginIp($('#login_ip').val());
  sendMsg.setDesc($('#desc').val());
  sendMsg.setChangeKey(false);
  tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPCHANGEAPPUSER, sendMsg, user_sign);
}

/**
 * 发送删除用户请求
 * @param userId
 */
function removeUserGenerator(userId) {
  let sendMsg = new ProxyServer_pb.MSG_CPRemoveAppUser();
  sendMsg.setAppUserId(userId);
  tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPREMOVEAPPUSER, sendMsg, user_sign);
}

tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCADDAPPUSER, (pb) => {
  const msg = ProxyServer_pb.MSG_PCAddAppUser.deserializeBinary(pb);
  if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
    ui_util.showFadeTip('添加用户成功');
    $('#userDlg').modal('hide');
    // 重新加载用户数据
    loadUsersGenerator();
  } else {
    ui_util.getErrorTips(msg.getErrorCode(), '添加用户')
  }
})

tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCREMOVEAPPUSER, (pb) => {
  const msg = ProxyServer_pb.MSG_PCRemoveAppUser.deserializeBinary(pb);
  if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
    ui_util.showFadeTip('删除用户成功');
    // 重新加载用户数据
    loadUsersGenerator();
  } else {
    ui_util.getErrorTips(msg.getErrorCode(), '删除用户')
  }
})

tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCCHANGEAPPUSER, (pb) => {
  const msg = ProxyServer_pb.MSG_PCChangeAppUser.deserializeBinary(pb);
  if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
    ui_util.showFadeTip('修改用户成功');
    $('#userDlg').modal('hide');
    // 重新加载用户数据
    loadUsersGenerator();
  } else {
    ui_util.getErrorTips(msg.getErrorCode(), '修改用户')
  }
})

tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCLOADAPPUSERS, (pb) => {
  const msg = ProxyServer_pb.MSG_PCLoadAppUsers.deserializeBinary(pb);
  if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
    let users = msg.getUsersList();
    tableData = [];
    for (let i = 0; i < users.length; ++i) {
      let user = users[i];
      let row = {
        id: user.getId(),
        groupName: user.getAppGroupName(),
        groupId: user.getAppGroupId(),
        userName: user.getAppUserName(),
        // createDate: new Date(user.getCreateTime()).toLocaleString(),
        createDate: user.getCreateTime(),
        loginIp: user.getLoginIp(),
        desc: user.getDesc(),
        authorities: user.getAuthoritiesList(),
      };
      tableData.push(row);
    }
    // console.log(tableData)
    createUserTable();

  } else {
    ui_util.showFadeTip('系统中没有任何用户');
  }
})
