'use strict';

const {ipc<PERSON><PERSON>er} = require('electron');
const ProxyServer_pb = require('../pb/ProxyServer_pb');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const PlatformProxyServer_pb = require('../pb/PlatformProxyServer_pb');
const ui_util = require('./ui_util');
const EventEmitter = require('events');
const util = require('../../lib/util');
const safe = require('../../lib/safe');
const config = require("../../config.json");
const logger = require('../../lib/util').logger;
const temp_config = require('../../../temp_config.json');
const tcpInstance = require('../../lib/v2/tls_client.js');

// 更新头部时间和底部年份,版本号
const myDate = new Date()
const _year=myDate.getFullYear(),_month=myDate.getMonth()+1,_day=myDate.getDate(),_week="日一二三四五六".charAt(new Date().getDay());
$("#date-time").text(_year+"年"+_month+"月"+_day+"日 星期"+ _week)
$('#new_year').text(myDate.getFullYear());

// 登录点击事件
$('#loginBtn').on('click', function (e) {
    if ($("#username").val() == "") {
        $("#username").focus();
        ui_util.showRetentionTip('请输入用户名！',"loginForm");
        return;
    } else if ($("#password").val() == "") {
        $("#password").focus();
        ui_util.showRetentionTip('请输入密码！',"loginForm");
        return;
    }
    let ip_value = $('input#ip').val()
    let port_value = $('input#port').val()
    let pattIp=/(?=(\b|\D))(((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))\.){3}((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))(?=(\b|\D))/;
    let pattPort = /([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-4]\d{4}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])/;
    if (!pattIp.test(ip_value)) {
        ui_util.showRetentionTip('请输入正确ip！',"loginForm");
        return false;
    }
    if(!pattPort.test(port_value)){
        ui_util.showRetentionTip('请输入正确端口号！',"loginForm");
        return false;
    }
    $("#retentionDanger [data-dismiss='alert']").click();
    //发送给服务器host
    temp_config.host = ip_value;
    temp_config.port = port_value;
    config.host = ip_value;
    config.port = port_value;
    loginGenerator($('#username').val(), $('#password').val())
    e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
});
//点击回车事件
$('input[wa-name="loginput"]').on('keydown', function (event) {
    if (event.keyCode == 13) {
        event.returnValue = false;
        event.cancel = true;
        $('#loginBtn').click();
    }
})
// 点击设置-修改密码
$('#settingBtn').on('click', function () {
    let source = "main"
    bdhtmlReplacePsdModify(source)
})
// 点击退出登录
$('#logoutBtn').on('click', function () {
    $('#confirmLogoutDlg').modal('show');
})
$('#logout_btn').on('click', function () {
    top.location = "./login.html";
})

// 初始化读取json文件中host
$('input#ip').val(temp_config.host)
$('input#port').val(temp_config.port)

// 弹出密码校验框
$('#psd_validateuser_btn').on('click',function (e) {
    if ($("#psd_validateuser_btn").hasClass("disabled")) {
        return;
    }
    // $('#psd_validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
    e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
    let username=$("body").attr("user_name_info")
    let psd=$('#psd_validateUserDlg #psd_password').val()
    validateUserGenerator(username,psd);

});

/**
 * 发送登录请求
 * @param userName 用户名
 * @param password 密码
 */
let socket = null;
let timeOutPrompt;
async function loginGenerator(userName, password) {
    await tcpInstance.connect(temp_config.host, temp_config.port);
    setTimeout(async function () {
        let sendMsg = new ProxyServer_pb.MSG_CPLogin();
        sendMsg.setUserName(userName);
        sendMsg.setPassword((password));
        const json = JSON.stringify(sendMsg.toObject());
        console.log(`loginGenerator`, json);
        await tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOGIN, sendMsg);
    }, 0)

    $('.main-content').busyLoad("show", {
        background: "rgba(0, 0, 0, 0.55)",
        fontawesome: "fa fa-spinner fa-spin fa-3x fa-fw"
    })
      timeOutPrompt= setTimeout(function(){
        $('.main-content').busyLoad("hide");
        ui_util.showFadeTip("登录超时，请确认与装置的连接是否正常")
    },10000);
}

/**
 * 发送加载告警信息请求
 */
function loadWarningMsg() {
    // 发送消息
    spopGenerator()
}

function spopGenerator() {
    // 连接服务器
    let sendMsg = new ProxyServer_pb.MSG_CPLoadWarning();
    tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADWARNING, sendMsg, user_sign, socket);
    // console.log('spopGenerator')
}

let menus = [];
let roleType, user_sign, user_name, user_id, sidebarCode,current_machine_code;
let menu_info;
let version;
let time_stamp;

// 登录消息处理
tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCLOGIN, (pb) => {
    clearTimeout(timeOutPrompt);
    $('.main-content').busyLoad("hide");
    if (config.load_warning_interval > 0) {
        setInterval(function () {
            loadWarningMsg();
        }, config.load_warning_interval);
    }
    // json文件替换host,port
    util.writeJson(temp_config.host,temp_config.port);
    util.writeTempJson(temp_config.host, temp_config.port);
    const msg = ProxyServer_pb.MSG_PCLogin.deserializeBinary(pb);
    roleType = msg.getRoleType(), user_sign = msg.getUserSign(), user_name = msg.getUserName(),current_machine_code = msg.getCurrentMachineCode(),
        user_id = msg.getAppUserId();// 相关变量定义
    menus = msg.getMenusList();
    console.log("menus", menus);
    menu_info = menus.join(",");
    console.log("menu_info", menu_info);
    version=msg.getVersion();
    util.setAesKey(msg.getCommuKey(), msg.getFileKey());

    // send safe param
    {
        let sm4param = Buffer.alloc(32);
        sm4param.write(msg.getCommuKey(), 0);
        sm4param.write(msg.getCommuIv(), 16);
        safe.send_safe_msg(safe.PACKET_TYPE_SET_PARAM, sm4param);
    }
    if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
        time_stamp=msg.getDeviceTime();
        util.setUserSign(user_sign);
        switch (msg.getRoleType()) {
            case PublicDefine_pb.RoleType.ROLETYPE_SYSADMIN:
            case PublicDefine_pb.RoleType.ROLETYPE_ADMIN:
                sidebarCode = "admin_index"
                bodyhtmlReplace()
                break;
            case PublicDefine_pb.RoleType.ROLETYPE_AUDITOR:
                sidebarCode = "auditor_index"
                bodyhtmlReplace()
                break;
            case PublicDefine_pb.RoleType.ROLETYPE_OPERATOR:
                sidebarCode = "operator_index"
                bodyhtmlReplace()
                break;
            default:
                break;
        }
    } else if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_INVALIDPASSWORD) {
        if((msg.getAllowedFailedCount()-msg.getFailedCount())===0){
            ui_util.showFadeTip('用户名或密码或UKey错误!在首次登录失败后' + msg.getSafeTime() + '秒内,下次输错,账号将被锁定',3000);
        }else{
            ui_util.showFadeTip('用户名或密码或UKey错误!在首次登录失败后' + msg.getSafeTime() + '秒内再连续输错' + (msg.getAllowedFailedCount()-msg.getFailedCount()) + '次,账号将被锁定',3000);
        }
    } else if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_LOGINTRIEDTIMESLIMIT) {
        ui_util.showFadeTip('登录超过失败次数限制，请' + msg.getCoolDownSec() + '秒之后再次登录',3000);
    } else if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_PASSWORDEXPIRED) {
        let source = "login"
        time_stamp=msg.getDeviceTime();
        bdhtmlReplacePsdModify(source,msg.getErrorCode())
    } else if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_NEEDUPDATEPASSWORD) {
        let source = "login"
        time_stamp=msg.getDeviceTime();
        bdhtmlReplacePsdModify(source,msg.getErrorCode())
    } else {
        ui_util.getErrorTips(msg.getErrorCode(), "登录")
    }
})

// 通知消息处理
tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCNOTIFYWARNING, (pb) => {
    const msg = ProxyServer_pb.MSG_PCNotifyWarning.deserializeBinary(pb);
    // console.log("msg",msg)
    if (msg.getMessage() !== "" || msg.getMessage() !== "[]") {
        let _msg = msg.getMessage()
        _msg = _msg.substring(1, _msg.length - 2)
        let _array = _msg.split("], ")
        for (let i = 0; i < _array.length; i++) {
            let _childMsg = _array[i].substring(1, _array[i].length)
            if (_childMsg) {
                let _childArray = _childMsg.split(",")
                let length = _childArray.length
                warning_msg = _childArray[length - 1]
                spop({
                    template: '<h4 class="spop-title">' + warning_msg + '</h4>',
                    // template: warning_msg,
                    group: 'submit-satus',
                    style: 'info',
                    position: 'bottom-right',
                    autoclose: 5000,
                });
            }
        }
    }
})

let thisTimeStamp;
/**
 * 首页/登陆页跳转密码修改页
 */
function bdhtmlReplacePsdModify(source,errorcode) {
    $.ajax({
        url: "main_index.html",
        type: "get",
        dataType: "html",
        success: function (data) {
            $("title").text("电力监控系统网络安全监测装置(基础型)")
            $("body").removeClass("login-layout blur-login").addClass("no-skin")
            $("body").attr("user_sign_info", user_sign)
            $("body").attr("user_name_info", user_name)
            $("body").attr("roleType_info", roleType)
            $("body").attr("user_id_info", user_id)
            $("body").attr("version_info", version)
            $("body").attr("menu_info", menu_info)
            $("body").attr("device_time_info",time_stamp)
            thisTimeStamp=parseInt($("body").attr("device_time_info"))
            if (source == "main") {
                $("body").attr("source", "main")
                $(".l-b-nav>li").removeClass('l-b-active');
                let imageList=$(".l-b-nav>li>.l-b-navbox-2>img")
                $.each(imageList,function (i,val) {
                    let imageSrc=$(val).attr('src')
                    let newImageSrc=imageSrc.replace('_selected','')
                    $(val).attr('src',newImageSrc)
                })
                $(".l-b-navbar").html()
            } else if (source == "login") {
                $("body").attr("source", "login")
                $("#bd_container").html(data);
                ui_util.getErrorTips(errorcode, "")
                // const t = setTimeout(ui_util.showFadeTip('首次登录请修改密码'), 10000)
                $("#sidebar").remove()
                $(".l-b-navbar").html()
            }
            // 修改右上角当前登录信息
            $('#userName').text($("body").attr("user_name_info"));
            /*setInterval(function () {
              thisTimeStamp +=1
              $('#deciveTime').text(util.getThisDate(thisTimeStamp));
            },1000)*/
            // 跳转密码修改页
            $.ajax({
                url: "modify_password.html",
                type: "get",
                dataType: "html",
                success: function (data) {
                    $(".parent_content").html(data);
                        $.ajax({
                        url: "footer.html",
                        type: "get",
                        dataType: "html",
                        success:function (data) {
                            let footerData=data
                            $("#bd_container").append($(footerData))
                            //修改页脚版权年份
                            const now = new Date()
                            $('#new_year').text(now.getFullYear());
                            $(".footer #version").html($("body").attr("version_info"))
                            $.busyLoadFull("hide");
                            if(errorcode=== PublicDefine_pb.ErrorCode.ERRORCODE_NEEDUPDATEPASSWORD){
                                $('#buttonState').prop('checked', true)  // 选中
                                document.getElementById("buttonState").disabled=true;
                            }
                        }
                    })
                }
            });
        }
    });
}

/**
 * 登录后进入首页并且动态加载左侧栏导航
 * @param datacode
 * @param roleType
 * @param user_sign
 * @param user_name
 */
let warning_msg;
function bodyhtmlReplace() {
    $.ajax({
        url: "main_index.html",
        type: "get",
        dataType: "html",
        success: function (data) {
            $("title").text("电力监控系统网络安全监测装置(基础型)")
            $("#bd_container").html(data);
            $("body").removeClass("login-layout blur-login").addClass("no-skin")
            console.log("user_sign:", user_sign, user_name, roleType, user_id);
            $("body").attr("user_sign_info", user_sign)
            $("body").attr("user_name_info", user_name)
            $("body").attr("roleType_info", roleType)
            $("body").attr("user_id_info", user_id)
            $("body").attr("menu_info", menu_info)
            $("body").attr("version_info", version)
            $("body").attr("device_time_info",time_stamp)
            $("body").attr("current_machine_code",current_machine_code)
            thisTimeStamp=parseInt($("body").attr("device_time_info"))
            if(sidebarCode==="admin_index"){
                // 点击重启装置事件
                $('#resetBtn').removeClass("hidden")
                $('#resetBtn').on('click', function () {
                    $('#confirmResetDlg').modal('show');
                })
                $('#reset_btn').on('click', function () {
                    loadRebootGenerator()
                })
            }else{
                $('#resetBtn').addClass("hidden")
            }
            $.ajax({
                url: "footer.html",
                type: "get",
                dataType: "html",
                success:function (data) {
                    let footerData=data
                    $("#bd_container").append($(footerData))
                    //修改页脚版权年份
                    const now = new Date()
                    $('#new_year').text(now.getFullYear());
                    $(".footer #version").html(version)
                }
            })
// 修改右上角当前登录信息.
            $('#userName').text(user_name);
            setInterval(function () {
                thisTimeStamp +=1
                $('#deciveTime').text(util.getThisDate(thisTimeStamp));
            },1000)
            if(Math.abs(time_stamp-util.getTimestamp())>30){
                ui_util.showFadeTip('装置与客户端所在机器时间误差大于30秒，请修改相应的时间！', 10000)
            }
            // 动态加载左侧栏导航
            $.ajax({
                url: sidebarCode + ".html",
                type: "get",
                dataType: "html",
                success: function (data) {
                    $("#sidebar").html(data);
                    if(config.enableDynamicNav===true){

                    }
                    let datacode, datacode2, datacode3;
// 侧栏导航切换-含一二级菜单
                    $(".l-b-nav>li").on("click",function () {
                        $(".l-b-nav>li").removeClass('l-b-active');
                        $(this).addClass('l-b-active');
                        let imageList=$(".l-b-nav>li>.l-b-navbox-2>img")
                        $.each(imageList,function (i,val) {
                            let imageSrc=$(val).attr('src')
                            let newImageSrc=imageSrc.replace('_selected','')
                            $(val).attr('src',newImageSrc)
                        })
                        let imgSrc=$(".l-b-nav>li.l-b-active>.l-b-navbox-2>img").attr('src')
                        let newImgSrc=imgSrc.slice(0,-4)+'_selected.png'
                        $(".l-b-nav>li.l-b-active>.l-b-navbox-2>img").attr('src',newImgSrc)
                        let data_nav_id=$(this).attr("data-nav-id").split(",")
                        let data_nav_name=$(this).attr("data-nav-name").split(",")
                        let htmlNavbar='<div class="container-fluid">\n' +
                            '<div class="navbar-header">\n' +
                            '<a class="navbar-brand"><strong>'+$(this).attr("data-tt")+'</strong></a>\n' +
                            '</div>\n' +
                            '<div class="collapse navbar-collapse" id="bs-example-navbar-collapse-6">\n' +
                            '<ul class="nav navbar-nav">';
                        for(let i=0;i<data_nav_id.length;i++){
                            if(i==0){
                                htmlNavbar +='<li class="active" data-code="'+data_nav_id[i]+'"><a href="javascript:;"><i style="font-size: 15px">'+data_nav_name[i]+'</i></a></li>'
                            }else if(i==data_nav_id.length-1){
                                htmlNavbar +='<li class data-code="'+data_nav_id[i]+'"><a href="javascript:;">\n' +
                                    '<i style="font-size: 15px">'+data_nav_name[i]+'</i>\n' +
                                    '</a></li></ul></div></div>';
                            }else{
                                htmlNavbar +='<li class data-code="'+data_nav_id[i]+'"><a href="javascript:;">\n' +
                                    '<i style="font-size: 15px">'+data_nav_name[i]+'</i></a></li>'
                            }
                        }
                        $("nav.navbar").html(htmlNavbar)
                        let datacd;
                        // 单功能页头部标签页导航切换.
                        $(".l-b-navbar .navbar-nav>li").on("click",function () {
                            $(".l-b-navbar .navbar-nav>li").removeClass("active")
                            $(this).addClass("active")
                            datacd= $(this).attr("data-code");
                            if (datacd) {
                                $.ajax({
                                    url: datacd + ".html",
                                    type: "get",
                                    dataType: "html",
                                    success: function (data) {
                                        $(".parent_content").html(data);
                                    }
                                });
                            }
                        })
                        datacode = $(".l-b-nav>li.l-b-active").attr("data-code");
                        if (datacode) {
                            $.ajax({
                                url: datacode + ".html",
                                type: "get",
                                dataType: "html",
                                success: function (data) {
                                    $(".parent_content").html(data);
                                }
                            });
                        }
                    })
                    // 初始化进入第一个功能页

                    let fst_nav_li=$(".l-b-nav>li:first-child")
                    let fst_nav_li_tt=fst_nav_li.attr("data-tt")
                    let fst_nav_li_name=fst_nav_li.attr("data-nav-name").split(',')[0]
                    $("nav.navbar").html('<div class="container-fluid">\n' +
                        '<div class="navbar-header">\n' +
                        '<a class="navbar-brand"><strong>'+fst_nav_li_tt+'</strong></a>\n' +
                        '</div>\n' +
                        '<div class="collapse navbar-collapse">\n' +
                        '<ul class="nav navbar-nav">\n' +
                        '<li class="active" data-code="personal_mgr"><a href="javascript:;"><i\n' +
                        ' style="font-size: 15px">'+fst_nav_li_name+'</i></a></li>\n' +
                        '</ul>\n' +
                        '</div>\n' +
                        '</div>')
                    datacode2 = $(".l-b-nav>li:first-child").attr("data-code");
                    $(".l-b-nav>li:first-child").addClass("l-b-active")
                    let imgSrc=$(".l-b-nav>li.l-b-active>.l-b-navbox-2>img").attr('src')
                    let newImgSrc=imgSrc.slice(0,-4)+'_selected.png'
                    $(".l-b-nav>li.l-b-active>.l-b-navbox-2>img").attr('src',newImgSrc)
                    $.ajax({
                        url: datacode2 + ".html",
                        type: "get",
                        dataType: "html",
                        success: function (data) {
                            $(".parent_content").html(data);
                            let timer=null;
                            /**
                             * 倒计时锁屏
                             * @param times
                             */
                            let sendMsg = new ProxyServer_pb.MSG_CPLoadGeneralParams();
                            sendMsg.addKeys(PublicDefine_pb.GeneralParamKey.PARAMKEY_CLIENTLOCKTIMEOUT)
                            tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADGENERALPARAMS, sendMsg).then();
                            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCLOADGENERALPARAMS, setCountDownTime)
                        }
                    });
                    return false
                }
            })
        }
    });
}
// TODO 在登录成功之后，需要获取到数据库返回的在线锁屏时间，并且打开计时器
function setCountDownTime(pb) {
    console.log('客户端超时配置res', pb)
    let timer=null;
    const msg = ProxyServer_pb.MSG_PCLoadGeneralParams.deserializeBinary(pb);
    let lock_timeout = 300; // 锁屏时间300秒 默认
    const paramsList = msg.getParamsList();
    try {
        lock_timeout = paramsList[0].getValue()
    } catch (e) {
        console.log('get lock_timeout err => ', e)
    }
    config.lock_timeout = lock_timeout
    console.log('获取锁屏时间(秒)', paramsList[0].getValue())
    function countDown(times) {
        timer=setInterval(function () {
            times --;
            // console.log(times)
            if(times<=0){
                // $('#psd_validateUserDlg').modal('show');
                $('#psd_validateUserForm').get(0).reset()
                clearInterval(timer);
                ipcRenderer.send('asynchronous-message', 'lock-timeout');
                let sendMsg = new ProxyServer_pb.MSG_CPAddCustomRunLog();
                sendMsg.setKind(true);
                sendMsg.setType(PublicDefine_pb.LogType.LOG_TYPE_LOGIN);
                sendMsg.setMessage("登录超时，断开连接。用户："+user_name);
                sendMsg.setLevel(PublicDefine_pb.LogLevel.LOG_LEVEL_INFO);
                tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPADDCUSTOMRUNLOG, sendMsg, user_sign,socket);
            }
        },1000)
    }
    // 鼠标和键盘监听触发
    document.onmousemove=function(){
        clearInterval(timer);
        countDown(config.lock_timeout)
    }
    document.onmousedown=function(){
        clearInterval(timer);
        countDown(config.lock_timeout)
    }
    document.oncontextmenu=function(){
        clearInterval(timer);
        countDown(config.lock_timeout)
    }
    document.onkeydown=function(){
        clearInterval(timer);
        countDown(config.lock_timeout)
    }
    countDown(config.lock_timeout)
}

/**
 * 发送验证密码参数
 * @param username
 * @param psd
 */
function validateUserGenerator(username,psd){
    // 发送消息
    let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
    sendMsg.setUserName(username)
    sendMsg.setPassword(psd)
    tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg, $("body").attr("user_sign_info"), socket);
}

tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCVALIDATEUSER, (pb) => {
    const msg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(pb);
    if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
        //ui_util.showFadeTip('验证用户通过!');
        $('#psd_validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
    } else {
        ui_util.getErrorTips(msg.getErrorCode(), '验证用户身份')
    }
})

// 添加简单的异常处理
process.on('uncaughtException', function (err) {
    ui_util.showFadeTip('操作失败，应用出现错误，请稍后再试。', 30);
    $('.main-content').busyLoad("hide");
    // 打印出错误
    console.error('uncaughtException', err);
    // console.error(err.stack);
});

/**
 * 客户端请求重启
 */
function loadRebootGenerator(){
    // 发送消息
    let sendMsg = new ProxyServer_pb.MSG_CPReboot();
    sendMsg.setServiceName("")
    tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPREBOOT, sendMsg, $("body").attr("user_sign_info"), socket);
}

tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCREBOOT, (pb) => {
    const msg = ProxyServer_pb.MSG_PCReboot.deserializeBinary(pb);
    const error_code = msg.getErrorCode();
    const text="客户端重启"
    if (error_code === 0) {
        ui_util.showFadeTip('客户端重启成功!');
    } else {
        ui_util.getErrorTips(error_code,text)
    }
})
