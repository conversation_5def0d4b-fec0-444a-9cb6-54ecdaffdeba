'use strict';

const ProxyServer_pb = require('../pb/ProxyServer_pb');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const util = require('../../lib/util');
const ui_util = require('./ui_util');
const tcp_client = require('../../lib/tcp_client');
const config = require("../../config.json");
const device_codec = require('../platform_codec/device_codec');
// 相关变量定义
const user_sign = $("body").attr("user_sign_info");
const user_name = $("body").attr("user_name_info");
console.log("user_sign:", user_sign + user_name);

$('.parent_content').busyLoad("hide");

let file="",filePath="",fileName="device.cfg";
// 初始化选择文件夹路径&选择文件
$('#select_folder').ace_file_input({
  no_file:'请选择文件夹',
  btn_choose:'选择',
  btn_change:null,
  droppable:false,
  onchange:null,
  thumbnail:false
});
document.querySelector('#select_folder').addEventListener('change', e => {
  for (let entry of e.target.files){
    console.log(entry.name, entry.path);
    filePath=entry.path
  }
});
$('#select_file').ace_file_input({
  no_file:'请选择文件',
  btn_choose:'选择',
  btn_change:null,
  droppable:false,
  onchange:null,
  thumbnail:false,
  allowExt: ['cfg'],
}).on('change', function(){
  let fileDir = $(this).val();
  let suffix = fileDir.substr(fileDir.lastIndexOf("."));
  if ("" == fileDir||".cfg" != suffix) {
    ui_util.showFadeTip('请选择.cfg后缀文件!');
    return false;
  }
  // console.log($(this).data('ace_input_files'));
  file=$(this).data('ace_input_files')[0].path
});
// 导入进度条动画加载&导出
$('#import').on('click', function () {
  $('#validateUserDlg').modal('show');
  $('#validateUserForm').get(0).reset()
  $('#opraType').val(1)
})
$('#export').on('click', function () {
  $('#validateUserDlg').modal('show');
  $('#validateUserForm').get(0).reset()
  $('#opraType').val(2)
})
$('#port_btn').on('click',function (e) {
  if ($("#port_btn").hasClass("disabled")) {
    return;
  }
  if(!($("#select_file").next("span.ace-file-container").hasClass("selected")
    ||$("#select_folder").next("span.ace-file-container").hasClass("selected"))){
    ui_util.showFadeTip('请选择文件或文件路径!')
    return;
  }
  $('#confirmPortDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  if($('#confirmPortDlg .modal-title').attr("wa-name")=="import_tt"){
    importDevices(file,fileName)
  }else if($('#confirmPortDlg .modal-title').attr("wa-name")=="export_tt"){
    exportDevices(filePath,fileName)
  }
})

// 弹出密码校验框
$('#validateuser_btn').on('click',function (e) {
  if ($("#validateuser_btn").hasClass("disabled")) {
    return;
  }
  $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  let username=user_name
  let psd=$('#validateUserDlg #password').val()
  validateUserGenerator(username,psd);
})

let deviceData = new Array();
// 设映射，key：设备id，value：设备信息，MSG_Device
let deviceMap = new Map();
let initData=new Array(),key=1;
// 条件筛选刷新表格
let type = $('#deviceTypeSelect [name="device-type"]:checked').val(),
  statis = $('#deviceStatisSelect [name="device-statis"]:checked').val(),
  search = $.trim($('#search').val());
$('#search').on('keydown', function (e) {
  if (e.keyCode == 13) {
    e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
    $('#query').click();
  }
})
$('#query').on('click', function () {
  type = $('#deviceTypeSelect [name="device-type"]:checked').val();
  statis = $('#deviceStatisSelect [name="device-statis"]:checked').val();
  search = $.trim($('#search').val());
  key=2;
  loadDeviceidsGenerator()
})

// 新增设备
$('#add_device').on('click', function () {
  $('#addDeviceDlg').modal('show');
  $('#snmp_msg').addClass('hidden')
  $('#addDeviceForm').get(0).reset();
  $('#addDeviceTitle').attr('wa-data', 'add')
  $('#addDeviceForm .form-group .form-control').removeAttr('disabled')
  $('#save_device_btn').show()
  $('#save_device_btn').addClass('disabled')
})
// 保存新增/修改设备参数
$('#save_device_btn').on('click', function (e) {
  if ($("#save_device_btn").hasClass("disabled")) {
    return false;
  }
  let ip=$('#device_ip').val();
  let ip2=$('#device_ip2').val();
  // $('#addDeviceDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  if ($("#addDeviceTitle").attr('wa-data') == 'add') {
    if (ip != "") {
        if ($('#device_ip').val() == $('#device_ip2').val()) {
          ui_util.showFadeTip('不能输入相同ip!');
          $('#device_ip').focus();
          return false;
        } else {
          for (let i = 0; i < initData.length; i++) {
            if (ip == initData[i].deviceIp) {
              let deviceName1 = initData[i].deviceName;
              ui_util.showFadeTip('ip已存在,重复的设备名称是'+deviceName1+'!');
              $('#device_ip').focus();
              return false;
            } else if (ip == initData[i].deviceIp2) {
              let deviceName2 = initData[i].deviceName;
              ui_util.showFadeTip('ip已存在,重复的设备名称是'+deviceName2+'!');
              $('#device_ip').focus();
              return false;
            }
          }
        }
    }
    if (ip2 != "") {
        if ($('#device_ip').val() == $('#device_ip2').val()) {
          ui_util.showFadeTip('不能输入相同ip!');
          $('#device_ip2').focus();
          return false;
        } else {
          for (let i = 0; i < initData.length; i++) {
            if (ip2 == initData[i].deviceIp) {
              let deviceName1 = initData[i].deviceName;
              ui_util.showFadeTip('ip2已存在,重复的设备名称是'+deviceName1+'!');
              $('#device_ip2').focus();
              return false;
            } else if (ip2 == initData[i].deviceIp2) {
              let deviceName2 = initData[i].deviceName;
              ui_util.showFadeTip('ip2已存在,重复的设备名称是'+deviceName2+'!');
              $('#device_ip2').focus();
              return false;
            }
          }
        }
    }
    addDeviceData();
  } else {
    if (ip != "") {
      if($('#device_ip').val() !== $('#old_ip1').val()){
        if ($('#device_ip').val() == $('#device_ip2').val()) {
          ui_util.showFadeTip('不能输入相同ip!');
          $('#device_ip').focus();
          return false;
        } else {
          for (let i = 0; i < initData.length; i++) {
            if (ip == initData[i].deviceIp) {
              let deviceName1 = initData[i].deviceName;
              ui_util.showFadeTip('ip已存在,重复的设备名称是'+deviceName1+'!');
              $('#device_ip').focus();
              return false;
            } else if (ip == initData[i].deviceIp2) {
              let deviceName2 = initData[i].deviceName;
              ui_util.showFadeTip('ip已存在,重复的设备名称是'+deviceName2+'!');
              $('#device_ip').focus();
              return false;
            }
          }
        }
      }
    }
    if (ip2 != "") {
      if($('#device_ip2').val() !== $('#old_ip2').val()){
        if ($('#device_ip').val() == $('#device_ip2').val()) {
          ui_util.showFadeTip('不能输入相同ip!');
          $('#device_ip2').focus();
          return false;
        } else {
          for (let i = 0; i < initData.length; i++) {
            if (ip2 == initData[i].deviceIp) {
              let deviceName1 = initData[i].deviceName;
              ui_util.showFadeTip('ip2已存在,重复的设备名称是'+deviceName1+'!');
              $('#device_ip2').focus();
              return false;
            } else if (ip2 == initData[i].deviceIp2) {
              let deviceName2 = initData[i].deviceName;
              ui_util.showFadeTip('ip2已存在,重复的设备名称是'+deviceName2+'!');
              $('#device_ip2').focus();
              return false;
            }
          }
        }
      }
    }
    updateDeviceData();
  }
})

// 删除设备参数
$('#remove_device_btn').on('click', function () {
  removeDeviceData();
});

// 设备类型选中事件
$('#device_type').change(function () {
  let options = $("#device_type option:selected");
  if (options.val() == "SW") {
    $('#snmp_msg').removeClass("hidden");
    $('#snmp_version').val("1");
    $('#snmp_auth').attr("disabled", "disabled");
    $('#snmp_auth').val("0");
    $('#snmp_encrypt').attr("disabled", "disabled");
    $('#snmp_encrypt').val("0");
    $('#snmp_username').attr("disabled", "disabled");
    $('#snmp_username').val("");
    $('#_groupname').removeClass("hidden")
    $('#_psd').addClass("hidden")
  } else {
    $('#snmp_msg').addClass("hidden");
  }
})
// snmp版本选中事件
$('#snmp_version').change(function () {
  let options = $("#snmp_version option:selected");
  if (options.val() == "1") {
    $('#snmp_username').attr("disabled", "disabled");
    $('#snmp_username').val("");
    $('#snmp_auth').attr("disabled", "disabled");
    $('#snmp_auth').val("0");
    $('#snmp_encrypt').attr("disabled", "disabled");
    $('#snmp_encrypt').val("0");
    $('#_groupname').removeClass("hidden")
    $('#_psd').addClass("hidden")
  } else {
    $('#snmp_username').removeAttr("disabled");
    $('#snmp_username').val("");
    $('#snmp_auth').removeAttr("disabled");
    $('#snmp_encrypt').removeAttr("disabled");
    $('#_groupname').addClass("hidden")
    $('#_psd').removeClass("hidden")
  }
})

/**
 * 资产状态统计图
 */
function assetStatusStatics() {
  let $assetStatusStatics = document.getElementById('asset-status-statics')
  $assetStatusStatics.style.width = (window.innerWidth - 138) + 'px';
  let assetStatusStatics = echarts.init($assetStatusStatics,'shine');
  let dataAxis = ['防火墙', '横向正向隔离装置', '横向反向隔离装置', '服务器', '交换机','纵向加密装置','防病毒系统','入侵检测系统','数据库', '网络安全监测装置'],
    data2 = [0, 0, 0, 0, 0, 0,0,0,0,0],
    data3 = [0, 0, 0, 0, 0, 0,0,0,0,0];
  let online = 0, sum = deviceData.length;
  $.each(deviceData, function (n, val) {
    if (deviceData[n].deviceStatus == "1") {
      online++;
      if (deviceData[n].deviceType == "FW") data2[0]++;
      if (deviceData[n].deviceType == "FID") data2[1]++;
      if (deviceData[n].deviceType == "BID") data2[2]++;
      if (deviceData[n].deviceType == "SVR") data2[3]++;
      if (deviceData[n].deviceType == "SW") data2[4]++;
      if (deviceData[n].deviceType == "VEAD") data2[5]++;
      if (deviceData[n].deviceType == "AV") data2[6]++;
      if (deviceData[n].deviceType == "IDS") data2[7]++;
      if (deviceData[n].deviceType == "DB") data2[8]++;
      if (deviceData[n].deviceType == "DCD") data2[9]++;
    } else if (deviceData[n].deviceStatus == "2") {
      if (deviceData[n].deviceType == "FW") data3[0]++;
      if (deviceData[n].deviceType == "FID") data3[1]++;
      if (deviceData[n].deviceType == "BID") data3[2]++;
      if (deviceData[n].deviceType == "SVR") data3[3]++;
      if (deviceData[n].deviceType == "SW") data3[4]++;
      if (deviceData[n].deviceType == "VEAD") data3[5]++;
      if (deviceData[n].deviceType == "AV") data3[6]++;
      if (deviceData[n].deviceType == "IDS") data3[7]++;
      if (deviceData[n].deviceType == "DB") data3[8]++;
      if (deviceData[n].deviceType == "DCD") data3[9]++;
    }
  })
  let option = {
    title: {text: '资产状态统计(在线' + online + '/' + sum + ')', left: '5'},
    color: ['#0098d9', '#a0a0a0'],
    tooltip: {
      trigger: 'axis',
      axisPointer: {type: 'shadow'},
      formatter: '<h5>{b0}</h5>' +
        '<span style="display:inline-block;margin-right:5px;width:10px;height:10px;background-color:#0098d9;border-radius:5px"></span>{a0}:{c0}<br>' +
        '<span style="display:inline-block;margin-right:5px;width:10px;height:10px;background-color:#a0a0a0;border-radius:5px"></span>{a1}:{c1}<br>',
        // '<span style="display:inline-block;margin-right:5px;width:10px;height:10px;background-color:#0098d9;border-radius:5px"></span>{a2}:{c2}<br>',
      backgroundColor: 'rgba(255,255,255,.8)',
      borderRadius: 0,
      // borderColor: '#e5323e',
      borderWidth: 1,
      textStyle: {fontSize: 13, fontFamily: 'SimHei', color: '#333333'},
    },
    legend: {icon: 'circle', data: ['在线', '离线'], top: '5'},
    calculable: true,
    grid: {x:39,x2:330,y:55,y2:80},
    xAxis: [{type: 'category', data: dataAxis, axisLabel: {rotate: -39},boundaryGap: true}],
    yAxis: [{type: 'value'}],
    series: [
      // {name: '未知', type: 'bar', data: data1,barWidth: 25},
      {name: '在线', type: 'bar', data: data2,barWidth: 25},
      {name: '离线', type: 'bar', data: data3,barWidth: 25}
    ]
  };
  assetStatusStatics.clear();
  assetStatusStatics.setOption(option);
  chartsResize($assetStatusStatics, assetStatusStatics)
}

/**
 * 图表自适应窗口宽度实现
 * @param $chart_val
 * @param chart_val
 */
function chartsResize($chart_val, chart_val) {
  window.addEventListener("resize", function () {
    $chart_val.style.width = window.innerWidth - 138 + 'px';
    chart_val.resize();
  });
}

/**
 * 初始化刷新表格.
 */
function loadDeviceTb() {
  $("#device-tb").jqGrid(
    {
      datatype: "local",
      height: "90%",
      autowidth: true,
      shrinkToFit: true,
      pager: '#device-pager',
      viewrecords: true,
      sortname: 'id',
      sortorder: 'desc',
      rowNum: 10,
      rowList: [5, 10, 20, 50, 100],
      recordtext: "{0} - {1}\u3000共 {2} 条",
      emptyrecords: "当前没有设备记录",
      loadtext: "读取中...",
      pgtext: "{0} 共 {1} 页",
      colNames: ['序号', '设备名称', '设备Ip', '设备Ip2', 'MAC地址', 'MAC地址2', '设备类型', '设备状态', '厂商', '序列号', '版本', '操作'],
      colModel: [
        {name: 'id', index: 'id',sorttype:'integer'},
        {name: 'deviceName', index: 'deviceName'},
        {name: 'deviceIp', index: 'deviceIp'},
        {name: 'deviceIp2', index: 'deviceIp2', hidden: true},
        {name: 'mac', index: 'mac',},
        {name: 'mac2', index: 'mac2', hidden: true},
        {name: 'deviceType', index: 'deviceType', formatter: transformType},
        {name: 'deviceStatus', index: 'deviceStatus',formatter:transformStatus},
        {name: 'manufacturer', index: 'manufacturer', hidden: true},
        {name: 'sequence', index: 'sequence', hidden: true},
        {name: 'sysVersion', index: 'sysVersion', hidden: true},
        {name: 'operation', index: 'operation', sortable: false, formatter: displayButtons}
      ]
    });

  // 清空和填充表格数据
  $("#device-tb").jqGrid('clearGridData');
  for (let i = 0; i <= deviceData.length; i++) {
    $("#device-tb").jqGrid('addRowData', i + 1, deviceData[i]);
  }
  // 数组数据分页.本地数据分页.
  $('.ui-pg-table.ui-pager-table #first_device-pager span')
    .addClass('ace-icon fa fa-angle-double-left bigger-100')
  $('.ui-pg-table.ui-pager-table #prev_device-pager span')
    .addClass('ace-icon fa fa-angle-left bigger-100')
  $('.ui-pg-table.ui-pager-table #next_device-pager span')
    .addClass('ace-icon fa fa-angle-right bigger-100')
  $('.ui-pg-table.ui-pager-table #last_device-pager span')
    .addClass('ace-icon fa fa-angle-double-right bigger-100')
  const localData = {page: 1, total: 2, records: "2", rows: []};
  localData.rows = deviceData;
  localData.records = deviceData.length;
  localData.total = (deviceData.length % 2 == 0) ? (deviceData.length / 2) : (Math.floor(deviceData.length / 2) + 1);
  const reader = {
    root: function (obj) {return localData.rows;},
    page: function (obj) {return localData.page;},
    total: function (obj) {return localData.total;},
    records: function (obj) {return localData.records;},
    repeatitems: false
  };
  $("#device-tb").setGridParam({
    data: localData.rows,
    reader: reader
  }).trigger('reloadGrid');

  /**\
   * 创造行末尾按钮并修改删除点击方法.
   */
  function displayButtons(cellvalue, options, rowObject) {
    return "<div class='hidden-sm hidden-xs btn-group'>" +
      "<button class='btn btn-minier btn-success wa-mlr5'" +
      "row-obj-str='" + JSON.stringify(rowObject) + "'" +
      "onclick=\"console.log($(this).attr('row-obj-str'))\n" +
      "$('#addDeviceDlg').modal('show')\n" +
      "if(JSON.parse($(this).attr('row-obj-str')).deviceType=='SW')\n" +
      "{$('#snmp_msg').removeClass('hidden')}else{$('#snmp_msg').addClass('hidden')}\n" +
      "$('#addDeviceForm').get(0).reset();\n" +
      "$('#addDeviceTitle').attr('wa-data', 'see')\n" +
      "$('#app_device_id').val(JSON.parse($(this).attr('row-obj-str')).id)\n" +
      "$('#device_name').val(JSON.parse($(this).attr('row-obj-str')).deviceName)\n" +
      "$('#device_type').val(JSON.parse($(this).attr('row-obj-str')).deviceType)\n" +
      "$('#device_ip').val(JSON.parse($(this).attr('row-obj-str')).deviceIp)\n" +
      "$('#device_ip2').val(JSON.parse($(this).attr('row-obj-str')).deviceIp2)\n" +
      "$('#device_mac').val(JSON.parse($(this).attr('row-obj-str')).mac)\n" +
      "$('#device_mac2').val(JSON.parse($(this).attr('row-obj-str')).mac2)\n" +
      "$('#manufacturer').val(JSON.parse($(this).attr('row-obj-str')).manufacturer)\n" +
      "$('#sequence').val(JSON.parse($(this).attr('row-obj-str')).sequence)\n" +
      "$('#serverVersion').val(JSON.parse($(this).attr('row-obj-str')).sysVersion)\n" +
      "$('#snmp_version').val(JSON.parse($(this).attr('row-obj-str')).snmpVersion)\n" +
      "$('#snmp_username').val(JSON.parse($(this).attr('row-obj-str')).snmpUsername)\n" +
      "$('#snmp_auth').val(JSON.parse($(this).attr('row-obj-str')).snmpAuth)\n" +
      "$('#snmp_encrypt').val(JSON.parse($(this).attr('row-obj-str')).snmpEncryp)\n" +
      "if(JSON.parse($(this).attr('row-obj-str')).snmpVersion=='1'){\n" +
      "$('#_groupname').removeClass('hidden')\n" +
      "$('#_psd').addClass('hidden')\n"+
      "$('#snmp_read_community').val(JSON.parse($(this).attr('row-obj-str')).snmpReadCommunity)\n" +
      "$('#snmp_write_community').val(JSON.parse($(this).attr('row-obj-str')).snmpWriteCommunity)}else{\n" +
      "$('#_groupname').addClass('hidden')\n" +
      "$('#_psd').removeClass('hidden')\n"+
      "$('#author_psd').val(JSON.parse($(this).attr('row-obj-str')).snmpReadCommunity)\n" +
      "$('#encrypt_psd').val(JSON.parse($(this).attr('row-obj-str')).snmpWriteCommunity)}\n" +
      "$('#addDeviceForm .form-group .form-control').attr('disabled','disabled')\n" +
      "$('#save_device_btn').hide()\">" +
      "<i class='ace-icon fa fa-info-circle bigger-80'>&nbsp;详情</i>" +
      "</button>" +
      "<button class='btn btn-minier btn-inverse wa-mlr5' " +
      "row-obj-str='" + JSON.stringify(rowObject) + "'" +
      "onclick=\"console.log($(this).attr('row-obj-str'))\n" +
      "$('#addDeviceDlg').modal('show')\n" +
      "if(JSON.parse($(this).attr('row-obj-str')).deviceType=='SW')\n" +
      "{$('#snmp_msg').removeClass('hidden')}else{$('#snmp_msg').addClass('hidden')}\n" +
      "$('#addDeviceForm').get(0).reset()\n" +
      "$('#addDeviceTitle').attr('wa-data', 'edit')\n" +
      "$('#app_device_id').val(JSON.parse($(this).attr('row-obj-str')).id)\n" +
      "$('#device_name').val(JSON.parse($(this).attr('row-obj-str')).deviceName)\n" +
      "$('#device_type').val(JSON.parse($(this).attr('row-obj-str')).deviceType)\n" +
      "$('#device_ip').val(JSON.parse($(this).attr('row-obj-str')).deviceIp)\n" +
      "$('#old_ip1').val(JSON.parse($(this).attr('row-obj-str')).deviceIp)\n" +
      "$('#device_ip2').val(JSON.parse($(this).attr('row-obj-str')).deviceIp2)\n" +
      "$('#old_ip2').val(JSON.parse($(this).attr('row-obj-str')).deviceIp2)\n" +
      "$('#device_mac').val(JSON.parse($(this).attr('row-obj-str')).mac)\n" +
      "$('#device_mac2').val(JSON.parse($(this).attr('row-obj-str')).mac2)\n" +
      "$('#manufacturer').val(JSON.parse($(this).attr('row-obj-str')).manufacturer)\n" +
      "$('#sequence').val(JSON.parse($(this).attr('row-obj-str')).sequence)\n" +
      "$('#serverVersion').val(JSON.parse($(this).attr('row-obj-str')).sysVersion)\n" +
      "$('#snmp_version').val(JSON.parse($(this).attr('row-obj-str')).snmpVersion)\n" +
      "$('#snmp_username').val(JSON.parse($(this).attr('row-obj-str')).snmpUsername)\n" +
      "$('#snmp_auth').val(JSON.parse($(this).attr('row-obj-str')).snmpAuth)\n" +
      "$('#snmp_encrypt').val(JSON.parse($(this).attr('row-obj-str')).snmpEncryp)\n" +
      "$('#addDeviceForm .form-group .form-control').removeAttr('disabled')\n" +
      "if(JSON.parse($(this).attr('row-obj-str')).snmpVersion=='1'){\n" +
      "$('#snmp_username').attr('disabled','disabled')\n" +
      "$('#snmp_auth').attr('disabled','disabled')\n" +
      "$('#snmp_encrypt').attr('disabled','disabled')\n" +
      "$('#_groupname').removeClass('hidden')\n" +
      "$('#_psd').addClass('hidden')\n"+
      "$('#snmp_read_community').val(JSON.parse($(this).attr('row-obj-str')).snmpReadCommunity)\n" +
      "$('#snmp_write_community').val(JSON.parse($(this).attr('row-obj-str')).snmpWriteCommunity)}else{\n" +
      "$('#snmp_username').removeAttr('disabled')\n" +
      "$('#snmp_auth').removeAttr('disabled')\n" +
      "$('#snmp_encrypt').removeAttr('disabled')\n" +
      "$('#_groupname').addClass('hidden')\n" +
      "$('#_psd').removeClass('hidden')\n"+
      "$('#author_psd').val(JSON.parse($(this).attr('row-obj-str')).snmpReadCommunity)\n" +
      "$('#encrypt_psd').val(JSON.parse($(this).attr('row-obj-str')).snmpWriteCommunity)}\n" +
      "$('#save_device_btn').show()\">" +
      "<i class='ace-icon fa fa-edit bigger-80'>&nbsp;修改</i>" +
      "</button>" +
      "<button class='btn btn-minier btn-danger'" +
      "row-id='" + rowObject.id + "'" +
      "onclick=\"console.log($(this).attr('row-id'))\n" +
      "$('#confirmDlg').modal('show')\n" +
      "$('#remove_device_id').val($(this).attr('row-id'))\">" +
      "<i class='ace-icon fa fa-trash-o bigger-80'>&nbsp;删除</i>" +
      "</button>" +
      "</div>";
  }
  /**
   * 设备类型&状态转换文本
   */
  function transformStatus(cellvalue, options, rowObject) {
    if (cellvalue == "1") return "在线";
    else if (cellvalue == "2") return "离线";
  }
  function transformType(cellvalue, options, rowObject) {
    if (cellvalue == "FW") return "防火墙";
    else if (cellvalue == "FID") return "横向正向隔离装置";
    else if (cellvalue == "BID") return "横向反向隔离装置";
    else if (cellvalue == "SVR") return "服务器";
    else if (cellvalue == "SW") return "交换机";
    else if (cellvalue == "VEAD") return "纵向加密装置";
    else if (cellvalue == "AV") return "防病毒系统";
    else if (cellvalue == "IDS") return "入侵检测系统";
    else if (cellvalue == "DB") return "数据库";
    else if (cellvalue == "DCD") return "网络安全监测装置";
    else return "";
  }
  jqgridColResize();
  tableResize();
}
loadDeviceTb()
/**
 * 重新调整jqgrid每列的宽度
 */
function jqgridColResize(){
  let td=$('#tdCompute')//获取计算实际列长度的容器
    ,tds//临时保存列
    ,arr=[];//用于保存最大的列宽
  //遍历每行获得每行中的最大列宽
  $('.ui-jqgrid-htable tr,.ui-jqgrid-btable tr:gt(0)').each(function(){
    $(this).find('td,th').each(function(idx){
      arr[idx]=Math.max(arr[idx]?arr[idx]:0,td.html($(this).text())[0].offsetWidth);
    })
  });
  //设置页头单元格宽度
  $('.ui-jqgrid-labels th').each(function(idx){this.style.width=arr[idx]+'px'});
  //设置内容表格中控制单元格宽度的单元格，在第一行
  $('.ui-jqgrid-btable tr:eq(0) td').each(function(idx){this.style.width=arr[idx]+'px'});
  // 设置操作栏固定宽度
  $('.ui-jqgrid .ui-jqgrid-htable th:last-child').css("width", "155");
  $('.ui-jqgrid-btable>tbody>tr>td:last-child').css("width", "155");
}
/**
 * 表格自适应窗口
 */
function tableResize() {
  $(window).on('resize.jqGrid', function () {
    $("#device-tb").jqGrid('setGridWidth', $(".page-content").width());
    jqgridColResize();
  });
  const parent_column = $("#device-tb").closest('[class*="col-"]');
  $(document).on('settings.ace.jqGrid', function (ev, event_name, collapsed) {
    if (event_name == 'sidebar_collapsed' || event_name == 'main_container_fixed') {
      setTimeout(function () {
        $("#device-tb").jqGrid('setGridWidth', parent_column.width());
        jqgridColResize();
      }, 0);
    }
  })
}

/**
 * 初始化页面.填充表格数组数据.
 */
let deviceids=new Array()
function loadDeviceidsGenerator() {
  // 连接服务器
  tcp_client.connect(loadDeviceidsHandler, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPQueryOnlineDeviceIds();
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPQUERYONLINEDEVICEIDS, sendMsg, user_sign);
}
function loadDeviceidsHandler(msgId, pb) {
  if (msgId !== ProxyServer_pb.PSMessageId.PSMESSAGEID_PCQUERYONLINEDEVICEIDS) {
    console.error('invalid msg', msgId);
    return;
  }
  const msg = ProxyServer_pb.MSG_PCQueryOnlineDeviceIds.deserializeBinary(pb);
  deviceids = msg.getDeviceIdsList();
  // console.log("deviceids",deviceids)

  pageInit(type, statis, search,key);
}
function pageInit(type, statis, search,key) {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      const devices = device_codec.unpackLoadDevice(dp_packet.content);
      deviceData = [];
      let _deviceStatus="";
      for (let i = 0; i < devices.length; i++) {
        if($.inArray(devices[i].getId(),deviceids) !==-1)_deviceStatus="1";
        else _deviceStatus="2";
        if(devices[i].getDeviceType().indexOf(type)!=-1&&_deviceStatus.indexOf(statis)!=-1&&devices[i].getDeviceName().indexOf(search)!=-1){
          const deviceObj = {
            id: devices[i].getId(),
            deviceName: devices[i].getDeviceName(),
            deviceIp: util.int2ip(devices[i].getIp1()),
            deviceIp2: util.int2ip(devices[i].getIp2()),
            mac: util.str2mac(devices[i].getMac1()),
            mac2: util.str2mac(devices[i].getMac2()),
            deviceType: devices[i].getDeviceType(),
            deviceStatus: _deviceStatus,
            manufacturer: devices[i].getManufacturer(),
            sequence: devices[i].getSerialNo(),
            sysVersion: devices[i].getSysVersion(),
            snmpVersion: devices[i].getSnmpVersion(),
            snmpUsername: devices[i].getSnmpUsername(),
            snmpAuth: devices[i].getSnmpAuth(),
            snmpEncryp: devices[i].getSnmpEncrypt(),
            snmpReadCommunity: devices[i].getSnmpReadCommunity(),
            snmpWriteCommunity: devices[i].getSnmpWriteCommunity(),
          }
          deviceData.push(deviceObj);
          deviceMap.set(devices[i].getId(), devices[i]);
        }
      }
      console.log('deviceData:', deviceData)
      console.log('deviceMap:', deviceMap)
      $("#device-tb").jqGrid("clearGridData"); //清空表格数据
      $("#device-tb").jqGrid('setGridParam',{  // 重新加载数据
        datatype:'local',
        data : deviceData,   //  data 是符合格式要求的需要重新加载的数据
        page:1
      }).trigger("reloadGrid");
      if(key===1){
        initData=deviceData
      }
      assetStatusStatics();
    }
    // 消息处理完成关闭连接
    tcp_client.close();
  }, config.host, config.port);

  // 发送消息
  tcp_client.send(device_codec.packLoadDevice());
}
loadDeviceidsGenerator();

/**
 * 发送验证密码参数
 * @param psd
 */
function validateUserGenerator(username,psd){
// 连接服务器
  tcp_client.connect(function(msgId, pb) {
    const msg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(pb);
    if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
      //ui_util.showFadeTip('验证用户通过!');
      if($('#opraType').val()==1){
        $('#confirmPortDlg').modal('show')
        // $('#port_btn').addClass('disabled')
        $('#confirmPortDlg .modal-title').attr("wa-name","import_tt")
        $('#confirmPortDlg .modal-body .form-group:nth-child(1)').removeClass("hidden")
        $('#confirmPortDlg .modal-body .form-group:nth-child(2)').addClass("hidden")
      }else if($('#opraType').val()==2){
        $('#confirmPortDlg').modal('show')
        // $('#port_btn').addClass('disabled')
        $('#confirmPortDlg .modal-title').attr("wa-name","export_tt")
        $('#confirmPortDlg .modal-body .form-group:nth-child(2)').removeClass("hidden")
        $('#confirmPortDlg .modal-body .form-group:nth-child(1)').addClass("hidden")
      }
    } else {
      ui_util.getErrorTips(msg.getErrorCode(), '验证用户身份')
    }
  }, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
  sendMsg.setUserName(username)
  sendMsg.setPassword(psd)
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg, user_sign);
}

/**
 * 导出资产
 */
function exportDevices(filePath,fileName) {
  tcp_client.connect(function (msgId, pb) {
      const msg = ProxyServer_pb.MSG_PCExportData.deserializeBinary(pb);
      let targetBuf = msg.getData();
      console.log("targetBuf",targetBuf)
        util.saveToCSV(targetBuf, filePath+"/"+fileName);
        ui_util.showFadeTip('导出资产配置成功')
    // 消息处理完成关闭连接
    tcp_client.close();
  }, config.host, config.port);

  // 发送消息
  let exportData = new ProxyServer_pb.MSG_CPExportData();
  exportData.setType(PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_DEVICE)
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPEXPORTDATA, exportData, user_sign);
}
/**
 * 导入资产
 */
function importDevices(file,fileName) {
  tcp_client.connect(function (msgId, pb) {
      $('.parent_content').busyLoad("hide");
    const msg = ProxyServer_pb.MSG_PCImportData.deserializeBinary(pb);
    // importedCount++;
    // $('.cust-elem').text(' 已导入'+importedCount+'/'+totalCount+'条资产信息');
    // if (importedCount === totalCount) {
    if (msg.getErrorCode()==0) {
      ui_util.showFadeTip('导入资产成功!');
      ui_util.showLoading(30);
    }else {
      ui_util.getErrorTips(msg.getErrorCode(),'导入资产');
    }
    // 消息处理完成关闭连接
    tcp_client.close();
    key=2;
    pageInit("", "", "",key);
    $('#deviceTypeSelect [name="device-type"]:eq(0)').prop('checked',true);
    $('#deviceStatisSelect [name="device-statis"]:eq(0)').prop('checked',true);
  }, config.host, config.port);

  // 发送消息
  let importData = new ProxyServer_pb.MSG_CPImportData();
  importData.setType(PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_DEVICE)
  const savedConfigs = util.readFromFile(file,false);
  importData.setData(savedConfigs.toString())
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPIMPORTDATA, importData, user_sign);
    $('.parent_content').busyLoad("show",{
        background: "rgba(0, 0, 0, 0.59)",
    fontawesome: "fa fa-spinner fa-spin fa-3x fa-fw"
    })
}

/**
 * 发送新增设备参数信息
 */
function addDeviceData() {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      ui_util.showFadeTip('新增资产成功!');
      $('#addDeviceDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
    } else {
      ui_util.getErrorTips(dp_packet.return_value,"新增资产")
      $('#addDeviceDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
    }
    // 消息处理完成关闭连接
    tcp_client.close();
    key=2;
    pageInit("", "", "",key);
    $('#deviceTypeSelect [name="device-type"]:eq(0)').prop('checked',true);
    $('#deviceStatisSelect [name="device-statis"]:eq(0)').prop('checked',true);
  }, config.host, config.port);

  // 发送消息
  let device = new PublicDefine_pb.MSG_Device();
  device.setId(0);
  device.setDeviceName($('#device_name').val());
  device.setDeviceType($('#device_type').val());
  device.setIp1(util.ip2int($('#device_ip').val()));
  device.setMac1(util.mac2str($('#device_mac').val()));
  device.setIp2(util.ip2int($('#device_ip2').val()));
  device.setMac2(util.mac2str($('#device_mac2').val()));
  device.setManufacturer($('#manufacturer').val());
  device.setSerialNo($('#sequence').val());
  device.setSysVersion($('#serverVersion').val());
  let options = $("#device_type option:selected");
  if (options.val() == "SW") {
    device.setSnmpVersion(parseInt($('#snmp_version').val()));
    device.setSnmpUsername($('#snmp_username').val());
    device.setSnmpAuth(parseInt($('#snmp_auth').val()));
    device.setSnmpEncrypt(parseInt($('#snmp_encrypt').val()));
    if($('#snmp_version').val()==='1'){
      device.setSnmpReadCommunity($('#snmp_read_community').val());
      device.setSnmpWriteCommunity($('#snmp_write_community').val());
    }else{
      device.setSnmpReadCommunity($('#author_psd').val());
      device.setSnmpWriteCommunity($('#encrypt_psd').val());
    }
  }
  tcp_client.send(device_codec.packAddDevice(device));
}

/**
 * 发送修改设备参数信息
 */
function updateDeviceData() {
// 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      ui_util.showFadeTip('更新资产成功!');
      $('#addDeviceDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
    }else {
      ui_util.getErrorTips(dp_packet.return_value,"更新资产")
      $('#addDeviceDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
    }
    // 消息处理完成关闭连接
    tcp_client.close();
    key=2;
    pageInit("", "", "",key);
    $('#deviceTypeSelect [name="device-type"]:eq(0)').prop('checked',true);
    $('#deviceStatisSelect [name="device-statis"]:eq(0)').prop('checked',true);
  }, config.host, config.port);

  // 发送消息
  let device = new PublicDefine_pb.MSG_Device();
  device.setId($('#app_device_id').val());
  device.setDeviceName($('#device_name').val());
  device.setDeviceType($('#device_type').val());
  device.setIp1(util.ip2int($('#device_ip').val()));
  device.setMac1(util.mac2str($('#device_mac').val()));
  device.setIp2(util.ip2int($('#device_ip2').val()));
  device.setMac2(util.mac2str($('#device_mac2').val()));
  device.setManufacturer($('#manufacturer').val());
  device.setSerialNo($('#sequence').val());
  device.setSysVersion($('#serverVersion').val());
  let options = $("#device_type option:selected");
  if (options.val() == "SW") {
    device.setSnmpVersion(parseInt($('#snmp_version').val()));
    device.setSnmpUsername($('#snmp_username').val());
    device.setSnmpAuth(parseInt($('#snmp_auth').val()));
    device.setSnmpEncrypt(parseInt($('#snmp_encrypt').val()));
    if($('#snmp_version').val()==='1'){
      device.setSnmpReadCommunity($('#snmp_read_community').val());
      device.setSnmpWriteCommunity($('#snmp_write_community').val());
    }else{
      device.setSnmpReadCommunity($('#author_psd').val());
      device.setSnmpWriteCommunity($('#encrypt_psd').val());
    }
  }
  tcp_client.send(device_codec.packUpdateDevice(device));
}

/**
 * 发送删除设备
 */
function removeDeviceData() {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      ui_util.showFadeTip('删除资产成功!');
    }else {
      ui_util.getErrorTips(dp_packet.return_value,"删除资产")
    }
    // 消息处理完成关闭连接
    tcp_client.close();
    key=2;
    pageInit("", "", "",key);
    $('#deviceTypeSelect [name="device-type"]:eq(0)').prop('checked',true);
    $('#deviceStatisSelect [name="device-statis"]:eq(0)').prop('checked',true);
  }, config.host, config.port);

  // 发送消息
  let ids = new Array();
  ids.push($('#remove_device_id').val())
  tcp_client.send(device_codec.packRemoveDevice(ids));
}
