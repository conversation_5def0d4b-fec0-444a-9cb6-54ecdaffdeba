'use strict';

const {ipc<PERSON><PERSON>er} = require('electron');
const ProxyServer_pb = require('../pb/ProxyServer_pb');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const util = require('../../lib/util');
const ui_util = require('./ui_util');
const tcp_client = require('../../lib/tcp_client');
const config = require("../../config.json");
const  fs = require('fs');
// 相关变量定义
const user_sign = $("body").attr("user_sign_info");
const user_name = $("body").attr("user_name_info");
console.log("user_sign:", user_sign + user_name);
const tcpInstance = require('../../lib/v2/tls_client.js');
let is_exported = false;

// 初始化日期控件
let beginTimeStore = '';
let endTimeStore = '';
$('#newTimeRange').daterangepicker({
  "timePicker": true,
  "timePicker24Hour": true,
  "linkedCalendars": false,
  "autoUpdateInput": true,
  "singleDatePicker": true,//单日历模式
  "startDate":moment().startOf('day'),
  // "endDate":moment().endOf('day'),
    "endDate": util.getDateMax(), //设置当前日期最大时间数(例如2020/01/18 23:59:59)
  // maxDate: moment(new Date()), //设置最大日期
  "locale": {
    format: 'YYYY/MM/DD HH:mm:ss',
    separator: ' - ',
    applyLabel: "确认",
    cancelLabel: "取消",
    fromLabel: "开始时间",
    toLabel: "结束时间",
    customRangeLabel: "自定义",
    daysOfWeek: ["日","一","二","三","四","五","六"],
    monthNames: ["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"]
  },
  // ranges: {
  //   '今天': [moment(),moment().endOf('day')],
  //   '昨天': [moment().subtract(1, 'days'),moment().endOf('day').subtract(1, 'days')],
  //   '近7天': [moment().subtract(7, 'days'), moment().endOf('day')],
  //   '本月': [moment().startOf('month'), moment().endOf('month').endOf('day')],
  //   '上月': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month').endOf('day')]
  // }
}, function(start, end, label) {
  beginTimeStore = start;
  endTimeStore = end;
  if(!this.startDate){
    this.element.val('');
  }else{
    this.element.val(this.startDate.format(this.locale.format) + this.locale.separator + this.endDate.format(this.locale.format));
  }
});

$('#endTimeRange').daterangepicker({
    "timePicker": true,
    "timePicker24Hour": true,
    "linkedCalendars": false,
    "autoUpdateInput": true,
    "singleDatePicker": true,//单日历模式
    "startDate":util.getDateMax(),
    // "endDate":moment().endOf('day'),
    "endDate": util.getDateMax(), //设置当前日期最大时间数(例如2020/01/18 23:59:59)
    // maxDate: moment(new Date()), //设置最大日期
    "locale": {
        format: 'YYYY/MM/DD HH:mm:ss',
        separator: ' - ',
        applyLabel: "确认",
        cancelLabel: "取消",
        fromLabel: "开始时间",
        toLabel: "结束时间",
        customRangeLabel: "自定义",
        daysOfWeek: ["日","一","二","三","四","五","六"],
        monthNames: ["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"]
    },
    // ranges: {
    //     '今天': [moment(),moment().endOf('day')],
    //     '昨天': [moment().subtract(1, 'days'),moment().endOf('day').subtract(1, 'days')],
    //     '近7天': [moment().subtract(7, 'days'), moment().endOf('day')],
    //     '本月': [moment().startOf('month'), moment().endOf('month').endOf('day')],
    //     '上月': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month').endOf('day')]
    // }
}, function(start, end, label) {
    beginTimeStore = start;
    endTimeStore = end;
    if(!this.startDate){
        this.element.val('');
    }else{
        this.element.val(this.startDate.format(this.locale.format) + this.locale.separator + this.endDate.format(this.locale.format));
    }
});

let filePath="",fileName="log_analysis.csv",imgName="log_analysis.png";
// 初始化选择文件夹路径&选择文件
// $('#select_folder').ace_file_input({
//   no_file:'请选择文件夹',
//   btn_choose:'选择',
//   btn_change:null,
//   droppable:false,
//   onchange:null,
//   thumbnail:false
// });
// document.querySelector('#select_folder').addEventListener('change', e => {
//   for (let entry of e.target.files){
//     console.log(entry.name, entry.path);
//     filePath=entry.path
//   }
// });

// 导出日志
$('#log_export').on('click', function () {
  $('#validateUserDlg').modal('show');
  $('#validateUserForm').get(0).reset()
})
// 弹出密码校验框
$('#validateuser_btn').on('click',function (e) {
  if ($("#validateuser_btn").hasClass("disabled")) {
    return;
  }
  $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  let username=user_name
  let psd=$('#validateUserDlg #password').val()
  validateUserGenerator(username,psd);
})
// 导出按钮
document.querySelector('#export_selectPath_btn').addEventListener('click', async (e) => {
    console.log('选择文件路径 =>')
    const folderPath = await ipcRenderer.invoke('select-folder');
    if (folderPath) {
        console.log('选中的文件夹路径:', folderPath);
        $("#export_file_path").val(folderPath);
        filePath = folderPath
    } else {
        console.log('用户取消选择文件夹');
    }
});
$('#port_btn').on('click',function (e) {
  if ($("#port_btn").hasClass("disabled")) {
    return;
  }
  if(!filePath){
    ui_util.showFadeTip('请选择文件路径!')
    return;
  }
  $('#confirmPortDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  // $('.modal-backdrop').css('opacity','0!important')
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  exportLogs(filePath,fileName,imgName)
})

// 条件筛选刷新表格
let operator_name=$('#username').val(),
  beginDate=util.getThisTimestamp($('#newTimeRange').val().substring(0,19))*1000,
  endDate=util.getThisTimestamp($('#endTimeRange').val().substring(0,19))*1000,
  index=1,
  count=10,
  log_type=-1,
  cont=$('#content').val();
$('#query').on('click', function () {
  operator_name=$.trim($('#username').val());
  let _begin=$('#newTimeRange').val().substring(0,19),
    _end=$('#endTimeRange').val().substring(0,19);
  beginDate = util.getThisTimestamp(_begin)*1000;
  endDate = util.getThisTimestamp(_end)*1000;
    // log_type=$('#log_type').val()===""?-1:parseInt($('#log_type').val()),
    cont=$.trim($('#content').val());
    // init(operator_name,beginDate,endDate,index, count,log_type);
    $("#log-excep-tb").bootstrapTable('selectPage', 1);// 查询后跳转至第1页
})

/**
 * 初始化刷新表格.
 */
let data = new Object();
    let excep_percent_data=new Array(),type_statis_data=new Array(),source_count_data =new Array(),level_count_data =new Array(),log_module_data =new Array();
function createLogsTable() {
  $("#log-excep-tb").bootstrapTable({
    method: 'post',
    data:data,
    toolbar:'#toolbar',
    cache: false, // 设置为 false 禁用 AJAX 数据缓存， 默认为true
    striped: true,  //表格显示条纹，默认为false
    pagination: true, // 在表格底部显示分页组件，默认false
    pageList: [10, 20, 50, 100], // 设置页面可以显示的数据条数
    pageNumber: index, //初始化加载第一页，默认第一页
    pageSize: count, // 页面数据条数
    queryParamsType:'limit',//查询参数组织方式
    // queryParams:queryParams,//请求服务器时所传的参数
    sidePagination:'server',//指定服务器端分页
    paginationShowPageGo:true,//跳转到
    columns : [
      {field : 'operator_name', title : '用户/主体',width:'10%',align:"center"},
      {field : 'logType', title : '异常类型',width:'10%',align:"center",formatter:function (value, row, index) {
          if(value==0) return "通信异常";
          else if(value==1) return "越权访问";
          else if(value==2) return "登录失败次数达到上限";
          else if(value==3) return "日志条数达到上限";
          else if(value==4) return "CPU利用率达到上限";
          else if(value==5) return "磁盘利用率达到上限";
          else if(value==6) return "内存利用率达到上限";
          else if(value==7) return "进程异常";
          else if(value==8) return "硬件异常";
          else if(value==9) return "其他";
          else if(value==10) return "IP异常";
        }},
      {field : 'time', title : '时间',width:'10%',align:"center",},
      {field : 'content', title : '异常内容',width:'69%',align:"center"}
    ],
    sortable: true, //是否启用排序
    sortName: 'time', // 要排序的字段
    sortOrder: 'decs', // 排序规则,
    showLoading: true,
    onPageChange: function (number, size) {
      index = number;
      count = size;
      // init(beginDate,endDate,index, count,log_source,log_level,log_type,log_module);
      init(operator_name,beginDate,endDate,index, count,log_type);
    }
  })
}
// 表格自适应窗口
$(window).resize(function () {
  $("#log-excep-tb").bootstrapTable('resetView', {
    width: $(".page-content").width()
  });
});
createLogsTable()
initOnMessage()
init(operator_name,beginDate,endDate,index, count,log_type);

/**
 * 日志异常类型占比饼图
 */
function levelPercent() {
  let $level_percent = document.getElementById('level_percent')
  $level_percent.style.width = (window.innerWidth - 138) / 2 + 'px';
  let _level_percent = echarts.init($level_percent, 'shine');
  let dataAxis = ['通信异常', '越权访问', '登录失败次数达到上限', '日志条数达到上限', 'CPU利用率达到上限', '磁盘利用率达到上限', '内存利用率达到上限', '进程异常', '硬件异常', '其他', 'IP异常'];
  let option = {
    title: {text: '日志异常类型占比', left: '39'},
    tooltip: {
      trigger: 'item',
      axisPointer: {type: 'shadow'},
      formatter: '<h5>{a}</h5>' +
        '<span style="display:inline-block;margin-right:5px;' +
        'width:10px;height:10px;background-color:#0098d9;border-radius:5px"></span>{b}:{c}({d}%)<br>',
      backgroundColor: 'rgba(255,255,255,.8)',
      borderRadius: 0,
      // borderColor:'#006699',
      borderWidth: 1,
      textStyle: {fontSize: 13, fontFamily: 'SimHei', color: '#333333'},
    },
    legend: {show: false, orient: 'vertical', top: '66', left: '60', data: dataAxis},
    series: [{
      name: '异常类型占比', type: 'pie', radius: '30%',
      center: ['42%', '55%'], data: excep_percent_data,
      itemStyle: {
        normal: {
          label: {show: true,fontSize: 10, formatter: '{b}:{c}({d}%)'},
          labelLine: {show: true}
        },
        emphasis: {shadowBlur: 10, shadowOffsetX: 0, shadowColor: 'rgba(0, 0, 0, 0.5)'}
      }
    },
    ]
  };
  _level_percent.clear();
  _level_percent.setOption(option);
  chartsResize($level_percent,_level_percent)
}
/**
 * 日志分类统计图
 */
function moduleStatis() {
  let $module_statis = document.getElementById('module_statis')
  $module_statis.style.width = (window.innerWidth - 138) / 2 + 'px';
  let _module_statis = echarts.init($module_statis, 'shine');
  let dataAxis = ['登录', '操作', '维护', '系统', '其他'];
  let labelOption = {
    normal: {
      show: true, position: 'top', distance: 0, align: 'center', verticalAlign: 'bottom', rotate: 0, formatter: '{c}', fontSize: 16,
      rich: {name: {textBorderColor: '#fff'}}}
  };
  let option = {
    // title: {text: '日志分类统计', left: '5'},
    color: '#0098d9',
    tooltip: {
      trigger: 'axis',
      axisPointer: {type: 'shadow'},
      formatter: '<h5>{b0}</h5>' +
        '<span style="display:inline-block;margin-right:5px;' +
        'width:10px;height:10px;background-color:#0098d9;border-radius:5px"></span>{a0}:{c0}<br>',
      backgroundColor: 'rgba(255,255,255,.8)',
      borderRadius: 0,
      // borderColor:'#006699',
      borderWidth: 1,
      textStyle: {fontSize: 13, fontFamily: 'SimHei', color: '#333333'},
    },
    legend: {icon: 'circle', data: '日志数量', bottom: '0'},
    calculable: true,
    grid: {x: 39, x2: 185, y: 80},
    xAxis: [{type: 'category', axisTick: {show: false}, data: dataAxis, boundaryGap: true}],
    yAxis: [{name: '数量/条', type: 'value'}],
    series: [
      {name: '日志数量', type: 'bar', label: labelOption, data: type_statis_data, barWidth: 30},
    ]
  };
  _module_statis.clear();
  _module_statis.setOption(option);
  chartsResize($module_statis,_module_statis)
}
//日志来源柱状图
function logSource() {
    let $log_source = document.getElementById('module_statis')
    $log_source.style.width = (window.innerWidth - 138) / 2 + 'px';
    let _log_source = echarts.init($log_source, 'shine');
    let dataAxis = ['应用日志', '操作系统日志'];
    let logSource = {
        normal: {
            show: true, position: 'top', distance: 0, align: 'center', verticalAlign: 'bottom', rotate: 0, formatter: '{c}', fontSize: 16,
            rich: {name: {textBorderColor: '#fff'}}}
    };
    let option = {
        // title: {text: '日志分类统计', left: '5'},
        color: '#0098d9',
        tooltip: {
            trigger: 'axis',
            axisPointer: {type: 'shadow'},
            formatter: '<h5>{b0}</h5>' +
                '<span style="display:inline-block;margin-right:5px;' +
                'width:10px;height:10px;background-color:#0098d9;border-radius:5px"></span>{a0}:{c0}<br>',
            backgroundColor: 'rgba(255,255,255,.8)',
            borderRadius: 0,
            // borderColor:'#006699',
            borderWidth: 1,
            textStyle: {fontSize: 13, fontFamily: 'SimHei', color: '#333333'},
        },
        legend: {icon: 'circle', data: '日志数量', bottom: '0'},
        calculable: true,
        grid: {x: 39, x2: 185, y: 80},
        xAxis: [{type: 'category', axisTick: {show: false}, data: dataAxis, boundaryGap: true}],
        yAxis: [{name: '数量/条', type: 'value'}],
        series: [
            {name: '日志数量', type: 'bar', label: logSource, data: source_count_data, barWidth: 30},
        ]
    };
    _log_source.clear();
    _log_source.setOption(option);
    chartsResize($log_source,_log_source)
}
//日志级别柱状图
function logLevel() {
    let $log_level = document.getElementById('module_statis')
    $log_level.style.width = (window.innerWidth - 138) / 2 + 'px';
    let _log_level = echarts.init($log_level, 'shine');
    let dataAxis = ['紧急', '重要','次要','一般','告知'];
    let logLevel = {
        normal: {
            show: true, position: 'top', distance: 0, align: 'center', verticalAlign: 'bottom', rotate: 0, formatter: '{c}', fontSize: 16,
            rich: {name: {textBorderColor: '#fff'}}}
    };
    let option = {
        // title: {text: '日志分类统计', left: '5'},
        color: '#0098d9',
        tooltip: {
            trigger: 'axis',
            axisPointer: {type: 'shadow'},
            formatter: '<h5>{b0}</h5>' +
                '<span style="display:inline-block;margin-right:5px;' +
                'width:10px;height:10px;background-color:#0098d9;border-radius:5px"></span>{a0}:{c0}<br>',
            backgroundColor: 'rgba(255,255,255,.8)',
            borderRadius: 0,
            // borderColor:'#006699',
            borderWidth: 1,
            textStyle: {fontSize: 13, fontFamily: 'SimHei', color: '#333333'},
        },
        legend: {icon: 'circle', data: '日志数量', bottom: '0'},
        calculable: true,
        grid: {x: 39, x2: 185, y: 80},
        xAxis: [{type: 'category', axisTick: {show: false}, data: dataAxis, boundaryGap: true}],
        yAxis: [{name: '数量/条', type: 'value'}],
        series: [
            {name: '日志数量', type: 'bar', label: logLevel, data: level_count_data, barWidth: 30},
        ]
    };
    _log_level.clear();
    _log_level.setOption(option);
    chartsResize($log_level,_log_level)
}
//日志模块柱状图
function logModule() {
    let $log_module = document.getElementById('module_statis')
    $log_module.style.width = (window.innerWidth - 138) / 2 + 'px';
    let _log_module = echarts.init($log_module, 'shine');
    let dataAxis = ['本地管理客户端', '采集模块','分析模块','服务代理模块','通信模块'];
    let logModule = {
        normal: {
            show: true, position: 'top', distance: 0, align: 'center', verticalAlign: 'bottom', rotate: 0, formatter: '{c}', fontSize: 16,
            rich: {name: {textBorderColor: '#fff'}}}
    };
    let option = {
        // title: {text: '日志分类统计', left: '5'},
        color: '#0098d9',
        tooltip: {
            trigger: 'axis',
            axisPointer: {type: 'shadow'},
            formatter: '<h5>{b0}</h5>' +
                '<span style="display:inline-block;margin-right:5px;' +
                'width:10px;height:10px;background-color:#0098d9;border-radius:5px"></span>{a0}:{c0}<br>',
            backgroundColor: 'rgba(255,255,255,.8)',
            borderRadius: 0,
            // borderColor:'#006699',
            borderWidth: 1,
            textStyle: {fontSize: 13, fontFamily: 'SimHei', color: '#333333'},
        },
        legend: {icon: 'circle', data: '日志数量', bottom: '0'},
        calculable: true,
        grid: {x: 39, x2: 185, y: 80},
        xAxis: [{type: 'category', axisTick: {show: false}, data: dataAxis,axisLabel: {rotate: -30}, boundaryGap: true}],
        yAxis: [{name: '数量/条', type: 'value'}],
        series: [
            {name: '日志数量', type: 'bar', label: logModule, data: log_module_data, barWidth: 30},
        ]
    };
    _log_module.clear();
    _log_module.setOption(option);
    chartsResize($log_module,_log_module)
}
$('#analysis_type').change(function() {
    if($('#analysis_type').val()=="LX"){
        moduleStatis();
    }else if($('#analysis_type').val()=="LY"){
        logSource();
    }else if($('#analysis_type').val()=="JB"){
        logLevel();
    }else if($('#analysis_type').val()=="MK"){
        logModule();
    }
})
/**
 * 图表自适应窗口宽度实现
 * @param $chart_val
 * @param chart_val
 */
function chartsResize($chart_val,chart_val) {
  window.addEventListener("resize", function () {
    $chart_val.style.width = (window.innerWidth - 138-50)/2 + 'px';
    chart_val.resize();
  });
}

// 是否为导出
/**
 * 页面定义的相关数据和初始化执行的相关操作
 */
function init(operator_name,beginDate,endDate,index, count,log_type) {
  // tcp_client.connect(onMsg, config.host, config.port);
  loadLogReportGenerator(operator_name,beginDate,endDate,index, count,log_type);
}
/**
 * 发送加载日志请求
 * @param index 索引，从0开始
 * @param count 加载数量
 */
function loadLogReportGenerator(operator_name,beginDate,endDate,index, count,log_type) {
  let sendMsg = new ProxyServer_pb.MSG_CPLoadRunLogReport();
  sendMsg.setStartTime(beginDate);
  sendMsg.setEndTime(endDate);
  sendMsg.setIndex(index);
  sendMsg.setCount(count);
  sendMsg.setType(log_type);
  sendMsg.setUserId(operator_name);
  is_exported = false
  tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADRUNLOGREPORT, sendMsg).then();
  // tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADRUNLOGREPORT, sendMsg, user_sign);
  // $.busyLoadFull("show",{
  //   background: "rgba(0, 0, 0, 0.55)",
  //   fontawesome: "fa fa-spinner fa-spin fa-3x fa-fw"
  // })
}
function initOnMessage() {
    // 页面数据监听
    tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCLOADRUNLOGREPORT, (pb) => {
        console.log('res =>', pb)
        let msg = ProxyServer_pb.MSG_PCLoadRunLogReport.deserializeBinary(pb)
        console.log('数据模式', is_exported)
        // 数据查询
        if (!is_exported) {
            const report = msg.getReport();
            let exceptionCount = report.getExceptionCountMap();
            let typeCount = report.getTypeCountMap();
            //日志来源对应条数
            let sourceCount = report.getSourceCountMap();
            //日志级别对应条数
            let levelCount = report.getLevelCountMap();
            //日志模块对应条数
            let moduleCount = report.getModuleCountMap();
            let expLogs = report.getLogsList();
            // let _totalCount = PublicDefine_pb.MSG_RunLogReport.deserializeBinary().getTotalCount();
            let _totalCount = msg.getTotalCount();
            // console.log('totalCount',totalCount)
            // console.log('expLogs',expLogs)
            data={"total": 0, "rows":[]};
            data.total=_totalCount;
            for (let i = 0; i < expLogs.length; ++i) {
                let expLog = expLogs[i];
                let row = {
                    operator_name: expLog.getUserId(),
                    logType: expLog.getType(),
                    time: new Date(expLog.getTime()).toLocaleString(),
                    content: expLog.getContent(),
                };
                data.rows.push(row);
            }
            excep_percent_data=[{value:exceptionCount.get(PublicDefine_pb.ExceptionLogType.EXCEPTION_LOG_TYPE_COMMU)===undefined?0
                    :exceptionCount.get(PublicDefine_pb.ExceptionLogType.EXCEPTION_LOG_TYPE_COMMU),name:'通信异常'}
                ,{value:exceptionCount.get(PublicDefine_pb.ExceptionLogType.EXCEPTION_LOG_TYPE_INVALID_ACCESS)===undefined?0
                        :exceptionCount.get(PublicDefine_pb.ExceptionLogType.EXCEPTION_LOG_TYPE_INVALID_ACCESS),name:'越权访问'}
                ,{value:exceptionCount.get(PublicDefine_pb.ExceptionLogType.EXCEPTION_LOG_TYPE_LOGIN_FAIL_MAX)===undefined?0
                        :exceptionCount.get(PublicDefine_pb.ExceptionLogType.EXCEPTION_LOG_TYPE_LOGIN_FAIL_MAX),name:'登录失败次数达到上限'}
                ,{value:exceptionCount.get(PublicDefine_pb.ExceptionLogType.EXCEPTION_LOG_TYPE_LOG_MAX)===undefined?0
                        :exceptionCount.get(PublicDefine_pb.ExceptionLogType.EXCEPTION_LOG_TYPE_LOG_MAX),name:'日志条数达到上限'},
                {value:exceptionCount.get(PublicDefine_pb.ExceptionLogType.EXCEPTION_LOG_TYPE_CPU_MAX)===undefined?0
                        :exceptionCount.get(PublicDefine_pb.ExceptionLogType.EXCEPTION_LOG_TYPE_CPU_MAX),name:'CPU利用率达到上限'},
                {value:exceptionCount.get(PublicDefine_pb.ExceptionLogType.EXCEPTION_LOG_TYPE_DISK_MAX)===undefined?0
                        :exceptionCount.get(PublicDefine_pb.ExceptionLogType.EXCEPTION_LOG_TYPE_DISK_MAX),name:'磁盘利用率达到上限'},
                {value:exceptionCount.get(PublicDefine_pb.ExceptionLogType.EXCEPTION_LOG_TYPE_MEM_MAX)===undefined?0
                        :exceptionCount.get(PublicDefine_pb.ExceptionLogType.EXCEPTION_LOG_TYPE_MEM_MAX),name:'内存利用率达到上限'},
                {value:exceptionCount.get(PublicDefine_pb.ExceptionLogType.EXCEPTION_LOG_TYPE_PROCESS)===undefined?0
                        :exceptionCount.get(PublicDefine_pb.ExceptionLogType.EXCEPTION_LOG_TYPE_PROCESS),name:'进程异常'},
                {value:exceptionCount.get(PublicDefine_pb.ExceptionLogType.EXCEPTION_LOG_TYPE_HARDWARE)===undefined?0
                        :exceptionCount.get(PublicDefine_pb.ExceptionLogType.EXCEPTION_LOG_TYPE_HARDWARE),name:'硬件异常'},
                {value:exceptionCount.get(PublicDefine_pb.ExceptionLogType.EXCEPTION_LOG_TYPE_OTHER)===undefined?0
                        :exceptionCount.get(PublicDefine_pb.ExceptionLogType.EXCEPTION_LOG_TYPE_OTHER),name:'其他'},
                {value:exceptionCount.get(PublicDefine_pb.ExceptionLogType.EXCEPTION_LOG_TYPE_IP)===undefined?0
                        :exceptionCount.get(PublicDefine_pb.ExceptionLogType.EXCEPTION_LOG_TYPE_IP),name:'IP异常'}]
            type_statis_data=[typeCount.get(PublicDefine_pb.LogType.LOG_TYPE_LOGIN)===undefined?0
                :typeCount.get(PublicDefine_pb.LogType.LOG_TYPE_LOGIN)
                ,typeCount.get(PublicDefine_pb.LogType.LOG_TYPE_OPERATE)===undefined?0
                    :typeCount.get(PublicDefine_pb.LogType.LOG_TYPE_OPERATE)
                ,typeCount.get(PublicDefine_pb.LogType.LOG_TYPE_MAINTAIN)===undefined?0
                    :typeCount.get(PublicDefine_pb.LogType.LOG_TYPE_MAINTAIN)
                ,typeCount.get(PublicDefine_pb.LogType.LOG_TYPE_SYSTEM)===undefined?0
                    :typeCount.get(PublicDefine_pb.LogType.LOG_TYPE_SYSTEM)
                ,typeCount.get(PublicDefine_pb.LogType.LOG_TYPE_OTHER)===undefined?0
                    :typeCount.get(PublicDefine_pb.LogType.LOG_TYPE_OTHER)]
            //日志来源各来源条数
            source_count_data=[sourceCount.get(PublicDefine_pb.LogSource.LOG_SOURCE_APP)===undefined?0
                :sourceCount.get(PublicDefine_pb.LogSource.LOG_SOURCE_APP)
                ,sourceCount.get(PublicDefine_pb.LogSource.LOG_SOURCE_OS)===undefined?0
                    :sourceCount.get(PublicDefine_pb.LogSource.LOG_SOURCE_OS)]
            //日志级别各级别条数
            level_count_data=[levelCount.get(PublicDefine_pb.LogLevel.LOG_LEVEL_FATAL)===undefined?0
                :levelCount.get(PublicDefine_pb.LogLevel.LOG_LEVEL_FATAL)
                ,levelCount.get(PublicDefine_pb.LogLevel.LOG_LEVEL_ERROR)===undefined?0
                    :levelCount.get(PublicDefine_pb.LogLevel.LOG_LEVEL_ERROR)
                ,levelCount.get(PublicDefine_pb.LogLevel.LOG_LEVEL_WARN)===undefined?0
                    :levelCount.get(PublicDefine_pb.LogLevel.LOG_LEVEL_WARN)
                ,levelCount.get(PublicDefine_pb.LogLevel.LOG_LEVEL_INFO)===undefined?0
                    :levelCount.get(PublicDefine_pb.LogLevel.LOG_LEVEL_INFO)
                ,levelCount.get(PublicDefine_pb.LogLevel.LOG_LEVEL_DEBUG)===undefined?0
                    :levelCount.get(PublicDefine_pb.LogLevel.LOG_LEVEL_DEBUG)]
            //日志模块各模块条数
            log_module_data=[moduleCount.get(PublicDefine_pb.LogModule.LOG_MODULE_UI)===undefined?0
                :moduleCount.get(PublicDefine_pb.LogModule.LOG_MODULE_UI)
                ,moduleCount.get(PublicDefine_pb.LogModule.LOG_MODULE_COLLECT)===undefined?0
                    :moduleCount.get(PublicDefine_pb.LogModule.LOG_MODULE_COLLECT)
                ,moduleCount.get(PublicDefine_pb.LogModule.LOG_MODULE_ANALYSIS)===undefined?0
                    :moduleCount.get(PublicDefine_pb.LogModule.LOG_MODULE_ANALYSIS)
                ,moduleCount.get(PublicDefine_pb.LogModule.LOG_MODULE_PROXY)===undefined?0
                    :moduleCount.get(PublicDefine_pb.LogModule.LOG_MODULE_PROXY)
                ,moduleCount.get(PublicDefine_pb.LogModule.LOG_MODULE_COMMUNICATE)===undefined?0
                    :moduleCount.get(PublicDefine_pb.LogModule.LOG_MODULE_COMMUNICATE)]
            console.log(type_statis_data)
            console.log(excep_percent_data)
            $("#log-excep-tb").removeClass("hidden")
            $("#log-excep-tb-nodata").addClass("hidden")
            $('#log-excep-tb').bootstrapTable('removeAll');
            $("#log-excep-tb").bootstrapTable("load",data);
            // 设置表格宽度问题
//   let _width=$(".page-content").width()
//   $("#collect-evt-tb>tbody>tr.no-records-found>td").css("width",_width-40+"px")
            if(data){
                if(data.rows !==undefined){
                    if(data.rows.length==0){
                        $("#log-excep-tb").addClass("hidden")
                        $("#log-excep-tb-nodata").removeClass("hidden")
                    }}
            }
            levelPercent();
            moduleStatis();
        // 导出
        } else {
            const reports = msg.getReport();
            let expLogs = reports.getLogsList();
            let str="";
            let operator_name="",logType="",time="",content=""
            for (const expLog of expLogs) {
                operator_name= expLog.getUserId(),
                    logType= expLog.getType(),
                    time= new Date(expLog.getTime()).toLocaleString(),
                    content= expLog.getContent(),
                    str +=operator_name+","+logType+","+time+","+content+"\n"
            }

            // fileName="log_analysis.csv",imgName="log_analysis.png"

            // 转出csv格式文件中中文乱码解决
            str ="\uFEFF"+str;
            console.log(str)
            const timeWatermark = String(moment(new Date()).format('YYYY-MM-DD-HH-mm-ss'));
            console.log(Buffer.from(str))
            util.saveToCSV(Buffer.from(str), filePath+"/"+`log_analysis-${timeWatermark}.csv`);
            // 导出图片.
            setTimeout(function() {
                html2canvas(document.querySelector("#runLog"), {}).then(canvas => {
                    //添加属性
                    canvas.setAttribute('id', 'thecanvas');
                    document.getElementById('images').innerHTML = '';
                    document.getElementById('images').appendChild(canvas);
                    let dataUrl = canvas.toDataURL();
                    // 获取图片资源,生成本地png图片
                    let imgRepalce = dataUrl.split(",")[1];//去掉图片base64码前面部分data:image/png;base64
                    let imgBuffer = new Buffer(imgRepalce, 'base64'); //把base64码转成buffer对象，
                    fs.writeFile(filePath+"/"+`log_analysis-${timeWatermark}.png`, imgBuffer, function(err) {
                    });
                })
                //const { x: left, y: top, width, height } = document.querySelector('#runLog').getBoundingClientRect();
                //ipc.send('capture_img',  { x: Math.floor(left), y: Math.floor(top), width: Math.floor(width), height:Math.floor(height) }, filePath+"/"+imgName);
            },1000);
            ui_util.showFadeTip('导出日志配置成功', 500);
        }

    })
    // 用户校验监听
    tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCVALIDATEUSER, (pb) => {
        const msg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(pb);
        if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
            //ui_util.showFadeTip('验证用户通过!');
            $('#confirmPortDlg').modal('show')
        } else {
            ui_util.getErrorTips(msg.getErrorCode(), '验证用户身份')
        }
    })
}

/**
 * 发送验证密码参数
 * @param psd
 */
function validateUserGenerator(username,psd){
// // 连接服务器
//   tcp_client.connect(function(msgId, pb) {
//     const msg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(pb);
//     if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
//       // ui_util.showFadeTip('验证用户通过!');
//         $('#confirmPortDlg').modal('show')
//     } else {
//       ui_util.getErrorTips(msg.getErrorCode(), '验证用户身份')
//     }
//   }, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
  sendMsg.setUserName(username)
  sendMsg.setPassword(psd)
  tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg).then();
  // tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg, user_sign);
}

/**
 * 导出日志
 */
function exportLogs(filePath,fileName,imgName) {
//   tcp_client.connect(function (msgId, pb) {
//     const msg = ProxyServer_pb.MSG_PCLoadRunLogReport.deserializeBinary(pb).getReport();
//     let expLogs = msg.getLogsList();
//     let str="";
//     let operator_name="",logType="",time="",content=""
//     for (const expLog of expLogs) {
//       operator_name= expLog.getUserId(),
//         logType= expLog.getType(),
//         time= new Date(expLog.getTime()).toLocaleString(),
//         content= expLog.getContent(),
//       str +=operator_name+","+logType+","+time+","+content+"\n"
//     }
//
//     // 转出csv格式文件中中文乱码解决
//     str ="\uFEFF"+str;
// console.log(str)
// console.log(Buffer.from(str))
//       util.saveToCSV(Buffer.from(str), filePath+"/"+fileName);
//       // 导出图片.
//      setTimeout(function() {
//          html2canvas(document.querySelector("#runLog"), {}).then(canvas => {
//              //添加属性
//              canvas.setAttribute('id', 'thecanvas');
//              document.getElementById('images').innerHTML = '';
//              document.getElementById('images').appendChild(canvas);
//              let dataUrl = canvas.toDataURL();
//              // 获取图片资源,生成本地png图片
//              let imgRepalce = dataUrl.split(",")[1];//去掉图片base64码前面部分data:image/png;base64
//              let imgBuffer = new Buffer(imgRepalce, 'base64'); //把base64码转成buffer对象，
//              fs.writeFile(filePath+"/"+imgName, imgBuffer, function(err) {
//              });
//          })
//        //const { x: left, y: top, width, height } = document.querySelector('#runLog').getBoundingClientRect();
//        //ipc.send('capture_img',  { x: Math.floor(left), y: Math.floor(top), width: Math.floor(width), height:Math.floor(height) }, filePath+"/"+imgName);
//      },1000);
//       ui_util.showFadeTip('导出日志配置成功', 500);
//
//     // 消息处理完成关闭连接
//     tcp_client.close();
//   }, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPLoadRunLogReport();
  sendMsg.setStartTime(beginDate);
  sendMsg.setEndTime(endDate);
  sendMsg.setType(log_type);
  sendMsg.setUserId(operator_name);
  is_exported = true
  tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADRUNLOGREPORT, sendMsg).then();
  // tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADRUNLOGREPORT, sendMsg, user_sign);
}

