'use strict';

const ProxyServer_pb = require('../../pb/ProxyServer_pb');
const PublicDefine_pb = require('../../pb/PublicDefine_pb');
const util = require('../../../lib/util');
const ui_util = require('../ui_util');
const tcp_client = require('../../../lib/tcp_client');
const config = require("../../../config.json");
const commu_param_codec = require('../../platform_codec/commu_param_codec');
// 相关变量定义
const user_sign = $("body").attr("user_sign_info");
const user_name = $("body").attr("user_name_info");
console.log("user_sign:", user_sign + user_name);

// 初始化页面表单
$('#commuForm').validator();
$('#commuForm').get(0).reset();
commuFormInitiate();

let file="",filePath="",fileName="communication.cfg";
// 初始化选择文件夹路径&选择文件
$('#select_folder').ace_file_input({
  no_file:'请选择文件夹',
  btn_choose:'选择',
  btn_change:null,
  droppable:false,
  onchange:null,
  thumbnail:false
});
document.querySelector('#select_folder').addEventListener('change', e => {
  for (let entry of e.target.files){
    console.log(entry.name, entry.path);
    filePath=entry.path
  }
});
$('#select_file').ace_file_input({
  no_file:'请选择文件',
  btn_choose:'选择',
  btn_change:null,
  droppable:false,
  onchange:null,
  thumbnail:false,
  allowExt: ['cfg'],
}).on('change', function(){
  let fileDir = $(this).val();
  let suffix = fileDir.substr(fileDir.lastIndexOf("."));
  if ("" == fileDir||".cfg" != suffix) {
    ui_util.showFadeTip('请选择.cfg后缀文件!');
    return false;
  }
  file=$(this).data('ace_input_files')[0].path
});

// 关闭tabs导航自动切换&上一步下一步
$('#commu-tabs a').on('show.bs.tab', function (e) {
  e.preventDefault();//阻止tabs切换
});
$('#next_page').on('click', function (e) {
  if ($("#next_page").hasClass("disabled")) {
    e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
    return;
  }
  $('#commu-tabs>li').removeClass('active');
  $('#commu-tabs>li:eq(1)').addClass('active');
  $('#commu-tab-cont>div').removeClass('active');
  $('#commu-tab-cont #platform').addClass('active');
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。

  $("#platform-tb").jqGrid('setGridWidth', $(".page-content").width());
  jqgridColResize()
});
$('#prev_page').on('click',function () {
  $('#commu-tabs>li').removeClass('active');
  $('#commu-tabs>li:eq(0)').addClass('active');
  $('#commu-tab-cont>div').removeClass('active');
  $('#commu-tab-cont #commu').addClass('active');
})

// 导入进度条动画加载&导出
$('#btn_import').on('click', function (e) {
  $('#validateUserDlg').modal('show');
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  $('#validateUserForm').get(0).reset()
  $('#opraType').val(1)
})
$('#btn_export').on('click', function (e) {
  $('#validateUserDlg').modal('show');
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  $('#validateUserForm').get(0).reset()
  $('#opraType').val(2)
})

$('#port_btn').on('click',function (e) {
  if ($("#port_btn").hasClass("disabled")) {
    return;
  }
  if($('#confirmPortDlg .modal-body .form-group:nth-child(1)').hasClass("hidden")){
    if(!$("#select_folder").next("span.ace-file-container").hasClass("selected")){
      ui_util.showFadeTip('请选择文件路径!')
      return;
    }
  }else if($('#confirmPortDlg .modal-body .form-group:nth-child(2)').hasClass("hidden")){
    if(!($("#select_file").next("span.ace-file-container").hasClass("selected"))){
      ui_util.showFadeTip('请选择文件!')
      return;
    }
  }
  $('#confirmPortDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  if($('#confirmPortDlg .modal-title').attr("wa-name")=="import_tt"){
    importCommuParam(file,fileName)
  }else if($('#confirmPortDlg .modal-title').attr("wa-name")=="export_tt"){
    exportCommuParam(filePath,fileName)
  }
})

// 初始化multiselect框
$('#permissionType').multiselect({
  noneSelectedText:"请选择",
  enableFiltering: true,
  enableHTML: true,
  buttonClass: 'btn btn-white btn-primary',
  templates: {
    button: '<button type="button" class="multiselect dropdown-toggle" data-toggle="dropdown"><span class="multiselect-selected-text"></span> &nbsp;<b class="fa fa-caret-down"></b></button>',
    ul: '<ul class="multiselect-container dropdown-menu"></ul>',
    filter: '<li class="multiselect-item filter"><div class="input-group"><span class="input-group-addon"><i class="fa fa-search"></i></span><input class="form-control multiselect-search" type="text"></div></li>',
    filterClearBtn: '<span class="input-group-btn"><button class="btn btn-default btn-white btn-grey multiselect-clear-filter" type="button"><i class="fa fa-times-circle red2"></i></button></span>',
    li: '<li><a tabindex="0"><label></label></a></li>',
    divider: '<li class="multiselect-item divider"></li>',
    liGroup: '<li class="multiselect-item multiselect-group"><label></label></li>'
  }
});

//新增单行平台参数
$('#add_plat').on('click', function () {
  $('#addPlatDlg').modal('show');
  $('#addPlatForm').get(0).reset();
  $('#addPlatTitle').text('新增平台参数')
  $('#addPlatTitle').attr('wa-data', 'add')
  $('#permissionType').multiselect('refresh')
  let ids = $("#platform-tb").jqGrid('getDataIDs');
  let rowid = Math.max.apply(Math, ids);//获取当前最大行号
  let newrowid = rowid + 1;
  $('#app_plat_rn').val(newrowid);
  $('#save_plat_btn').addClass('disabled')
})
// 新增/修改单行平台参数 更新表格
$('#save_plat_btn').on('click', function (e) {
  if ($("#save_plat_btn").hasClass("disabled")) {
    return;
  }
  let old_ip=$('#old_ip').val();
  let ip=$('#addPlatDlg #platip').val();
  let ipArray=new Array()
  // 获取表格ip列数据
  let ids = $('#platform-tb').getDataIDs();//返回数据表的ID数组["66","39"..]
  let len = ids.length;
  for(let i=0; i<len; i++){
    let getRow = $('#platform-tb').getRowData(ids[i]);//获取当前的数据行
    let colVal = getRow.ip;
ipArray.push(colVal)
  }
  if (ip != ""&&ip!=old_ip) {
    for (let i = 0; i < ipArray.length; i++) {
      if (ip == ipArray[i]) {
        ui_util.showFadeTip('ip已存在!');
        $('#addPlatDlg #platip').focus();
        return false;
      }
    }
  }
  let plat_rn ="";
  let datarow = {
    ip: $('#platip').val(),
    port: $('#platport').val(),
    authorityValue: $('#permissionType').val()==null?0:eval($('#permissionType').val().join("+")),
    authority: $('#permissionType').val()==null?0:eval($('#permissionType').val().join("+")),
    groupId: $('#groupId').val(),
    priority: $('#priority').val(),
  }
  if ($("#addPlatTitle").attr('wa-data') == 'add') {
    plat_rn = $('#app_plat_rn').val()
    $("#platform-tb").jqGrid('addRowData', plat_rn, datarow);
  } else {
    plat_rn = $('#platform-tb').jqGrid('getGridParam','selrow')
    $("#platform-tb").jqGrid('setRowData', plat_rn, datarow);
  }
  $('#addPlatDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
})

// 删除单行平台参数 更新表格
$('#remove_plat_btn').on('click', function () {
  let plat_rn =$('#platform-tb').jqGrid('getGridParam','selrow')
  // for (let i =  rowIds.length-1; i>=0; i --){
    $("#platform-tb").jqGrid("delRowData", plat_rn);
  // }
  $("#platform-tb").trigger("reloadGrid");
});

// 保存通信&平台所有信息
$('#save_all_data').on('click', function () {
  $('#validateUserDlg').modal('show');
  $('#validateUserForm').get(0).reset()
  $('#opraType').val("save")
})
$('#save_all_btn').on('click', function () {
  //生成表格所有信息
  let rowNum = $("#platform-tb").jqGrid('getGridParam', 'rowNum');//获取当前页条数
  let total = $("#platform-tb").jqGrid('getGridParam', 'records');//获取总数
  $("#platform-tb").jqGrid('setGridParam', {rowNum: total}).trigger('reloadGrid');  //把grid重新加载成total
  let data = $("#platform-tb").jqGrid('getRowData');   //获取全部数据
  $("#platform-tb").jqGrid('setGridParam', {rowNum: rowNum}).trigger('reloadGrid');  //还原成原先状态
  console.log(data)
  saveAllData(data);
})

// 弹出密码校验框
$('#validateuser_btn').on('click',function (e) {
  if ($("#validateuser_btn").hasClass("disabled")) {
    return;
  }
  $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  let username=user_name
  let psd=$('#validateUserDlg #password').val()
  validateUserGenerator(username,psd);
})

/**
 * 初始化平台页tb
 */
let platData = new Array();
function loadPlatformTb() {
  let SelectRowIndx;
  $("#platform-tb").jqGrid(
    {
      datatype: "local",
      height: "90%",
      autowidth: true,
      shrinkToFit: true,//按比例初始化列宽
      viewrecords: true,
      sortname: 'rn',
      sortorder: 'desc',
      emptyrecords: "当前没有平台参数记录",
      loadtext: "读取中...",
      // multiselect: true,
      rownumbers: true,
      loadComplete: function (a, b, c) {
        $("#platform-tb").jqGrid('setLabel', 'rn', '编号', {
          'text-align': 'center',
          'vertical-align': 'middle',
          "width": "35"
        });
      },
      colNames: ['平台ip', '事件上传端口', '权限值','平台ip地址权限','分组号','优先级', '操作'],
      colModel: [
        // {name: 'id', index: 'id', hidden: true},
        {name: 'ip', index: 'ip',},
        {name: 'port', index: 'port',},
        {name: 'authorityValue', index: 'authorityValue',hidden: true},
        {name: 'authority', index: 'authority', formatter: transformAuthor},
        {name: 'groupId', index: 'groupId',hidden: true},
        {name: 'priority', index: 'priority',hidden: true},
        {name: 'operation', index: 'operation', sortable: false, formatter: displayButtons}]
    });

  // 清空和填充表格数据
  $("#platform-tb").jqGrid('clearGridData');
  for (let i = 0; i <= platData.length; i++) {
    $("#platform-tb").jqGrid('addRowData', i + 1, platData[i]);
  }
  /**
   * ip权限value转换文本
   */
  function transformAuthor(cellvalue, options, rowObject) {
    let cellv=""
    if ((cellvalue&1) === 1) cellv +="上传权限,";
    if ((cellvalue&2) === 2) cellv +="读权限,";
    if ((cellvalue&4) === 4) cellv +="对检测装置写权限,";
    if ((cellvalue&8) === 8) cellv +="对检测对象写权限";
    return cellv
  }
  /**\
   * 创造行末尾按钮并修改删除点击方法.
   * @param cellvalue
   * @param options
   * @param rowObject
   * @returns {string}
   */
  function displayButtons(cellvalue, options, rowObject) {
    return "<div class='hidden-sm hidden-xs btn-group'>" +
      "<button class='btn btn-minier btn-inverse wa-mlr5' " +
      "row-obj-str='" + JSON.stringify(rowObject) + "'" +
      "onclick=\"let _domList=$(this).parent().parent().parent().children()\n" +
      "$('#addPlatDlg').modal('show')\n" +
      "$('#addPlatForm').get(0).reset()\n" +
      "$('#addPlatTitle').text('修改平台参数')\n" +
      "$('#addPlatTitle').attr('wa-data', 'edit')\n" +
      // "$('#app_plat_rn').val($('#platform-tb').jqGrid('getGridParam','selrow'))\n" +
      " $('#permissionType').multiselect('refresh')\n" +
      "$('#old_ip').val($(_domList[1]).attr('title'))\n" +
      "$('#platip').val($(_domList[1]).attr('title'))\n" +
      "$('#platport').val($(_domList[2]).attr('title'))\n" +
      "let arr=new Array()\n" +
      "if ((parseInt($(_domList[3]).attr('title'))&1) === 1){arr.push('1')}\n" +
      "if ((parseInt($(_domList[3]).attr('title'))&2) === 2){arr.push('2')}\n" +
      "if ((parseInt($(_domList[3]).attr('title'))&4) === 4){arr.push('4')}\n" +
      "if ((parseInt($(_domList[3]).attr('title'))&8) === 8){arr.push('8')}\n" +
      "$('#permissionType').multiselect('select', arr)\n" +
      "$('#groupId').val($(_domList[5]).attr('title'))\n" +
      "$('#priority').val($(_domList[6]).attr('title'))\">" +
      "<i class='ace-icon fa fa-edit bigger-80'>&nbsp;修改</i>" +
      "</button>" +
      "<button class='btn btn-minier btn-danger wa-mlr5' " +
      "onclick=\"$('#confirmDlg').modal('show')\">" +
      "<i class='ace-icon fa fa-trash-o bigger-80'>&nbsp;删除</i>" +
      "</button>" +
      "</div>";
  }
  jqgridColResize()
  tableResize();
}

/**
 * 重新调整jqgrid每列的宽度
 */
function jqgridColResize(){
  let td=$('#tdCompute')//获取计算实际列长度的容器
    ,tds//临时保存列
    ,arr=[];//用于保存最大的列宽
  //遍历每行获得每行中的最大列宽
  $('.ui-jqgrid-htable tr,.ui-jqgrid-btable tr:gt(0)').each(function(){
    $(this).find('td,th').each(function(idx){
      arr[idx]=Math.max(arr[idx]?arr[idx]:0,td.html($(this).text())[0].offsetWidth);
    })
  });
  //设置页头单元格宽度
  $('.ui-jqgrid-labels th').each(function(idx){this.style.width=arr[idx]+'px'});
  //设置内容表格中控制单元格宽度的单元格，在第一行
  $('.ui-jqgrid-btable tr:eq(0) td').each(function(idx){this.style.width=arr[idx]+'px'});
  // 设置操作栏固定宽度
  $('.ui-jqgrid .ui-jqgrid-htable th:last-child').css("width", "95");
  $('.ui-jqgrid-btable>tbody>tr>td:last-child').css("width","95");
}
/**
 * 表格自适应窗口
 */
function tableResize(){
  $(window).on('resize.jqGrid', function () {
    $("#platform-tb").jqGrid('setGridWidth', $(".page-content").width());
    jqgridColResize()
  });
  const parent_column = $("#platform-tb").closest('[class*="col-"]');
  $(document).on('settings.ace.jqGrid', function (ev, event_name, collapsed) {
    if (event_name == 'sidebar_collapsed' || event_name == 'main_container_fixed') {
      setTimeout(function () {
        $("#platform-tb").jqGrid('setGridWidth', parent_column.width());
        jqgridColResize()
      }, 0);
    }
  })
}

/**
 * 初始化通信frm表单,获取平台tb数组数据
 */
function commuFormInitiate() {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      const commu_param = commu_param_codec.unpackLoadCommuParam(dp_packet.content);
      console.log('commu_param', commu_param)
      console.log('commu_param', commu_param.getPlatformsList())
      $('#app_commu_id').val(commu_param.getId());
      $('#tcp_port').val(commu_param.getServerListenPort());
      $('#syslog_port').val(commu_param.getSyslogListenPort());
      $('#snmp_port').val(commu_param.getSnmpListenPort());
      $('#agent_port').val(commu_param.getProxyListenPport());

      platData = []; //清空platData数组
      const platlist = commu_param.getPlatformsList();
      for (let i = 0; i < platlist.length; i++) {
        const platObj = {
          // id: commu_param.getId(),
          ip: util.int2ip(platlist[i].getIp()),
          port: platlist[i].getPort(),
          authorityValue: platlist[i].getAuthority(),
          authority: platlist[i].getAuthority(),
          groupId: platlist[i].getGroup()>>4,
          priority: platlist[i].getGroup()&0b1111,
        }
        platData.push(platObj)
      }
      loadPlatformTb();
    }
    // 消息处理完成关闭连接
    tcp_client.close();
  }, config.host, config.port);

  // 发送消息
  tcp_client.send(commu_param_codec.packLoadCommuParam());
}

/**
 * 发送验证密码参数
 * @param psd
 */
function validateUserGenerator(username,psd){
// 连接服务器
  tcp_client.connect(function(msgId, pb) {
    const msg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(pb);
    if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
      if($('#opraType').val()==1){
        $('#confirmPortDlg').modal('show')
        $('#confirmPortDlg .modal-title').attr("wa-name","import_tt")
        $('#confirmPortDlg .modal-body .form-group:nth-child(1)').removeClass("hidden")
        $('#confirmPortDlg .modal-body .form-group:nth-child(2)').addClass("hidden")
      }else if($('#opraType').val()==2){
        $('#confirmPortDlg').modal('show')
        $('#confirmPortDlg .modal-title').attr("wa-name","export_tt")
        $('#confirmPortDlg .modal-body .form-group:nth-child(2)').removeClass("hidden")
        $('#confirmPortDlg .modal-body .form-group:nth-child(1)').addClass("hidden")
      }else if($('#opraType').val()=="save"){
        $('#confirmDlg2').modal('show')
      }
    } else {
      ui_util.getErrorTips(msg.getErrorCode(), '验证用户身份')
      commuFormInitiate()
    }
  }, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
  sendMsg.setUserName(username)
  sendMsg.setPassword(psd)
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg, user_sign);
}

/**
 * 导出资产
 */
function exportCommuParam(filePath,fileName) {
  tcp_client.connect(function (msgId, pb) {
    const msg = ProxyServer_pb.MSG_PCExportData.deserializeBinary(pb);
    if (msg.getErrorCode()==0) {
      let targetBuf = msg.getData();
      util.saveToCSV(targetBuf, filePath+"/"+fileName);
      ui_util.showFadeTip('导出通信参数成功')
    }else {
      ui_util.getErrorTips(msg.getErrorCode(),'导出通信参数');
    }
    // 消息处理完成关闭连接
    tcp_client.close();
    commuFormInitiate()
  }, config.host, config.port);

  // 发送消息
  let exportData = new ProxyServer_pb.MSG_CPExportData();
  exportData.setType(PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_COMMUNICATE)
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPEXPORTDATA, exportData, user_sign);
}
/**
 * 导入资产
 */
function importCommuParam(file,fileName) {
  tcp_client.connect(function (msgId, pb) {
      $('.parent_content').busyLoad("hide");
    const msg = ProxyServer_pb.MSG_PCImportData.deserializeBinary(pb);
    if (msg.getErrorCode()==0) {
      ui_util.showFadeTip('导入通信参数成功!');
      ui_util.showLoading(30);
    }else {
      ui_util.getErrorTips(msg.getErrorCode(),'导入通信参数');
    }
    // 消息处理完成关闭连接
    tcp_client.close();
    commuFormInitiate()
  }, config.host, config.port);

  // 发送消息
  let importData = new ProxyServer_pb.MSG_CPImportData();
  importData.setType(PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_COMMUNICATE)
  const savedConfigs = util.readFromFile(file,false);
  importData.setData(savedConfigs.toString())
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPIMPORTDATA, importData, user_sign);
    $('.parent_content').busyLoad("show",{
        background: "rgba(0, 0, 0, 0.59)",
    fontawesome: "fa fa-spinner fa-spin fa-3x fa-fw"
  })
}

/**
 *
 * 发送通信&平台参数数据(总
 */
function saveAllData(data_list) {
// 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      ui_util.showFadeTip('通信平台参数更新成功!');
      $("#platform-tb").trigger("reloadGrid");
      ui_util.showLoading(30);
    } else {
      ui_util.getErrorTips(dp_packet.return_value,'通信平台参数更新')
    }
    // 消息处理完成关闭连接
    tcp_client.close();
    // commuFormInitiate();
  }, config.host, config.port);

  // 发送消息
  let commu_param = new PublicDefine_pb.MSG_CommuParam(); // 通信页frm表单参数
  commu_param.setId(parseInt($('#app_commu_id').val())); // commu id
  commu_param.setServerListenPort(parseInt($('#tcp_port').val()));
  commu_param.setSnmpListenPort(parseInt($('#snmp_port').val()));
  commu_param.setSyslogListenPort(parseInt($('#syslog_port').val()));
  commu_param.setProxyListenPport(parseInt($('#agent_port').val()));
  for (let i = 0; i < data_list.length; i++) { //平台表格数组参数遍历
    let platform = new PublicDefine_pb.MSG_Platform();
    platform.setIp(parseInt(util.ip2int(data_list[i].ip)));
    platform.setPort(parseInt(data_list[i].port));
    platform.setAuthority(parseInt(data_list[i].authorityValue));
    platform.setGroup((parseInt(data_list[i].groupId)<<4) + parseInt(data_list[i].priority));
    commu_param.addPlatforms(platform);
  }

  // console.log(commu_param, commu_param_codec.packAddCommuParam(commu_param))
  tcp_client.send(commu_param_codec.packUpdateCommuParam(commu_param));
}
