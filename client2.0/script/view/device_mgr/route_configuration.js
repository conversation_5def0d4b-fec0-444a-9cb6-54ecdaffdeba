'use strict';

const {ipc<PERSON><PERSON>er} = require('electron');
const ProxyServer_pb = require('../../pb/ProxyServer_pb');
const PublicDefine_pb = require('../../pb/PublicDefine_pb');
const ui_util = require('../ui_util');
const EventEmitter = require('events');
const tcp_client = require('../../../lib/tcp_client');
const config = require("../../../config.json");
const route_codec = require('../../platform_codec/route_codec');
const util = require('../../../lib/util');
// 相关变量定义
const user_sign = $("body").attr("user_sign_info");
const user_name = $("body").attr("user_name_info");;
console.log("user_sign:", user_sign + user_name);

let file="",filePath="",fileName="route.cfg";
// 初始化选择文件夹路径&选择文件
$('#select_folder').ace_file_input({
  no_file:'请选择文件夹',
  btn_choose:'选择',
  btn_change:null,
  droppable:false,
  onchange:null,
  thumbnail:false
});
document.querySelector('#select_folder').addEventListener('change', e => {
  for (let entry of e.target.files){
    console.log(entry.name, entry.path);
    filePath=entry.path
  }
});
$('#select_file').ace_file_input({
  no_file:'请选择文件',
  btn_choose:'选择',
  btn_change:null,
  droppable:false,
  onchange:null,
  thumbnail:false,
  allowExt: ['cfg'],
}).on('change', function(){
  let fileDir = $(this).val();
  let suffix = fileDir.substr(fileDir.lastIndexOf("."));
  if ("" == fileDir||".cfg" != suffix) {
    ui_util.showFadeTip('请选择.cfg后缀文件!');
    return false;
  }
  // console.log($(this).data('ace_input_files'));
  file=$(this).data('ace_input_files')[0].path
});

//点击新增路由
$('#add_route').on('click', function () {
  $('#validateUserDlg').modal('show');
  $('#validateUserForm').get(0).reset()
  $('#opraType').val("add")
})
//点击保存 发送路由参数到服务器
$('#btn_route_save').on('click', function (e) {
  if ($("#btn_route_save").hasClass("disabled")) {
    return;
  }
  if ($("#addRouteTitle").attr('wa-data')=='add') {
    addRouteFormSave();
  } else {
    editRouteFormSave();
  }
  $('#addRouteDlg').modal('hide');
  e.preventDefault();//阻止bootstrap button点击自动刷新页面默认行为。
})

// 点击确定删除路由
$('#remove_route_btn').on('click', function () {
  removeRouteData($('#remove_route_id').val());
});

// 导入进度条动画加载&导出
$('#btn_import').on('click', function () {
  $('#validateUserDlg').modal('show');
  $('#validateUserForm').get(0).reset()
  $('#opraType').val(1)
})
$('#btn_export').on('click', function () {
  $('#validateUserDlg').modal('show');
  $('#validateUserForm').get(0).reset()
  $('#opraType').val(2)
})

$('#port_btn').on('click',function (e) {
  if ($("#port_btn").hasClass("disabled")) {
    return;
  }
  if($('#confirmPortDlg .modal-body .form-group:nth-child(1)').hasClass("hidden")){
    if(!$("#select_folder").next("span.ace-file-container").hasClass("selected")){
      ui_util.showFadeTip('请选择文件路径!')
      return;
    }
  }else if($('#confirmPortDlg .modal-body .form-group:nth-child(2)').hasClass("hidden")){
    if(!($("#select_file").next("span.ace-file-container").hasClass("selected"))){
      ui_util.showFadeTip('请选择文件!')
      return;
    }
  }
  $('#confirmPortDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  if($('#confirmPortDlg .modal-title').attr("wa-name")=="import_tt"){
    importRoutes(file,fileName)
  }else if($('#confirmPortDlg .modal-title').attr("wa-name")=="export_tt"){
    exportRoutes(filePath,fileName)
  }
})

let opraRouteId,opraSeg,opraMask,opraGetway,opraRemoveId
// 弹出密码校验框
$('#validateuser_btn').on('click',function (e) {
  if ($("#validateuser_btn").hasClass("disabled")) {
    return;
  }
  $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  let username=user_name
  let psd=$('#validateUserDlg #password').val()
  opraRouteId=$('#opraRouteId').val()
  opraSeg=$('#opraSeg').val()
  opraMask=$('#opraMask').val()
  opraGetway=$('#opraGetway').val()
  opraRemoveId=$('#opraRemoveId').val()
  validateUserGenerator(username,psd);
})

/**
 * 初始化刷新表格.
 */
let routeData = new Array();
// 设映射，key：路由id，value：路由信息，MSG_Route
let routeMap = new Map();
function loadRouteConfigTb() {
  $("#route-config-tb").jqGrid(
    {
      datatype: "local",
      height: "90%",
      autowidth: true,
      shrinkToFit: true,//按比例初始化列宽
      pager: '#route-config-pager',
      viewrecords: true,
      rowNum: 10,//每页显示数据个数
      rowList: [5, 10, 20, 50, 100],
      recordtext: "{0} - {1}\u3000共 {2} 条",
      emptyrecords: "当前没有路由配置记录",
      loadtext: "读取中...",
      pgtext: "{0} 共 {1} 页",
      colNames: ['id', '目的网段', '目的掩码', '网关地址', '操作'],
      colModel: [
        {name: 'id', index: 'id', hidden: true},
        {name: 'routeSegment', index: 'routeSegment',},
        {name: 'routeMask', index: 'routeMask',},
        {name: 'routeGateway', index: 'routeGateway',},
        {name: 'operation', index: 'operation', sortable: false, formatter: displayButtons
      }]
    });

  // 清空和填充表格数据
  $("#route-config-tb").jqGrid('clearGridData');
  for (let i = 0; i <= routeData.length; i++) {
    $("#route-config-tb").jqGrid('addRowData', i + 1, routeData[i]);
  }
// 数组数据分页.本地数据分页.
  $('.ui-pg-table.ui-pager-table #first_route-config-pager span')
    .addClass('ace-icon fa fa-angle-double-left bigger-100')
  $('.ui-pg-table.ui-pager-table #prev_route-config-pager span')
    .addClass('ace-icon fa fa-angle-left bigger-100')
  $('.ui-pg-table.ui-pager-table #next_route-config-pager span')
    .addClass('ace-icon fa fa-angle-right bigger-100')
  $('.ui-pg-table.ui-pager-table #last_route-config-pager span')
    .addClass('ace-icon fa fa-angle-double-right bigger-100')
  const localData = {page: 1, total: 2, records: "2", rows: []};
  localData.rows = routeData;
  localData.records = routeData.length;
  localData.total = (routeData.length % 2 == 0) ? (routeData.length / 2) : (Math.floor(routeData.length / 2) + 1);
  const reader = {
    root: function (obj) {return localData.rows;},
    page: function (obj) {return localData.page;},
    total: function (obj) {return localData.total;},
    records: function (obj) {return localData.records;},
    repeatitems: false
  };
  $("#route-config-tb").setGridParam({
    data: localData.rows,
    reader: reader
  }).trigger('reloadGrid');
  /**\
   * 创造行末尾按钮并修改删除点击方法.
   */
  function displayButtons(cellvalue, options, rowObject) {
    return "<div class='hidden-sm hidden-xs btn-group'>" +
      "<button class='btn btn-minier btn-inverse wa-mlr5'" +
      "row-obj-str='"+JSON.stringify(rowObject)+"'" +
      "onclick=\"$('#validateUserDlg').modal('show')\n"+
    "$('#validateUserForm').get(0).reset()\n"+
    "$('#opraType').val('edit')\n"+
      "$('#opraRouteId').val(JSON.parse($(this).attr('row-obj-str')).id)\n" +
      "$('#opraSeg').val(JSON.parse($(this).attr('row-obj-str')).routeSegment)\n" +
      "$('#opraMask').val(JSON.parse($(this).attr('row-obj-str')).routeMask)\n" +
      "$('#opraGetway').val(JSON.parse($(this).attr('row-obj-str')).routeGateway)\">" +
      "<i class='ace-icon fa fa-edit bigger-80'>&nbsp;修改</i>" +
      "</button>"+
      "<button class='btn btn-minier btn-danger'" +
      "row-id='"+rowObject.id+"'" +
      "onclick=\"$('#validateUserDlg').modal('show')\n"+
      "$('#validateUserForm').get(0).reset()\n"+
      "$('#opraType').val('del')\n"+
      "$('#opraRemoveId').val($(this).attr('row-id'))\">" +
      "<i class='ace-icon fa fa-trash-o bigger-80'>&nbsp;删除</i>" +
      "</button>" +
      "</div>";
  }

  jqgridColResize()
  tableResize();
}

/**
 * 重新调整jqgrid每列的宽度
 */
function jqgridColResize(){
  let td=$('#tdCompute')//获取计算实际列长度的容器
    ,tds//临时保存列
    ,arr=[];//用于保存最大的列宽
  //遍历每行获得每行中的最大列宽
  $('.ui-jqgrid-htable tr,.ui-jqgrid-btable tr:gt(0)').each(function(){
    $(this).find('td,th').each(function(idx){
      arr[idx]=Math.max(arr[idx]?arr[idx]:0,td.html($(this).text())[0].offsetWidth);
    })
  });
  //设置页头单元格宽度
  $('.ui-jqgrid-labels th').each(function(idx){this.style.width=arr[idx]+'px'});
  //设置内容表格中控制单元格宽度的单元格，在第一行
  $('.ui-jqgrid-btable tr:eq(0) td').each(function(idx){this.style.width=arr[idx]+'px'});
}
/**
 * 表格自适应窗口
 */
function tableResize(){
  $(window).on('resize.jqGrid', function () {
    $("#route-config-tb").jqGrid('setGridWidth', $(".page-content").width());
    jqgridColResize()
  });
  const parent_column = $("#route-config-tb").closest('[class*="col-"]');
  $(document).on('settings.ace.jqGrid', function (ev, event_name, collapsed) {
    if (event_name == 'sidebar_collapsed' || event_name == 'main_container_fixed') {
      setTimeout(function () {
        $("#route-config-tb").jqGrid('setGridWidth', parent_column.width());
        jqgridColResize()
      }, 0);
    }
  })
}

/**
 * 初始化页面.填充表格数组数据.
 */
function pageInit() {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      // console.log('dp_packet', dp_packet);
      routeData=[]; //清空routeData数组
      const routes = route_codec.unpackLoadRoute(dp_packet.content);
      // console.log('route', routes);
      for (let i = 0; i < routes.length; i++) {
        const routeObj = {
          id: routes[i].getId(),
          routeSegment: util.int2ip(routes[i].getDestSegment()),
          routeMask: util.int2ip(routes[i].getDestMask()),
          routeGateway: util.int2ip(routes[i].getGateway())
        }
        routeData.push(routeObj)
      }
      // console.log('routeData:', routeData)
      loadRouteConfigTb();
    }
    // 消息处理完成关闭连接
    tcp_client.close();
  }, config.host, config.port);

  // 发送消息
  tcp_client.send(route_codec.packLoadRoute());
}
pageInit();

/**
 * 发送验证密码参数
 * @param psd
 */
function validateUserGenerator(username,psd){
// 连接服务器
  tcp_client.connect(function(msgId, pb) {
    const msg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(pb);
    if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
      if($('#opraType').val()==1){
        $('#confirmPortDlg').modal('show')
        $('#confirmPortDlg .modal-title').attr("wa-name","import_tt")
        $('#confirmPortDlg .modal-body .form-group:nth-child(1)').removeClass("hidden")
        $('#confirmPortDlg .modal-body .form-group:nth-child(2)').addClass("hidden")
      }else if($('#opraType').val()==2){
        $('#confirmPortDlg').modal('show')
        $('#confirmPortDlg .modal-title').attr("wa-name","export_tt")
        $('#confirmPortDlg .modal-body .form-group:nth-child(2)').removeClass("hidden")
        $('#confirmPortDlg .modal-body .form-group:nth-child(1)').addClass("hidden")
      }else if($('#opraType').val()=="add"){
        $('#addRouteDlg').modal('show');
        $('#addRouteForm').get(0).reset();
        $('#addRouteTitle').text('新增路由')
        $('#addRouteTitle').attr('wa-data', 'add')
        $('#btn_route_save').addClass('disabled')
      }else if($('#opraType').val()=="edit"){
        $('#addRouteDlg').modal('show')
        $('#addRouteForm').get(0).reset()
        $('#addRouteTitle').text('修改路由')
        $('#addRouteTitle').attr('wa-data', 'edit')
        $('#addRouteForm #app_route_id').val(opraRouteId)
        $('#addRouteForm #seg').val(opraSeg)
        $('#addRouteForm #mask').val(opraMask)
        $('#addRouteForm #gateway').val(opraGetway)
      }else if($('#opraType').val()=="del"){
        $('#confirmDlg').modal('show')
        $('#remove_route_id').val(opraRemoveId)
      }
    } else {
      ui_util.getErrorTips(msg.getErrorCode(), '验证用户身份')
    }
  }, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
  sendMsg.setUserName(username)
  sendMsg.setPassword(psd)
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg, user_sign);
}

/**
 * 导出资产
 */
function exportRoutes(filePath,fileName) {
  tcp_client.connect(function (msgId, pb) {
    const msg = ProxyServer_pb.MSG_PCExportData.deserializeBinary(pb);
    if (msg.getErrorCode()==0) {
      let targetBuf = msg.getData();
      util.saveToCSV(targetBuf, filePath+"/"+fileName);
      ui_util.showFadeTip('导出路由配置成功')
    }else {
      ui_util.getErrorTips(msg.getErrorCode(),'导出路由配置');
    }
    // 消息处理完成关闭连接
    tcp_client.close();
  }, config.host, config.port);

  // 发送消息
  let exportData = new ProxyServer_pb.MSG_CPExportData();
  exportData.setType(PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_ROUTE)
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPEXPORTDATA, exportData, user_sign);
}
/**
 * 导入资产
 */
function importRoutes(file,fileName) {
  tcp_client.connect(function (msgId, pb) {
      $('.parent_content').busyLoad("hide");
    const msg = ProxyServer_pb.MSG_PCImportData.deserializeBinary(pb);
    if (msg.getErrorCode()==0) {
      ui_util.showFadeTip('导入路由配置成功!');
    }else {
      ui_util.getErrorTips(msg.getErrorCode(),'导入路由配置');
    }
    // 消息处理完成关闭连接
    tcp_client.close();
    pageInit();
  }, config.host, config.port);

  // 发送消息
  let importData = new ProxyServer_pb.MSG_CPImportData();
  importData.setType(PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_ROUTE)
  const savedConfigs = util.readFromFile(file,false);
  importData.setData(savedConfigs.toString())
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPIMPORTDATA, importData, user_sign);
    $('.parent_content').busyLoad("show",{
        background: "rgba(0, 0, 0, 0.59)",
    fontawesome: "fa fa-spinner fa-spin fa-3x fa-fw"
  })
}

/**
 * 发送新增路由form参数
 */
function addRouteFormSave() {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      ui_util.showFadeTip('新增路由成功!');
    } else {
      ui_util.getErrorTips(dp_packet.return_value,'新增路由')
    }
    // 消息处理完成关闭连接
    tcp_client.close();
    pageInit();
  }, config.host, config.port);

  // 发送消息
  let route = new PublicDefine_pb.MSG_Route();
  route.setId(0);
  route.setDestSegment(util.ip2int($('#addRouteForm #seg').val()));
  route.setDestMask(util.ip2int($('#addRouteForm #mask').val()));
  route.setGateway(util.ip2int($('#addRouteForm #gateway').val()));
  // console.log('route:', route)
  tcp_client.send(route_codec.packAddRoute(route));
}

/**
 * 发送修改路由form参数
 */
function editRouteFormSave() {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      ui_util.showFadeTip('更新路由成功!');
    } else {
      ui_util.getErrorTips(dp_packet.return_value,'更新路由')
    }
    // 消息处理完成关闭连接
    tcp_client.close();
    pageInit();
  }, config.host, config.port);

  // 发送消息
  let route = new PublicDefine_pb.MSG_Route();
  route.setId(parseInt($('#addRouteForm #app_route_id').val()));
  route.setDestSegment(util.ip2int($('#addRouteForm #seg').val()));
  route.setDestMask(util.ip2int($('#addRouteForm #mask').val()));
  route.setGateway(util.ip2int($('#addRouteForm #gateway').val()));
  // console.log('route:', route)
  tcp_client.send(route_codec.packUpdateRoute(route));
}

/**
 * 发送删除路由信息
 */
function removeRouteData(routeid) {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      ui_util.showFadeTip('删除路由成功!', ids);
    } else {
      ui_util.getErrorTips(dp_packet.return_value,'删除路由')
    }
    // 消息处理完成关闭连接
    tcp_client.close();

    pageInit();
  }, config.host, config.port);

  // 发送消息
  let ids = new Array();
  ids.push(routeid);
  tcp_client.send(route_codec.packRemoveRoute(ids));
}
