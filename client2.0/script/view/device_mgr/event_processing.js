'use strict';

const {ipc<PERSON><PERSON><PERSON>} = require('electron');
const ProxyServer_pb = require('../../pb/ProxyServer_pb');
const PublicDefine_pb = require('../../pb/PublicDefine_pb');
const util = require('../../../lib/util');
const ui_util = require('../ui_util');
const tcp_client = require('../../../lib/tcp_client');
const config = require("../../../config.json");
const event_param_codec = require('../../platform_codec/event_param_codec');
// 相关变量定义
const user_sign = $("body").attr("user_sign_info");
const user_name = $("body").attr("user_name_info");
console.log("user_sign:", user_sign + user_name);

let file="",filePath="",fileName="event.cfg";
// 初始化选择文件夹路径&选择文件
$('#select_folder').ace_file_input({
  no_file:'请选择文件夹',
  btn_choose:'选择',
  btn_change:null,
  droppable:false,
  onchange:null,
  thumbnail:false
});
document.querySelector('#select_folder').addEventListener('change', e => {
  for (let entry of e.target.files){
    console.log(entry.name, entry.path);
    filePath=entry.path
  }
});
$('#select_file').ace_file_input({
  no_file:'请选择文件',
  btn_choose:'选择',
  btn_change:null,
  droppable:false,
  onchange:null,
  thumbnail:false,
  allowExt: ['cfg'],
}).on('change', function(){
  let fileDir = $(this).val();
  let suffix = fileDir.substr(fileDir.lastIndexOf("."));
  if ("" == fileDir||".cfg" != suffix) {
    ui_util.showFadeTip('请选择.cfg后缀文件!');
    return false;
  }
  // console.log($(this).data('ace_input_files'));
  file=$(this).data('ace_input_files')[0].path
});

// 初始化页面表单
$('#handlerForm').validator();
$('#handlerForm').get(0).reset();
handlerFormInitiate();

// 导入进度条动画加载&导出
$('#btn_import').on('click', function (e) {
  $('#validateUserDlg').modal('show');
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  $('#validateUserForm').get(0).reset()
  $('#opraType').val(1)
})
$('#btn_export').on('click', function (e) {
  $('#validateUserDlg').modal('show');
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  $('#validateUserForm').get(0).reset()
  $('#opraType').val(2)
})

//点击发送事件处理Frm参数
$('#btn_handler_save').on('click', function (e) {
  if ($("#btn_handler_save").hasClass("disabled")) {
    e.preventDefault();//阻止bootstrap button点击自动刷新页面默认行为。
    return;
  }
  $('#validateUserDlg').modal('show');
  $('#validateUserForm').get(0).reset()
  $('#opraType').val("save")
  e.preventDefault();//阻止bootstrap button点击自动刷新页面默认行为。
});

$('#port_btn').on('click',function (e) {
  if ($("#port_btn").hasClass("disabled")) {
    return;
  }
  if($('#confirmPortDlg .modal-body .form-group:nth-child(1)').hasClass("hidden")){
    if(!$("#select_folder").next("span.ace-file-container").hasClass("selected")){
      ui_util.showFadeTip('请选择文件路径!')
      return;
    }
  }else if($('#confirmPortDlg .modal-body .form-group:nth-child(2)').hasClass("hidden")){
    if(!($("#select_file").next("span.ace-file-container").hasClass("selected"))){
      ui_util.showFadeTip('请选择文件!')
      return;
    }
  }
  $('#confirmPortDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  if($('#confirmPortDlg .modal-title').attr("wa-name")=="import_tt"){
    importEvents(file,fileName)
  }else if($('#confirmPortDlg .modal-title').attr("wa-name")=="export_tt"){
    exportEvents(filePath,fileName)
  }
})

// 弹出密码校验框
$('#validateuser_btn').on('click',function (e) {
  if ($("#validateuser_btn").hasClass("disabled")) {
    return;
  }
  $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  let username=user_name
  let psd=$('#validateUserDlg #password').val()
  validateUserGenerator(username,psd);
})

/**
 * 初始化Frm表单函数
 */
function handlerFormInitiate(){
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      // console.log('dp_packet', dp_packet);
      const event_param = event_param_codec.unpackLoadEventParam(dp_packet.content);
      // console.log('event_param',event_param)
      // console.log('event_param', event_param.getCpuThreshold(), event_param.getHistoryEventReportInterval());
      $('#cpu_limit').val(event_param.getCpuThreshold());
      $('#mem_limit').val(event_param.getMemThreshold());
      $('#trf_limit').val(event_param.getIfFlowThreshold());
      $('#login_limit').val(event_param.getLoginFailedThreshold());
      $('#merg_time').val(event_param.getEventMergeCycle());
      $('#disk_limit').val(event_param.getDiskThreshold());
      $('#time_interval').val(event_param.getHistoryEventReportInterval());
    } else {
      ui_util.showFadeTip('获取表单信息失败!');
    }

      // 消息处理完成关闭连接
      tcp_client.close();
  }, config.host, config.port);

  // 发送消息
  tcp_client.send(event_param_codec.packLoadEventParam());
}

/**
 * 发送验证密码参数
 * @param psd
 */
function validateUserGenerator(username,psd){
// 连接服务器
  tcp_client.connect(function(msgId, pb) {
    const msg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(pb);
    if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
      if($('#opraType').val()==1){
        $('#confirmPortDlg').modal('show')
        // $('#port_btn').addClass('disabled')
        $('#confirmPortDlg .modal-title').attr("wa-name","import_tt")
        $('#confirmPortDlg .modal-body .form-group:nth-child(1)').removeClass("hidden")
        $('#confirmPortDlg .modal-body .form-group:nth-child(2)').addClass("hidden")
      }else if($('#opraType').val()==2){
        $('#confirmPortDlg').modal('show')
        // $('#port_btn').addClass('disabled')
        $('#confirmPortDlg .modal-title').attr("wa-name","export_tt")
        $('#confirmPortDlg .modal-body .form-group:nth-child(2)').removeClass("hidden")
        $('#confirmPortDlg .modal-body .form-group:nth-child(1)').addClass("hidden")
      }else if($('#opraType').val()=="save"){
        handlerFormSave();
      }
    } else {
      ui_util.getErrorTips(msg.getErrorCode(), '验证用户身份')
      handlerFormInitiate()
    }
  }, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
  sendMsg.setUserName(username)
  sendMsg.setPassword(psd)
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg, user_sign);
}

/**
 * 导出资产
 */
function exportEvents(filePath,fileName) {
  tcp_client.connect(function (msgId, pb) {
    const msg = ProxyServer_pb.MSG_PCExportData.deserializeBinary(pb);
    if (msg.getErrorCode()==0) {
      let targetBuf = msg.getData();
      util.saveToCSV(targetBuf, filePath+"/"+fileName);
      ui_util.showFadeTip('导出事件处理成功')
    }else {
      ui_util.getErrorTips(msg.getErrorCode(),'导出事件处理');
    }
    // 消息处理完成关闭连接
    tcp_client.close();
    handlerFormInitiate()
  }, config.host, config.port);

  // 发送消息
  let exportData = new ProxyServer_pb.MSG_CPExportData();
  exportData.setType(PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_EVENT)
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPEXPORTDATA, exportData, user_sign);
}
/**
 * 导入资产
 */
function importEvents(file,fileName) {
  tcp_client.connect(function (msgId, pb) {
      $('.parent_content').busyLoad("hide");
    const msg = ProxyServer_pb.MSG_PCImportData.deserializeBinary(pb);
    if (msg.getErrorCode()==0) {
      ui_util.showFadeTip('导入事件处理成功!');
      ui_util.showLoading(30);
    }else {
      ui_util.getErrorTips(msg.getErrorCode(),'导入事件处理');
    }
    // 消息处理完成关闭连接
    tcp_client.close();
    handlerFormInitiate()
  }, config.host, config.port);

  // 发送消息
  let importData = new ProxyServer_pb.MSG_CPImportData();
  importData.setType(PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_EVENT)
  const savedConfigs = util.readFromFile(file,false);
  importData.setData(savedConfigs.toString())
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPIMPORTDATA, importData, user_sign);
    $('.parent_content').busyLoad("show",{
        background: "rgba(0, 0, 0, 0.59)",
    fontawesome: "fa fa-spinner fa-spin fa-3x fa-fw"
  })
}

/**
 *
 * 发送事件处理Frm参数
 */
function handlerFormSave() {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      ui_util.showFadeTip('事件参数保存成功!');
      ui_util.showLoading(30);
    } else {
      ui_util.getErrorTips(dp_packet.return_value,'事件参数保存')
    }

      // 消息处理完成关闭连接
      tcp_client.close();
  }, config.host, config.port);

  // 发送消息
  let event_param = new PublicDefine_pb.MSG_EventParam();
  event_param.setCpuThreshold($('#cpu_limit').val());
  event_param.setMemThreshold($('#mem_limit').val());
  event_param.setIfFlowThreshold($('#trf_limit').val());
  event_param.setLoginFailedThreshold($('#login_limit').val());
  event_param.setEventMergeCycle($('#merg_time').val());
  event_param.setDiskThreshold($('#disk_limit').val());
  event_param.setHistoryEventReportInterval($('#time_interval').val());
  tcp_client.send(event_param_codec.packAddEventParam(event_param));
}
