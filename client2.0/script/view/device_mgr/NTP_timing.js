'use strict';

const ProxyServer_pb = require('../../pb/ProxyServer_pb');
const PublicDefine_pb = require('../../pb/PublicDefine_pb');
const util = require('../../../lib/util');
const ui_util = require('../ui_util');
const tcp_client = require('../../../lib/tcp_client');
const config = require("../../../config.json");
const ntp_param_codec = require('../../platform_codec/ntp_param_codec');
// 相关变量定义
const user_sign = $("body").attr("user_sign_info");
const user_name = $("body").attr("user_name_info");
console.log("user_sign:", user_sign + user_name);

// 初始化页面表单
$('#ntpForm').validator();
$('#ntpForm').get(0).reset();
ntpFormInitiate();

let file="",filePath="",fileName="NTP.cfg";
// 初始化选择文件夹路径&选择文件
$('#select_folder').ace_file_input({
  no_file:'请选择文件夹',
  btn_choose:'选择',
  btn_change:null,
  droppable:false,
  onchange:null,
  thumbnail:false
});
document.querySelector('#select_folder').addEventListener('change', e => {
  for (let entry of e.target.files){
    console.log(entry.name, entry.path);
    filePath=entry.path
  }
});
$('#select_file').ace_file_input({
  no_file:'请选择文件',
  btn_choose:'选择',
  btn_change:null,
  droppable:false,
  onchange:null,
  thumbnail:false,
  allowExt: ['cfg'],
}).on('change', function(){
  let fileDir = $(this).val();
  let suffix = fileDir.substr(fileDir.lastIndexOf("."));
  if ("" == fileDir||".cfg" != suffix) {
    ui_util.showFadeTip('请选择.cfg后缀文件!');
    return false;
  }
  // console.log($(this).data('ace_input_files'));
  file=$(this).data('ace_input_files')[0].path
});
// 导入进度条动画加载&导出
$('#btn_import').on('click', function (e) {
  $('#validateUserDlg').modal('show');
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  $('#validateUserForm').get(0).reset()
  $('#opraType').val(1)
})
$('#btn_export').on('click', function (e) {
  $('#validateUserDlg').modal('show');
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  $('#validateUserForm').get(0).reset()
  $('#opraType').val(2)
})

//点击发送事件处理Frm参数
$('#btn_ntp_save').on('click', function (e) {
  if ($("#btn_ntp_save").hasClass("disabled")) {
    e.preventDefault();//阻止bootstrap button点击自动刷新页面默认行为。
    return;
  }
  $('#validateUserDlg').modal('show');
  $('#validateUserForm').get(0).reset()
  $('#opraType').val("save")
  e.preventDefault();//阻止bootstrap button点击自动刷新页面默认行为。
});

$('#port_btn').on('click',function (e) {
  if ($("#port_btn").hasClass("disabled")) {
    return;
  }
  if($('#confirmPortDlg .modal-body .form-group:nth-child(1)').hasClass("hidden")){
    if(!$("#select_folder").next("span.ace-file-container").hasClass("selected")){
      ui_util.showFadeTip('请选择文件路径!')
      return;
    }
  }else if($('#confirmPortDlg .modal-body .form-group:nth-child(2)').hasClass("hidden")){
    if(!($("#select_file").next("span.ace-file-container").hasClass("selected"))){
      ui_util.showFadeTip('请选择文件!')
      return;
    }
  }
  $('#confirmPortDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  if($('#confirmPortDlg .modal-title').attr("wa-name")=="import_tt"){
    importNtpParam(file,fileName)
  }else if($('#confirmPortDlg .modal-title').attr("wa-name")=="export_tt"){
    exportNtpParam(filePath,fileName)
  }
})

// 弹出密码校验框
$('#validateuser_btn').on('click',function (e) {
  if ($("#validateuser_btn").hasClass("disabled")) {
    return;
  }
  $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  let username=user_name
  let psd=$('#validateUserDlg #password').val()
  validateUserGenerator(username,psd);
})

/**
 * 初始化Frm表单函数
 */
function ntpFormInitiate(){
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      // console.log('dp_packet', dp_packet);
      const ntp_param = ntp_param_codec.unpackLoadNtpParam(dp_packet.content);
      // console.log('ntp_param', ntp_param);
      // console.log('ntp_param', ntp_param.getMainClockMainNetworkIp(), ntp_param.getNtpIsBroadcast());
      $('#app_ntp_id').val(ntp_param.getId());
      $('#mmip').val(util.int2ip(ntp_param.getMainClockMainNetworkIp()));
      $('#msip').val(util.int2ip(ntp_param.getMainClockBackupNetworkIp()));
      $('#smip').val(util.int2ip(ntp_param.getBackupClockMainNetworkIp()));
      $('#ssip').val(util.int2ip(ntp_param.getBackupClockBackupNetworkIp()));
      $('#port').val(ntp_param.getNtpServerPort());
      $('#cycle').val(ntp_param.getNtpVerifyTimeCycle());
      if(ntp_param.getNtpIsBroadcast()==0){
        $('#enable').prop('checked','checked');
      }else if(ntp_param.getNtpIsBroadcast()==1){
        $('#p2p').prop('checked','checked');
      }
    }
      // 消息处理完成关闭连接
      tcp_client.close();
  }, config.host, config.port);

  // 发送消息
  tcp_client.send(ntp_param_codec.packLoadNtpParam());
}

/**
 * 发送验证密码参数
 * @param psd
 */
function validateUserGenerator(username,psd){
// 连接服务器
  tcp_client.connect(function(msgId, pb) {
    const msg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(pb);
    if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
      if($('#opraType').val()==1){
        $('#confirmPortDlg').modal('show')
        $('#confirmPortDlg .modal-title').attr("wa-name","import_tt")
        $('#confirmPortDlg .modal-body .form-group:nth-child(1)').removeClass("hidden")
        $('#confirmPortDlg .modal-body .form-group:nth-child(2)').addClass("hidden")
      }else if($('#opraType').val()==2){
        $('#confirmPortDlg').modal('show')
        $('#confirmPortDlg .modal-title').attr("wa-name","export_tt")
        $('#confirmPortDlg .modal-body .form-group:nth-child(2)').removeClass("hidden")
        $('#confirmPortDlg .modal-body .form-group:nth-child(1)').addClass("hidden")
      }else if($('#opraType').val()=="save"){
        ntpFormSave();
      }
    } else {
      ui_util.getErrorTips(msg.getErrorCode(), '验证用户身份')
      ntpFormInitiate()
    }
  }, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
  sendMsg.setUserName(username)
  sendMsg.setPassword(psd)
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg, user_sign);
}

/**
 * 导出资产
 */
function exportNtpParam(filePath,fileName) {
  tcp_client.connect(function (msgId, pb) {
    const msg = ProxyServer_pb.MSG_PCExportData.deserializeBinary(pb);
    if (msg.getErrorCode()==0) {
      let targetBuf = msg.getData();
      util.saveToCSV(targetBuf, filePath+"/"+fileName);
      ui_util.showFadeTip('导出NTP对时成功')
    }else {
      ui_util.getErrorTips(msg.getErrorCode(),'导出NTP对时');
    }
    // 消息处理完成关闭连接
    tcp_client.close();
    ntpFormInitiate()
  }, config.host, config.port);

  // 发送消息
  let exportData = new ProxyServer_pb.MSG_CPExportData();
  exportData.setType(PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_NTP)
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPEXPORTDATA, exportData, user_sign);
}
/**
 * 导入资产
 */
function importNtpParam(file,fileName) {
  tcp_client.connect(function (msgId, pb) {
      $('.parent_content').busyLoad("hide");
    const msg = ProxyServer_pb.MSG_PCImportData.deserializeBinary(pb);
    if (msg.getErrorCode()==0) {
      ui_util.showFadeTip('导入NTP对时成功!');
      ui_util.showLoading(30);
    }else {
      ui_util.getErrorTips(msg.getErrorCode(),'导入NTP对时');
    }
    // 消息处理完成关闭连接
    tcp_client.close();
    ntpFormInitiate()
  }, config.host, config.port);

  // 发送消息
  let importData = new ProxyServer_pb.MSG_CPImportData();
  importData.setType(PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_NTP)
  const savedConfigs = util.readFromFile(file,false);
  importData.setData(savedConfigs.toString())
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPIMPORTDATA, importData, user_sign);
    $('.parent_content').busyLoad("show",{
        background: "rgba(0, 0, 0, 0.59)",
    fontawesome: "fa fa-spinner fa-spin fa-3x fa-fw"
  })
}

/**
 *
 * 发送事件处理Frm参数
 */
function ntpFormSave() {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      ui_util.showFadeTip('NTP参数保存成功!');
      ui_util.showLoading(30);
    } else {
      ui_util.getErrorTips(dp_packet.return_value,'NTP参数保存')
    }
      // 消息处理完成关闭连接
      tcp_client.close();
  }, config.host, config.port);

  // 发送消息
  let ntp_param = new PublicDefine_pb.MSG_NtpParam();
  ntp_param.setId(1);
  ntp_param.setMainClockMainNetworkIp(util.ip2int($('#mmip').val()));
  ntp_param.setMainClockBackupNetworkIp(util.ip2int($('#msip').val()));
  ntp_param.setBackupClockMainNetworkIp(util.ip2int($('#smip').val()));
  ntp_param.setBackupClockBackupNetworkIp(util.ip2int($('#ssip').val()));
  ntp_param.setNtpServerPort($('#port').val());
  ntp_param.setNtpVerifyTimeCycle($('#cycle').val());
  const is_broadcast=$('[name="is_broadcast"]:checked').val();
  if(is_broadcast =="0"){
    ntp_param.setNtpIsBroadcast(0);
  }else if(is_broadcast=="1"){
    ntp_param.setNtpIsBroadcast(1);
  }
  // console.log("ntp_param",ntp_param)
  tcp_client.send(ntp_param_codec.packUpdateNtpParam(ntp_param));
}
