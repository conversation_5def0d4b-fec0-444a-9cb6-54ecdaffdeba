'use strict';

const {ipc<PERSON><PERSON><PERSON>} = require('electron');
const ProxyServer_pb = require('../../pb/ProxyServer_pb');
const PublicDefine_pb = require('../../pb/PublicDefine_pb');
const ui_util = require('../ui_util');
const EventEmitter = require('events');
const tcp_client = require('../../../lib/tcp_client');
const config = require("../../../config.json");
const net_card_codec = require('../../platform_codec/net_card_codec');

const util = require('../../../lib/util');
// 相关变量定义
const user_sign = $("body").attr("user_sign_info");
const user_name = $("body").attr("user_name_info");
console.log("user_sign:", user_sign + user_name);

let file="",filePath="",fileName="network.cfg";
// 初始化选择文件夹路径&选择文件
$('#select_folder').ace_file_input({
  no_file:'请选择文件夹',
  btn_choose:'选择',
  btn_change:null,
  droppable:false,
  onchange:null,
  thumbnail:false
});
document.querySelector('#select_folder').addEventListener('change', e => {
  for (let entry of e.target.files){
    console.log(entry.name, entry.path);
    filePath=entry.path
  }
});
$('#select_file').ace_file_input({
  no_file:'请选择文件',
  btn_choose:'选择',
  btn_change:null,
  droppable:false,
  onchange:null,
  thumbnail:false,
  allowExt: ['cfg'],
}).on('change', function(){
  let fileDir = $(this).val();
  let suffix = fileDir.substr(fileDir.lastIndexOf("."));
  if ("" == fileDir||".cfg" != suffix) {
    ui_util.showFadeTip('请选择.cfg后缀文件!');
    return false;
  }
  // console.log($(this).data('ace_input_files'));
  file=$(this).data('ace_input_files')[0].path
});
// 导入进度条动画加载&导出
$('#btn_import').on('click', function () {
  $('#validateUserDlg').modal('show');
  $('#validateUserForm').get(0).reset()
  $('#opraType').val(1)
})
$('#btn_export').on('click', function () {
  $('#validateUserDlg').modal('show');
  $('#validateUserForm').get(0).reset()
  $('#opraType').val(2)
})
//新增网卡配置
$('#btn_Newly').on('click', function () {
    if(netData.length<8){
        $('#validateUserDlg').modal('show');
        $('#validateUserForm').get(0).reset();
        $('#opraType').val(3);
    }else{
        ui_util.showFadeTip('网卡数量已达上限8个，不允许新增!');
        return false;
    }

})


//点击保存 发送网卡参数到服务器
$('#btn_net_save').on('click', function (e) {
  if ($("#btn_net_save").hasClass("disabled")) {
    return;
  }
  // 焦点离开触发ip验重事件
  let old_ip=$('#old_ip').val();
  let ip=$('#editNetDlg #ip').val();
  if (ip != ""&&ip !=old_ip) {
    for (let i = 0; i < netData.length; i++) {
      if (ip == netData[i].ip) {
        ui_util.showFadeTip('ip已存在!');
        $('#editNetDlg #ip').focus();
        return false;
      }
    }
  }
  $('#editNetDlg').modal('hide');
  e.preventDefault();//阻止bootstrap button点击自动刷新页面默认行为。
  saveNetConfigData();
})
//新增网卡配置点击保存 发送网卡参数到服务器
$('#add_btn_net_save').on('click', function (e) {
    if ($("#add_btn_net_save").hasClass("disabled")) {
        return;
    }
    // 焦点离开触发ip验重事件
    let old_ip=$('#add_old_ip').val();
    let ip=$('#add_editNetDlg #add_ip').val();
    let nicName = $('#add_editNetDlg #add_eth_nameStr').val();//新增时获取输入的网卡名称
    if(nicName==""||nicName==undefined||nicName==""){
        ui_util.showFadeTip('请输入网卡名称!');
        $('#add_editNetDlg #add_eth_nameStr').focus();
        return false;
    }
    if (ip != ""&&ip !=old_ip) {
        for (let i = 0; i < netData.length; i++) {
            if (ip == netData[i].ip) {
                ui_util.showFadeTip('ip已存在!');
                $('#add_editNetDlg #add_ip').focus();
                return false;
            }
            if(nicName==netData[i].eth_nameStr){
                ui_util.showFadeTip('网卡名称已存在!');
                $('#add_editNetDlg #add_eth_nameStr').focus();
                return false;
            }
        }
    }
    $('#add_editNetDlg').modal('hide');
    e.preventDefault();//阻止bootstrap button点击自动刷新页面默认行为。
    addNetConfigData();
})
//删除网卡配置
$('#removeUserBtn').on('click', function (e) {

    $('#confirmDlg').modal('hide');
    e.preventDefault();//阻止bootstrap button点击自动刷新页面默认行为。
    deleteNetConfigData();
})


$('#port_btn').on('click',function (e) {
  if ($("#port_btn").hasClass("disabled")) {
    return;
  }
  if($('#confirmPortDlg .modal-body .form-group:nth-child(1)').hasClass("hidden")){
    if(!$("#select_folder").next("span.ace-file-container").hasClass("selected")){
      ui_util.showFadeTip('请选择文件路径!')
      return;
    }
  }else if($('#confirmPortDlg .modal-body .form-group:nth-child(2)').hasClass("hidden")){
    if(!($("#select_file").next("span.ace-file-container").hasClass("selected"))){
      ui_util.showFadeTip('请选择文件!')
      return;
    }
  }
  $('#confirmPortDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  if($('#confirmPortDlg .modal-title').attr("wa-name")=="import_tt"){
    importNetCards(file,fileName)
  }else if($('#confirmPortDlg .modal-title').attr("wa-name")=="export_tt"){
    exportNetCards(filePath,fileName)
  }
})

let opraNetId,opraNameStr,opraOldIp,opraIp,opraMask
// 弹出密码校验框
$('#validateuser_btn').on('click',function (e) {
  if ($("#validateuser_btn").hasClass("disabled")) {
    return;
  }
  $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  let username=user_name
  let psd=$('#validateUserDlg #password').val()
  opraNetId=$('#opraNetId').val()
  opraNameStr=$('#opraNameStr').val()
  opraOldIp=$('#opraOldIp').val()
  opraIp=$('#opraIp').val()
  opraMask=$('#opraMask').val()
  validateUserGenerator(username,psd);
})

/**
 * 初始化刷新表格.
 */
let netData = new Array();
// 设映射，key：网卡id，value：网卡信息，MSG_NetCard
let netMap = new Map();
function loadNetConfigTb() {
  $("#net-config-tb").jqGrid(
    {
      datatype: "local",
      height: "90%",
      autowidth: true,
      shrinkToFit: true,//按比例初始化列宽
      pager: '#net-config-pager',
      viewrecords: true,
      sortname: 'eth_nameStr',
      sortorder: 'asc',
      rowNum: 10,//每页显示数据个数
      rowList: [5, 10, 20, 50, 100],
      recordtext: "{0} - {1}\u3000共 {2} 条",
      emptyrecords: "当前没有网卡配置记录",
      loadtext: "读取中...",
      pgtext: "{0} 共 {1} 页",
      // rownumbers: true,
      colNames: [ '网卡名称', 'ip地址', '子网掩码', '操作'],
      colModel: [
        // {name: 'id', index: 'id',sorttype:'integer'},
        {name: 'eth_nameStr', index: 'eth_nameStr',},
        {name: 'ip', index: 'ip',},
        {name: 'mask', index: 'mask',},
        {name: 'operation', index: 'operation', sortable: false, formatter: displayButtons}]
    });

  // 清空和填充表格数据
  $("#net-config-tb").jqGrid('clearGridData');
  for (let i = 0; i <= netData.length; i++) {
    $("#net-config-tb").jqGrid('addRowData', i + 1, netData[i]);
  }
// 数组数据分页
  $('.ui-pg-table.ui-pager-table #first_net-config-pager span')
    .addClass('ace-icon fa fa-angle-double-left bigger-100')
  $('.ui-pg-table.ui-pager-table #prev_net-config-pager span')
    .addClass('ace-icon fa fa-angle-left bigger-100')
  $('.ui-pg-table.ui-pager-table #next_net-config-pager span')
    .addClass('ace-icon fa fa-angle-right bigger-100')
  $('.ui-pg-table.ui-pager-table #last_net-config-pager span')
    .addClass('ace-icon fa fa-angle-double-right bigger-100')
  const localData = {page: 1, total: 2, records: "2", rows: []};
  localData.rows = netData;
  localData.records = netData.length;
  localData.total = (netData.length % 2 == 0) ? (netData.length / 2) : (Math.floor(netData.length / 2) + 1);
  const reader = {
    root: function (obj) {return localData.rows;},
    page: function (obj) {return localData.page;},
    total: function (obj) {return localData.total;},
    records: function (obj) {return localData.records;},
    repeatitems: false
  };
  $("#net-config-tb").setGridParam({
    data: localData.rows,
    reader: reader
  }).trigger('reloadGrid');
  /**\
   * 创造行末尾按钮并修改删除点击方法.
   */
  function displayButtons(cellvalue, options, rowObject) {
    return "<div class='hidden-sm hidden-xs btn-group'>" +
      "<button class='btn btn-minier btn-inverse wa-mlr5' " +
      "row-obj-str='" + JSON.stringify(rowObject) + "'" +
      "onclick=\"$('#validateUserDlg').modal('show')\n"+
    "$('#validateUserForm').get(0).reset()\n"+
    "$('#opraType').val('edit')\n"+
      "$('#opraNetId').val(JSON.parse($(this).attr('row-obj-str')).id)\n" +
      "$('#opraNameStr').val(JSON.parse($(this).attr('row-obj-str')).eth_nameStr)\n" +
      "$('#opraOldIp').val(JSON.parse($(this).attr('row-obj-str')).ip)\n" +
      "$('#opraIp').val(JSON.parse($(this).attr('row-obj-str')).ip)\n" +
      "$('#opraMask').val(JSON.parse($(this).attr('row-obj-str')).mask)\">" +
      "<i class='ace-icon fa fa-edit bigger-80'>&nbsp;修改</i>" +
      "</button>" +"" +
      "<button class='btn btn-minier btn-danger'" +
      "row-id='" + rowObject.id + "'" +
      "onclick=\"$('#validateUserDlg').modal('show')\n" +
      "$('#validateUserForm').get(0).reset()\n" +
      "$('#opraType').val('4')\n" +
      "$('#remove_user_id').val($(this).attr('row-id'))\">" +
      "<i class='ace-icon fa fa-trash-o bigger-80'>&nbsp;删除</i>" +
      "</button>" +
      "</div>";
  }
  //新增按钮点击方法
    function disAddButtons(cellvalue, options, rowObject) {
        return "<div class='hidden-sm hidden-xs btn-group'>" +
            "<button class='btn btn-minier btn-inverse wa-mlr5' " +
            "row-obj-str='" + JSON.stringify(rowObject) + "'" +
            "onclick=\"$('#validateUserDlg').modal('show')\n"+
            "$('#validateUserForm').get(0).reset()\n"+
            "$('#opraType').val('3')\n"+
            "<i class='ace-icon fa fa-edit bigger-80'>&nbsp;新增</i>" +
            "</button>" +
            "</div>";
    }

  jqgridColResize();
  tableResize();
}

/**
 * 重新调整jqgrid每列的宽度
 */
function jqgridColResize(){
  let td=$('#tdCompute')//获取计算实际列长度的容器
    ,tds//临时保存列
    ,arr=[];//用于保存最大的列宽
  //遍历每行获得每行中的最大列宽
  $('.ui-jqgrid-htable tr,.ui-jqgrid-btable tr:gt(0)').each(function(){
    $(this).find('td,th').each(function(idx){
      arr[idx]=Math.max(arr[idx]?arr[idx]:0,td.html($(this).text())[0].offsetWidth);
    })
  });
  //设置页头单元格宽度
  $('.ui-jqgrid-labels th').each(function(idx){this.style.width=arr[idx]+'px'});
  //设置内容表格中控制单元格宽度的单元格，在第一行
  $('.ui-jqgrid-btable tr:eq(0) td').each(function(idx){this.style.width=arr[idx]+'px'});
}
/**
 * 表格自适应窗口
 */
function tableResize(){
  $(window).on('resize.jqGrid', function () {
    $("#net-config-tb").jqGrid('setGridWidth', $(".page-content").width());
    jqgridColResize()
  });
  const parent_column = $("#net-config-tb").closest('[class*="col-"]');
  $(document).on('settings.ace.jqGrid', function (ev, event_name, collapsed) {
    if (event_name == 'sidebar_collapsed' || event_name == 'main_container_fixed') {
      setTimeout(function () {
        $("#net-config-tb").jqGrid('setGridWidth', parent_column.width());
        jqgridColResize()
      }, 0);
    }
  })
}

/**
 * 初始化页面.填充表格数组数据.
 */
function pageInit() {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      netData = []; //清空数组
      const net_cards = net_card_codec.unpackLoadNetCard(dp_packet.content);
      // console.log('net_card', net_cards[0].getNetcardName(), net_cards[0].getMask());
      // console.log('net_card', net_cards);
      for (let i = 0; i < net_cards.length; i++) {
        const netObj = {
          id: net_cards[i].getId(),
          eth_nameStr: $.trim(net_cards[i].getNetcardName()),
          ip: util.int2ip(net_cards[i].getIp()),
          mask: util.int2ip(net_cards[i].getMask())
        }
        netData.push(netObj)
      }
      // console.log('netData:', netData)
      loadNetConfigTb();
    }
    // 消息处理完成关闭连接
    tcp_client.close();
  }, config.host, config.port);

  // 发送消息
  tcp_client.send(net_card_codec.packLoadNetCard());
}
pageInit();

/**
 * 发送验证密码参数
 * @param psd
 */
function validateUserGenerator(username,psd){
// 连接服务器
  tcp_client.connect(function(msgId, pb) {
    const msg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(pb);
    if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
      if($('#opraType').val()==1){
        $('#confirmPortDlg').modal('show')
        $('#confirmPortDlg .modal-title').attr("wa-name","import_tt")
        $('#confirmPortDlg .modal-body .form-group:nth-child(1)').removeClass("hidden")
        $('#confirmPortDlg .modal-body .form-group:nth-child(2)').addClass("hidden")
      }else if($('#opraType').val()==2){
        $('#confirmPortDlg').modal('show')
        $('#confirmPortDlg .modal-title').attr("wa-name","export_tt")
        $('#confirmPortDlg .modal-body .form-group:nth-child(2)').removeClass("hidden")
        $('#confirmPortDlg .modal-body .form-group:nth-child(1)').addClass("hidden")
      }else if($('#opraType').val()==3){
          if(netData.length<8){
              $('#add_editNetDlg').modal('show')
              $('#add_editNetForm').get(0).reset()
              $('#add_app_net_id').val()
              $('#add_eth_nameStr').val()
              $('#add_old_ip').val()
              $('#add_ip').val()
              $('#add_mask').val()
          }else{
              ui_util.showFadeTip('网卡数量已达上限8个，不允许新增!');
              return false;
          }
      }else if($('#opraType').val()==4){
        $('#confirmDlg').modal('show')
        $('#editNetForm').get(0).reset()
        $('#app_net_id').val(opraNetId)
        $('#eth_nameStr').val(opraNameStr)
        $('#old_ip').val(opraOldIp)
        $('#ip').val(opraIp)
        $('#mask').val(opraMask)
      }else if($('#opraType').val()=="edit"){
        $('#editNetDlg').modal('show')
        $('#editNetForm').get(0).reset()
        $('#app_net_id').val(opraNetId)
        $('#eth_nameStr').val(opraNameStr)
        $('#old_ip').val(opraOldIp)
        $('#ip').val(opraIp)
        $('#mask').val(opraMask)
      }
    } else {
      ui_util.getErrorTips(msg.getErrorCode(), '验证用户身份')
    }
  }, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
  sendMsg.setUserName(username)
  sendMsg.setPassword(psd)
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg, user_sign);
}

/**
 * 导出资产
 */
function exportNetCards(filePath,fileName) {
  tcp_client.connect(function (msgId, pb) {
    const msg = ProxyServer_pb.MSG_PCExportData.deserializeBinary(pb);
    if (msg.getErrorCode()==0) {
      let targetBuf = msg.getData();
      util.saveToCSV(targetBuf, filePath+"/"+fileName);
      ui_util.showFadeTip('导出网卡配置成功')
    }else {
      ui_util.getErrorTips(msg.getErrorCode(),'导出网卡配置');
    }
    // 消息处理完成关闭连接
    tcp_client.close();
  }, config.host, config.port);

  // 发送消息
  let exportData = new ProxyServer_pb.MSG_CPExportData();
  exportData.setType(PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_NET_CARD)
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPEXPORTDATA, exportData, user_sign);
}
/**
 * 导入资产
 */
function importNetCards(file,fileName) {
  tcp_client.connect(function (msgId, pb) {
      $('.parent_content').busyLoad("hide");
    const msg = ProxyServer_pb.MSG_PCImportData.deserializeBinary(pb);
    if (msg.getErrorCode()==0) {
      ui_util.showFadeTip('导入网卡配置成功!');
    }else {
      ui_util.getErrorTips(msg.getErrorCode(),'导入网卡配置');
    }
    // 消息处理完成关闭连接
    tcp_client.close();
    pageInit();
  }, config.host, config.port);

  // 发送消息
  let importData = new ProxyServer_pb.MSG_CPImportData();
  importData.setType(PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_NET_CARD)
  const savedConfigs = util.readFromFile(file,false);
  importData.setData(savedConfigs.toString())
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPIMPORTDATA, importData, user_sign);
    $('.parent_content').busyLoad("show",{
        background: "rgba(0, 0, 0, 0.59)",
    fontawesome: "fa fa-spinner fa-spin fa-3x fa-fw"
  })
}

/**
 * 发送网卡参数信息
 */
function saveNetConfigData() {
    // 连接服务器
    tcp_client.connect(function (dp_packet) {
        if (dp_packet.return_value === 0) {
            ui_util.showFadeTip('网卡更新成功!');
        } else {
            ui_util.getErrorTips(dp_packet.return_value,'网卡更新')
        }
        // 消息处理完成关闭连接
        tcp_client.close();
        pageInit();
    }, config.host, config.port);

    // 发送消息
    let net_card = new PublicDefine_pb.MSG_NetCard();
    net_card.setId($('#app_net_id').val());
    net_card.setNetcardName($('#eth_nameStr').val());
    net_card.setIp(util.ip2int($('#ip').val()));
    net_card.setMask(util.ip2int($('#mask').val()));
    tcp_client.send(net_card_codec.packUpdateNetCard(net_card));

}
//新增网卡
function addNetConfigData() {
    // 连接服务器
    tcp_client.connect(function (dp_packet) {
        if (dp_packet.return_value === 0) {
            ui_util.showFadeTip('网卡新增成功!');
        } else {
            ui_util.getErrorTips(dp_packet.return_value,'网卡新增')
        }
        // 消息处理完成关闭连接
        tcp_client.close();
        pageInit();
    }, config.host, config.port);

    // 发送消息
    let net_card = new PublicDefine_pb.MSG_NetCard();
    net_card.setId($('#add_app_net_id').val());
    net_card.setNetcardName($('#add_eth_nameStr').val());
    net_card.setIp(util.ip2int($('#add_ip').val()));
    net_card.setMask(util.ip2int($('#add_mask').val()));
    tcp_client.send(net_card_codec.packAddNetCard(net_card));

}
//删除网卡
function deleteNetConfigData() {
    // 连接服务器
    tcp_client.connect(function (dp_packet) {
        if (dp_packet.return_value === 0) {
            ui_util.showFadeTip('网卡删除成功!');
        } else {
            ui_util.getErrorTips(dp_packet.return_value,'网卡删除')
        }
        // 消息处理完成关闭连接
        tcp_client.close();
        pageInit();
    }, config.host, config.port);

    // 发送消息
    let net_card = new Array();
    net_card.push(parseInt($('#remove_user_id').val()))
    tcp_client.send(net_card_codec.packRemoveNetCard(net_card));

}
