'use strict';

const ProxyServer_pb = require('../pb/ProxyServer_pb');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const util = require('../../lib/util');
const ui_util = require('./ui_util');
const tcp_client = require('../../lib/tcp_client');
const config = require("../../config.json");
// const collect_event_codec = require('../platform_codec/collect_event_codec');
// 相关变量定义
const user_sign = $("body").attr("user_sign_info");
const user_name = $("body").attr("user_name_info");
const tcpInstance = require('../../lib/v2/tls_client.js');
const {ipcRenderer} = require('electron');

console.log("user_sign:", user_sign + user_name);

let filePath="",logFileName="";
// 初始化选择文件夹路径&选择文件
// $('#select_folder').ace_file_input({
//   no_file:'请选择文件夹',
//   btn_choose:'选择',
//   btn_change:null,
//   droppable:false,
//   onchange:null,
//   thumbnail:false
// });
// document.querySelector('#select_folder').addEventListener('change', e => {
//   for (let entry of e.target.files){
//     console.log(entry.name, entry.path);
//     filePath=entry.path
//   }
// });

// 备份日志
  $('#backup').on('click',function () {
    $('#confirmDlgTitle').text('备份');
    $('#confirmDlgTitle').attr('wa-data', 'backup')
    $('#confirmDlg .modal-body p').text('确定备份日志？')
    $('#confirmDlg').modal('show');
  })
$('#confirmBtn').on('click',function (e) {
    let log_source=$('#log_source').val()===""?-1:parseInt($('#log_source').val()),
  log_level=$('#log_level').val()===""?-1:parseInt($('#log_level').val())
    backupLogGenerator(log_source,log_level);
  $('#confirmDlg').modal('hide');
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
})


// 导出按钮
document.querySelector('#export_selectPath_btn').addEventListener('click', async (e) => {
    console.log('选择文件路径 =>')
    const folderPath = await ipcRenderer.invoke('select-folder');
    if (folderPath) {
        console.log('选中的文件夹路径:', folderPath);
        $("#export_file_path").val(folderPath);
        filePath = folderPath
    } else {
        console.log('用户取消选择文件夹');
    }
});
$('#port_btn').on('click',function (e) {
  if ($("#port_btn").hasClass("disabled")) {
    return;
  }
  if(!filePath){
    ui_util.showFadeTip('请选择文件路径!')
    return;
  }
  $('#confirmPortDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  exportLogs(filePath,logFileName)
})

// 点击确定删除用户
$('#removeFileBtn').on('click', function () {
  removeLogFileGenerator($('#file_name').val());
});

/**
 * 初始化刷新表格.
 */
let data = new Array();
function createLogsTable() {
  $("#log-backup-tb").jqGrid(
    {
      datatype: "local",
      height: "90%",
      autowidth: true,
      shrinkToFit: true,
      pager: '#log-backup-pager',
      viewrecords: true,
      sortname:'logname',
      sortorder:'desc',
      rowNum: 10,
      rowList: [5, 10, 20, 50, 100],
      recordtext: "{0} - {1}\u3000共 {2} 条",
      emptyrecords: "当前没有异常日志记录",
      loadtext: "读取中...",
      pgtext: "{0} 共 {1} 页",
      rownumbers: true,
      loadComplete: function () {
        setTimeout(function () {
          // 自动生成序号
          $("#log-backup-tb").jqGrid('setLabel', 'rn', '序号',{'text-align':'center'});
        }, 0)
      },
      colNames: ['备份文件', '操作'],
      colModel: [
        {name: 'logname', index: 'logname'},
        {name: 'operation', index: 'operation', sortable: false, formatter: displayButtons}]
    });

  // 清空和填充表格数据
  $("#log-backup-tb").jqGrid('clearGridData');
  for (let i = 0; i <= data.length; i++) {
    $("#log-backup-tb").jqGrid('addRowData', i + 1, data[i]);
  }
  // 数组数据分页.本地数据分页.
  $('.ui-pg-table.ui-pager-table #first_log-backup-pager span')
    .addClass('ace-icon fa fa-angle-double-left bigger-100')
  $('.ui-pg-table.ui-pager-table #prev_log-backup-pager span')
    .addClass('ace-icon fa fa-angle-left bigger-100')
  $('.ui-pg-table.ui-pager-table #next_log-backup-pager span')
    .addClass('ace-icon fa fa-angle-right bigger-100')
  $('.ui-pg-table.ui-pager-table #last_log-backup-pager span')
    .addClass('ace-icon fa fa-angle-double-right bigger-100')
  const localData = {page: 1, total: 2, records: "2", rows: []};
  localData.rows = data;
  localData.records = data.length;
  localData.total = (data.length % 2 == 0) ? (data.length / 2) : (Math.floor(data.length / 2) + 1);
  const reader = {
    root: function (obj) {return localData.rows;},
    page: function (obj) {return localData.page;},
    total: function (obj) {return localData.total;},
    records: function (obj) {return localData.records;},
    repeatitems: false
  };
  $("#log-backup-tb").setGridParam({
    data: localData.rows,
    reader: reader
  }).trigger('reloadGrid');
  /**\
   * 创造行末尾按钮并修改删除点击方法.
   * @param cellvalue
   * @param options
   * @param rowObject
   * @returns {string}
   */
  function displayButtons(cellvalue, options, rowObject) {
    return "<div class='hidden-sm hidden-xs btn-group'>" +
      "<button class='btn btn-minier btn-inverse wa-mlr5'" +
      "row-name='" + rowObject.logname + "'" +
      "onclick=\"$('#validateUserDlg').modal('show')\n" +
      "$('#validateUserForm').get(0).reset()\n" +
      "console.log($(this).parent().parent().prev().text())\n" +
      "$('#validateUserForm #log_file_name').val($(this).parent().parent().prev().text())\">备份导出" +
      "</button>" +
      "<button class='btn btn-minier btn-danger'" +
      "row-name='" + rowObject.logname + "'" +
      "onclick=\"$('#confirmDelDlg').modal('show')\n" +
      "$('#file_name').val($(this).attr('row-name'))\">" +
      "<i class='ace-icon fa fa-trash-o bigger-80'>&nbsp;删除</i>" +
      "</button>" +
      "</div>";
    jqgridColResize()
    tableResize();
  }
}

/**
 * 重新调整jqgrid每列的宽度
 */
function jqgridColResize() {
  let td = $('#tdCompute')//获取计算实际列长度的容器
    , tds//临时保存列
    , arr = [];//用于保存最大的列宽
  //遍历每行获得每行中的最大列宽
  $('.ui-jqgrid-htable tr,.ui-jqgrid-btable tr:gt(0)').each(function () {
    $(this).find('td,th').each(function (idx) {
      arr[idx] = Math.max(arr[idx] ? arr[idx] : 0, td.html($(this).text())[0].offsetWidth);
    })
  });
  //设置页头单元格宽度
  $('.ui-jqgrid-labels th').each(function (idx) {
    this.style.width = arr[idx] + 'px'
  });
  //设置内容表格中控制单元格宽度的单元格，在第一行
  $('.ui-jqgrid-btable tr:eq(0) td').each(function (idx) {
    this.style.width = arr[idx] + 'px'
  });
  // 设置操作栏固定宽度
  // $('.ui-jqgrid .ui-jqgrid-htable th:last-child').css("width", "50");
  // $('.ui-jqgrid-btable>tbody>tr>td:last-child').css("width","50");
}

/**
 * 表格自适应窗口
 */
function tableResize() {
  $(window).on('resize.jqGrid', function () {
    $("#log-backup-tb").jqGrid('setGridWidth', $(".page-content").width());
    jqgridColResize()
  });
  const parent_column = $("#log-backup-tb").closest('[class*="col-"]');
  $(document).on('settings.ace.jqGrid', function (ev, event_name, collapsed) {
    if (event_name == 'sidebar_collapsed' || event_name == 'main_container_fixed') {
      setTimeout(function () {
        $("#log-backup-tb").jqGrid('setGridWidth', parent_column.width());
        jqgridColResize()
      }, 0);
    }
  })
}

initOnMessage()
/**
 * 页面定义的相关数据和初始化执行的相关操作
 */
function init() {
  // tcp_client.connect(onMsg, config.host, config.port);
  loadLogsGenerator();
}
init();
/**
 * 发送加载日志请求
 * @param index 索引，从0开始
 * @param count 加载数量
 */
function loadLogsGenerator() {
  // 发送加载日志请求
  let sendMsg = new ProxyServer_pb.MSG_CPLoadLogs();
  // tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADLOGS, sendMsg, user_sign);
  tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADLOGS, sendMsg).then();
}
/**
 * 消息处理函数
 * @param msgId 消息id
 * @param pb 原始pb字节
 */
function onMsg(msgId, pb) {
  const msg = ProxyServer_pb.MSG_PCLoadLogs.deserializeBinary(pb);
  let lognames = msg.getLogNamesList();
  console.log('lognames',lognames)
  data=[];
  for (let i = 0; i < lognames.length; ++i) {
    let logname = lognames[i];
    let row = {
      logname: logname,
    };
    data.push(row);
  }
  createLogsTable()

  // 消息处理完成关闭连接
  tcp_client.close();
}
function initOnMessage() {
  // 页面数据监听
  tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCLOADLOGS, (pb) => {
    const msg = ProxyServer_pb.MSG_PCLoadLogs.deserializeBinary(pb);
    let lognames = msg.getLogNamesList();
    console.log('lognames',lognames)
    data=[];
    for (let i = 0; i < lognames.length; ++i) {
      let logname = lognames[i];
      let row = {
        logname: logname,
      };
      data.push(row);
    }
    createLogsTable()
  })
  // 备份
  tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCBACKUPLOG, (pb) => {
      const msg = ProxyServer_pb.MSG_PCBackupLog.deserializeBinary(pb);
      if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
          ui_util.showFadeTip('备份日志成功！');
      } else {
          ui_util.showFadeTip('备份日志失败！');
      }
      init();
  })
  // 用户校验
  tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCVALIDATEUSER, (pb) => {
      // $('#validateuser_btn').off('click'); // 用完一定要手动移除该事件，否则会积累触发点击事件
        const msg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(pb);
        if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
            //ui_util.showFadeTip('验证用户通过!');
            $('#confirmPortDlg').modal('show')
        } else {
            ui_util.getErrorTips(msg.getErrorCode(), '验证用户身份')
        }
      // tcpInstance.disconnect()
  })
  // 备份导出
  tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCEXPORTLOG, (pb) => {
      console.log('响应数据_备份导出', pb, logFileName, pb.length)
      // const msg = ProxyServer_pb.MSG_PCLogs.deserializeBinary(pb);
      // let lognames = msg.getLogData()
      // console.log('lognames', lognames)
      // 转出csv格式文件中中文乱码解决
      util.saveToCSV(Buffer.from(pb), filePath+"/"+ `${String(new Date().getTime())}` +logFileName);
      ui_util.showFadeTip('导出日志配置成功')
  })
  // 删除
  tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCDELETELOG, (pb) => {
      const msg = ProxyServer_pb.MSG_PCDeleteLog.deserializeBinary(pb);
      if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
          ui_util.showFadeTip('删除日志文件成功');
      }else {
          ui_util.getErrorTips(dp_packet.return_value,'删除日志文件')
      }
      init();
  })


}

/**
 * 发送备份日志请求
 * @param level
 */
function backupLogGenerator(log_source,log_level){
  // tcp_client.connect(function (msgId, pb) {
  //   const msg = ProxyServer_pb.MSG_PCBackupLog.deserializeBinary(pb);
  //   if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
  //     ui_util.showFadeTip('备份日志成功！');
  //   } else {
  //     ui_util.showFadeTip('备份日志失败！');
  //   }
  //   // 消息处理完成关闭连接
  //   tcp_client.close();
  //   // 重新加载用户数据
  //   init();
  // }, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPBackupLog();
  sendMsg.setSource(log_source);
  sendMsg.setLogLevel(log_level);
  tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPBACKUPLOG, sendMsg).then();
  // tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPBACKUPLOG, sendMsg, user_sign);
}

// 弹出密码校验框
$('#validateuser_btn').on('click',function (e) {

    if ($("#validateuser_btn").hasClass("disabled")) {
        return;
    }
    $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
    e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
    logFileName=$('#validateUserDlg #log_file_name').val()
    console.log('需要导出的', logFileName,'e', e)
    let username=user_name
    let psd=$('#validateUserDlg #password').val()
    validateUserGenerator(username,psd);
})

/**
 * 发送验证密码参数
 * @param psd
 */
function validateUserGenerator(username,psd){
// 连接服务器
//   tcp_client.connect(function(msgId, pb) {
//     const msg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(pb);
//     if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
//       //ui_util.showFadeTip('验证用户通过!');
//       $('#confirmPortDlg').modal('show')
//     } else {
//       ui_util.getErrorTips(msg.getErrorCode(), '验证用户身份')
//     }
//   }, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
  sendMsg.setUserName(username)
  sendMsg.setPassword(psd)
  tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg).then();
  // tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg, user_sign);
}

/**
 * 导出日志
 */
function exportLogs(filePath,logfileName) {
  // tcp_client.connect(function (msgId, pb) {
  //   const msg = ProxyServer_pb.MSG_PCLogs.deserializeBinary(pb);
  //   let lognames = msg.getLogData()
  //   // 转出csv格式文件中中文乱码解决
  //   util.saveToCSV(Buffer.from(lognames), filePath+"/"+logfileName);
  //   ui_util.showFadeTip('导出日志配置成功')
  //   // 消息处理完成关闭连接
  //   // tcp_client.close();
  // }, config.host, config.port);
  console.log('发送导出请求', filePath)
  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPExportLog();
  sendMsg.setFileName(logfileName)
  // tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPEXPORTLOG, sendMsg, user_sign);
  tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPEXPORTLOG, sendMsg).then();
}

/**
 * 发送删除日志请求
 * @param file_name
 */
function removeLogFileGenerator(file_name) {
  // tcp_client.connect(function (msgId, pb) {
  //   const msg = ProxyServer_pb.MSG_PCDeleteLog.deserializeBinary(pb);
  //   if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
  //     ui_util.showFadeTip('删除日志文件成功');
  //   }else {
  //     ui_util.getErrorTips(dp_packet.return_value,'删除日志文件')
  //   }
  //   // 消息处理完成关闭连接
  //   tcp_client.close();
  //   // 重新加载用户数据
  //   init();
  // }, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPDeleteLog();
  sendMsg.setFileName(file_name);
  tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPDELETELOG, sendMsg).then();
  // tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPDELETELOG, sendMsg, user_sign);
}
