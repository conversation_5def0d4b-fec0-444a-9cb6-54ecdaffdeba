'use strict';
/* 2018.11.15 qiao*/
const {ipc<PERSON><PERSON><PERSON>} = require('electron');
const ProxyServer_pb = require('../pb/ProxyServer_pb');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const ui_util = require('./ui_util');
const tcp_client = require('../../lib/tcp_client');
const config = require("../../config.json");
// 相关变量定义
const roleType = parseInt(ui_util.getUrlParam("role_type"));
const user_sign = ui_util.getUrlParam("user_sign");
const user_name = ui_util.getUrlParam("user_name");
console.log("user_sign:", user_sign ,user_name,roleType);
// 修改右上角当前登录的用户
$('#userName').text(user_name);

// // 跳转到主页
// $('#hreftohome').on('click',function () {
//   switch (roleType) {
//     case PublicDefine_pb.RoleType.ROLETYPE_SYSADMIN:
//     case PublicDefine_pb.RoleType.ROLETYPE_ADMIN:
//       window.location.replace("admin_index.html?user_sign=" + user_sign + '&user_name=' + user_name + "&role_type=" + roleType);
//       break;
//     case PublicDefine_pb.RoleType.ROLETYPE_AUDITOR:
//       window.location.replace("auditor_index.html?user_sign=" + user_sign +
//       '&user_name=' + user_name + "&role_type=" + roleType);
//       break;
//     case PublicDefine_pb.RoleType.ROLETYPE_OPERATOR:
//       window.location.replace("operator_index.html?user_sign=" + user_sign +
//       '&user_name=' + user_name + "&role_type=" + roleType);
//       break;
//     default:
//       ui_util.showFadeTip('登录失败，角色类型不存在');
//       break;
//   }
// })

// 侧栏导航切换-含一二级菜单
let datacode;
$(".submenu>li").on('click',function () {
  $(".nav.nav-list li").removeClass('active');
  $(this).addClass('active');
  datacode = $(".submenu>li.active").attr("data-code");
  if(datacode==undefined||datacode==null||datacode==""){
    window.location.href="admin_index.html";
  }else{
    $.ajax({
      url: datacode + ".html",
      type: "get",
      dataType: "html",
      success: function (data) {
        $(".parent_content").html(data);
      }
    });
  }
})

$(".nav.nav-list >li.li_fst_class").on('click',function () {
  $(".nav.nav-list li").removeClass('active');
  $(".nav.nav-list li").removeClass('open');
  $(this).addClass('active');
  datacode = $(".nav.nav-list >li.li_fst_class.active").attr("data-code");
  if(datacode==undefined||datacode==null||datacode==""){
    if($(this).parent().attr('wa-name')=='operator_list'){
      window.location.href="operator_index.html";
    }else if($(this).parent().attr('wa-name')=='audit_list'){
      window.location.href="auditor_index.html";
    }
  }else{
    $.ajax({
      url: datacode + ".html",
      type: "get",
      dataType: "html",
      success: function (data) {
        $(".parent_content").html(data);
      }
    });
  }
})

// 点击退出登录
$('#logoutBtn').on('click',function () {
  $('#confirmLogoutDlg').modal('show');
})
$('#logout_btn').on('click',function () {
  top.location = "./login.html";
})
