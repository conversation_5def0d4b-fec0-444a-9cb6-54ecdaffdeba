'use strict';

const ProxyServer_pb = require('../../pb/ProxyServer_pb');
const PublicDefine_pb = require('../../pb/PublicDefine_pb');
const util = require('../../../lib/util');
const ui_util = require('../ui_util');
const tcp_client = require('../../../lib/tcp_client');
const config = require("../../../config.json");
const cert_codec = require('../../platform_codec/certificate_codec');
// 相关变量定义
const user_sign = $("body").attr("user_sign_info");
const user_name = $("body").attr("user_name_info");
console.log("user_sign:", user_sign + user_name);

let filePath = "", fileName = "";
let file2 = "", filePath2 = "", fileName2 = "certificate.cfg";
let csrFileName = "jy.csr"
let id, romove_id, cert_type, reserved, cert_content_length, platform_ip, cert_name, cert_content,csr_content;
let newCont, newContStr;

// 初始化选择文件夹路径&选择文件 导入导出
$('#select_folder').ace_file_input({
    no_file: '请选择文件夹',
    btn_choose: '选择',
    btn_change: null,
    droppable: false,
    onchange: null,
    thumbnail: false
});
document.querySelector('#select_folder').addEventListener('change', e => {
    for (let entry of e.target.files) {
        console.log(entry.name, entry.path);
        filePath2 = entry.path
    }
});
$('#select_file').ace_file_input({
    no_file: '请选择文件',
    btn_choose: '选择',
    btn_change: null,
    droppable: false,
    onchange: null,
    thumbnail: false,
    allowExt: ['cfg'],
}).on('change', function () {
    let fileDir = $(this).val();
    let suffix = fileDir.substr(fileDir.lastIndexOf("."));
    if ("" == fileDir || ".cfg" != suffix) {
        ui_util.showFadeTip('请选择.cfg后缀文件!');
        return false;
    }
    file2 = $(this).data('ace_input_files')[0].path
});
// 初始化选择证书文件.
$('#certFile1').ace_file_input({
    no_file: '请选择.cer后缀文件',
    btn_choose: '选择',
    btn_change: null,
    droppable: false,
    onchange: null,
    thumbnail: false,
    allowExt: ['cer'],
}).on('change', function () {
    let fileDir = $(this).val();
    let suffix = fileDir.substr(fileDir.lastIndexOf("."));
    if ("" == fileDir || ".cer" !== suffix) {
        ui_util.showFadeTip('请选择.cer文件!');
        return false;
    }
    filePath = $(this).data('ace_input_files')[0].path
    fileName = $(this).data('ace_input_files')[0].name
    $("#cert_name").val(fileName.slice(0, fileName.length - 4))
    cert_content_length = util.readFromFile(filePath, false).length;
    cert_content = util.readFromFile(filePath, false);
});

// 证书类型下拉框选中切换
$('#cert_type').change(function () {
    if ($(this).find("option:selected").val() === "0x1" || $(this).find("option:selected").val() === "0x20") {
        $("#platform_ip").attr("disabled", true)
        $("#platform_ip").val("0.0.0.0")
    } else {
        $("#platform_ip").attr("disabled", false)
        $("#platform_ip").val("")
    }
})

// 查看证书列表
// 新增证书
$('#add_cert').on('click', function () {
    $('#validateUserDlg').modal('show')
    $('#validateUserForm').get(0).reset()
    $('#opraType').val('add')
})
// 保存新增/修改证书参数
$('#save_cert_btn').on('click', function (e) {
    if ($("#save_cert_btn").hasClass("disabled")) {
        return false;
    }
    if (!($("#certFile1").next("span.ace-file-container").hasClass("selected"))) {
        ui_util.showFadeTip('请选择证书!')
        return;
    }
    let ip = $('#platform_ip').val();
    let old_ip = $('#old_ip').val();
    cert_type = parseInt($('#cert_type').val());
    if ($("#addCertTitle").attr('wa-data') == 'add') {
        if(cert_type===2){
            if (ip != "") {
                for (let i = 0; i < certData.length; i++) {
                    if (ip == certData[i].certIp) {
                        ui_util.showFadeTip('ip已存在!');
                        $('#platform_ip').focus();
                        return false;
                    }
                }
            }
            if(ip==="0.0.0.0"){
                ui_util.showFadeTip('平台ip不合法!');
                $('#platform_ip').focus();
                return false;
            }
        }
        let idColData = $('#certificate-tb').getCol("id", false)
        let max
        if (idColData.length === 0) {
            max = 0
        } else {
            idColData.sort(function (a, b) {
                return a - b;
            })
            max = parseInt(idColData[idColData.length - 1])
        }
        id = max + 1;
        reserved = 0;
        platform_ip = $('#platform_ip').val();
        cert_name = $('#cert_name').val() + ".cer";
        $('#confirmDlg2').modal('show')
        $("#confirmDlg2 .modal-title").attr('wa-data', 'add')
    } else {
        if(cert_type===2){
            if (ip != "" && ip != old_ip) {
                for (let i = 0; i < certData.length; i++) {
                    if (ip == certData[i].certIp) {
                        ui_util.showFadeTip('ip已存在!');
                        $('#platform_ip').focus();
                        return false;
                    }
                }
            }
            if(ip==="0.0.0.0"){
                ui_util.showFadeTip('平台ip不合法!');
                $('#platform_ip').focus();
                return false;
            }
        }
        id = parseInt($('#app_cert_id').val());
        reserved = 0;
        platform_ip = $('#platform_ip').val();
        cert_name = $('#cert_name').val() + ".cer";
        $('#confirmDlg2').modal('show')
        $("#confirmDlg2 .modal-title").attr('wa-data', 'edit')
    }
    e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
})
$('#save_all_btn').on('click', function () {
    if ($("#confirmDlg2 .modal-title").attr('wa-data') == 'add') {
        addCertData(id, cert_type, reserved, cert_content_length, platform_ip, cert_name, cert_content);
    } else {
        editCertData(id, cert_type, reserved, cert_content_length, platform_ip, cert_name, cert_content);
    }
})
// 删除证书
$('#remove_cert_btn').on('click', function () {
    removeCertData(romove_id, cert_type, reserved, cert_content_length, platform_ip, cert_name, cert_content);
});

// 导出证书申请文件
$('#csr_export').on('click', function () {
    $('#validateUserDlg').modal('show');
    $('#validateUserForm').get(0).reset()
    $('#opraType').val("request_csr")
})

// 导入进度条动画加载&导出
$('#btn_import').on('click', function () {
    $('#validateUserDlg').modal('show');
    $('#validateUserForm').get(0).reset()
    $('#opraType').val("import")
})
$('#btn_export').on('click', function () {
    if (certData.length === 0) {
        ui_util.showFadeTip('当前没有可以导出的证书!')
        return;
    }
    $('#validateUserDlg').modal('show');
    $('#validateUserForm').get(0).reset()
    $('#opraType').val("export")
})

$('#port_btn').on('click', function (e) {
    if ($("#port_btn").hasClass("disabled")) {
        return;
    }
    if ($('#confirmPortDlg .modal-body .form-group:nth-child(1)').hasClass("hidden")) {
        if (!$("#select_folder").next("span.ace-file-container").hasClass("selected")) {
            ui_util.showFadeTip('请选择文件路径!')
            return;
        }
    } else if ($('#confirmPortDlg .modal-body .form-group:nth-child(2)').hasClass("hidden")) {
        if (!($("#select_file").next("span.ace-file-container").hasClass("selected"))) {
            ui_util.showFadeTip('请选择文件!')
            return;
        }
    }
    $('#confirmPortDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
    e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
    if ($('#confirmPortDlg .modal-title').attr("wa-name") == "import_tt") {
        importCertificate(file2, fileName2)
    } else if ($('#confirmPortDlg .modal-title').attr("wa-name") == "export_tt") {
        exportCertificate(filePath2, fileName2)
    }else if ($('#confirmPortDlg .modal-title').attr("wa-name") == "request_csr_path") {
        exportRequestCsrPath(filePath2, csrFileName)
    }
})
// 弹出密码校验框
$('#validateuser_btn').on('click', function (e) {
    if ($("#validateuser_btn").hasClass("disabled")) {
        return;
    }
    $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
    e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
    let username = user_name
    let psd = $('#validateUserDlg #password').val()
    validateUserGenerator(username, psd);
})

/**
 * 初始化刷新表格.
 */
let certData = new Array();

function loadRouteConfigTb() {
    $("#certificate-tb").jqGrid(
        {
            datatype: "local",
            height: "90%",
            autowidth: true,
            shrinkToFit: true,//按比例初始化列宽
            pager: '#certificate-pager',
            viewrecords: true,
            sortname: 'id',
            sortorder: 'desc',
            rowNum: 10,//每页显示数据个数
            rowList: [5, 10, 20, 50, 100],
            recordtext: "{0} - {1}\u3000共 {2} 条",
            emptyrecords: "当前没有证书记录",
            loadtext: "读取中...",
            pgtext: "{0} 共 {1} 页",
            colNames: ['序号', '保留', '证书内容长度', '证书名称', '证书类型', '证书ip', '证书内容', '操作'],
            colModel: [
                {name: 'id', index: 'id', sorttype: 'integer'},
                {name: 'certReserved', index: 'certReserved', hidden: true},
                {name: 'certLen', index: 'certLen', hidden: true},
                {name: 'certName', index: 'certName'},
                {name: 'certType', index: 'certType', formatter: transformType},
                {name: 'certIp', index: 'certIp',},
                {name: 'certContent', index: 'certContent', hidden: true},
                {
                    name: 'operation', index: 'operation', sortable: false, formatter: displayButtons
                }]
        });

    // 清空和填充表格数据
    $("#certificate-tb").jqGrid('clearGridData');
    for (let i = 0; i <= certData.length; i++) {
        $("#certificate-tb").jqGrid('addRowData', i + 1, certData[i]);
    }
// 数组数据分页.本地数据分页.
    $('.ui-pg-table.ui-pager-table #first_certificate-pager span')
        .addClass('ace-icon fa fa-angle-double-left bigger-100')
    $('.ui-pg-table.ui-pager-table #prev_certificate-pager span')
        .addClass('ace-icon fa fa-angle-left bigger-100')
    $('.ui-pg-table.ui-pager-table #next_certificate-pager span')
        .addClass('ace-icon fa fa-angle-right bigger-100')
    $('.ui-pg-table.ui-pager-table #last_certificate-pager span')
        .addClass('ace-icon fa fa-angle-double-right bigger-100')
    const localData = {page: 1, total: 2, records: "2", rows: []};
    localData.rows = certData;
    localData.records = certData.length;
    localData.total = (certData.length % 2 == 0) ? (certData.length / 2) : (Math.floor(certData.length / 2) + 1);
    const reader = {
        root: function (obj) {
            return localData.rows;
        },
        page: function (obj) {
            return localData.page;
        },
        total: function (obj) {
            return localData.total;
        },
        records: function (obj) {
            return localData.records;
        },
        repeatitems: false
    };
    $("#certificate-tb").setGridParam({
        data: localData.rows,
        reader: reader
    }).trigger('reloadGrid');

    /**
     * 证书类型转换文本
     */
    function transformType(cellvalue, options, rowObject) {
        if (cellvalue == 1) return "CA根证书";
        else if (cellvalue == 2) return "平台证书";
        else if (cellvalue == 32) return "装置证书";
    }

    /**\
     * 创造行末尾按钮并修改删除点击方法.
     */
    function displayButtons(cellvalue, options, rowObject) {
        console.log(rowObject.certContent.toString().length)
        return "<div class='hidden-sm hidden-xs btn-group'>" +
            "<button class='btn btn-minier btn-inverse wa-mlr5'" +
            "row-obj-str='" + JSON.stringify(rowObject) + "'" +
            "row-content-str='" + rowObject.certContent.toString('base64') + "'" +
            "onclick=\"$('#validateUserDlg').modal('show')\n" +
            "$('#validateUserForm').get(0).reset()\n" +
            "$('#opraType').val('edit')\n" +
            "$('#opra_id').val(JSON.parse($(this).attr('row-obj-str')).id)\n" +
            "$('#opra_cert_type').val(JSON.parse($(this).attr('row-obj-str')).certType)\n" +
            "$('#opra_reserved').val(JSON.parse($(this).attr('row-obj-str')).certReserved)\n" +
            "$('#opra_cert_length').val(JSON.parse($(this).attr('row-obj-str')).certLen)\n" +
            "$('#opra_platform_ip').val(JSON.parse($(this).attr('row-obj-str')).certIp)\n" +
            "$('#opra_cert_name').val(JSON.parse($(this).attr('row-obj-str')).certName)\n" +
            "$('#opra_cert_content').val($(this).attr('row-content-str'))\">" +
            "<i class='ace-icon fa fa-edit bigger-80'>&nbsp;修改</i>" +
            "</button>" +
            "<button class='btn btn-minier btn-danger'" +
            "row-obj-str='" + JSON.stringify(rowObject) + "'" +
            "row-content-str='" + rowObject.certContent.toString('base64') + "'" +
            "onclick=\"$('#validateUserDlg').modal('show')\n" +
            "$('#validateUserForm').get(0).reset()\n" +
            "$('#opraType').val('del')\n" +
            "$('#opraRemoveId').val(JSON.parse($(this).attr('row-obj-str')).id)\n" +
            "$('#opra_cert_type').val(JSON.parse($(this).attr('row-obj-str')).certType)\n" +
            "$('#opra_reserved').val(JSON.parse($(this).attr('row-obj-str')).certReserved)\n" +
            "$('#opra_cert_length').val(JSON.parse($(this).attr('row-obj-str')).certLen)\n" +
            "$('#opra_platform_ip').val(JSON.parse($(this).attr('row-obj-str')).certIp)\n" +
            "$('#opra_cert_name').val(JSON.parse($(this).attr('row-obj-str')).certName)\n" +
            "$('#opra_cert_content').val($(this).attr('row-content-str'))\">" +
            "<i class='ace-icon fa fa-trash-o bigger-80'>&nbsp;删除</i>" +
            "</button>" +
            "</div>";
    }

    jqgridColResize()
    tableResize();
}

loadRouteConfigTb()

/**
 * 重新调整jqgrid每列的宽度
 */
function jqgridColResize() {
    let td = $('#tdCompute')//获取计算实际列长度的容器
        , tds//临时保存列
        , arr = [];//用于保存最大的列宽
    //遍历每行获得每行中的最大列宽
    $('.ui-jqgrid-htable tr,.ui-jqgrid-btable tr:gt(0)').each(function () {
        $(this).find('td,th').each(function (idx) {
            arr[idx] = Math.max(arr[idx] ? arr[idx] : 0, td.html($(this).text())[0].offsetWidth);
        })
    });
    //设置页头单元格宽度
    $('.ui-jqgrid-labels th').each(function (idx) {
        this.style.width = arr[idx] + 'px'
    });
    //设置内容表格中控制单元格宽度的单元格，在第一行
    $('.ui-jqgrid-btable tr:eq(0) td').each(function (idx) {
        this.style.width = arr[idx] + 'px'
    });
    // 设置操作栏固定宽度
    $('.ui-jqgrid .ui-jqgrid-htable th:last-child').css("width", "66");
    $('.ui-jqgrid-btable>tbody>tr>td:last-child').css("width", "66");
}

/**
 * 表格自适应窗口
 */
function tableResize() {
    $(window).on('resize.jqGrid', function () {
        $("#certificate-tb").jqGrid('setGridWidth', $(".page-content").width());
        jqgridColResize()
    });
    const parent_column = $("#certificate-tb").closest('[class*="col-"]');
    $(document).on('settings.ace.jqGrid', function (ev, event_name, collapsed) {
        if (event_name == 'sidebar_collapsed' || event_name == 'main_container_fixed') {
            setTimeout(function () {
                $("#certificate-tb").jqGrid('setGridWidth', parent_column.width());
                jqgridColResize()
            }, 0);
        }
    })
}

/**
 * 初始化页面.填充表格数组数据.
 */
function pageInit() {
    // 连接服务器
    tcp_client.connect(function (dp_packet) {
        if (dp_packet.return_value === 0) {
            const certs = cert_codec.unpackLoadCert(dp_packet.content);
            certData = [];
            let _deviceStatus = "";
            for (let i = 0; i < certs.length; i++) {
                const certObj = {
                    id: certs[i].getId(),
                    certReserved: certs[i].getReserved(),
                    certLen: certs[i].getCertContentLength(),
                    certName: certs[i].getCertName(),
                    certType: certs[i].getCertType(),
                    certIp: util.int2ip(certs[i].getPlatformIp()),
                    certContent: certs[i].getCertContent()
                }
                certData.push(certObj);
            }
            console.log('certData:', certData)
            $("#certificate-tb").jqGrid("clearGridData"); //清空表格数据
            $("#certificate-tb").jqGrid('setGridParam', {  // 重新加载数据
                datatype: 'local',
                data: certData,   //  data 是符合格式要求的需要重新加载的数据
                page: 1
            }).trigger("reloadGrid");
        }
        // 消息处理完成关闭连接
        tcp_client.close();
    }, config.host, config.port);

    // 发送消息
    tcp_client.send(cert_codec.packLoadCert());
}

pageInit();

/**
 * 发送验证密码参数
 * @param psd
 */
function validateUserGenerator(username, psd) {
// 连接服务器
    tcp_client.connect(function (msgId, pb) {
        const msg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(pb);
        if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
            // ui_util.showFadeTip('验证用户通过!');
            if ($('#opraType').val() == "add") {
                $('#addCertDlg').modal('show');
                $('#addCertForm').get(0).reset();
                $('#addCertTitle').attr('wa-data', 'add')
                $('#addCertTitle').text("新增证书")
                $('#addCertForm .form-group').removeClass("hidden")
                $('#addCertForm .form-group .form-control').attr("disabled", false)
                $("#platform_ip").attr("disabled", true)
                $("#cert_name").attr("disabled", true)
                $("#certFile1").next().removeClass("selected")
                $("#certFile1").next().children("span").attr("data-title", "")
                $("#certFile1").next().children("span").children("i").removeClass("fa-file").addClass("fa-upload")
                $('#certDesc').find('p:first-child').find('b').text("")
                $('#certDesc').find('p:nth-child(2)').find('b').text("* 新增证书需重启服务代理,期间会暂时断开与装置连接")
                $('#save_cert_btn').show()
            } else if ($('#opraType').val() == "edit") {
                $('#addCertDlg').modal('show');
                $('#addCertForm').get(0).reset();
                $('#addCertTitle').attr('wa-data', 'edit')
                $('#addCertTitle').text("修改证书")
                $('#addCertForm .form-group').removeClass("hidden")
                $('#addCertForm .form-group .form-control').attr("disabled", false)
                $("#app_cert_id").val($('#opra_id').val())
                $("#cert_name").val($('#opra_cert_name').val().slice(0, $('#opra_cert_name').val().length - 4))
                $("#platform_ip").val($('#opra_platform_ip').val())
                $("#certFile1").next().addClass("selected")
                $("#certFile1").next().children("span").attr("data-title", $('#opra_cert_name').val())
                $("#certFile1").next().children("span").children("i").removeClass("fa-upload").addClass("fa-file")
                if (parseInt($('#opra_cert_type').val()) === 1) {
                    $("#cert_type").val("0x1")
                    $("#platform_ip").attr("disabled", true)
                } else if (parseInt($('#opra_cert_type').val()) === 2) {
                    $("#cert_type").val("0x2")
                    $("#platform_ip").attr("disabled", false)
                } else {
                    $("#cert_type").val("0x20")
                    $("#platform_ip").attr("disabled", true)
                }
                $("#old_ip").val($('#opra_platform_ip').val())
                // $("#cert_type").attr("disabled",true)
                $("#app_cert_cont_length").val($('#opra_cert_length').val())
                cert_content_length = parseInt($('#app_cert_cont_length').val())
                $("#app_cert_cont").val($('#opra_cert_content').val())
                cert_content = Buffer.from($("#app_cert_cont").val(), 'base64')
                $('#certDesc').find('p:first-child').find('b').text("* 修改证书名称时无需再手动加后缀")
                $('#certDesc').find('p:nth-child(2)').find('b').text("* 修改证书需重启服务代理,期间会暂时断开与装置连接")
                $('#save_cert_btn').show()
            } else if ($('#opraType').val() == "del") {
                $("#confirmDlg").modal("show")
                romove_id = parseInt($('#opraRemoveId').val())
                cert_type = parseInt($('#opra_cert_type').val())
                reserved = parseInt($('#opra_reserved').val())
                cert_content_length = parseInt($('#opra_cert_length').val())
                platform_ip = $('#opra_platform_ip').val()
                cert_name = $('#opra_cert_name').val()
                cert_content = Buffer.from($("#opra_cert_content").val(), 'base64')
            }
            if ($('#opraType').val() == "import") {
                $('#confirmPortDlg').modal('show')
                $('#confirmPortDlg .modal-title').attr("wa-name", "import_tt")
                $('#confirmPortDlg .modal-body .form-group:nth-child(1)').removeClass("hidden")
                $('#confirmPortDlg .modal-body .form-group:nth-child(2)').addClass("hidden")
            } else if ($('#opraType').val() == "export") {
                $('#confirmPortDlg').modal('show')
                $('#confirmPortDlg .modal-title').attr("wa-name", "export_tt")
                $('#confirmPortDlg .modal-body .form-group:nth-child(2)').removeClass("hidden")
                $('#confirmPortDlg .modal-body .form-group:nth-child(1)').addClass("hidden")
            }else if($('#opraType').val() == "request_csr"){
                $('#exportCsrDlg').modal('show')
                $('#exportCsrDlg .modal-title').attr("wa-name", "export_csr_tt")
                $('#exportCsrDlg .modal-body .form-group:nth-child(2)').removeClass("hidden")
                $('#exportCsrDlg .modal-body .form-group:nth-child(1)').addClass("hidden")
            }
        } else {
            ui_util.getErrorTips(msg.getErrorCode(), '验证用户身份')
        }
    }, config.host, config.port);

    // 发送消息
    let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
    sendMsg.setUserName(username)
    sendMsg.setPassword(psd)
    tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg, user_sign);
}

/**
 * 发送新增证书参数信息
 * @param id
 * @param cert_type
 * @param reserved
 * @param cert_content_length
 * @param platform_ip
 * @param cert_name
 * @param cert_content
 */
function addCertData(id, cert_type, reserved, cert_content_length, platform_ip, cert_name, cert_content) {
    // 连接服务器
    tcp_client.connect(function (dp_packet) {
        if (dp_packet.return_value === 0) {
            ui_util.showFadeTip('新增证书成功!');
            $('#addCertDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
            pageInit();
        } else {
            ui_util.getErrorTips(dp_packet.return_value, "新增证书")
        }
        // 消息处理完成关闭连接
        tcp_client.close();
        pageInit();
    }, config.host, config.port);

    // 发送消息
    let cert_param = new PublicDefine_pb.MSG_Cert();
    cert_param.setId(id);
    cert_param.setCertType(cert_type);
    cert_param.setReserved(reserved);
    cert_param.setCertContentLength(cert_content_length);
    cert_param.setPlatformIp(util.ip2int(platform_ip));
    cert_param.setCertName(cert_name);
    cert_param.setCertContent(cert_content);
    tcp_client.send(cert_codec.packAddCert(cert_param, cert_content));
}

/**
 * 发送修改证书参数信息
 * @param id
 * @param cert_type
 * @param reserved
 * @param cert_content_length
 * @param platform_ip
 * @param cert_name
 * @param cert_content
 */
function editCertData(id, cert_type, reserved, cert_content_length, platform_ip, cert_name, cert_content) {
    // 连接服务器
    tcp_client.connect(function (dp_packet) {
        if (dp_packet.return_value === 0) {
            ui_util.showFadeTip('修改证书成功!');
            $('#addCertDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
            pageInit();
        } else {
            ui_util.getErrorTips(dp_packet.return_value, "修改证书")
        }
        // 消息处理完成关闭连接
        tcp_client.close();
        pageInit();
    }, config.host, config.port);

    // 发送消息
    let cert_param = new PublicDefine_pb.MSG_Cert();
    cert_param.setId(id);
    cert_param.setCertType(cert_type);
    cert_param.setReserved(reserved);
    cert_param.setCertContentLength(cert_content_length);
    cert_param.setPlatformIp(util.ip2int(platform_ip));
    cert_param.setCertName(cert_name);
    cert_param.setCertContent(cert_content);
    tcp_client.send(cert_codec.packEditCert(cert_param, cert_content));
}

/**
 * 发送删除证书参数信息
 */
function removeCertData(romove_id, cert_type, reserved, cert_content_length, platform_ip, cert_name, cert_content) {
    // 连接服务器
    tcp_client.connect(function (dp_packet) {
        if (dp_packet.return_value === 0) {
            ui_util.showFadeTip('删除证书成功!');
            pageInit();
        } else {
            ui_util.getErrorTips(dp_packet.return_value, "删除证书")
        }
        // 消息处理完成关闭连接
        tcp_client.close();
        pageInit();
    }, config.host, config.port);

    // 发送消息
    let cert_param = new PublicDefine_pb.MSG_Cert();
    cert_param.setId(romove_id);
    cert_param.setCertType(cert_type);
    cert_param.setReserved(reserved);
    cert_param.setCertContentLength(cert_content_length);
    cert_param.setPlatformIp(util.ip2int(platform_ip));
    cert_param.setCertName(cert_name);
    tcp_client.send(cert_codec.packRemoveCert(cert_param));
}

/**
 * 导出证书
 */
function exportCertificate(filePath2, fileName2) {
    tcp_client.connect(function (msgId, pb) {
        const msg = ProxyServer_pb.MSG_PCExportData.deserializeBinary(pb);
        if (msg.getErrorCode() == 0) {
            let targetBuf = msg.getData();
            util.saveToCSV(targetBuf, filePath2 + "/" + fileName2);
            ui_util.showFadeTip('导出证书成功')
        } else {
            ui_util.getErrorTips(msg.getErrorCode(), '导出证书');
        }
        // 消息处理完成关闭连接
        tcp_client.close();
    }, config.host, config.port);

    // 发送消息
    let exportData = new ProxyServer_pb.MSG_CPExportData();
    exportData.setType(PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_CERT)
    tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPEXPORTDATA, exportData, user_sign);
}

/**
 * 导入证书
 */
function importCertificate(file2, fileName2) {
    tcp_client.connect(function (msgId, pb) {
        $('.parent_content').busyLoad("hide");
        const msg = ProxyServer_pb.MSG_PCImportData.deserializeBinary(pb);
        if (msg.getErrorCode() == 0) {
            ui_util.showFadeTip('导入证书成功!');
        } else {
            ui_util.getErrorTips(msg.getErrorCode(), '导入证书');
        }
        // 消息处理完成关闭连接
        tcp_client.close();
        pageInit();
    }, config.host, config.port);

    // 发送消息
    let importData = new ProxyServer_pb.MSG_CPImportData();
    importData.setType(PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_CERT)
    const savedConfigs = util.readFromFile(file2, false);
    importData.setData(savedConfigs.toString())
    tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPIMPORTDATA, importData, user_sign);
    $('.parent_content').busyLoad("show", {
        background: "rgba(0, 0, 0, 0.59)",
        fontawesome: "fa fa-spinner fa-spin fa-3x fa-fw"
    })
}

$('#csr_btn').on('click', function (e) {
    if ($("#csr_btn").hasClass("disabled")) {
        return;
    }
    let csr_org =$('#csr_org').val();
    if (csr_org == "") {
        ui_util.showFadeTip('请输入厂站名称!')
        return;
    }
    $('#exportCsrDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
    e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
    if ($('#exportCsrDlg .modal-title').attr("wa-name") == "export_csr_tt") {
        exportRequestCsr(csr_org)
    }
})
/*
 导出证书申请文件
  */
function exportRequestCsr(csr_org) {
    tcp_client.connect(function (msgId, pb) {
        $('.parent_content').busyLoad("hide");
        const msg = ProxyServer_pb.MSG_PCRequestCsr.deserializeBinary(pb);
        // 消息处理完成关闭连接
        if(msg.getCsr().length > 0){
            csr_content = msg.getCsr();
            $('#confirmPortDlg').modal('show')
            $('#confirmPortDlg .modal-title').attr("wa-name", "request_csr_path")
            $('#confirmPortDlg .modal-body .form-group:nth-child(2)').removeClass("hidden")
            $('#confirmPortDlg .modal-body .form-group:nth-child(1)').addClass("hidden")
        }
        tcp_client.close();
        pageInit();
    }, config.host, config.port);
    // 发送消息
    let csrData = new ProxyServer_pb.MSG_CPRequestCsr();
    csrData.setCountry("CN");
    csrData.setProvince("shandong");
    csrData.setCity("jinan");
    csrData.setOrg(csr_org);
    csrData.setEmail("");
    tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPREQUESTCSR, csrData, user_sign);
}

function exportRequestCsrPath(csrFilePath, csrFileName){
    if ($("#port_btn").hasClass("disabled")) {
        return;
    }
    $('#confirmPortDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
    util.saveToFile(csr_content, csrFilePath+"\\"+csrFileName, false);
    ui_util.showFadeTip('导出证书申请文件成功')
}