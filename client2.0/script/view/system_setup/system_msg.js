'use strict';

const ProxyServer_pb = require('../../pb/ProxyServer_pb');
const PublicDefine_pb = require('../../pb/PublicDefine_pb');
const util = require('../../../lib/util');
const ui_util = require('../ui_util');
const tcp_client = require('../../../lib/tcp_client');
const config = require("../../../config.json");
// 相关变量定义
const user_sign = $("body").attr("user_sign_info");
const user_name = $("body").attr("user_name_info");
console.log("user_sign:", user_sign + user_name);

// 初始化页面表单
$('#sysMsgForm').get(0).reset();
// 是否启用证书链验证开关事件
$('#certCheckSwitch [name="switch-field-1"]').change(function() {
  if ($('[name="switch-field-1"]:checked').length > 0) {
    certCheckGenerator(1)
  } else {
    certCheckGenerator(0)
  }
});
/**
 * 初始化Frm表单函数
 */
function loadSystemMsgGenerator(){
  // 连接服务器
  tcp_client.connect(loadSystemMsgHandler, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPLoadSystemOpenedService();
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADSYSTEMOPENEDSERVICE, sendMsg, user_sign);
}
function loadSystemMsgHandler(msgId, pb) {
  if (msgId !== ProxyServer_pb.PSMessageId.PSMESSAGEID_PCLOADSYSTEMOPENEDSERVICE) {
    console.error('invalid msg', msgId);
    return;
  }
  const msg = ProxyServer_pb.MSG_PCLoadSystemOpenedService.deserializeBinary(pb);
  $('#app_disk_stat').val(msg.getAppDiskStat())
  $('#is_root_user_enabled').val(msg.getIsRootUserEnabled()===true?"是":"否")
  $('#opened_ports').val(msg.getOpenedPorts())
  // 消息处理完成关闭连接
  tcp_client.close();
}
function loadCertCheckGenerator(){
  // 连接服务器
  tcp_client.connect(function(msgId, pb) {
    if (msgId !== ProxyServer_pb.PSMessageId.PSMESSAGEID_PCLOADGENERALPARAMS) {
      console.error('invalid msg', msgId);
      return;
    }
    const msg = ProxyServer_pb.MSG_PCLoadGeneralParams.deserializeBinary(pb);
    const paramsList = msg.getParamsList();
    console.log("paramsList",paramsList)
    if(paramsList[0].getValue()==="1"){
      $('[name="switch-field-1"]').prop("checked",true)
    }else {
      $('[name="switch-field-1"]').prop("checked",false)
    }
    // 消息处理完成关闭连接
    tcp_client.close();
  }, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPLoadGeneralParams();
  sendMsg.addKeys(PublicDefine_pb.GeneralParamKey.PARAMKEY_ENABLECERTCHAINVERIFY)
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADGENERALPARAMS, sendMsg, user_sign);
}

loadSystemMsgGenerator()
loadCertCheckGenerator()
/**
 * 是否启用证书链验证配置
 */
function certCheckGenerator(key){
  // 连接服务器
  tcp_client.connect(function(msgId, pb) {
    if (msgId !== ProxyServer_pb.PSMessageId.PSMESSAGEID_PCUPDATEGENERALPARAMS) {
      console.error('invalid msg', msgId);
      return;
    }
    const msg = ProxyServer_pb.MSG_PCUpdateGeneralParams.deserializeBinary(pb);
    const error_code = msg.getErrorCode();
    const text="证书链验证配置"
    if (error_code === 0) {
      ui_util.showFadeTip('证书链验证配置成功!');
    } else {
      ui_util.getErrorTips(error_code,text)
    }
    // 消息处理完成关闭连接
    tcp_client.close();
  }, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPUpdateGeneralParams();
  let param = sendMsg.addParams();
  param.setKey(PublicDefine_pb.GeneralParamKey.PARAMKEY_ENABLECERTCHAINVERIFY);
  param.setValue(String(key));
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPUPDATEGENERALPARAMS, sendMsg, user_sign);
}