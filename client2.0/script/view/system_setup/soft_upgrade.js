'use strict';

const ProxyServer_pb = require('../../pb/ProxyServer_pb');
const PublicDefine_pb = require('../../pb/PublicDefine_pb');
const util = require('../../../lib/util');
const ui_util = require('../ui_util');
const tcp_client = require('../../../lib/tcp_client');
const config = require("../../../config.json");
const soft_upgrade_codec = require('../../platform_codec/soft_upgrade_codec');
// 相关变量定义
const user_sign = $("body").attr("user_sign_info");
const user_name = $("body").attr("user_name_info");
const current_machine_code = $("body").attr("current_machine_code");
console.log("user_sign:", user_sign + user_name);

$('.parent_content').busyLoad("hide");

let filePath="";
$("#btn_upgrade").addClass("disabled")
// 初始化上传文件插件
$('#id-input-file').ace_file_input({
  style: 'well',
  btn_choose: '请选择.jy后缀文件',
  btn_change: null,
  no_icon: 'ace-icon fa fa-cloud-upload',
  allowExt: ['jy'],
  // droppable: true,
  thumbnail: 'large',
  preview_error : function(filename, error_code) {
    console.log(error_code);
  }
}).on('change', function(){
  let fileDir = $(this).val();
  let suffix = fileDir.substr(fileDir.lastIndexOf("."));
  if ("" == fileDir||".jy" != suffix) {
    ui_util.showFadeTip('请选择.jy后缀文件!');
    $("#btn_upgrade").addClass("disabled")
    return false;
  }
  filePath=$(this).data('ace_input_files')[0].path
  $("#btn_upgrade").removeClass("disabled")
});

let msgQueue;
// 升级所选文件
$('#btn_upgrade').on('click',function (e) {
  if ($("#btn_upgrade").hasClass("disabled")) {
    return false;
  }
  $('#validateUserDlg').modal('show');
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  $('#validateUserForm').get(0).reset();
  $('#validateUserDlg #code').val(current_machine_code);
  msgQueue=util.readFromFile(filePath,false)
})
// 弹出密码校验框
$('#validateuser_btn').on('click',function (e) {
  if ($("#validateuser_btn").hasClass("disabled")) {
    return;
  }
  $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  let username=user_name
  // let psd=$('#validateUserDlg #password').val()
  let machineCode = $('#validateUserDlg #machineCode').val()
  validateUserGenerator(username,"",machineCode);
})

/**
 * 发送验证密码参数
 * @param psd
 */
function validateUserGenerator(username,psd,machineCode){
// 连接服务器
  let socket = tcp_client.connectEx(function(msgId, pb) {
    const msg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(pb);
    if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
      // ui_util.showFadeTip('验证用户通过!');
      $('.parent_content').busyLoad("show",{
        background: "rgba(0, 0, 0, 0.59)",
        fontawesome: "fa fa-spinner fa-spin fa-3x fa-fw"
      })
      setTimeout(function() {
        loadSoftUpgrade();
      },1000)
    } else {
      ui_util.getErrorTips(msg.getErrorCode(), '验证用户身份')
    }
  }, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
  sendMsg.setUserName(username);
  sendMsg.setPassword(psd);
  sendMsg.setActiveCode(machineCode);
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg, user_sign, socket);
}

/**
 * 发送升级数据内容.
 * @param content
 */
function loadSoftUpgrade() {
  // 连接服务器
  let socket = tcp_client.connectEx(function (dp_packet) {
    const text="软件升级"
      console.log('--------------------------软件升级--------------------------')
    $('.parent_content').busyLoad("hide");
    if (dp_packet.return_value === 0) {
      ui_util.showFadeTip('软件升级成功!');
    } else {
      ui_util.getErrorTips(dp_packet.return_value,text)
    }
    // 消息处理完成关闭连接
    tcp_client.close();
  }, config.host, config.port);

  // 发送消息
  tcp_client.send(soft_upgrade_codec.packSoftUpgrade(msgQueue), socket);
}

//复制校验码事件
$('#copyCode').on('click',function (e) {
    var Code=document.getElementById("code");
    Code.select(); // 选择对象
    document.execCommand("Copy"); // 执行浏览器复制命令
    ui_util.showFadeTip('校验码已复制');
})