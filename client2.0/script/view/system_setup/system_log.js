'use strict';

const ProxyServer_pb = require('../../pb/ProxyServer_pb');
const PublicDefine_pb = require('../../pb/PublicDefine_pb');
const util = require('../../../lib/util');
const ui_util = require('../ui_util');
const tcp_client = require('../../../lib/tcp_client');
const config = require("../../../config.json");
// 相关变量定义
const user_sign = $("body").attr("user_sign_info");
const user_name = $("body").attr("user_name_info");

let filePath ,fileName;
let log_content;

// 初始化选择文件夹路径&选择文件 导入导出
$('#select_folder').ace_file_input({
    no_file: '请选择文件夹',
    btn_choose: '选择',
    btn_change: null,
    droppable: false,
    thumbnail: false,
    onchange: null
});
document.querySelector('#select_folder').addEventListener('change', e => {
    for (let entry of e.target.files) {
        console.log(entry.name, entry.path);
        filePath = entry.path
    }
});
/**
 * 消息处理函数
 * @param msgId 消息id
 * @param pb 原始pb字节
 */
function onMsg(msgId, pb) {
    const msg = ProxyServer_pb.MSG_PCListFile.deserializeBinary(pb);
    if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
        let fieldList = msg.getFilesList();
        console.log('fieldList',fieldList)
        data=[];
        for (let i = 0; i < fieldList.length; ++i) {
            let field = fieldList[i];
            let isDir = field.getIsDir()==false?"否":"是";
            let owner = field.getOwner();
            let authority = field.getAuthority();
            let size =field.getSize();
            let name = field.getName();
            let modifyDate = field.getModifyDate();
            let row = {
                isDir: isDir,
                owner:owner,
                authority:authority,
                size:size,
                name:name,
                modifyDate:modifyDate,
            };
            data.push(row);
        }
        createLogsTable()

        // 消息处理完成关闭连接
        tcp_client.close();
    }else{
        ui_util.showFadeTip('系统中没有任何日志');
    }
}


/**
 * 初始化刷新表格.
 */
let data = new Array();
function createLogsTable() {
    $("#system-log-tb").jqGrid(
        {
            datatype: "local",
            height: "90%",
            autowidth: true,
            shrinkToFit: true,
            pager: '#system-log-pager',
            viewrecords: true,
            sortname:'createDate',
            sortorder:'desc',
            rowNum: 10,
            rowList: [5, 10, 20, 50, 100],
            recordtext: "{0} - {1}\u3000共 {2} 条",
            emptyrecords: "当前没有日志记录",
            loadtext: "读取中...",
            pgtext: "{0} 共 {1} 页",
            rownumbers: true,
            loadComplete: function () {
                setTimeout(function () {
                    // 自动生成序号
                    $("#system-log-tb").jqGrid('setLabel', 'rn', '序号',{'text-align':'center'});
                }, 0)
            },
            colNames: ['是否目录文件','所有者','权限','大小(B)','上次修改时间','文件名', '操作'],
            colModel: [
                {name: 'isDir', index: 'isDir'},
                {name: 'owner', index: 'owner'},
                {name: 'authority', index: 'authority'},
                {name: 'size', index: 'size',sorttype:'integer'},
                {name: 'modifyDate', index: 'modifyDate'},
                {name: 'name', index: 'name'},
                {name: 'operation', index: 'operation', sortable: false, formatter: displayButtons}]
        });

    // 清空和填充表格数据
    $("#system-log-tb").jqGrid('clearGridData');
    for (let i = 0; i <= data.length; i++) {
        $("#system-log-tb").jqGrid('addRowData', i + 1, data[i]);
    }
    // 数组数据分页.本地数据分页.
    $('.ui-pg-table.ui-pager-table #first_system-log-pager span')
        .addClass('ace-icon fa fa-angle-double-left bigger-100')
    $('.ui-pg-table.ui-pager-table #prev_system-log-pager span')
        .addClass('ace-icon fa fa-angle-left bigger-100')
    $('.ui-pg-table.ui-pager-table #next_system-log-pager span')
        .addClass('ace-icon fa fa-angle-right bigger-100')
    $('.ui-pg-table.ui-pager-table #last_system-log-pager span')
        .addClass('ace-icon fa fa-angle-double-right bigger-100')
    const localData = {page: 1, total: 2, records: "2", rows: []};
    localData.rows = data;
    localData.records = data.length;
    localData.total = (data.length % 2 == 0) ? (data.length / 2) : (Math.floor(data.length / 2) + 1);
    const reader = {
        root: function (obj) {return localData.rows;},
        page: function (obj) {return localData.page;},
        total: function (obj) {return localData.total;},
        records: function (obj) {return localData.records;},
        repeatitems: false
    };
    $("#system-log-tb").setGridParam({
        data: localData.rows,
        reader: reader
    }).trigger('reloadGrid');
    /**
     * 创造行末尾按钮并修改删除点击方法.
     * @param cellvalue
     * @param options
     * @param rowObject
     * @returns {string}
     */
    function displayButtons(cellvalue, options, rowObject) {
        return "<div class='hidden-sm hidden-xs btn-group'>" +
            "<button class='btn btn-minier btn-inverse wa-mlr5'" +
            "row-name='" + rowObject.logname + "'" +
            "onclick=\"$('#validateUserDlg').modal('show')\n" +
            "$('#validateUserForm').get(0).reset()\n" +
            "$('#validateUserForm #opraType').val('selectCompressFormat')\n"+
            "$('#nil').show()\n"+
            "console.log($(this).parent().parent().prev().text())\n" +
            "$('#validateUserForm #log_file_name').val($(this).parent().parent().prev().text())\">导出日志" +
            "</button>" +
            "<button class='btn btn-minier btn-danger'" +
            "row-name='" + rowObject.logname + "'" +
            "onclick=\"$('#validateUserDlg').modal('show')\n" +
            "$('#validateUserForm').get(0).reset()\n" +
            "$('#validateUserForm #opraType').val('clearDebugLog')\n"+
            "$('#validateUserForm #log_file_name').val($(this).parent().parent().prev().text())\">&nbsp;清空</i>" +
            "</button>" +
            "</div>";
        jqgridColResize()
        tableResize();
    }
}

/**
 * 重新调整jqgrid每列的宽度
 */
function jqgridColResize() {
    let td = $('#tdCompute')//获取计算实际列长度的容器
        , tds//临时保存列
        , arr = [];//用于保存最大的列宽
    //遍历每行获得每行中的最大列宽
    $('.ui-jqgrid-htable tr,.ui-jqgrid-btable tr:gt(0)').each(function () {
        $(this).find('td,th').each(function (idx) {
            arr[idx] = Math.max(arr[idx] ? arr[idx] : 0, td.html($(this).text())[0].offsetWidth);
        })
    });
    //设置页头单元格宽度
    $('.ui-jqgrid-labels th').each(function (idx) {
        this.style.width = arr[idx] + 'px'
    });
    //设置内容表格中控制单元格宽度的单元格，在第一行
    $('.ui-jqgrid-btable tr:eq(0) td').each(function (idx) {
        this.style.width = arr[idx] + 'px'
    });
    // 设置操作栏固定宽度
    // $('.ui-jqgrid .ui-jqgrid-htable th:last-child').css("width", "50");
    // $('.ui-jqgrid-btable>tbody>tr>td:last-child').css("width","50");
}

/**
 * 表格自适应窗口
 */
function tableResize() {
    $(window).on('resize.jqGrid', function () {
        $("#system-log-tb").jqGrid('setGridWidth', $(".page-content").width());
        jqgridColResize()
    });
    const parent_column = $("#system-log-tb").closest('[class*="col-"]');
    $(document).on('settings.ace.jqGrid', function (ev, event_name, collapsed) {
        if (event_name == 'sidebar_collapsed' || event_name == 'main_container_fixed') {
            setTimeout(function () {
                $("#system-log-tb").jqGrid('setGridWidth', parent_column.width());
                jqgridColResize()
            }, 0);
        }
    })
}

/**
 * 页面定义的相关数据和初始化执行的相关操作
 */
function init() {
    tcp_client.connect(onMsg, config.host, config.port);
    loadLogsGenerator();
}
init();
/**
 * 发送加载日志请求
 * @param index 索引，从0开始
 * @param count 加载数量
 */
function loadLogsGenerator() {
    // 发送加载日志请求
    let sendMsg = new ProxyServer_pb.MSG_CPListFile();
    sendMsg.setPath("/sd/logs/");
    tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLISTFILE, sendMsg, user_sign);
}

// 弹出密码校验框
$('#validateuser_btn').on('click',function (e) {
    if ($("#validateuser_btn").hasClass("disabled")) {
        return;
    }
    $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
    e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
    let username=user_name
    let psd=$('#validateUserDlg #password').val()
    validateUserGenerator(username,psd);
})

/**
 * 发送验证密码参数
 * @param psd
 */
function validateUserGenerator(username,psd){
// 连接服务器
    tcp_client.connect(function(msgId, pb) {
        const msg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(pb);
        if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
            ui_util.showFadeTip('验证用户通过!');
            if ($('#opraType').val() == "selectCompressFormat") {
                $('#selectCompressFormatDlg').modal('show');
                $('#confirmDelDlg').modal('hide');
            }else if($('#opraType').val() == "clearDebugLog"){
                $('#selectCompressFormatDlg').modal('hide');
                $('#confirmDelDlg').modal('show');
            }
        } else {
            ui_util.getErrorTips(msg.getErrorCode(), '验证用户身份')
        }
    }, config.host, config.port);

    // 发送消息
    let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
    sendMsg.setUserName(username)
    sendMsg.setPassword(psd)
    tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg, user_sign);
}

// 点击确定清空日志内容
$('#removeFileBtn').on('click', function () {
    let logFileName = $('#log_file_name').val();
    clearLogFileGenerator(logFileName);
});

// 点击压缩日志内容
$('#file_Compress_btn').on('click', function () {
    let compress_log_format=$('#compress_log_format').val()===""?-1:$('#compress_log_format').val();
    if(compress_log_format == -1){
        ui_util.showFadeTip('请选择压缩格式!')
        return;
    }
    let logFileName = $('#log_file_name').val();
    CompressLogFileGenerator(logFileName,compress_log_format);
});

/**
 * 发送清空日志请求
 * @param file_name
 */
function clearLogFileGenerator(logFileName) {
    tcp_client.connect(function (msgId, pb) {
        const msg = ProxyServer_pb.MSG_PCClearDebugLog.deserializeBinary(pb);
        if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
            ui_util.showFadeTip('清空日志文件成功');
        }else {
            ui_util.getErrorTips(dp_packet.return_value,'清空日志文件')
        }
        // 消息处理完成关闭连接
        tcp_client.close();
        // 重新加载用户数据
        init();
    }, config.host, config.port);

    // 发送消息
    let sendMsg = new ProxyServer_pb.MSG_CPClearDebugLog();
    sendMsg.setFileName(logFileName);
    tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPCLEARDEBUGLOG, sendMsg, user_sign);
}

/**
 * 发送导出日志请求
 * @param file_name
 */
function CompressLogFileGenerator(logFileName,compress_log_format) {
    tcp_client.connect(function (msgId, pb) {
        const msg = ProxyServer_pb.MSG_PCDownloadFile.deserializeBinary(pb);
        if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
            log_content = msg.getContent();
            $('#confirmPortDlg').modal('show');
            $('#confirmPortDlg .modal-title').attr("wa-name", "request_log_path");
            //$('#confirmPortDlg .modal-body .form-group:nth-child(2)').removeClass("hidden");
            //$('#confirmPortDlg .modal-body .form-group:nth-child(1)').addClass("hidden");
        }else {
            ui_util.getErrorTips(dp_packet.return_value,'导出日志文件');
        }
        // 消息处理完成关闭连接
        tcp_client.close();
        // 重新加载用户数据
        init();
    }, config.host, config.port);

    // 发送消息

    let format = proto.proto.CompressFormat
    let sendMsg = new ProxyServer_pb.MSG_CPDownloadFile();
    sendMsg.setFileName(logFileName);
    if(compress_log_format == 'NIL'){
        sendMsg.setFormat(format.NIL)
    }else if(compress_log_format == 'ZIP'){
        sendMsg.setFormat(format.ZIP)
    }else if(compress_log_format == 'GZIP'){
        sendMsg.setFormat(format.GZIP)
    }else if(compress_log_format == 'JAR'){
        sendMsg.setFormat(format.JAR)
    }
    tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPDOWNLOADFILE, sendMsg, user_sign);
}

$('#port_btn').on('click', function (e) {
    if ($("#port_btn").hasClass("disabled")) {
        return;
    }
    if ($('#confirmPortDlg .modal-body .form-group:nth-child(1)').hasClass("hidden")) {
        if (!$("#select_folder").next("span.ace-file-container").hasClass("selected")) {
            ui_util.showFadeTip('请选择文件路径!')
            return;
        }
    } else if ($('#confirmPortDlg .modal-body .form-group:nth-child(2)').hasClass("hidden")) {
        if (!($("#select_file").next("span.ace-file-container").hasClass("selected"))) {
            ui_util.showFadeTip('请选择文件!')
            return;
        }
    }
    $('#confirmPortDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
    e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
    if ($('#confirmPortDlg .modal-title').attr("wa-name") == "request_log_path") {
        fileName = $("#log_file_name").val();
        exportCompressFormatLogPath(filePath,fileName);
    }
})

function exportCompressFormatLogPath(file_path, file_name){
    if ($("#port_btn").hasClass("disabled")) {
        return;
    }
    $('#confirmPortDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
    let fileLogName = fileName;
    if(fileName == ''){
        fileLogName = "logs";
    }
    let compress_log_format=$('#compress_log_format').val()===""?-1:$('#compress_log_format').val();
    if(compress_log_format == 'ZIP'){
        fileLogName = fileLogName + ".zip";
    }else if(compress_log_format == 'GZIP'){
        fileLogName = fileLogName + ".tar.gz";
    }else if(compress_log_format == 'JAR'){
        fileLogName = fileLogName + ".jar";
    }else{
        fileLogName = fileLogName;
    }
    util.saveToFile(log_content, file_path+"\\"+fileLogName, false);
    $('#selectCompressFormatDlg').modal('hide');
    ui_util.showFadeTip('导出日志文件成功')
}

// 点击全部导出
$('#all_export').on('click', function () {
    $('#validateUserDlg').modal('show');
    $('#opraType').val("selectCompressFormat");
    $('#validateUserDlg #password').val("");
    $('#compress_log_format').val("");
    $("#log_file_name").val("");
    $('#nil').hide();
});

// 点击全部清空
$('#all_clear').on('click', function () {
    $('#validateUserDlg').modal('show');
    $('#opraType').val("clearDebugLog");
    $('#validateUserDlg #password').val("");
});
