'use strict';

const ProxyServer_pb = require('../../pb/ProxyServer_pb');
const PublicDefine_pb = require('../../pb/PublicDefine_pb');
const util = require('../../../lib/util');
const ui_util = require('../ui_util');
const tcp_client = require('../../../lib/tcp_client');
const config = require("../../../config.json");
const device_codec = require('../../platform_codec/device_codec');
// 相关变量定义
const user_sign = $("body").attr("user_sign_info");
const user_name = $("body").attr("user_name_info");
console.log("user_sign:", user_sign + user_name);

$('.parent_content').busyLoad("hide");

let eventsData = new Array();
let evtDataArr=new Array();

// 新增屏蔽事件
$('#add_filted_evt').on('click', function () {
  $('#validateUserDlg').modal('show');
  $('#validateUserForm').get(0).reset()
  $('#opraType').val(1)
})
// 保存屏蔽事件参数
$('#save_evt_btn').on('click', function (e) {
  if ($("#save_evt_btn").hasClass("disabled")) {
    return false;
  }
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  let evtArr=$('#addFiltedEventForm .modal-body [name="form-field-checkbox"]:checked')
  if ($(evtArr).length === 0) {
    ui_util.showFadeTip('请选择要屏蔽事件!');
    return false;
  } else {
    addFiltedEventData(evtArr);
  }
})

// 删除屏蔽事件
$('#remove_evt_btn').on('click', function () {
  let _id=parseInt($('#remove_evt_id').val())
  removeFiltedEventData(_id);
});

// 弹出密码校验框
$('#validateuser_btn').on('click', function (e) {
  if ($("#validateuser_btn").hasClass("disabled")) {
    return;
  }
  $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  let username = user_name
  let psd = $('#validateUserDlg #password').val()
  validateUserGenerator(username, psd);
})

/**
 * 初始化刷新表格.
 */
function loadFiltedEventTb() {
  $("#evt-tb").jqGrid(
    {
      datatype: "local",
      height: "90%",
      autowidth: true,
      shrinkToFit: true,
      pager: '#evt-pager',
      viewrecords: true,
      sortname: 'id',
      sortorder: 'desc',
      rowNum: 10,
      rowList: [5, 10, 20, 50, 100],
      recordtext: "{0} - {1}\u3000共 {2} 条",
      emptyrecords: "当前没有屏蔽事件记录",
      loadtext: "读取中...",
      pgtext: "{0} 共 {1} 页",
      rownumbers: true,
      loadComplete: function (a, b, c) {
        $("#evt-tb").jqGrid('setLabel', 'rn', '编号', {
          'text-align': 'center',
          'vertical-align': 'middle',
          "width": "35"
        });
      },
      colNames: ['_id','设备类型', '事件类型', '事件', '操作'],
      colModel: [
        {name: '_id', index: '_id',hidden:true},
        {name: 'deviceType', index: 'deviceType'},
        {name: 'evtType', index: 'evtType'},
        {name: 'childType', index: 'childType'},
        {name: 'operation', index: 'operation', sortable: false, formatter: displayButtons}
      ]
    });

  // 清空和填充表格数据
  $("#evt-tb").jqGrid('clearGridData');
  for (let i = 0; i <= eventsData.length; i++) {
    $("#evt-tb").jqGrid('addRowData', i + 1, eventsData[i]);
  }
  // 数组数据分页.本地数据分页.
  $('.ui-pg-table.ui-pager-table #first_evt-pager span')
    .addClass('ace-icon fa fa-angle-double-left bigger-100')
  $('.ui-pg-table.ui-pager-table #prev_evt-pager span')
    .addClass('ace-icon fa fa-angle-left bigger-100')
  $('.ui-pg-table.ui-pager-table #next_evt-pager span')
    .addClass('ace-icon fa fa-angle-right bigger-100')
  $('.ui-pg-table.ui-pager-table #last_evt-pager span')
    .addClass('ace-icon fa fa-angle-double-right bigger-100')
  const localData = {page: 1, total: 2, records: "2", rows: []};
  localData.rows = eventsData;
  localData.records = eventsData.length;
  localData.total = (eventsData.length % 2 == 0) ? (eventsData.length / 2) : (Math.floor(eventsData.length / 2) + 1);
  const reader = {
    root: function (obj) {return localData.rows;},
    page: function (obj) {return localData.page;},
    total: function (obj) {return localData.total;},
    records: function (obj) {return localData.records;},
    repeatitems: false
  };
  $("#evt-tb").setGridParam({
    data: localData.rows,
    reader: reader
  }).trigger('reloadGrid');
  /**\
   * 创造行末尾按钮并修改删除点击方法.
   */
  function displayButtons(cellvalue, options, rowObject) {
    return "<div class='hidden-sm hidden-xs btn-group'>" +
      "<button class='btn btn-minier btn-danger'" +
      "row-id='" + rowObject._id + "'" +
      "onclick=\"$('#validateUserDlg').modal('show');\n" +
      "$('#validateUserForm').get(0).reset()\n" +
      "$('#opraType').val(2)\n" +
      "$('#opra_evt_id').val($(this).attr('row-id'))\">" +
      "<i class='ace-icon fa fa-trash-o bigger-80'>&nbsp;取消屏蔽</i>" +
      "</button>" +
      "</div>";
  }

  jqgridColResize();
  tableResize();
}

loadFiltedEventTb()

/**
 * 重新调整jqgrid每列的宽度
 */
function jqgridColResize() {
  let td = $('#tdCompute')//获取计算实际列长度的容器
    , tds//临时保存列
    , arr = [];//用于保存最大的列宽
  //遍历每行获得每行中的最大列宽
  $('.ui-jqgrid-htable tr,.ui-jqgrid-btable tr:gt(0)').each(function () {
    $(this).find('td,th').each(function (idx) {
      arr[idx] = Math.max(arr[idx] ? arr[idx] : 0, td.html($(this).text())[0].offsetWidth);
    })
  });
  //设置页头单元格宽度
  $('.ui-jqgrid-labels th').each(function (idx) {
    this.style.width = arr[idx] + 'px'
  });
  //设置内容表格中控制单元格宽度的单元格，在第一行
  $('.ui-jqgrid-btable tr:eq(0) td').each(function (idx) {
    this.style.width = arr[idx] + 'px'
  });
}

/**
 * 表格自适应窗口
 */
function tableResize() {
  $(window).on('resize.jqGrid', function () {
    $("#evt-tb").jqGrid('setGridWidth', $(".page-content").width());
    jqgridColResize();
  });
  const parent_column = $("#evt-tb").closest('[class*="col-"]');
  $(document).on('settings.ace.jqGrid', function (ev, event_name, collapsed) {
    if (event_name == 'sidebar_collapsed' || event_name == 'main_container_fixed') {
      setTimeout(function () {
        $("#evt-tb").jqGrid('setGridWidth', parent_column.width());
        jqgridColResize();
      }, 0);
    }
  })
}

/**
 * 加载所有事件
 */
function loadAllEventGer() {
  evtDataArr = [];
  $.getJSON("evt_analysis.json", function (data){
    //存储数据变量
    evtDataArr=data.evt_analysis;
    // console.log("evtDataArr",evtDataArr)
  })
}

/**
 * 初始化页面
 */
let events = new Array()

function LoadFiltedEventGenerator() {
  // 连接服务器
  let socket=tcp_client.connect(LoadFiltedEventHandler, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPLoadFiltedEvent();
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADFILTEDEVENT, sendMsg, user_sign, socket);
}

function LoadFiltedEventHandler(msgId, pb) {
  if (msgId !== ProxyServer_pb.PSMessageId.PSMESSAGEID_PCLOADFILTEDEVENT) {
    console.error('invalid msg', msgId);
    return;
  }
  const msg = ProxyServer_pb.MSG_PCLoadFiltedEvent.deserializeBinary(pb);
  events = msg.getFiledEventsList();

  pageInit();
}
function pageInit() {
  eventsData = [];
  for (let i = 0; i < events.length; i++) {
    //设备类型&事件类型&事件描述转换文本
    let deviceType,evtType,childType;
    $.each(evtDataArr, function (infoIndex, info){
      if(events[i].getDeviceType()===info["device_num"]&&events[i].getEventType()===info["evt_type_val"]&&events[i].getEventSubType()===info["child_type_val"]){
        evtType = info["evt_type"]
        childType = info["child_type"]
        // return false;
      }
    })
    if (events[i].getDeviceType() == "0")
      deviceType="防火墙";
    else if (events[i].getDeviceType() == "1")
      deviceType="横向正向隔离装置";
    else if (events[i].getDeviceType() == "2")
      deviceType="横向反向隔离装置";
    else if (events[i].getDeviceType() == "3")
      deviceType="服务器";
    else if (events[i].getDeviceType() == "4")
      deviceType="交换机";
    else if (events[i].getDeviceType() == "9")
      deviceType="网络安全监测装置";
    else
      deviceType="";
    const eventObj = {
      _id: events[i].getId(),
      deviceType: deviceType,
      evtType: evtType,
      childType: childType,
    }
    eventsData.push(eventObj);
  }
  // console.log('eventsData:', eventsData)
  $("#evt-tb").jqGrid("clearGridData"); //清空表格数据
  $("#evt-tb").jqGrid('setGridParam', {  // 重新加载数据
    datatype: 'local',
    data: eventsData,   //  data 是符合格式要求的需要重新加载的数据
    page: 1
  }).trigger("reloadGrid");
}

loadAllEventGer()
LoadFiltedEventGenerator();

/**
 * 发送验证密码参数
 * @param psd
 */
function validateUserGenerator(username, psd) {
// 连接服务器
  let socket=tcp_client.connect(function (msgId, pb) {
    const msg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(pb);
    if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
      // ui_util.showFadeTip('验证用户通过!');
      if ($('#opraType').val() == 1) {
        $('#addFiltedEventDlg').modal('show');
        $("#addFiltedEventForm .modal-body .col-sm-10").html("")
        let subHtml='';
        $.each(evtDataArr, function (infoIndex, info){
          let device_type=info["device_type"]
          let deviceInt=info["device_num"]
          let evt_type_val=info["evt_type_val"]
          let child_type_val=info["child_type_val"]
          let child_type=info["child_type"]
          subHtml ='<label class="radio-inline">' +
            '<input name="form-field-checkbox" type="checkbox" class="ace" ' +
            'dev-int="'+deviceInt+'" evt-type-val="'+evt_type_val+'" value="'+child_type_val+'">' +
            '<span class="lbl">'+child_type+'</span>' +
            '</label>'
            $('#'+device_type+'Evts').append(subHtml)
        })
      } else if ($('#opraType').val() == 2) {
        $('#confirmDlg').modal('show')
        $('#remove_evt_id').val($('#opra_evt_id').val())
      }
    } else {
      ui_util.getErrorTips(msg.getErrorCode(), '验证用户身份')
    }
  }, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
  sendMsg.setUserName(username)
  sendMsg.setPassword(psd)
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg, user_sign, socket);
}

/**
 * 发送新增屏蔽事件参数信息
 */
function addFiltedEventData(evtArr) {
  // 连接服务器
  let socket=tcp_client.connect(function (msgId, pb) {
    const msg = ProxyServer_pb.MSG_PCAddFiltedEvent.deserializeBinary(pb);
    if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
      ui_util.showFadeTip('新增屏蔽事件成功!');
      $('#addFiltedEventDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
    } else {
      ui_util.getErrorTips(msg.getErrorCode(), '新增屏蔽事件')
      $('#addFiltedEventDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
    }
    // 消息处理完成关闭连接
    tcp_client.close();
    LoadFiltedEventGenerator();
  }, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPAddFiltedEvent();
  let filedEvent;
  for (let i = 0; i < $(evtArr).length; i++) {
    filedEvent=sendMsg.addFiledEvent()
    filedEvent.setDeviceType(parseInt($($(evtArr)[i]).attr('dev-int')))
    filedEvent.setEventType(parseInt($($(evtArr)[i]).attr('evt-type-val')))
    filedEvent.setEventSubType(parseInt($($(evtArr)[i]).val()))
  }
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPADDFILTEDEVENT, sendMsg, user_sign, socket);
}

/**
 * 发送删除屏蔽事件
 */
function removeFiltedEventData(_id) {
  // 连接服务器
  let socket=tcp_client.connect(function (msgId, pb) {
    const msg = ProxyServer_pb.MSG_PCRemoveFiltedEvent.deserializeBinary(pb);
    if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
      ui_util.showFadeTip('删除屏蔽事件成功!');
    } else {
      ui_util.getErrorTips(dp_packet.return_value, "删除屏蔽事件")
    }
    // 消息处理完成关闭连接
    tcp_client.close();
    LoadFiltedEventGenerator();
  }, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPRemoveFiltedEvent();
  sendMsg.setId(_id)
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPREMOVEFILTEDEVENT, sendMsg, user_sign, socket);
}
