'use strict';

const ProxyServer_pb = require('../../pb/ProxyServer_pb');
const PublicDefine_pb = require('../../pb/PublicDefine_pb');
const util = require('../../../lib/util');
const ui_util = require('../ui_util');
const tcp_client = require('../../../lib/tcp_client');
const config = require("../../../config.json");
const tcpInstance = require('../../../lib/v2/tls_client.js');
const ntp_param_codec = require('../../platform_codec/ntp_param_codec');
// 相关变量定义
const user_sign = $("body").attr("user_sign_info");
const user_name = $("body").attr("user_name_info");
console.log("user_sign:", user_sign + user_name);

// 初始化页面表单
$('#policyForm').validator();
$('#policyForm').get(0).reset();

// 点击跳转到事件处理页.
$('#hrefToSetEvt').on('click', function (e) {
  e.preventDefault();//阻止bootstrap button点击自动刷新页面默认行为。
  $(".nav.nav-list li").removeClass('active');
  $(".nav.nav-list li").removeClass('open');
  $('li[data-code="event_processing"]').addClass('active');
  $('li[data-code="event_processing"]').parent().parent().addClass('active open');
    $.ajax({
      url: "event_processing.html",
      type: "get",
      dataType: "html",
      success: function (data) {
        $(".parent_content").html(data);
        $.ajax({
          url: "footer.html",
          type: "get",
          dataType: "html",
          success:function (data) {
            let footerData=data
            $(".page-content").append($(footerData))
            //修改页脚版权年份
            const now = new Date()
            $('#new_year').text(now.getFullYear());
            $(".footer #version").html(version)
            $.busyLoadFull("hide");
          }
        })
        $(".spop-container .spop").remove();
      }
    });
});

//点击发送策略配置Frm参数
$('#btn_policy_save').on('click', function (e) {
  if ($("#btn_policy_save").hasClass("disabled")) {
    e.preventDefault();//阻止bootstrap button点击自动刷新页面默认行为。
    return;
  }
  $('#validateUserDlg').modal('show');
  $('#validateUserForm').get(0).reset()
  e.preventDefault();//阻止bootstrap button点击自动刷新页面默认行为。
});

// 弹出密码校验框
$('#validateuser_btn').on('click',function (e) {
  if ($("#validateuser_btn").hasClass("disabled")) {
    return;
  }
  $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  let username=user_name
  let psd=$('#validateUserDlg #password').val()
  validateUserGenerator(username,psd);
})

/**
 * 初始化Frm表单函数
 */
function loadPolicyConfigGenerator(){
  // 连接服务器
  // tcp_client.connect(loadPolicyConfigHandler, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPLoadGeneralParams();
  sendMsg.addKeys(PublicDefine_pb.GeneralParamKey.PARAMKEY_LOGINFAILEDTHRESHOLDTIME)
  sendMsg.addKeys(PublicDefine_pb.GeneralParamKey.PARAMKEY_LOGINCOOLDOWN)
  sendMsg.addKeys(PublicDefine_pb.GeneralParamKey.PARAMKEY_REPEATEDPASSWORDCOUNT)
  sendMsg.addKeys(PublicDefine_pb.GeneralParamKey.PARAMKEY_PASSWORDEXPIRETIME)
  sendMsg.addKeys(PublicDefine_pb.GeneralParamKey.PARAMKEY_CLIENTLOCKTIMEOUT)
  // tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADGENERALPARAMS, sendMsg, user_sign);
  tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADGENERALPARAMS, sendMsg).then();
}

function initOnMessage() {
  // 保存
  tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCUPDATEGENERALPARAMS, (pb) => {
    const msg = ProxyServer_pb.MSG_PCUpdateGeneralParams.deserializeBinary(pb);
    const error_code = msg.getErrorCode();
    const text="策略配置"
    if (error_code === 0) {
      ui_util.showFadeTip('策略配置保存成功!');
    } else {
      ui_util.getErrorTips(error_code,text)
    }
  })
  // 校验
  tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCVALIDATEUSER, (pb) => {
    const msg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(pb);
    console.log('user_validate =>', msg.getErrorCode())
    if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
      updatePolicyConfig();
    } else {
      ui_util.getErrorTips(msg.getErrorCode(), '验证用户身份')
    }
  })
  // 回显数据监听
  tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCLOADGENERALPARAMS, (pb) => {
    const msg = ProxyServer_pb.MSG_PCLoadGeneralParams.deserializeBinary(pb);
    const paramsList = msg.getParamsList();
    console.log("paramsList",paramsList)
    $('#fail_threshold_time').val(paramsList[0].getValue()/60)
    $('#log_cool_down').val(paramsList[1].getValue()/60)
    $('#repeat_psd_count').val(paramsList[2].getValue())
    $('#expire_time').val(paramsList[3].getValue()/3600/24)
    $('#lock_timeout').val(paramsList[4].getValue())
    console.log('xxxx', paramsList[4].getValue())
  })
}
initOnMessage()
loadPolicyConfigGenerator()

/**
 * 发送验证密码参数
 * @param psd
 */
function validateUserGenerator(username,psd){
// // 连接服务器
//   tcp_client.connect(function(msgId, pb) {
//     const msg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(pb);
//     if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
//       updatePolicyConfig();
//     } else {
//       ui_util.getErrorTips(msg.getErrorCode(), '验证用户身份')
//     }
//   }, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
  sendMsg.setUserName(username)
  sendMsg.setPassword(psd)
  tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg).then();
  // tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg, user_sign);
}

/**
 *
 * 发送策略配置Frm参数
 */
function updatePolicyConfig() {
  // 连接服务器
  // tcp_client.connect(updatePolicyConfigHandler, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPUpdateGeneralParams();
  let param = sendMsg.addParams();
  param.setKey(PublicDefine_pb.GeneralParamKey.PARAMKEY_LOGINFAILEDTHRESHOLDTIME);
  param.setValue(String($('#fail_threshold_time').val()*60));
  param = sendMsg.addParams();
  param.setKey(PublicDefine_pb.GeneralParamKey.PARAMKEY_LOGINCOOLDOWN);
  param.setValue(String($('#log_cool_down').val()*60));
  param = sendMsg.addParams();
  param.setKey(PublicDefine_pb.GeneralParamKey.PARAMKEY_REPEATEDPASSWORDCOUNT);
  param.setValue($('#repeat_psd_count').val())
  param = sendMsg.addParams();
  param.setKey(PublicDefine_pb.GeneralParamKey.PARAMKEY_PASSWORDEXPIRETIME);
  param.setValue(String($('#expire_time').val()*24*3600))
  param.setKey(PublicDefine_pb.GeneralParamKey.PARAMKEY_CLIENTLOCKTIMEOUT);
  param.setValue(String($('#lock_timeout').val()))
  tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPUPDATEGENERALPARAMS, sendMsg).then();
  // tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPUPDATEGENERALPARAMS, sendMsg, user_sign);
}
function updatePolicyConfigHandler(msgId, pb) {
  if (msgId !== ProxyServer_pb.PSMessageId.PSMESSAGEID_PCUPDATEGENERALPARAMS) {
    console.error('invalid msg', msgId);
    return;
  }
  const msg = ProxyServer_pb.MSG_PCUpdateGeneralParams.deserializeBinary(pb);
  const error_code = msg.getErrorCode();
  const text="策略配置"
  if (error_code === 0) {
    ui_util.showFadeTip('策略配置保存成功!');
  } else {
    ui_util.getErrorTips(error_code,text)
  }

  // 消息处理完成关闭连接
  tcp_client.close();
}
