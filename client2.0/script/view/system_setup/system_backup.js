'use strict';

const ProxyServer_pb = require('../../pb/ProxyServer_pb');
const PublicDefine_pb = require('../../pb/PublicDefine_pb');
const util = require('../../../lib/util');
const ui_util = require('../ui_util');
const tcp_client = require('../../../lib/tcp_client');
const config = require("../../../config.json");
const system_backup_codec = require('../../platform_codec/system_backup_codec');
// 相关变量定义
const user_sign = $("body").attr("user_sign_info");
const user_name = $("body").attr("user_name_info");
console.log("user_sign:", user_sign + user_name);

let filePath="",filePath2="",fileName2="data_backup.bak";
$("#btn_upgrade").addClass("disabled")
// $("#port_btn").addClass("disabled")
// 初始化选择文件夹路径-备份
$('#select_folder').ace_file_input({
  no_file:'请选择文件夹',
  btn_choose:'选择',
  btn_change:null,
  droppable:false,
  onchange:null,
  thumbnail:false
});
document.querySelector('#select_folder').addEventListener('change', e => {
  for (let entry of e.target.files){
    // console.log(entry.name, entry.path);
    filePath2=entry.path
  }
});
// 初始化选择文件-数据恢复
$('#id-input-file').ace_file_input({
  style: 'well',
  btn_choose: '请选择.bak后缀文件',
  btn_change: null,
  no_icon: 'ace-icon fa fa-cloud-download',
  allowExt: ['bak'],
  // droppable: true,
  thumbnail: 'large',
  preview_error : function(filename, error_code) {
    console.log(error_code);
  }
}).on('change', function(){
  let fileDir = $(this).val();
  let suffix = fileDir.substr(fileDir.lastIndexOf("."));
  if ("" == fileDir||".bak" != suffix) {
    ui_util.showFadeTip('请选择.bak后缀文件!');
    $("#btn_upgrade").addClass("disabled")
    return false;
  }
  filePath=$(this).data('ace_input_files')[0].path
  $("#btn_upgrade").removeClass("disabled")
});

let msgQueue;
// 备份、恢复所选文件数据
$('#btn_upgrade').on('click',function (e) {
  if ($("#btn_upgrade").hasClass("disabled")) {
    return false;
  }
  $('#validateUserDlg').modal('show');
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  $('#validateUserForm').get(0).reset()
  $('#opraType').val("recover")
  msgQueue=util.readFromFile(filePath,false)
})
$('#backup').on('click',function (e) {
  $('#validateUserDlg').modal('show');
  $('#validateUserForm').get(0).reset()
  $('#opraType').val("backup")
  // $('#validateUserForm #log_file_name').val($(this).parent().parent().prev().text())
})

// 弹出密码校验框
$('#validateuser_btn').on('click',function (e) {
  if ($("#validateuser_btn").hasClass("disabled")) {
    return;
  }
  $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  let username=user_name
  let psd=$('#validateUserDlg #password').val()
  validateUserGenerator(username,psd);
})
// 数据备份
$('#port_btn').on('click',function (e) {
  if ($("#port_btn").hasClass("disabled")) {
    return;
  }
  $('#confirmPortDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  backupDatas(filePath2,fileName2)
})

/**
 * 发送验证密码参数
 * @param psd
 */
function validateUserGenerator(username,psd){
// 连接服务器
  tcp_client.connect(function(msgId, pb) {
    const msg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(pb);
    if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
      // ui_util.showFadeTip('验证用户通过!');
      if($('#opraType').val()==="recover"){
        recoveryDatas();
      }else if($('#opraType').val()==="backup"){
        $('#confirmPortDlg').modal('show')
      }
    } else {
      ui_util.showFadeTip('密码输入不正确!');
    }
  }, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
  sendMsg.setUserName(username)
  sendMsg.setPassword(psd)
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg, user_sign);
}

/**
 * 发送恢复数据内容.
 * @param content
 */
function recoveryDatas() {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    const text="数据恢复"
    if (dp_packet.return_value === 0) {
      ui_util.showFadeTip('数据恢复成功,需重启装置!');
    } else {
      ui_util.getErrorTips(dp_packet.return_value,text)
    }
    // 消息处理完成关闭连接
    tcp_client.close();
  }, config.host, config.port);

  // 发送消息
  tcp_client.send(system_backup_codec.packDataRecovery(msgQueue));
}

/**
 * 发送备份数据内容.
 */
function backupDatas(filePath2,fileName2) {
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      const msg = system_backup_codec.unpackDataBackup(dp_packet.content)
      // 转出csv格式文件中中文乱码解决
      util.saveToFile(Buffer.from(msg), filePath2+"/"+fileName2, false);
      ui_util.showFadeTip('数据备份成功')
    }

    // 消息处理完成关闭连接
    tcp_client.close();
  }, config.host, config.port);

  // 发送消息
  tcp_client.send(system_backup_codec.packDataBackup());
}

