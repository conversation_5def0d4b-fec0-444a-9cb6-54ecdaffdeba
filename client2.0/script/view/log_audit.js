'use strict';
const {ipc<PERSON>enderer} = require('electron');

const ProxyServer_pb = require('../pb/ProxyServer_pb');
const PublicDefine_pb = require('../pb/PublicDefine_pb');
const util = require('../../lib/util');
const ui_util = require('./ui_util');
const tcp_client = require('../../lib/tcp_client');
const config = require("../../config.json");
// const collect_event_codec = require('../platform_codec/collect_event_codec');
// 相关变量定义
const user_sign = $("body").attr("user_sign_info");
const user_name = $("body").attr("user_name_info");
console.log("user_sign:", user_sign + user_name);
const tcpInstance = require('../../lib/v2/tls_client.js');
const PlatformProxyServer_pb = require("../pb/PlatformProxyServer_pb");


$('.parent_content').busyLoad("hide");
// 初始化日期控件
let beginTimeStore = '';
let endTimeStore = '';
$('#newTimeRange').daterangepicker({
    "timePicker": true,
    "timePicker24Hour": true,
    "linkedCalendars": false,
    "autoUpdateInput": true,
    "singleDatePicker": true,//单日历模式
    "startDate": moment().startOf('day'),
    // "endDate": moment().endOf('day'),
    "endDate": util.getDateMax(), //设置当前日期最大时间数(例如2020/01/18 23:59:59)
    // maxDate: moment(new Date()), //设置最大日期
    "locale": {
        format: 'YYYY/MM/DD HH:mm:ss',
        separator: ' - ',
        applyLabel: "确认",
        cancelLabel: "取消",
        fromLabel: "开始时间",
        toLabel: "结束时间",
        customRangeLabel: "自定义",
        daysOfWeek: ["日", "一", "二", "三", "四", "五", "六"],
        monthNames: ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"]
    },
    // ranges: {
    //     '今天': [moment(), moment().endOf('day')],
    //     '昨天': [moment().subtract(1, 'days'), moment().endOf('day').subtract(1, 'days')],
    //     '近7天': [moment().subtract(7, 'days'), moment().endOf('day')],
    //     '本月': [moment().startOf('month'), moment().endOf('month').endOf('day')],
    //     '上月': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month').endOf('day')]
    // },
}, function (start, end, label) {
    beginTimeStore = start;
    endTimeStore = end;
    if (!this.startDate) {
        this.element.val('');
    } else {
        this.element.val(this.startDate.format(this.locale.format) + this.locale.separator + this.endDate.format(this.locale.format));
    }
});

$('#endTimeRange').daterangepicker({
    "timePicker": true,
    "timePicker24Hour": true,
    "linkedCalendars": false,
    "autoUpdateInput": true,
    "singleDatePicker": true,
    "startDate": util.getDateMax(),
    // "endDate": moment().endOf('day'),
    "endDate": util.getDateMax(), //设置当前日期最大时间数(例如2020/01/18 23:59:59)
    // maxDate: moment(new Date()), //设置最大日期
    "locale": {
        format: 'YYYY/MM/DD HH:mm:ss',
        separator: ' - ',
        applyLabel: "确认",
        cancelLabel: "取消",
        fromLabel: "开始时间",
        toLabel: "结束时间",
        customRangeLabel: "自定义",
        daysOfWeek: ["日", "一", "二", "三", "四", "五", "六"],
        monthNames: ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"]
    },
    // ranges: {
    //     '今天': [moment(), moment().endOf('day')],
    //     '昨天': [moment().subtract(1, 'days'), moment().endOf('day').subtract(1, 'days')],
    //     '近7天': [moment().subtract(7, 'days'), moment().endOf('day')],
    //     '本月': [moment().startOf('month'), moment().endOf('month').endOf('day')],
    //     '上月': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month').endOf('day')]
    // },
}, function (start, end, label) {
    beginTimeStore = start;
    endTimeStore = end;
    if (!this.startDate) {
        this.element.val('');
    } else {
        this.element.val(this.startDate.format(this.locale.format) + this.locale.separator + this.endDate.format(this.locale.format));
    }
});


let filePath = "", fileName = "log.csv";
// 初始化选择文件夹路径&选择文件
// $('#select_folder').ace_file_input({
//     no_file: '请选择文件夹',
//     btn_choose: '选择',
//     btn_change: null,
//     droppable: false,
//     onchange: null,
//     thumbnail: false
// });
// document.querySelector('#select_folder').addEventListener('change', e => {
//     for (let entry of e.target.files) {
//         console.log(entry.name, entry.path);
//         filePath = entry.path
//     }
// });

// 导出日志
$('#log_export').on('click', function () {
    $('#validateUserDlg').modal('show');
    $('#validateUserForm').get(0).reset()
})
// 弹出密码校验框
$('#validateuser_btn').on('click', function (e) {
    if ($("#validateuser_btn").hasClass("disabled")) {
        return;
    }
    $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
    e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
    let username = user_name
    let psd = $('#validateUserDlg #password').val()
    validateUserGenerator(username, psd);
})
// 导出按钮
document.querySelector('#export_selectPath_btn').addEventListener('click', async (e) => {
    console.log('选择文件路径 =>')
    const folderPath = await ipcRenderer.invoke('select-folder');
    if (folderPath) {
        console.log('选中的文件夹路径:', folderPath);
        $("#export_file_path").val(folderPath);
        filePath = folderPath
    } else {
        console.log('用户取消选择文件夹');
    }
});
// 确定导出日志
$('#port_btn').on('click', function (e) {
    if ($("#port_btn").hasClass("disabled")) {
        return;
    }
    // if (!$("#select_folder").next("span.ace-file-container").hasClass("selected")) {
    if (!filePath) {
        ui_util.showFadeTip('请选择文件路径!')
        return;
    }
    $('#confirmPortDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
    e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
    operator_name = $.trim($('#username').val());
    let _begin = $('#newTimeRange').val().substring(0, 19),
        _end = $('#endTimeRange').val().substring(0, 19);
    exp_beginDate = util.getThisTimestamp(_begin) * 1000;
    exp_endDate = util.getThisTimestamp(_end) * 1000;
    exp_index = 1
    log_source = $('#log_source').val() === "" ? -1 : parseInt($('#log_source').val()),
    log_type = $('#log_type').val() === "" ? -1 : parseInt($('#log_type').val()),
    log_level = $('#log_level').val() === "" ? -1 : parseInt($('#log_level').val()),
    log_module = $('#log_module').val() === "" ? -1 : parseInt($('#log_module').val())
    , is_exported = true;
    exportLogs(log_source, log_level, log_type, log_module, operator_name, is_exported)
})

// 条件筛选刷新表格
let operator_name = $('#username').val(),
    beginDate = util.getThisTimestamp($('#newTimeRange').val().substring(0, 19)) * 1000,
    endDate = util.getThisTimestamp($('#endTimeRange').val().substring(0, 19)) * 1000,
    index = 1,
    count = 10,
    log_source = -1,
    log_level = -1,
    log_type = -1,
    log_module = -1,
    is_exported = false;
// cont=$('#content').val();
$('#query').on('click', function () {
    operator_name = $.trim($('#username').val());
    let _begin = $('#newTimeRange').val().substring(0, 19),
        _end = $('#endTimeRange').val().substring(0, 19);
    beginDate = util.getThisTimestamp(_begin) * 1000;
    endDate = util.getThisTimestamp(_end) * 1000;
    index = 1,
        count = 10,
        log_source = $('#log_source').val() === "" ? -1 : parseInt($('#log_source').val()),
        log_type = $('#log_type').val() === "" ? -1 : parseInt($('#log_type').val()),
        log_level = $('#log_level').val() === "" ? -1 : parseInt($('#log_level').val()),
        log_module = $('#log_module').val() === "" ? -1 : parseInt($('#log_module').val())
        , is_exported = false;
    // cont=$.trim($('#content').val());
    // init(beginDate,endDate,index, count,log_source,log_level,log_type,log_module,operator_name,is_exported);
    $("#collect-evt-tb").bootstrapTable('selectPage', 1);// 查询后跳转至第1页
})

/**
 * 初始化刷新表格.
 */
let data = new Object();

function createLogsTable() {
    $("#collect-evt-tb").bootstrapTable({
        method: 'post',
        data: data,
        toolbar: '#toolbar',
        cache: false, // 设置为 false 禁用 AJAX 数据缓存， 默认为true
        striped: true,  //表格显示条纹，默认为false
        pagination: true, // 在表格底部显示分页组件，默认false
        pageList: [10, 20, 50, 100], // 设置页面可以显示的数据条数
        pageNumber: index, //初始化加载第一页，默认第一页
        pageSize: count, // 页面数据条数
        queryParamsType: 'limit',//查询参数组织方式
        // queryParams:queryParams,//请求服务器时所传的参数
        sidePagination: 'server',//指定服务器端分页
        paginationShowPageGo: true,//跳转到
        columns: [
            {
                field: 'source', title: '来源', width: '8%', align: "center", formatter: function (value, row, index) {
                    if (value == 0) return "应用日志";
                    else if (value == 1) return "操作系统日志";
                }
            },
            {
                field: 'logLevel',
                title: '级别',
                width: '8%',
                align: "center",
                formatter: function (value, row, index) {
                    if (value == 0) return "紧急";
                    else if (value == 1) return "重要";
                    else if (value == 2) return "次要";
                    else if (value == 3) return "一般";
                    else if (value == 4) return "告知";
                }
            },
            {field: 'time', title: '日期/时间', width: '8%', align: "center",},
            {
                field: 'logType',
                title: '日志类型',
                width: '8%',
                align: "center",
                formatter: function (value, row, index) {
                    if (value == 0) return "登录";
                    else if (value == 1) return "操作";
                    else if (value == 2) return "维护";
                    else if (value == 3) return "系统";
                    else if (value == 4) return "其他";
                }
            },
            {
                field: 'module',
                title: '日志模块',
                width: '10%',
                align: "center",
                formatter: function (value, row, index) {
                    if (value == 0) return "本地管理客户端";
                    else if (value == 1) return "采集模块";
                    else if (value == 2) return "分析模块";
                    else if (value == 3) return "服务代理模块";
                    else if (value == 4) return "通信模块";
                }
            },
            {
                field: 'result', title: '结果', width: '8%', align: "center", formatter: function (value, row, index) {
                    if (value == true) return "成功";
                    else if (value == false) return "失败";
                }
            },
            {field: 'operator_ip', title: '用户ID/主体ID/地址', align: "center", width: '8%'},
            {field: 'operator_name', title: '用户/主体名', align: "center", width: '8%'},
            {field: 'content', title: '操作内容', align: "center", width: '36%'}
        ],
        rowStyle: function (row, index) {
            //这里有5个取值代表5中颜色['active', 'success', 'info', 'warning', 'danger'];
            let strclass = "";
            let style;
            if (row.logLevel == 0) {
                style = {css: {'background': '#f2dede'}};
                return style
                // strclass = 'danger'
            } else if (row.logLevel == 1) {
                style = {css: {'background': '#fcf8e3'}};
                return style
            } else if (row.logLevel == 2) {
                style = {css: {'background': '#d9edf7'}};
                return style
            } else if (row.logLevel == 3) {
                style = {css: {'background': '#dff0d8'}};
                return style
            } else if (row.logLevel == 4) {
                style = {css: {'background': '#f5f5f5'}};
                return style
            } else return {};
            return {classes: strclass}
        },
        sortable: true, //是否启用排序
        sortName: 'time', // 要排序的字段
        sortOrder: 'decs', // 排序规则,
        showLoading: true,
        onPageChange: function (number, size) {
            index = number;
            count = size;
            is_exported = false
            init(beginDate, endDate, index, count, log_source, log_level, log_type, log_module, operator_name, is_exported);
        }
    });
// 表格自适应窗口
    $(window).resize(function () {
        $("#collect-evt-tb").bootstrapTable('resetView', {
            width: $(".page-content").width()
        });
    });
}

createLogsTable()
initOnMessage()
init(beginDate, endDate, index, count, log_source, log_level, log_type, log_module, operator_name, is_exported);

/**
 * 页面定义的相关数据和初始化执行的相关操作
 */
function init(beginDate, endDate, index, count, log_source, log_level, log_type, log_module, operator_name, is_exported) {
    loadLogAuditGenerator(beginDate, endDate, index, count, log_source, log_level, log_type, log_module, operator_name, is_exported);
}

/**
 * 发送加载日志请求
 * @param index 索引，从0开始
 * @param count 加载数量
 */
function loadLogAuditGenerator(beginDate, endDate, index, count, log_source, log_level, log_type, log_module, operator_name, is_exported) {
    // let socket = tcp_client.connectEx(onMsg, config.host, config.port);
    let sendMsg = new ProxyServer_pb.MSG_CPLoadRunLogs();
    sendMsg.setStartTime(beginDate);
    sendMsg.setEndTime(endDate);
    sendMsg.setIndex(index);
    sendMsg.setCount(count);
    sendMsg.setSource(log_source);
    sendMsg.setLevel(log_level);
    sendMsg.setType(log_type);
    sendMsg.setModule(log_module);
    sendMsg.setOperatorName(operator_name)
    sendMsg.setIsExported(is_exported);
    console.log('send =>', sendMsg)
    tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADRUNLOGS, sendMsg).then();
}

let exp_index = 0
let exp_beginDate = null;
let exp_endDate = null
let exp_count = 10
let exp_log_source = null
let exp_log_level = null
function initOnMessage() {
    // 页面数据监听
    tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCLOADRUNLOGS, (pb) => {
        console.log('是否为导出模式', is_exported)
        const msg = ProxyServer_pb.MSG_PCLoadRunLogs.deserializeBinary(pb);
        let logs = msg.getLogsList();

        // 非导出 数据查询
        if (!is_exported) {
            console.log('是否导出：is_exported =>', is_exported, logs.length)
            let _totalCount = msg.getTotalCount();
            data = {"total": 0, "rows": []};
            data.total = _totalCount;
            for (let i = 0; i < logs.length; ++i) {
                let log = logs[i];
                // if(log.getOperatorName().indexOf(operator_name) !==-1&&log.getContent().indexOf(cont) !==-1){
                let row = {
                    source: log.getSource(),
                    logLevel: log.getLevel(),
                    time: new Date(log.getTime()).toLocaleString(),
                    logType: log.getType(),
                    module: log.getModule(),
                    result: log.getSuccess(),
                    operator_ip: log.getOperatorIp(),
                    operator_name: log.getOperatorName(),
                    content: log.getContent(),
                };
                data.rows.push(row);
                // }
            }
            console.log(data)
            $("#collect-evt-tb").removeClass("hidden")
            $("#collect-evt-tb-nodata").addClass("hidden")
            $('#collect-evt-tb').bootstrapTable('removeAll');
            $("#collect-evt-tb").bootstrapTable("load", data);
            // 设置表格宽度问题
//   let _width=$(".page-content").width()
//   $("#collect-evt-tb>tbody>tr.no-records-found>td").css("width",_width-40+"px")
            if (data) {
                if (data.rows !== undefined) {
                    if (data.rows.length == 0) {
                        $("#collect-evt-tb").addClass("hidden")
                        $("#collect-evt-tb-nodata").removeClass("hidden")
                    }
                }
            }
        } else {
            console.log(
                `是否导出：is_exported => ${is_exported}` ,
                `logs => ${logs.length}`,
                `exp_index => ${exp_index}`,
                )
            let _begin = $('#newTimeRange').val().substring(0, 19),
                _end = $('#endTimeRange').val().substring(0, 19);
            beginDate = util.getThisTimestamp(_begin) * 1000;
            // 导出方式
            console.log("logs.length", logs.length)
            console.log("recv resp", exp_index)
            // portedCount = portedCount+msg.getIndex()
            portedCount = exp_index * 10
            totalCount = msg.getTotalCount()
            for (const log of logs) {
                sourceInit(log.getSource())
                logLevelInit(log.getLevel())
                logTypeInit(log.getType())
                moduleInit(log.getModule())
                resultInit(log.getSuccess())
                str += _source + "," + _logLevel + "," + new Date(log.getTime()).toLocaleString() + "," + _logType + "," + _module + "," + _result + "," +
                    log.getOperatorIp() + "," + log.getOperatorName() + "," + log.getContent() + "\n"
            }
            console.log('日子打印', logs)
            if (logs.length === 10) {
                // window.setTimeout(function () {
                $('.busy-load-text').text(' 已导出' + portedCount + '/' + totalCount + '条日志');
                // send request
                ++exp_index;
                console.log('二次请求用',
                    exp_beginDate,
                    exp_endDate,
                    exp_index,
                    exp_count,
                    exp_log_source,
                    exp_log_level,
                    exp_beginDate,
                    log_type,
                    log_module,
                    operator_name
                    )
                let sendMsg = new ProxyServer_pb.MSG_CPLoadRunLogs();
                sendMsg.setStartTime(exp_beginDate);
                sendMsg.setEndTime(exp_endDate);
                sendMsg.setIndex(exp_index);
                sendMsg.setCount(exp_count);
                sendMsg.setSource($('#log_source').val() === "" ? -1 : parseInt($('#log_source').val()));
                sendMsg.setLevel($('#log_level').val() === "" ? -1 : parseInt($('#log_level').val()));
                sendMsg.setType(log_type);
                sendMsg.setModule(log_module);
                operator_name = $.trim($('#username').val());
                let _begin = $('#newTimeRange').val().substring(0, 19),
                    _end = $('#endTimeRange').val().substring(0, 19);
                sendMsg.setOperatorName(operator_name),
                sendMsg.setIsExported(is_exported);
                tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADRUNLOGS, sendMsg).then();
                // tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADRUNLOGS, sendMsg, user_sign, socket)
                console.log("send req", exp_index)
                // }, 1000);
            } else {
                // 转出csv格式文件中中文乱码解决
                str = "\uFEFF" + str;
                console.log(str)
                const timeWatermark = String(moment(new Date()).format('YYYY-MM-DD-HH-mm-ss'));
                util.saveToCSV(Buffer.from(str), filePath + "/" + `log-${timeWatermark}.csv`);
                ui_util.showFadeTip('导出日志配置成功')
                $('.parent_content').busyLoad("hide");
                // 消息处理完成关闭连接
                // is_exported=false
                // init(beginDate,endDate,index, count,log_source,log_level,log_type,log_module,operator_name,is_exported);
            }
        }

    })
    // 用户校验监听
    tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCVALIDATEUSER, (pb) => {
        const msg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(pb);
        if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
            //ui_util.showFadeTip('验证用户通过!');
            $('#confirmPortDlg').modal('show')
        } else {
            ui_util.getErrorTips(msg.getErrorCode(), '验证用户身份')
        }
    })
    // 导出
    // tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCLOADRUNLOGS, (pb) => {})
}

const requestMsgId = util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET)

/**
 * 发送验证密码参数
 * @param psd
 */
function validateUserGenerator(username, psd) {
// 连接服务器
//   let socket = tcp_client.connectEx(function (msgId, pb) {
//     const msg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(pb);
//     if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
//       //ui_util.showFadeTip('验证用户通过!');
//       $('#confirmPortDlg').modal('show')
//     } else {
//       ui_util.getErrorTips(msg.getErrorCode(), '验证用户身份')
//     }
//   }, config.host, config.port);

    // 发送消息
    let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
    sendMsg.setUserName(username)
    sendMsg.setPassword(psd)
    tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg).then();
    // tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg, user_sign, socket);
}

/**
 * 导出日志
 */
let str = "";
let totalCount = 0;
let portedCount = 0;
let _source = "", _logLevel = "", _logType = "", _module = "", _result = "";

function exportLogs(exp_log_source, exp_log_level, exp_log_type, exp_log_module, exp_operator_name, exp_is_exported) {
    // let socket = tcp_client.connectEx(exportHandler, config.host, config.port, false);

//     function exportHandler(msgId, pb) {
//         const msg = ProxyServer_pb.MSG_PCLoadRunLogs.deserializeBinary(pb);
//         let logs = msg.getLogsList();
//         console.log("", logs.length)
//         console.log("recv resp", exp_index)
//         // portedCount = portedCount+msg.getIndex()
//         portedCount = exp_index * 10
//         totalCount = msg.getTotalCount()
//         for (const log of logs) {
//             sourceInit(log.getSource())
//             logLevelInit(log.getLevel())
//             logTypeInit(log.getType())
//             moduleInit(log.getModule())
//             resultInit(log.getSuccess())
//             str += _source + "," + _logLevel + "," + new Date(log.getTime()).toLocaleString() + "," + _logType + "," + _module + "," + _result + "," +
//                 log.getOperatorIp() + "," + log.getOperatorName() + "," + log.getContent() + "\n"
//         }
//         if (logs.length === 10) {
//             // window.setTimeout(function () {
//             $('.busy-load-text').text(' 已导出' + portedCount + '/' + totalCount + '条日志');
//             // send request
//             exp_index++;
//             let sendMsg = new ProxyServer_pb.MSG_CPLoadRunLogs();
//             sendMsg.setStartTime(exp_beginDate);
//             sendMsg.setEndTime(exp_endDate);
//             sendMsg.setIndex(exp_index);
//             sendMsg.setCount(exp_count);
//             sendMsg.setSource(exp_log_source);
//             sendMsg.setLevel(exp_log_level);
//             sendMsg.setType(exp_log_type);
//             sendMsg.setModule(exp_log_module);
//             sendMsg.setOperatorName(exp_operator_name),
//                 sendMsg.setIsExported(exp_is_exported);
//             tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADRUNLOGS, sendMsg, user_sign, socket)
//             console.log("send req", exp_index)
//             // }, 1000);
//         } else {
//             // 转出csv格式文件中中文乱码解决
//             str = "\uFEFF" + str;
//             console.log(str)
// // console.log(Buffer.from(str))
//             util.saveToCSV(Buffer.from(str), filePath + "/" + fileName);
//             ui_util.showFadeTip('导出日志配置成功')
//             $('.parent_content').busyLoad("hide");
//             // 消息处理完成关闭连接
//             tcp_client.close(socket);
//             // is_exported=false
//             // init(beginDate,endDate,index, count,log_source,log_level,log_type,log_module,operator_name,is_exported);
//         }
//     };
    console.log('一次请求用',
        exp_beginDate,
        exp_endDate,
        exp_index,
        exp_count,
        exp_log_source,
        exp_log_level,
        exp_beginDate,
        exp_log_type,
        exp_log_module,
        exp_operator_name,
    )
    // 发送消息
    let sendMsg = new ProxyServer_pb.MSG_CPLoadRunLogs();
    sendMsg.setStartTime(exp_beginDate);
    sendMsg.setEndTime(exp_endDate);
    sendMsg.setIndex(exp_index);
    sendMsg.setCount(exp_count);
    sendMsg.setSource(exp_log_source);
    sendMsg.setLevel(exp_log_level);
    sendMsg.setType(exp_log_type);
    sendMsg.setModule(exp_log_module);
    sendMsg.setOperatorName(exp_operator_name),
    sendMsg.setIsExported(exp_is_exported);
    tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADRUNLOGS, sendMsg).then();
    // tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADRUNLOGS, sendMsg, user_sign, socket);
    let span = $("<span>", {class: "fa fa-spinner fa-spin fa-3x fa-fw"})
    // .append($("<span>", {class:"cust-elem",text: ' 已导入'+portedCount+'/'+totalCount+'条日志'}))
    $('.parent_content').busyLoad("show", {
        background: "rgba(0, 0, 0, 0.59)",
        animation: "fade",
        custom: span,
        text: ' 已导入' + portedCount + '/' + totalCount + '条日志',
        fontSize: "3rem",
        textPosition: "bottom"
    });
    // $.busyLoadFull("show",{
    //   background: "rgba(0, 0, 0, 0.55)",
    //   fontawesome: "fa fa-spinner fa-spin fa-3x fa-fw"
    // })
}

function sourceInit(sourceVal) {
    switch (sourceVal) {
        case 0:
            _source = "应用日志";
            break;
        case 1:
            _source = "操作系统日志";
            break;
    }
}

function logLevelInit(logLevelVal) {
    switch (logLevelVal) {
        case 0:
            _logLevel = "紧急";
            break;
        case 1:
            _logLevel = "重要";
            break;
        case 2:
            _logLevel = "次要";
            break;
        case 3:
            _logLevel = "一般";
            break;
        case 4:
            _logLevel = "告知";
            break;
    }
}

function logTypeInit(logTypeVal) {
    switch (logTypeVal) {
        case 0:
            _logType = "登录";
            break;
        case 1:
            _logType = "操作";
            break;
        case 2:
            _logType = "维护";
            break;
        case 3:
            _logType = "系统";
            break;
        case 4:
            _logType = "其他";
            break;
    }
}

function moduleInit(moduleVal) {
    switch (moduleVal) {
        case 0:
            _module = "本地管理客户端";
            break;
        case 1:
            _module = "采集模块";
            break;
        case 2:
            _module = "分析模块";
            break;
        case 3:
            _module = "服务代理模块";
            break;
        case 4:
            _module = "通信模块";
            break;
    }
}

function resultInit(resultVal) {
    switch (resultVal) {
        case true:
            _result = "成功";
            break;
        case false:
            _result = "失败";
            break;
    }
}
