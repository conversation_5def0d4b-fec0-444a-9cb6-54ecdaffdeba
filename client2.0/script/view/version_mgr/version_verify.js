'use strict';

const ProxyServer_pb = require('../../pb/ProxyServer_pb');
const PublicDefine_pb = require('../../pb/PublicDefine_pb');
const util = require('../../../lib/util');
const ui_util = require('./../ui_util');
const tcp_client = require('../../../lib/tcp_client');
const config = require("../../../config.json");
const device_codec = require('../../platform_codec/device_codec');
const version_codec = require('../../platform_codec/version_codec');
// 相关变量定义
const user_sign = $("body").attr("user_sign_info");
const user_name = $("body").attr("user_name_info");
console.log("user_sign:", user_sign + user_name);

const version_data=Buffer.from('eb02000000000012444344303030303032374b45444f4e47dbaa', 'hex');

let validateDlgId,validateDlgIp;
//定时刷新页面.
let timer=null;
loadVersionVerifyGenerator();
timer=setInterval(function(){
  loadVersionVerifyGenerator();
}, 10000);
ui_util.stopTimeOut(timer)

// 点击创建新版本校验
$('#add_verify').on('click', function () {
  $('#validateUserDlg').modal('show');
  $('#validateUserForm').get(0).reset()
  $('#opraType').val(1)
})

// 启动单行设备版本校验
$('#start_device_btn').on('click', function () {
  $('#addVerifyDlg').modal('hide');
  startVersionVerify(util.genTaskId(),$('#deviceIp').val(),version_data);// 启动时针对每条设备生成新id
});

//查看版本校验状态&结果
$('#seeStateDlg').on('shown.bs.modal', function () {
  if($('#seeStateTitle').attr('wa-name')=='see_result'){
    getVersionVerifyResult($('#versionVerifyId').val(),$('#versionVerifyIp').val(),version_data);
  }else{
    // 考虑点击查看状态操作时.
    // getBaselineState($('#baselineId').val(),$('#baselineIp').val());
  }
})

// 确认取消&移除版本校验
$('#stop_btn').on('click', function () {
  if($('#stopTitle').attr('wa-name')=='_stop'){
    stopVersionVerify($('#versionVerifyId2').val(),$('#versionVerifyIp2').val(),version_data);
  }else{
    removeVersionVerifyGenerator($('#versionVerifyId2').val());
  }
});

$('#validateuser_btn').on('click',function (e) {
  if ($("#validateuser_btn").hasClass("disabled")) {
    return;
  }
  $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  let username=user_name
  let psd=$('#validateUserDlg #password').val()
  validateDlgId=$('#opraId').val() ,validateDlgIp=$('#opraIp').val()
  validateUserGenerator(username,psd);
})

/**
 * 初始化版本校验表格&设备(状态上线)表格
 */
let versionVerifyData = new Array();
let deviceData = new Array();

function loadVersionVerifyTb() {
  $("#version-verify-tb").jqGrid(
    {
      datatype: "local",
      height: "90%",
      autowidth: true,
      shrinkToFit: true,
      pager: '#version-verify-pager',
      viewrecords: true,
      rowNum: 10,
      rowList: [5, 10, 20, 50, 100],
      recordtext: "{0} - {1}\u3000共 {2} 条",
      emptyrecords: "当前没有版本校验记录",
      loadtext: "读取中...",
      pgtext: "{0} 共 {1} 页",
      colNames: ['编号','版本校验状态','监控对象Ip','启动时间','取消时间','完成时间','操作'],
      colModel: [
        {name: 'taskid', index: 'taskid', sortable: false},
        {name: 'versionVerifyState', index: 'baselineState', sortable: false,formatter:displayStates},
        {name: 'destIp', index: 'destIp', sortable: false},
        {name: 'startTime', index: 'startTime',},
        {name: 'stopTime', index: 'stopTime',},
        {name: 'completeTime', index: 'completeTime',},
        {name: 'operation', index: 'operation', sortable: false, formatter: displayButtons}
      ],
      sortable:true,
      sortname: 'startTime',
      sortorder:'desc',
    });

  // 清空和填充表格数据
  $("#version-verify-tb").jqGrid('clearGridData');
  for (let i = 0; i <= versionVerifyData.length; i++) {
    $("#version-verify-tb").jqGrid('addRowData', i + 1, versionVerifyData[i]);
  }
  // 数组数据分页.
  $('.ui-pg-table.ui-pager-table #first_version-verify-pager span')
    .addClass('ace-icon fa fa-angle-double-left bigger-100')
  $('.ui-pg-table.ui-pager-table #prev_version-verify-pager span')
    .addClass('ace-icon fa fa-angle-left bigger-100')
  $('.ui-pg-table.ui-pager-table #next_version-verify-pager span')
    .addClass('ace-icon fa fa-angle-right bigger-100')
  $('.ui-pg-table.ui-pager-table #last_version-verify-pager span')
    .addClass('ace-icon fa fa-angle-double-right bigger-100')
  const localData = {page: 1, total: 2, records: "2", rows: []};
  localData.rows = versionVerifyData;
  localData.records = versionVerifyData.length;
  localData.total = (versionVerifyData.length % 2 == 0) ? (versionVerifyData.length / 2) : (Math.floor(versionVerifyData.length / 2) + 1);
  const reader = {
    root: function (obj) {return localData.rows;},
    page: function (obj) {return localData.page;},
    total: function (obj) {return localData.total;},
    records: function (obj) {return localData.records;},
    repeatitems: false
  };
  $("#version-verify-tb").setGridParam({
    data: localData.rows,
    reader: reader
  }).trigger('reloadGrid');

  /**\
   * 创造行末尾按钮.
   */
  function displayButtons(cellvalue, options, rowObject) {
    let html="<div class='hidden-sm hidden-xs btn-group'>"
    let html2= "<button class='btn btn-minier btn-success wa-mlr5'" +
      "row-id='" + rowObject.taskid + "'" +
      "row-ip='" + rowObject.destIp + "'" +
      "onclick=\"$('#validateUserDlg').modal('show');\n" +
      "$('#validateUserForm').get(0).reset()\n" +
      "$('#opraType').val(2)\n" +
      "$('#opraId').val($(this).attr('row-id'))\n" +
      "$('#opraIp').val($(this).attr('row-ip'))\">" +
      "<i class='ace-icon fa fa-eye bigger-80'>&nbsp;查看结果</i>" +
      "</button>";
    let html3= "<button class='btn btn-minier btn-success disabled wa-mlr5'" +
      "row-id='" + rowObject.taskid + "'" +
      "row-ip='" + rowObject.destIp + "'>" +
      "<i class='ace-icon fa fa-eye bigger-80'>&nbsp;查看结果</i>" +
      "</button>";
    if(cellvalue == 0){
      html +=html2+
      "<button class='btn btn-minier btn-inverse'" +
        "row-id='" + rowObject.taskid + "'" +
        "row-ip='" + rowObject.destIp + "'" +
        "onclick=\"$('#validateUserDlg').modal('show');\n" +
        "$('#validateUserForm').get(0).reset()\n" +
        "$('#opraType').val(3)\n" +
        "$('#opraId').val($(this).attr('row-id'))\n" +
        "$('#opraIp').val($(this).attr('row-ip'))\">" +
        "<i class='ace-icon fa fa-ban bigger-80'>&nbsp;取消</i>" +
        "</button>" +
        "</div>"
    }else if(cellvalue==1){
      html += html3+
        "<button class='btn btn-minier btn-danger'" +
        "row-id='" + rowObject.taskid + "'" +
        "row-ip='" + rowObject.destIp + "'" +
        "onclick=\"$('#validateUserDlg').modal('show');\n" +
        "$('#validateUserForm').get(0).reset()\n" +
        "$('#opraType').val(4)\n" +
        "$('#opraId').val($(this).attr('row-id'))\n" +
        "$('#opraIp').val($(this).attr('row-ip'))\">" +
        "<i class='ace-icon fa fa-trash bigger-80'>&nbsp;删除</i>" +
        "</button>" +
        "</div>"
    }else{
      html +=html2+
      "<button class='btn btn-minier btn-danger'" +
        "row-id='" + rowObject.taskid + "'" +
        "row-ip='" + rowObject.destIp + "'" +
        "onclick=\"$('#validateUserDlg').modal('show');\n" +
        "$('#validateUserForm').get(0).reset()\n" +
        "$('#opraType').val(4)\n" +
        "$('#opraId').val($(this).attr('row-id'))\n" +
        "$('#opraIp').val($(this).attr('row-ip'))\">" +
        "<i class='ace-icon fa fa-trash bigger-80'>&nbsp;删除</i>" +
        "</button>" +
        "</div>"
    }
    return html;
  }
  /**
   * 设置核查状态字段
   */
  function displayStates(cellvalue, options, rowObject) {
    if(cellvalue==0) return '正在执行中'
    else if(cellvalue==1) return '已取消'
    else if(cellvalue==2) return '启动失败'
    else if(cellvalue==3) return '启动成功'
  }

  jqgridColResize()
  tableResize();
}

function loadDeviceTb() {
  $("#device-tb").jqGrid(
    {
      datatype: "local",
      height: "90%",
      autowidth: true,
      shrinkToFit: true,
      pager: '#device-pager',
      viewrecords: true,
      rowNum: 10,
      rowList: [5, 10, 20, 50, 100],
      recordtext: "{0} - {1}\u3000共 {2} 条",
      emptyrecords: "当前没有在线服务器或网安设备",
      loadtext: "读取中...",
      pgtext: "{0} 共 {1} 页",
      colNames: ['序号', '核查主机', 'Ip', 'MAC', '操作'],
      colModel: [
        {name: 'id', index: 'id'},
        {name: 'deviceName', index: 'deviceName'},
        {name: 'deviceIp', index: 'deviceIp'},
        {name: 'mac', index: 'mac',},
        {name: 'operation', index: 'operation', sortable: false, formatter: displayButtons}
      ]
    });

  // 清空和填充表格数据
  $("#device-tb").jqGrid('clearGridData');
  for (let i = 0; i <= deviceData.length; i++) {
    $("#device-tb").jqGrid('addRowData', i + 1, deviceData[i]);
  }
  // 表格自适应modal窗口
  $(window).on('resize.jqGrid', function () {
    $("#device-tb").jqGrid('setGridWidth', $("#addVerifyDlg .modal-content>.modal-body .col-xs-12").width());
  });
  const parent_column = $("#device-tb").closest('[class*="col-"]');
  $(document).on('settings.ace.jqGrid', function (ev, event_name, collapsed) {
    if (event_name == 'sidebar_collapsed' || event_name == 'main_container_fixed') {
      setTimeout(function () {
        $("#device-tb").jqGrid('setGridWidth', parent_column.width());
      }, 0);
    }
  })
  // 数组数据分页.
  $('.ui-pg-table.ui-pager-table #first_device-pager span')
    .addClass('ace-icon fa fa-angle-double-left bigger-100')
  $('.ui-pg-table.ui-pager-table #prev_device-pager span')
    .addClass('ace-icon fa fa-angle-left bigger-100')
  $('.ui-pg-table.ui-pager-table #next_device-pager span')
    .addClass('ace-icon fa fa-angle-right bigger-100')
  $('.ui-pg-table.ui-pager-table #last_device-pager span')
    .addClass('ace-icon fa fa-angle-double-right bigger-100')
  const localData = {page: 1, total: 2, records: "2", rows: []};
  localData.rows = deviceData;
  localData.records = deviceData.length;
  localData.total = (deviceData.length % 2 == 0) ? (deviceData.length / 2) : (Math.floor(deviceData.length / 2) + 1);
  const reader = {
    root: function (obj) {return localData.rows;},
    page: function (obj) {return localData.page;},
    total: function (obj) {return localData.total;},
    records: function (obj) {return localData.records;},
    repeatitems: false
  };
  $("#device-tb").setGridParam({
    data: localData.rows,
    reader: reader
  }).trigger('reloadGrid');

  /**\
   * 创造行末尾按钮操作方法.
   */
  function displayButtons(cellvalue, options, rowObject) {
    return "<div class='hidden-sm hidden-xs btn-group'>" +
      "<button class='btn btn-minier btn-success'" +
      "row-ip='" + rowObject.deviceIp + "'" +
      "onclick=\"$('#confirmStartDlg').modal('show')\n" +
      "$('#deviceIp').val($(this).attr('row-ip'))\">" +
      "<i class='ace-icon fa fa-play-circle bigger-80'>&nbsp;启动</i>" +
      "</button>" +
      "</div>";
  }
}

/**
 * 重新调整jqgrid每列的宽度
 */
function jqgridColResize(){
  let td=$('#tdCompute')//获取计算实际列长度的容器
    ,tds//临时保存列
    ,arr=[];//用于保存最大的列宽
  //遍历每行获得每行中的最大列宽
  $('.ui-jqgrid-htable tr,.ui-jqgrid-btable tr:gt(0)').each(function(){
    $(this).find('td,th').each(function(idx){
      arr[idx]=Math.max(arr[idx]?arr[idx]:0,td.html($(this).text())[0].offsetWidth);
    })
  });
  //设置页头单元格宽度
  $('.ui-jqgrid-labels th').each(function(idx){this.style.width=arr[idx]+'px'});
  //设置内容表格中控制单元格宽度的单元格，在第一行
  $('.ui-jqgrid-btable tr:eq(0) td').each(function(idx){this.style.width=arr[idx]+'px'});
  // 设置操作栏固定宽度
  $('.ui-jqgrid .ui-jqgrid-htable th:last-child').css("width", "89");
  $('.ui-jqgrid-btable>tbody>tr>td:last-child').css("width","89");
}
/**
 * 表格自适应窗口
 */
function tableResize(){
  $(window).on('resize.jqGrid', function () {
    $("#device-tb").jqGrid('setGridWidth', $(".page-content").width());
    jqgridColResize();
  });
  const parent_column = $("#device-tb").closest('[class*="col-"]');
  $(document).on('settings.ace.jqGrid', function (ev, event_name, collapsed) {
    if (event_name == 'sidebar_collapsed' || event_name == 'main_container_fixed') {
      setTimeout(function () {
        $("#device-tb").jqGrid('setGridWidth', parent_column.width());
        jqgridColResize();
      }, 0);
    }
  })
}

/**
 * 获取初始化版本校验表格数据
 */
function loadVersionVerifyGenerator() {
  // 连接服务器
  tcp_client.connect(loadVersionVerifyHandler, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPLoadVersionManages();
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADVERSIONMANAGES, sendMsg, user_sign);
}

function loadVersionVerifyHandler(msgId, pb) {
  if (msgId !== ProxyServer_pb.PSMessageId.PSMESSAGEID_PCLOADVERSIONMANAGES) {
    console.error('invalid msg', msgId);
    return;
  }
  const msg = ProxyServer_pb.MSG_PCLoadVersionManages.deserializeBinary(pb);
  const versionVerifys = msg.getVerifyListList();
  // console.log(versionVerifys)
  versionVerifyData = [];
  for (const versionVerify of versionVerifys) {
    let _state=0;
    if(versionVerify.getStopTime()==0&&versionVerify.getCompleteTime()==0) _state=0
    else if(versionVerify.getStopTime()>0) _state=1
    else if(versionVerify.getCompleteTime()>0){
      if(versionVerify.getError()>0) _state=2
      else _state=3
    }
    const versionVerifyObj = {
      taskid: $.trim(versionVerify.getTaskId()),
      versionVerifyState:_state,
      destIp: versionVerify.getDestIp(),
      startTime: versionVerify.getStartTime()==0?'-':util.getThisDateMs(versionVerify.getStartTime()),
      stopTime: versionVerify.getStopTime()==0?'-':util.getThisDateMs(versionVerify.getStopTime()),
      completeTime: versionVerify.getCompleteTime()==0?'-':util.getThisDateMs(versionVerify.getCompleteTime()),
      operation:_state
    }
    versionVerifyData.push(versionVerifyObj)
  }
  loadVersionVerifyTb();
}

/**
 * 发送验证密码参数
 * @param psd
 */
function validateUserGenerator(username,psd){
// 连接服务器
  tcp_client.connect(function(msgId, pb) {
    const msg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(pb);
    if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
      //ui_util.showFadeTip('验证用户通过!');
      if($('#opraType').val()==1){
        $('#addVerifyDlg').modal('show');
        loadDeviceidsGenerator();
      }else if($('#opraType').val()==2){
        $('#seeStateDlg').modal('show')
        $('#seeStateTitle').attr('wa-name','see_result')
        $('#seeStateTitle').text('查看该设备版本校验结果')
        $('#versionVerifyId').val(validateDlgId)
        $('#versionVerifyIp').val(validateDlgIp)
      }else if($('#opraType').val()==3){
        $('#confirmStopDlg').modal('show')
        $('#stopTitle').attr('wa-name','_stop')
        $('#stopTitle').text('确认取消')
        $('#confirmStopDlg .modal-body p').text('确定取消版本校验？')
        $('#versionVerifyId2').val(validateDlgId)
        $('#versionVerifyIp2').val(validateDlgIp)
      }else if($('#opraType').val()==4){
        $('#confirmStopDlg').modal('show')
        $('#stopTitle').attr('wa-name','_remove')
        $('#stopTitle').text('确认删除')
        $('#confirmStopDlg .modal-body p').text('确定删除版本校验？')
        $('#versionVerifyId2').val(validateDlgId)
      }

    } else {
      ui_util.showFadeTip('密码输入不正确!');
    }
  }, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
  sendMsg.setUserName(username)
  sendMsg.setPassword(psd)
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg, user_sign);
}

/**
 * 获取初始化设备表格数据
 */
function loadDeviceidsGenerator() {
  // 连接服务器
  tcp_client.connect(loadDeviceidsHandler, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPQueryOnlineDeviceIds();
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPQUERYONLINEDEVICEIDS, sendMsg, user_sign);
}
function loadDeviceidsHandler(msgId, pb) {
  if (msgId !== ProxyServer_pb.PSMessageId.PSMESSAGEID_PCQUERYONLINEDEVICEIDS) {
    console.error('invalid msg', msgId);
    return;
  }
  const msg = ProxyServer_pb.MSG_PCQueryOnlineDeviceIds.deserializeBinary(pb);
  const deviceids = msg.getDeviceIdsList();
  // console.log("deviceids",deviceids)

  loadDevicesGenerator(deviceids);
}

function loadDevicesGenerator(deviceids) {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      const devices = device_codec.unpackLoadDevice(dp_packet.content);
      deviceData = [];
      for (let i = 0; i < devices.length; i++) {
        if($.inArray(devices[i].getId(),deviceids)!==-1){// 仅在线服务器或网安设备
          if(devices[i].getDeviceType()=="DCD"||devices[i].getDeviceType()=="SVR"){
            const deviceObj = {
              id: devices[i].getId(),
              deviceName: devices[i].getDeviceName(),
              deviceIp: util.int2ip(devices[i].getIp1()),
              mac: util.str2mac(devices[i].getMac1())
            }
            deviceData.push(deviceObj)
          }
        }
      }
      loadDeviceTb();
    }
    // 消息处理完成关闭连接
    tcp_client.close();
  }, config.host, config.port);

  // 发送消息
  tcp_client.send(device_codec.packLoadDevice());
}

/**
 * 启动版本校验
 * @param task_id
 * @param dest_ip
 * @param version_data
 */
function startVersionVerify(task_id, dest_ip,version_data) {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      ui_util.showFadeTip('启动该设备版本校验成功!');
    } else {
      ui_util.getErrorTips(dp_packet.return_value,'启动版本校验')
    }
    // 消息处理完成关闭连接
    tcp_client.close();
    loadVersionVerifyGenerator();
  }, config.host, config.port);

  // 发送消息
  tcp_client.send(version_codec.packStartVersionVerify(task_id, util.ip2int(dest_ip),version_data));
}

/**
 * 查看版本校验结果
 */
function getVersionVerifyResult(task_id,dest_ip,version_data) {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      const versionVerify = version_codec.unpackVersionVerifyGetResult(dp_packet.content);
      const _result=versionVerify.getResult()===""?"暂无结果":versionVerify.getResult();
      $('#seeStateDlg .modal-body>#versionStateCont').text(_result)
    }
    // 消息处理完成关闭连接
    tcp_client.close();
  }, config.host, config.port);

  // 发送消息
  tcp_client.send(version_codec.packVersionVerifyGetResult(task_id, util.ip2int(dest_ip),version_data));
}

/**
 * 取消版本校验
 * @param task_id
 * @param dest_ip
 */
function stopVersionVerify(task_id,dest_ip,version_data) {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      ui_util.showFadeTip('取消版本校验成功!');
    } else {
      ui_util.getErrorTips(dp_packet.return_value,'取消版本校验')
    }
    // 消息处理完成关闭连接
    tcp_client.close();
    loadVersionVerifyGenerator();
  }, config.host, config.port);

  // 发送消息
  tcp_client.send(version_codec.packStopVersionVerify(task_id, util.ip2int(dest_ip),version_data));
}

/**
 * 移除已完成或者已取消的版本校验
 * @param taskId
 */
function removeVersionVerifyGenerator(taskId) {
  // 连接服务器
  tcp_client.connect(removeVersionVerifyHandler, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPRemoveVersionManage();
  sendMsg.setTaskId(taskId);
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPREMOVEVERSIONMANAGE, sendMsg, user_sign);
}

function removeVersionVerifyHandler(msgId, pb) {
  if (msgId !== ProxyServer_pb.PSMessageId.PSMESSAGEID_PCREMOVEVERSIONMANAGE) {
    console.error('invalid msg', msgId);
    return;
  }
  ui_util.showFadeTip('删除版本校验成功!');
  loadVersionVerifyGenerator();
}
