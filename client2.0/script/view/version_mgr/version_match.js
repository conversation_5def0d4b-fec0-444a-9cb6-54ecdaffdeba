'use strict';

const ProxyServer_pb = require('../../pb/ProxyServer_pb');
const PublicDefine_pb = require('../../pb/PublicDefine_pb');
const util = require('../../../lib/util');
const ui_util = require('../ui_util');
const tcp_client = require('../../../lib/tcp_client');
const config = require("../../../config.json");
const device_codec = require('../../platform_codec/device_codec');
const version_codec = require('../../platform_codec/version_codec');
// 相关变量定义
const user_sign = $("body").attr("user_sign_info");
const user_name = $("body").attr("user_name_info");
console.log("user_sign:", user_sign + user_name);

let validateDlgId,validateDlgIp,validateDlgName,validateDlgType;
$('#validateuser_btn').on('click',function (e) {
  if ($("#validateuser_btn").hasClass("disabled")) {
    return;
  }
  $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
  e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
  let username=user_name
  let psd=$('#validateUserDlg #password').val()
  validateDlgIp=$('#opraIp').val()
  validateUserGenerator(username,psd);
})

const version_data=Buffer.from('eb04000000000012444344303030303032374b45444f4e47ddaa', 'hex');
// 单行设备版本匹配
$('#match_device_btn').on('click', function () {
  $('#confirmMatchDlg').modal('hide');
  matchVersionVerify(util.genTaskId(),$('#deviceIp').val(),version_data);// 启动时针对每条设备生成新id
});

/**
 * 初始化设备表格
 */
let deviceData = new Array();

function loadDeviceTb() {
  $("#device-tb").jqGrid(
    {
      datatype: "local",
      height: "90%",
      autowidth: true,
      shrinkToFit: true,
      pager: '#device-pager',
      viewrecords: true,
      rowNum: 10,
      rowList: [5, 10, 20, 50, 100],
      recordtext: "{0} - {1}\u3000共 {2} 条",
      emptyrecords: "当前没有在线服务器或网安设备",
      loadtext: "读取中...",
      pgtext: "{0} 共 {1} 页",
      colNames: ['序号', '设备名称', '设备Ip', '设备Ip2', 'MAC地址', 'MAC地址2', '设备类型', '厂商', '序列号', '版本', '操作'],
      colModel: [
        {name: 'id', index: 'id'},
        {name: 'deviceName', index: 'deviceName'},
        {name: 'deviceIp', index: 'deviceIp'},
        {name: 'deviceIp2', index: 'deviceIp2', hidden: true},
        {name: 'mac', index: 'mac',},
        {name: 'mac2', index: 'mac2', hidden: true},
        {name: 'deviceType', index: 'deviceType', formatter: transformType},
        {name: 'manufacturer', index: 'manufacturer', hidden: true},
        {name: 'sequence', index: 'sequence', hidden: true},
        {name: 'sysVersion', index: 'sysVersion', hidden: true},
        {name: 'operation', index: 'operation', sortable: false, formatter: displayButtons}
      ]
    });

  // 清空和填充表格数据
  $("#device-tb").jqGrid('clearGridData');
  for (let i = 0; i <= deviceData.length; i++) {
    $("#device-tb").jqGrid('addRowData', i + 1, deviceData[i]);
  }
  // 数组数据分页.本地数据分页.
  $('.ui-pg-table.ui-pager-table #first_device-pager span')
    .addClass('ace-icon fa fa-angle-double-left bigger-100')
  $('.ui-pg-table.ui-pager-table #prev_device-pager span')
    .addClass('ace-icon fa fa-angle-left bigger-100')
  $('.ui-pg-table.ui-pager-table #next_device-pager span')
    .addClass('ace-icon fa fa-angle-right bigger-100')
  $('.ui-pg-table.ui-pager-table #last_device-pager span')
    .addClass('ace-icon fa fa-angle-double-right bigger-100')
  const localData = {page: 1, total: 2, records: "2", rows: []};
  localData.rows = deviceData;
  localData.records = deviceData.length;
  localData.total = (deviceData.length % 2 == 0) ? (deviceData.length / 2) : (Math.floor(deviceData.length / 2) + 1);
  const reader = {
    root: function (obj) {return localData.rows;},
    page: function (obj) {return localData.page;},
    total: function (obj) {return localData.total;},
    records: function (obj) {return localData.records;},
    repeatitems: false
  };
  $("#device-tb").setGridParam({
    data: localData.rows,
    reader: reader
  }).trigger('reloadGrid');
  /**\
   * 创造行末尾按钮并修改删除点击方法.
   */
  function displayButtons(cellvalue, options, rowObject) {
    return "<div class='hidden-sm hidden-xs btn-group'>" +
      "<button class='btn btn-minier btn-inverse wa-mr5'" +
      "row-id='" + rowObject.id + "'" +
      "row-ip='" + rowObject.deviceIp + "'" +
      "onclick=\"$('#validateUserDlg').modal('show');\n" +
      "$('#validateUserForm').get(0).reset()\n" +
      "$('#opraIp').val($(this).attr('row-ip'))\">" +
      "<i class='ace-icon fa fa-exchange bigger-80'>&nbsp;匹配</i>" +
      "</button>"+
      "</div>";
  }
  /**
   * 设备类型value转换文本
   */
  function transformType(cellvalue, options, rowObject) {
    if (cellvalue == "FW") return "防火墙";
    else if (cellvalue == "FID") return "横向正向隔离装置";
    else if (cellvalue == "BID") return "横向反向隔离装置";
    else if (cellvalue == "SVR") return "服务器";
    else if (cellvalue == "SW") return "交换机";
    else if (cellvalue == "DCD") return "网络安全监测装置";
    else return "";
  }
  jqgridColResize();
  tableResize();
}

/**
 * 重新调整jqgrid每列的宽度
 */
function jqgridColResize(){
  let td=$('#tdCompute')//获取计算实际列长度的容器
    ,tds//临时保存列
    ,arr=[];//用于保存最大的列宽
  //遍历每行获得每行中的最大列宽
  $('.ui-jqgrid-htable tr,.ui-jqgrid-btable tr:gt(0)').each(function(){
    $(this).find('td,th').each(function(idx){
      arr[idx]=Math.max(arr[idx]?arr[idx]:0,td.html($(this).text())[0].offsetWidth);
    })
  });
  //设置页头单元格宽度
  $('.ui-jqgrid-labels th').each(function(idx){this.style.width=arr[idx]+'px'});
  //设置内容表格中控制单元格宽度的单元格，在第一行
  $('.ui-jqgrid-btable tr:eq(0) td').each(function(idx){this.style.width=arr[idx]+'px'});
}
/**
 * 表格自适应窗口
 */
function tableResize(){
  $(window).on('resize.jqGrid', function () {
    $("#device-tb").jqGrid('setGridWidth', $(".page-content").width());
    jqgridColResize();
  });
  const parent_column = $("#device-tb").closest('[class*="col-"]');
  $(document).on('settings.ace.jqGrid', function (ev, event_name, collapsed) {
    if (event_name == 'sidebar_collapsed' || event_name == 'main_container_fixed') {
      setTimeout(function () {
        $("#device-tb").jqGrid('setGridWidth', parent_column.width());
        jqgridColResize();
      }, 0);
    }
  })
}

/**
 * 获取初始化设备表格数据
 */
function loadDeviceidsGenerator() {
  // 连接服务器
  tcp_client.connect(loadDeviceidsHandler, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPQueryOnlineDeviceIds();
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPQUERYONLINEDEVICEIDS, sendMsg, user_sign);
}
function loadDeviceidsHandler(msgId, pb) {
  if (msgId !== ProxyServer_pb.PSMessageId.PSMESSAGEID_PCQUERYONLINEDEVICEIDS) {
    console.error('invalid msg', msgId);
    return;
  }
  const msg = ProxyServer_pb.MSG_PCQueryOnlineDeviceIds.deserializeBinary(pb);
  const deviceids = msg.getDeviceIdsList();
  // console.log("deviceids",deviceids)

  loadDevicesGenerator(deviceids);
}

function loadDevicesGenerator(deviceids) {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      const devices = device_codec.unpackLoadDevice(dp_packet.content);
      deviceData = [];
      for (let i = 0; i < devices.length; i++) {
        if($.inArray(devices[i].getId(),deviceids)!==-1){// 仅在线服务器或网安设备
          if(devices[i].getDeviceType()=="DCD"||devices[i].getDeviceType()=="SVR"){
          const deviceObj = {
            id: devices[i].getId(),
            deviceName: devices[i].getDeviceName(),
            deviceIp: util.int2ip(devices[i].getIp1()),
            deviceIp2: util.int2ip(devices[i].getIp2()),
            mac: util.str2mac(devices[i].getMac1()),
            mac2: util.str2mac(devices[i].getMac2()),
            deviceType: devices[i].getDeviceType(),
            manufacturer: devices[i].getManufacturer(),
            sequence: devices[i].getSerialNo(),
            sysVersion: devices[i].getSysVersion(),
            snmpVersion: devices[i].getSnmpVersion(),
            snmpUsername: devices[i].getSnmpUsername(),
            snmpAuth: devices[i].getSnmpAuth(),
            snmpEncryp: devices[i].getSnmpEncrypt(),
            snmpReadCommunity: devices[i].getSnmpReadCommunity(),
            snmpWriteCommunity: devices[i].getSnmpWriteCommunity(),
          }
          deviceData.push(deviceObj)
        }
        }
      }
      loadDeviceTb();
    }
    // 消息处理完成关闭连接
    tcp_client.close();
  }, config.host, config.port);

  // 发送消息
  tcp_client.send(device_codec.packLoadDevice());
}
loadDeviceidsGenerator()

/**
 * 发送验证密码参数
 * @param psd
 */
function validateUserGenerator(username,psd){
// 连接服务器
  tcp_client.connect(function(msgId, pb) {
    const msg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(pb);
    if (msg.getErrorCode() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
      //ui_util.showFadeTip('验证用户通过!');
      $('#confirmMatchDlg').modal('show')
      $('#deviceIp').val(validateDlgIp)
    } else {
      ui_util.showFadeTip('密码输入不正确!');
    }
  }, config.host, config.port);

  // 发送消息
  let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
  sendMsg.setUserName(username)
  sendMsg.setPassword(psd)
  tcp_client.sendGuiMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg, user_sign);
}

/**
 * 发送版本匹配参数
 * @param task_id
 * @param dest_ip
 * @param version_data
 */
function matchVersionVerify(task_id, dest_ip,version_data) {
  // 连接服务器
  tcp_client.connect(function (dp_packet) {
    if (dp_packet.return_value === 0) {
      ui_util.showFadeTip('版本匹配成功!');
    }else {
      ui_util.getErrorTips(dp_packet.return_value,'版本匹配')
    }
    // 消息处理完成关闭连接
    tcp_client.close();
    // loadBaselinesGenerator();
  }, config.host, config.port);

  // 发送消息
  tcp_client.send(version_codec.packMatchVersionVerify(task_id, util.ip2int(dest_ip),version_data));
}

