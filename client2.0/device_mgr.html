<div class="main-content">
  <div class="main-content-inner">
    <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs">
      <div class="page-content">
        <div class="page-header">
          <h1>设备管理</h1>
        </div><!-- /.page-header -->
        <div class="row wa-mb10">
          <div class="col-xs-12">
            <div id="asset-status-statics" style="height: 239px"></div>
          </div>
        </div>
        <div class="row wa-mb10">
          <div class="col-xs-12 text-right wa-mb10">
            <a href="javascript:;" type="button" class="btn btn-sm btn-primary" id="add_device">
              <i class="ace-icon fa fa-plus"></i>新增设备</a>
            <a href="javascript:;" type="button" class="btn btn-sm btn-primary" id="import">导入</a>
            <a href="javascript:;" type="button" class="btn btn-sm btn-primary" id="export">导出</a>
          </div>
          <div class="col-xs-12">
            <form class="form-horizontal" role="form" id="deviceForm">
              <div class="form-group wa-mb0">
                <label class="col-sm-1 control-label no-padding-right">
                  <b>设备类型:</b>
                </label>
                <div class="col-sm-11" id="deviceTypeSelect">
                  <label class="radio-inline">
                    <input type="radio" name="device-type" value="" checked>不限
                  </label>
                  <label class="radio-inline">
                    <input type="radio" name="device-type" value="FW">防火墙
                  </label>
                  <label class="radio-inline">
                    <input type="radio" name="device-type" value="FID">横向正向隔离装置
                  </label>
                  <label class="radio-inline">
                    <input type="radio" name="device-type" value="BID">横向反向隔离装置
                  </label>
                  <label class="radio-inline">
                    <input type="radio" name="device-type" value="SVR">服务器
                  </label>
                  <label class="radio-inline">
                    <input type="radio" name="device-type" value="SW">交换机
                  </label>
                  <label class="radio-inline">
                    <input type="radio" name="device-type" value="VEAD">纵向加密装置
                  </label>
                  <label class="radio-inline">
                    <input type="radio" name="device-type" value="AV">防病毒系统
                  </label>
                  <label class="radio-inline">
                    <input type="radio" name="device-type" value="IDS">入侵检测系统
                  </label>
                  <label class="radio-inline">
                    <input type="radio" name="device-type" value="DB">数据库
                  </label>
                  <label class="radio-inline">
                    <input type="radio" name="device-type" value="DCD">网络安全监测装置
                  </label>
                </div>
              </div>
              <div class="form-group wa-mb0">
                <label class="col-sm-1 control-label no-padding-right">
                  <b>设备状态:</b>
                </label>
                <div class="col-sm-11" id="deviceStatisSelect">
                  <label class="radio-inline">
                    <input type="radio" name="device-statis" value="" checked>不限
                  </label>
                  <label class="radio-inline">
                    <input type="radio" name="device-statis" value="1">在线
                  </label>
                  <label class="radio-inline">
                    <input type="radio" name="device-statis" value="2">离线
                  </label></div>
              </div>
              <div class="form-group">
                <label class="col-sm-1 control-label no-padding-right">
                  <b>过滤条件:</b>
                </label>
                <div class="col-sm-11">
                  <input type="text" id="search" name="search"
                         placeholder="设备名称不超过64个字" class="col-sm-5" maxlength="64">
                </div>
              </div>
            </form>
          </div>
          <div class="col-sm-12 text-right">
            <a href="javascript:;" type="button" class="btn btn-sm btn-success" id="query"><i
                class="ace-icon fa fa-refresh"></i> 查询</a>
          </div>
        </div>
        <div class="row">
          <div class="col-xs-12">
            <table id="device-tb" class="table table-bordered table-hover"></table>
            <div id="device-pager"></div>
            <!-----用来计算单元格内容实际长度的--------->
            <div class="ui-jqgrid ui-widget ui-widget-content ui-corner-all" style="position:absolute;top:-9999px">
              <div class="ui-jqgrid-view">
                <div class="ui-jqgrid-bdiv">
                  <div style="position: relative;">
                    <table cellspacing="0" cellpadding="0" border="0">
                      <tr class="ui-widget-content jqgrow ui-row-ltr" style="table-layout:table">
                        <td id="tdCompute" style="background:#eee;width:auto"></td>
                      </tr>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div><!-- /.page-content -->
    </div>
  </div><!-- /.main-content -->
  <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
    <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
  </a>
  <!-- 验证用户身份-->
  <div class="modal" id="validateUserDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <form class="form-horizontal" id="validateUserForm" method="post" action="" data-toggle="validator" role="form">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header" style="color: white;background: deepskyblue">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            <h4 class="modal-title">验证用户身份</h4>
          </div>
          <div class="modal-body" style="padding: 10px 50px;">
            <p>请输入登录密码</p>
            <input type="hidden" id="opraType"/>
            <input type="password" id="password" class="form-control" placeholder="密码" required/>
            <div class="help-block with-errors"></div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
            <button type="submit" class="btn btn-primary" id="validateuser_btn">确定
            </button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </form>
  </div><!-- /.modal -->
  <!-- 新增&修改设备信息-->
  <div class="modal" id="addDeviceDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <form class="form-horizontal" id="addDeviceForm" method="post" action="" data-toggle="validator" role="form">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header" style="color: white;background: deepskyblue">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
            <h4 class="modal-title" id="addDeviceTitle" wa-data="add">基本属性</h4>
          </div>
          <div class="modal-body">
            <input type="hidden" id="app_device_id"/>
            <div class="form-group">
              <label class="col-sm-2 control-label text-right">设备名称:</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" id="device_name" name="device_name"
                       placeholder="设备名称不超过64个字" maxlength="64"
                       pattern="^[\u4e00-\u9fa5_a-zA-Z0-9
                       \u3002|\uff1f|\uff01|\uff0c|\u3001|\uff1b|\uff1a|\u201c|\u201d|\u2018|\u2019|\uff08|\uff09|\u300a|\u300b|\u3008|\u3009|\u3010|\u3011|\u300e|\u300f|\u300c|\u300d|\ufe43|\ufe44|\u3014|\u3015|\u2026|\u2014|\uff5e|\ufe4f|\uffe5
                       ,.,@-]+$"
                       required/>
                <div class="help-block with-errors"></div>
              </div>
              <label class="col-sm-2 control-label text-right">设备类型:</label>
              <div class="col-sm-4">
                <select class="form-control" id="device_type" name="device_type">
                  <option value="FW">防火墙</option>
                  <option value="FID">横向正向隔离装置</option>
                  <option value="BID">横向反向隔离装置</option>
                  <option value="SVR">服务器</option>
                  <option value="SW">交换机</option>
                  <option value="VEAD">纵向加密装置</option>
                  <option value="AV">防病毒系统</option>
                  <option value="IDS">入侵检测系统</option>
                  <option value="DB">数据库</option>
                  <option value="DCD">网络安全监测装置</option>
                </select>
              </div>
            </div>
            <input type="hidden" class="form-control" id="old_ip1" />
            <input type="hidden" class="form-control" id="old_ip2" />
            <div class="form-group">
              <label class="col-sm-2 control-label text-right">设备地址:</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" id="device_ip" name="device_ip"
                       placeholder="请输入设备ip,格式为0.0.0.0"
                       title="请输入设备ip,格式为0.0.0.0"
                       onmouseover="this.title"
                       maxlength="15"
                       pattern="(?=(\b|\D))(((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))\.){3}((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))(?=(\b|\D))"
                       required/>
                <div class="help-block with-errors"></div>
              </div>
              <label class="col-sm-2 control-label text-right">设备地址2:</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" id="device_ip2" name="device_ip2"
                       placeholder="请输入设备ip2,格式为0.0.0.0"
                       title="请输入设备ip,格式为0.0.0.0"
                       onmouseover="this.title"
                       maxlength="15"
                       pattern="(?=(\b|\D))(((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))\.){3}((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))(?=(\b|\D))"/>
                <div class="help-block with-errors"></div>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label text-right">MAC地址:</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" id="device_mac" name="device_mac"
                       placeholder="请输入MAC,格式为00:00:00:00:00:00"
                       title="请输入MAC2,格式为00:00:00:00:00:00"
                       onmouseover="this.title"
                       maxlength="100"
                       pattern="[A-F\d]{2}:[A-F\d]{2}:[A-F\d]{2}:[A-F\d]{2}:[A-F\d]{2}:[A-F\d]{2}"
                       required/>
                <div class="help-block with-errors"></div>
              </div>
              <label class="col-sm-2 control-label text-right">MAC地址2:</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" id="device_mac2" name="device_mac2"
                       placeholder="请输入MAC2,格式为00:00:00:00:00:00"
                       title="请输入MAC2,格式为00:00:00:00:00:00"
                       onmouseover="this.title"
                       maxlength="100"
                       pattern="[A-F\d]{2}:[A-F\d]{2}:[A-F\d]{2}:[A-F\d]{2}:[A-F\d]{2}:[A-F\d]{2}"/>
                <div class="help-block with-errors"></div>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label text-right">设备厂家:</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" id="manufacturer" name="manufacturer"
                       placeholder="请输入设备厂家" maxlength="64"
                       pattern="^[\u4e00-\u9fa5_a-zA-Z0-9
                       \u3002|\uff1f|\uff01|\uff0c|\u3001|\uff1b|\uff1a|\u201c|\u201d|\u2018|\u2019|\uff08|\uff09|\u300a|\u300b|\u3008|\u3009|\u3010|\u3011|\u300e|\u300f|\u300c|\u300d|\ufe43|\ufe44|\u3014|\u3015|\u2026|\u2014|\uff5e|\ufe4f|\uffe5
                       ,.,@-]+$"
                       required/>
                <div class="help-block with-errors"></div>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label text-right">序列号:</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" id="sequence" name="sequence"
                       placeholder="请输入序列号" maxlength="64"
                       pattern="^[\u4e00-\u9fa5_a-zA-Z0-9
                       \u3002|\uff1f|\uff01|\uff0c|\u3001|\uff1b|\uff1a|\u201c|\u201d|\u2018|\u2019|\uff08|\uff09|\u300a|\u300b|\u3008|\u3009|\u3010|\u3011|\u300e|\u300f|\u300c|\u300d|\ufe43|\ufe44|\u3014|\u3015|\u2026|\u2014|\uff5e|\ufe4f|\uffe5
                       ,.,@-]+$"/>
                <div class="help-block with-errors"></div>
              </div>
              <label class="col-sm-2 control-label text-right">系统版本:</label>
              <div class="col-sm-4">
                <input type="text" class="form-control" id="serverVersion" name="serverVersion"
                       placeholder="请输入系统版本" maxlength="64"
                       pattern="^[\u4e00-\u9fa5_a-zA-Z0-9
                       \u3002|\uff1f|\uff01|\uff0c|\u3001|\uff1b|\uff1a|\u201c|\u201d|\u2018|\u2019|\uff08|\uff09|\u300a|\u300b|\u3008|\u3009|\u3010|\u3011|\u300e|\u300f|\u300c|\u300d|\ufe43|\ufe44|\u3014|\u3015|\u2026|\u2014|\uff5e|\ufe4f|\uffe5
                       ,.,@-]+$"/>
                <div class="help-block with-errors"></div>
              </div>
            </div>
            <div class="row hidden" id="snmp_msg">
              <div class="col-xs-12">
                <h3>Snmp配置</h3>
                <div class="form-group"
                     style="border-top: 1px dotted #E2E2E2;">
                  <label class="col-sm-2 control-label text-right">snmp版本:</label>
                  <div class="col-sm-4">
                    <select class="form-control" id="snmp_version" name="snmp_version">
                      <!--<option value="0">1</option>-->
                      <option value="1">2c</option>
                      <option value="2">3</option>
                    </select>
                  </div>
                  <label class="col-sm-2 control-label text-right">snmp用户名:</label>
                  <div class="col-sm-4">
                    <input type="text" class="form-control" id="snmp_username" name="snmp_username"
                           placeholder="请输入snmp用户名" maxlength="20" disabled="disabled"
                           pattern="^[\u4e00-\u9fa5_a-zA-Z0-9
                       \u3002|\uff1f|\uff01|\uff0c|\u3001|\uff1b|\uff1a|\u201c|\u201d|\u2018|\u2019|\uff08|\uff09|\u300a|\u300b|\u3008|\u3009|\u3010|\u3011|\u300e|\u300f|\u300c|\u300d|\ufe43|\ufe44|\u3014|\u3015|\u2026|\u2014|\uff5e|\ufe4f|\uffe5
                       ,.,@-]+$"/>
                    <div class="help-block with-errors"></div>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label text-right">snmp认证算法:</label>
                  <div class="col-sm-4">
                    <select class="form-control" id="snmp_auth" name="snmp_auth" disabled="disabled">
                      <option value="0">不启用</option>
                      <option value="1">MD5</option>
                      <option value="2">SHA</option>
                    </select>
                  </div>
                  <label class="col-sm-2 control-label text-right">snmp加密算法:</label>
                  <div class="col-sm-4">
                    <select class="form-control" id="snmp_encrypt" name="snmp_encrypt" disabled="disabled">
                      <option value="0">不启用</option>
                      <option value="1">DES</option>
                      <option value="2">AES</option>
                    </select>
                  </div>
                </div>
                <div class="form-group" id="_groupname">
                  <label class="col-sm-2 control-label text-right">读团体名(必填):</label>
                  <div class="col-sm-4">
                    <input type="text" class="form-control" id="snmp_read_community" name="snmp_read_community"
                           placeholder="请输入团体名"
                           minlength="2"
                           maxlength="20"
                           pattern="^[\u4e00-\u9fa5_a-zA-Z0-9
                       \u3002|\uff1f|\uff01|\uff0c|\u3001|\uff1b|\uff1a|\u201c|\u201d|\u2018|\u2019|\uff08|\uff09|\u300a|\u300b|\u3008|\u3009|\u3010|\u3011|\u300e|\u300f|\u300c|\u300d|\ufe43|\ufe44|\u3014|\u3015|\u2026|\u2014|\uff5e|\ufe4f|\uffe5
                       ,.,@-]+$"
                    />
                    <div class="help-block with-errors"></div>
                  </div>
                  <label class="col-sm-2 control-label text-right">写团体名(必填):</label>
                  <div class="col-sm-4">
                    <input type="text" class="form-control" id="snmp_write_community" name="snmp_write_community"
                           placeholder="请输入团体名"
                           minlength="2"
                           maxlength="20"
                           pattern="^[\u4e00-\u9fa5_a-zA-Z0-9
                       \u3002|\uff1f|\uff01|\uff0c|\u3001|\uff1b|\uff1a|\u201c|\u201d|\u2018|\u2019|\uff08|\uff09|\u300a|\u300b|\u3008|\u3009|\u3010|\u3011|\u300e|\u300f|\u300c|\u300d|\ufe43|\ufe44|\u3014|\u3015|\u2026|\u2014|\uff5e|\ufe4f|\uffe5
                       ,.,@-]+$"
                    />
                    <div class="help-block with-errors"></div>
                  </div>
                </div>
                <div class="form-group hidden" id="_psd">
                  <label class="col-sm-2 control-label text-right">认证密码(必填):</label>
                  <div class="col-sm-4">
                    <input type="text" class="form-control" id="author_psd" name="author_psd"
                           placeholder="请输入认证密码,最大20位,不允许输入,/;\和中文"
                           title="请输入认证密码,最大20位,不允许输入,/;\和中文"
                           onmouseover="this.title"
                           minlength="6"
                           maxlength="20"
                           pattern="^[\u4e00-\u9fa5_a-zA-Z0-9~`!@#$%^&*(){}|_:'<>.?]+$"/>
                    <div class="help-block with-errors"></div>
                  </div>
                  <label class="col-sm-2 control-label text-right">加密密码(必填):</label>
                  <div class="col-sm-4">
                    <input type="text" class="form-control" id="encrypt_psd" name="encrypt_psd"
                           placeholder="请输入加密密码,最大20位,不允许输入,/;\和中文"
                           title="请输入加密密码,最大20位,不允许输入,/;\和中文"
                           onmouseover="this.title"
                           minlength="6"
                           maxlength="20"
                           pattern="^[\u4e00-\u9fa5_a-zA-Z0-9~`!@#$%^&*(){}|_:'<>.?]+$"/>
                    <div class="help-block with-errors"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="submit" id="save_device_btn" class="btn btn-primary">保存</button>
            <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </form>
  </div><!-- /.modal -->
  <!-- 确认删除对话框 -->
  <div class="modal fade" id="confirmDlg">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
            <span class="sr-only">Close</span>
          </button>
          <h4 class="modal-title">删除</h4>
        </div>
        <div class="modal-body">
          <input type="hidden" id="remove_device_id"/>
          确定删除此设备信息？
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
          <button type="button" class="btn btn-primary" data-dismiss="modal" id="remove_device_btn">确定</button>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
  </div><!-- /.modal -->
  <!-- 导入导出选择对话框 -->
  <div class="modal fade" id="confirmPortDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <form class="form-horizontal" id="confirmPortForm" method="post" action="" data-toggle="validator" role="form">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
            <span class="sr-only">Close</span>
          </button>
          <h4 class="modal-title" wa-name="import_tt">请选择</h4>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label class="col-sm-2 control-label text-right" for="select_file">请选择:</label>
            <div class="col-sm-9">
              <label class="ace-file-input">
                <input type="file" id="select_file" required>
              </label>
              <!--<div class="help-block with-errors"></div>-->
            </div>
          </div>
          <div class="form-group hidden">
            <label class="col-sm-2 control-label text-right" for="select_folder">请选择:</label>
            <div class="col-sm-9">
              <label class="ace-file-input">
                <input type="file" id="select_folder" webkitdirectory directory  required>
              </label>
              <!--<div class="help-block with-errors"></div>-->
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
          <button type="button" class="btn btn-primary" id="port_btn">确定</button>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.form-horizontal-->
    </form><!-- /.modal-dialog -->
  </div><!-- /.modal -->
  <script>
    delete require.cache[require.resolve('./script/view/device_mgr.js')];
    require('./script/view/device_mgr.js');
    $('#deviceForm').validator();
    $('#addDeviceForm').validator();
    $('#confirmPortForm').validator();
  </script>
</div>
