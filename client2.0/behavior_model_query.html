<!--&lt;!&ndash; element-plus &ndash;&gt;-->

<!--<script src="vue/admin/controller/port_param.js"></script>-->

<div class="main-content">
    <div class="main-content-inner">
        <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs" style="height: calc(100% - 100px);">
            <div class="page-content">
                <div class="page-header">
                    <h1>数据调阅
                        <small>
                            <i class="ace-icon fa fa-angle-double-right"></i>
                            行为调阅
                        </small>
                    </h1>
                </div>
                <!-- /.page-header -->
                <div class="row">
                    <div id="app">
                        <div ref="box" style="height: 100%">
                            <el-row style="padding: 10px 0 10px 10px">
                                <el-col :span="4" style="margin-right: 10px">
                                    <el-input v-model="searchForm.GID" placeholder="输入GID" clearable class="search-input" />
                                </el-col>
                                <el-col :span="4">
                                    <el-input v-model="searchForm.DNAME" placeholder="输入设备名称" clearable class="search-input"/>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-table stripe :data="filteredAssetList" :height="queryModelHeight" style="width: 100%">
                                    <el-table-column type="index" label="序号" width="80"></el-table-column>
                                    <el-table-column label="GID" prop="GID"></el-table-column>
                                    <el-table-column label="设备名称" prop="DNAME"></el-table-column>
                                    <el-table-column label="操作" width="200">
                                        <template #default="scope">
                                            <el-button type="primary" size="small" @click="() => { getQueryBehaviorByAssetId(scope.row) }">调阅</el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </el-row>
                            <!-- 调阅弹窗 -->
                            <el-dialog v-if="queryModelVisible" v-model="queryModelVisible" title="调阅"  width="80%" @close="closeQueryModel">
                                <el-row :gutter="20" class="select-row">
                                    <!-- 行为模型 -->
                                    <el-col :span="5">
                                        <div class="select-block">
                                            <div class="select-title">行为模型</div>
                                            <div class="custom-select-wrapper">
                                                <el-select
                                                        v-model="type"
                                                        placeholder="请选择"
                                                        class="custom-select"
                                                        :suffix-icon="null"
                                                        @change="typeChangeFn"
                                                >
                                                    <el-option label="全部" :value="0"></el-option>
                                                    <el-option label="硬件" :value="1"></el-option>
                                                    <el-option label="软件" :value="2"></el-option>
                                                    <el-option label="网络" :value="3"></el-option>
                                                </el-select>
                                                <span class="fake-arrow">
                                                  <svg width="14" height="14" viewBox="0 0 24 24" fill="#2699d4">
                                                    <path d="M7 10l5 5 5-5z" />
                                                  </svg>
                                                </span>
                                            </div>
                                        </div>
                                    </el-col>
                                    <!-- 行为子模型 -->
                                    <el-col :span="5">
                                        <div class="select-block">
                                            <div class="select-title">行为子模型</div>
                                            <div class="custom-select-wrapper">
                                                <el-select
                                                        v-model="searchData.modelType"
                                                        :disabled="type === 0"
                                                        placeholder="请选择"
                                                        class="custom-select"
                                                        :suffix-icon="null"
                                                >
                                                    <el-option v-if="type === 0" label="全部" :value="0"></el-option>
                                                    <el-option
                                                            v-for="item in modelTypeEnums"
                                                            :key="item.value"
                                                            :label="item.label"
                                                            :value="item.value"
                                                    ></el-option>
                                                </el-select>
                                                <span class="fake-arrow">
                                                  <svg width="14" height="14" viewBox="0 0 24 24" fill="#2699d4">
                                                    <path d="M7 10l5 5 5-5z" />
                                                  </svg>
                                                </span>
                                            </div>
                                        </div>
                                    </el-col>
                                    <!-- 开始时间 -->
                                    <el-col :span="8">
                                        <div class="select-block">
                                            <div class="select-title">开始时间区间</div>
                                            <el-date-picker v-model="dateRange" type="datetimerange" placeholder="选择时间区间" value-format="YYYY-MM-DD HH:mm:ss" format="YYYY-MM-DD HH:mm:ss" style="width: 100%;" @change="changeTimeRange"/>
                                        </div>
                                    </el-col>
                                    <!-- 查询按钮 -->
                                    <el-col :span="3" class="btn-col">
                                        <div class="select-block">
                                            <div class="select-title" style="visibility:hidden;">查询</div>
                                            <el-button type="primary" style="width: 70%;" @click="queryBehaviorBySearchData">查询</el-button>
                                        </div>
                                    </el-col>
                                    <el-col :span="3" class="btn-col">
                                        <div class="select-block">
                                            <div class="select-title" style="visibility:hidden;">重置</div>
                                            <el-button style="width: 70%;" @click="resetQueryBehaviorBySearchData">重置</el-button>
                                        </div>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20" class="select-row" style="margin-top: 5px">
                                    <el-col :span="5">
                                        <div class="select-block">
                                            <div class="select-title">行为种类标识</div>
                                            <div class="custom-select-wrapper">
                                                <el-select
                                                        v-model="baseFlag"
                                                        multiple
                                                        filterable
                                                        placeholder="请选择"
                                                        class="custom-select"
                                                        :suffix-icon="null"
                                                        @change="changeBaseFlag"
                                                >
                                                    <el-option v-for="item in baseFlagEnum" :label="item.name" :value="item.code"></el-option>
                                                </el-select>
                                                <span class="fake-arrow">
                                                  <svg width="14" height="14" viewBox="0 0 24 24" fill="#2699d4">
                                                    <path d="M7 10l5 5 5-5z" />
                                                  </svg>
                                                </span>
                                            </div>
                                        </div>
                                    </el-col>
                                </el-row>
                                <div :style="{ height: `${queryModelHeight}px` }">
                                    <div class="info_box">
                                        <el-table stripe :data="queryData" :height="queryModelInfoHeight" style="width: 100%">
                                            <el-table-column type="index" label="序号" width="80"></el-table-column>
                                            <el-table-column label="行为编码" prop="CODENAME"></el-table-column>
                                            <el-table-column label="发生时间" prop="TIME"></el-table-column>
                                            <el-table-column label="动作类型" prop="TYPE_NAME"></el-table-column>
                                            <el-table-column label="操作" width="300">
                                                <template #default="scope">
                                                    <el-button v-if="'SUB' in scope.row" type="primary" size="small" @click="() => { getSUBOrSUBByRowFn('SUB', scope.row) }">主体</el-button>
                                                    <el-button v-if="'OBJ' in scope.row" type="primary" size="small" @click="() => { getSUBOrSUBByRowFn('OBJ', scope.row) }">客体</el-button>
                                                    <el-button type="primary" size="small" @click="() => { actionResultFn(scope.row) }">动作结果</el-button>
                                                </template>
                                            </el-table-column>
                                        </el-table>
                                        <div style="display: flex;align-items: center;justify-content: center">
                                            <el-pagination
                                                    :current-page="pageNum"
                                                    background
                                                    :page-size="1000"
                                                    layout="prev, jumper, next"
                                                    :total="queryModelCount"
                                                    class="custom-pagination"
                                                    @current-change="queryCurrentChange"
                                            ></el-pagination>
                                        </div>

                                    </div>
                                </div>

                            </el-dialog>
                            <!-- 动作结果 -->
                            <el-dialog v-if="actionResultVisible" v-model="actionResultVisible" title="动作结果"  width="70%">
                                <div :style="{ height: `${queryModelHeight}px` }">
                                    <div class="info_box">
                                        <el-table stripe :data="actionResultData" :height="queryModelInfoHeight" style="width: 100%">
                                            <el-table-column label="事件ID" prop="ID"></el-table-column>
                                            <el-table-column label="标签类型" prop="NAME"></el-table-column>
                                            <el-table-column label="标签内容" prop="LABEL"></el-table-column>
                                            <el-table-column label="分析规则ID" prop="R"></el-table-column>
                                            <el-table-column label="事件ID" prop="ID"></el-table-column>
                                            <el-table-column label="事件关联行为明细" prop="RDS"></el-table-column>
                                        </el-table>
                                    </div>
                                </div>
                            </el-dialog>
                            <!-- 主体/客体 -->
                            <el-dialog v-if="SUBOrOBJVisible" v-model="SUBOrOBJVisible" :title="SUBOrOBJTitle"  width="80%">
                                <div :style="{ height: `${queryModelHeight}px` }">
                                    <div class="info_box">
                                        <div :style="{ height: `${queryModelInfoHeight}px`, overflowY: 'auto', padding: '0 20px 0 20px' }">
                                            <template v-if="SUBOrOBJShowData.length">
                                                <!-- 数据外层 -->
                                                <el-row :gutter="10" v-for="(item, key) in SUBOrOBJShowData" :key="'SUBOrOBJShowData' + key" style="margin-top: 10px;margin-bottom: 10px;border: 1px solid black;padding: 10px 0 10px 0;">
                                                    <!-- 第一层：  item => ASSET HARDWARE SOFTWARE NETWORK   -->
                                                    <template v-for="(field, itemKey) of Object.keys(item)" :key="'asset' + itemKey">
                                                        <template v-if="!(Array.isArray(item[field]))">
<!--                                                            <template v-if="getDataType(item[field]) === 'OBJECT'">-->
                                                                <!-- 一级标题： 硬件 -->
                                                                <el-row class="field-arr-label"><el-col :span="24" class="next_row">基本信息:</el-col></el-row>
                                                                <el-row :gutter="10" style="padding: 10px 0 10px 0;border-bottom: 1px solid #ebeef5;border-top: 1px solid #ebeef5;">
                                                                    <el-col v-for="(assetName, assetNameIndex) in  Object.keys(item[field])" :key="'assetNameIndex' + assetNameIndex" :span="8" class="next_row" >
                                                                        <span class="field-label">{{  SUBOrOBJMap['INFO'][assetName] }}:</span>{{ item[field][assetName]  }}
                                                                    </el-col>
                                                                </el-row>
<!--                                                            </template>-->
<!--                                                            <template v-else>-->
<!--                                                                <el-col :span="8" class="next_row" >-->
<!--                                                                    <span class="field-label">{{  SUBOrOBJMap['INFO'][field] }}:</span>{{ item[field]  }}-->
<!--                                                                </el-col>-->
<!--                                                            </template>-->
                                                        </template>
                                                        <!-- 数组 -->
                                                        <template v-else>
                                                            <el-col :key="'itemKey' + key" :span="24" style="padding: 0">
                                                                <!-- 硬件/软件/网络 -->
                                                                <el-row class="field-arr-label"><el-col :span="24" class="next_row">基本信息: {{ SUBOrOBJShowName }} </el-col></el-row>
                                                                <el-row :gutter="10" style="padding: 10px 0 10px 0;border-bottom: 1px solid #ebeef5;border-top: 1px solid #ebeef5;" >
                                                                    <template v-if="!item[field].length"><el-col style="text-align: center;">暂无数据</el-col></template>
                                                                    <template v-else>
                                                                        <el-row class="next_row">
                                                                            <!-- 遍历 : 硬件/软件/网络.... -->
                                                                            <el-row v-for="(item2, index2) in item[field]" :key="'item2' + index2" style="padding-bottom: 5px;padding-left: 5px;border-bottom: 1px solid #ebeef5;">
                                                                                <el-col :span="24"><div class="item2Index itemIndex_class">{{ index2 + 1 }}</div></el-col>
                                                                                <!-- 遍历硬件每一项的字段名 CPU -->
                                                                                <template v-for="(field2, itemKey2) in Object.keys(item2)"  :key="'itemKey2' + key + itemKey + index2">
                                                                                    <el-col :span="8" v-if="!(Array.isArray(item2[field2]))" class="next_row">
                                                                                        <span class="field-label" style="font-weight: lighter;">{{ SUBOrOBJMap['INFO'][field2] }}:</span>
                                                                                        <el-tooltip :content="String(item2[field2])" effect="light">
                                                                                            <!-- item[field][index2][field2] -->
                                                                                            <span class="ellipsis-span">{{ item2[field2] }}</span>
                                                                                        </el-tooltip>
                                                                                    </el-col>
                                                                                    <template v-else>
                                                                                        <el-col :span="24" class="active next_row">
                                                                                            <!-- CPU  -->
                                                                                            <el-row class="field-arr-label-next"><el-col :span="24">{{ SUBOrOBJMap['INFO'][field2] }}:</el-col></el-row>
                                                                                            <el-row :gutter="10" style="padding: 0 10px 10px 10px;">
                                                                                                <template v-if="!item2[field2].length"><el-col style="padding-left: 30px;">暂无数据</el-col></template>
                                                                                                <template v-else>
                                                                                                    <!--                                                                                                遍历cpu -->
                                                                                                    <template v-for="(item3, index3) in item2[field2]">
                                                                                                        <!--                                                                                                         遍历cpu每一项的字段 -->
                                                                                                        <el-col :span="24"><div class="item3Index itemIndex_class">{{ index3 + 1 }}</div></el-col>
                                                                                                        <template v-for="(field3, itemKey3) in Object.keys(item3)" :key="itemKey3+itemKey2+11">
                                                                                                            <el-col :span="8" v-if="!(Array.isArray(item3[field3]))" class="next_row">
                                                                                                                <span class="field-label1">{{ SUBOrOBJMap[field2]['INFO'][field3] }}:</span>
                                                                                                                <el-tooltip :content="String(item3[field3])" effect="light">
                                                                                                                    <!-- item[field][index2][field3] -->
                                                                                                                    <span class="ellipsis-span">{{ item3[field3] }}</span>
                                                                                                                </el-tooltip>
                                                                                                            </el-col>
                                                                                                            <el-col v-else style="border: 1px solid blanchedalmond;">
                                                                                                                <!-- 逻辑核心 -->
                                                                                                                <template v-if="!item3[field3].length"><el-col style="padding-left: 30px;">暂无数据</el-col></template>
                                                                                                                <template v-else>
                                                                                                                    <el-row :span="24" class="field-arr-label-next-next">{{ SUBOrOBJMap[field2]['INFO'][field3] }}:</el-row>
                                                                                                                    <!-- 逻辑核心每一项 -->
                                                                                                                    <el-row v-for="(item4, itemKey4) in item3[field3]" :key="'itemKey4' + itemKey4" style="padding: 0 15px 0 10px;">
                                                                                                                        <el-col :span="24"><div class="item4Index itemIndex_class">{{ itemKey4 + 1 }}</div></el-col>

                                                                                                                        <!-- 遍历逻辑核心单项所有字段 -->
                                                                                                                        <template v-for="(field4, itemKey4) in Object.keys(item4)" :key="'itemKey4'+'itemKey3'+ itemKey4">
                                                                                                                            <el-col v-if="!Array.isArray(item4[field4])"  :span="8">
                                                                                                                                <span class="field-label2">{{ SUBOrOBJMap[field2][field3]['INFO'][field4] }}:</span>
                                                                                                                                <el-tooltip :content="String(item4[field4])" effect="light">
                                                                                                                                    <span class="ellipsis-span">{{ item4[field4] }}</span>
                                                                                                                                </el-tooltip>
                                                                                                                            </el-col>
                                                                                                                            <template v-else>
                                                                                                                                <el-col :span="24" class="field-arr-label-next-next-next">{{ SUBOrOBJMap[field2][field3]['INFO'][field4] }}:</el-col>
                                                                                                                                <template v-if="!item4[field4].length"><el-col style="padding-left: 30px;">暂无数据</el-col></template>
                                                                                                                                <template v-else>
                                                                                                                                    <el-col v-for="(item5, itemKey5) in item4[field4]" :key="'itemKey5' + itemKey5" :span="24" style="margin-bottom: 18px;margin-top: 10px">
                                                                                                                                        <el-col :span="24"><div class="item5Index itemIndex_class">{{ itemKey5 + 1 }}</div></el-col>
                                                                                                                                        <el-row>
                                                                                                                                            <el-col  :span="8" v-for="(field5, itemKey5) in Object.keys(item5)" :key="'itemKey5' + itemKey5">
                                                                                                                                                <span class="field-label3">{{ SUBOrOBJMap[field2][field3][field4]['INFO'][field5] }}:</span>
                                                                                                                                                <el-tooltip :content="String(item5[field5])" effect="light">
                                                                                                                                                    <span class="ellipsis-span">{{ item5[field5] }}</span>
                                                                                                                                                </el-tooltip>
                                                                                                                                            </el-col>
                                                                                                                                        </el-row>
                                                                                                                                    </el-col>
                                                                                                                                </template>
                                                                                                                            </template>
                                                                                                                        </template>
                                                                                                                    </el-row>
                                                                                                                </template>
                                                                                                            </el-col>
                                                                                                        </template>
                                                                                                    </template>
                                                                                                </template>
                                                                                            </el-row>
                                                                                        </el-col>
                                                                                    </template>
                                                                                </template>
                                                                            </el-row>
                                                            </el-col>
                                                        </template>
                                                </el-row>
                                                </el-col>
                                            </template>
                                            </template>
                                            </el-row>
                                            </template>
                                        </div>

                                    </div>
                                </div>
                            </el-dialog>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /.main-content -->
    <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
        <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
    </a>
    <!-- 验证用户身份-->
    <div class="modal" style="z-index: 4000;" id="validateUserDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         aria-hidden="true">
        <form class="form-horizontal" id="validateUserForm" method="post" action="" data-toggle="validator" role="form">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header" style="color: white;background: deepskyblue">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                        <h4 class="modal-title">验证用户身份</h4>
                    </div>
                    <div class="modal-body" style="padding: 10px 50px;">
                        <p>请输入登录密码</p>
                        <input type="hidden" id="opraType"/>
                        <input type="password" id="password" class="form-control" placeholder="密码" required/>
                        <div class="help-block with-errors"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary" id="validateuser_btn">确定
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <!-- /.modal -->
    <!-- 导入导出选择对话框 -->
    <div class="modal fade" id="confirmPortDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         aria-hidden="true">
        <form class="form-horizontal" id="confirmPortForm" method="post" action="" data-toggle="validator" role="form">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                            <span class="sr-only">Close</span>
                        </button>
                        <h4 class="modal-title" wa-name="import_tt">请选择</h4>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label class="col-sm-2 control-label text-right" for="select_file">请选择:</label>
                            <div class="col-sm-9">
                                <label class="ace-file-input">
                                    <input type="file" id="select_file" required/>
                                </label>
                            </div>
                        </div>
                        <div class="form-group hidden">
                            <label class="col-sm-2 control-label text-right" for="select_folder">请选择:</label>
                            <div class="col-sm-9">
                                <label class="ace-file-input">
                                    <input type="file" id="select_folder" webkitdirectory directory required/>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" id="port_btn">确定</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <!-- /.modal -->

</div>
<style>
    .info_box {
        width: 100%;
        height: 100%;
    }
    /* 去掉el-input内置的边框 */
    .el-input__inner {
        border: none !important;
    }
    .el-dialog {
        padding: 0 !important;
    }
    .el-dialog__header {
        background-color: #00bfff !important;
        color: white !important;
        padding-top: 10px !important;
        padding-left: 10px !important;
    }
    .el-dialog__body {
    }
    .el-dialog__title {
        color: white !important;
    }
    .dialog_content {
        height: 100px;
        padding: 20px !important;
    }
    .dialog-footer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 20px !important;
        background-color: #eff3f8;
    }
    /* ------------------------------------------------------------------------------------------------- */
    /* 让 #app 占满剩余空间 */
    .main-content {
        height: 100%;

    }
    .main-content-inner, .page-content, .row {
        height: 100%;
    }

    html, body {
        height: 100%;
        margin: 0;
        padding: 0;
    }

    #app {
        display: flex;
        flex-direction: column;
        height: 100%;
        background-color: #f9f9f9;
    }


    .app-bottom {
        flex: 1;
        display: flex;
        overflow: hidden;
    }

    .app-top {
        height: 100px;
        border: 1px solid #e1e1e1;
        border-radius: 4px;
        margin: 10px;
        padding: 10px;
        color: #333;
        font-weight: 500;
        background: transparent;
    }

    .app-bottom-left {
        width: 200px;
        height: calc(100% - 100px);
        border: 1px solid #e1e1e1;
        border-radius: 4px;
        margin: 10px;
        padding: 10px;
        background: transparent;
        color: #444;
    }

    .app-bottom-right {
        flex: 1;
        height: calc(100% - 100px);
        border: 1px solid #e1e1e1;
        border-radius: 4px;
        margin: 10px;
        padding: 16px;
        background: transparent;
        color: #444;
    }



</style>

<style>
    .el-select__input {
        border: none !important;
    }
    .textarea {
        border: none !important;
    }
    /* 隐藏跳转框前后的文字 */
    .custom-pagination .el-pagination__jump {
        margin-left: 0;
        font-size: 0;
    }
    .custom-pagination .el-pagination__jump:before,
    .custom-pagination .el-pagination__jump:after {
        content: none;
    }
    .pagination-container {
        background: #fff;
    }
    .next_row {
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    }
    .arrow-down {
        display: block;
        position: relative;
        width: 60px;
        height: 55px;
        margin: 20px auto;
        filter: drop-shadow(0 0 6px rgba(52, 152, 219, 0.3)); /* 蓝色外发光 */
    }

    /* 箭杆 - 蓝色渐变 */
    .arrow-down::before {
        content: "";
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 6px;
        height: calc(100% - 25px);
        background: linear-gradient(
                to bottom,
                rgba(52, 152, 219, 1) 0%,       /* 顶部实心蓝 */
                rgba(52, 152, 219, 0.6) 80%,    /* 中部半透明 */
                rgba(52, 152, 219, 0.2) 100%    /* 底部几乎透明 */
        );
        border-radius: 3px; /* 圆角使边缘更柔和 */
    }

    /* 箭头 - 蓝色渐变三角 */
    .arrow-down::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 25px;
        height: 25px;
        background: linear-gradient(
                to bottom,
                rgba(52, 152, 219, 0.8) 0%,
                rgba(52, 152, 219, 0.3) 100%
        );
        clip-path: polygon(0% 0%, 100% 0%, 50% 100%);
    }
    .ellipsis-span {
        display: inline-block;
        max-width: 120px; /* 可根据 el-col 宽度灵活调整 */
        overflow: hidden;
        padding-bottom: 3px;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;
    }
    .field-arr-label {
        color: #333;
        background: #fafafa; /* 外层深色渐变 */
        margin-left: 2px;
        margin-top: 10px;
        margin-bottom: 10px;
        font-size: 1.2em;
        font-weight: bold;
        padding-left: 5px;
        border-left: 4px solid #409eff; /* 蓝色左侧边框 */
        border-radius: 4px;
    }
    .field-arr-label-next {
        color: black;
        margin-top: 10px;
        margin-left: 10px;
        font-size: 1.1em;
        padding: 8px 12px;
        margin-bottom: 10px;
        font-weight: bold;
        border-left: 4px solid #36A1D1; /* 蓝色左侧边框 */
        border-radius: 4px;
    }
    .field-arr-label-next-next {
        color: #2b2d30; /* 更醒目的蓝色 */
        margin: 12px 0;
        font-weight: 700;
        font-size: 1em;
        padding: 8px 15px;
        /*background-color: #a9e8e6; !* 浅蓝色背景 *!*/
        border-left: 4px solid #2E86AB; /* 左侧装饰条 */
        border-radius: 4px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05); /* 轻微阴影 */
    }
    .field-arr-label-next-next-next {
        color: #2b2d30; /* 更醒目的蓝色 */
        margin: 12px 0;
        font-weight: 700;
        font-size: 0.9em;
        padding: 8px 18px;
        /*background-color: #a9e8e6; !* 浅蓝色背景 *!*/
        border-left: 4px solid #256D89; /* 左侧装饰条 */
        border-radius: 4px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05); /* 轻微阴影 */
    }
    .active {
        transition: all 0.3s ease; /* 悬停动画 */
    }
    .active:hover {
        background-color: #e1f0ff;
        transform: translateY(-1px);
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
    }
    .field-label {
        font-size: 1.1em;
        font-weight: bold;
    }
    .field-label1 {
        font-size: 1em;
        font-weight: bold;
    }
    .field-label2 {
        font-size: 0.9em;
        font-weight: bold;
    }
    .field-label3 {
        font-size: 0.8em;
        font-weight: bold;
    }
    /* 隐藏跳转框前后的文字 */
    .custom-pagination .el-pagination__jump {
        margin-left: 0;
        font-size: 0;
    }
    .custom-pagination .el-pagination__jump:before,
    .custom-pagination .el-pagination__jump:after {
        content: none;
    }
    .el-table__header th {
        border-bottom: 3px solid #e1e1e1 !important;
    }
    /* 表格最底部的线（蓝色，加粗） */
    .el-table::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        border-bottom: 2px solid #e1e1e1 !important;
    }
    .cell-input {
        height: 26px;
        margin-left: -10px;
    }

    .cell-select .el-select__wrapper {
        height: 26px;
        min-height: 26px;
        margin-left: -11px;
    }

    .el-table .el-table__row {
        height: 50px;
    }
    .btn-prev {
        border-radius: 50% !important;
    }
    .btn-next {
        border-radius: 50% !important;
    }

    .item2Index {
        width: 18px;               /* 圆圈宽度 */
        height: 18px;              /* 圆圈高度 */
        background-color: #409eff;
        margin: 5px 0 5px 2px;
        color: white;
    }
    /* CPU */
    .item3Index {
        background-color: #36A1D1;
        width: 18px;               /* 圆圈宽度 */
        height: 18px;              /* 圆圈高度 */
        margin: 5px 0 5px 2px;
        color: #F0F9FF;
    }
    /* 漏记核心 */
    .item4Index {
        background-color: #2E86AB;
        width: 18px;               /* 圆圈宽度 */
        height: 18px;              /* 圆圈高度 */
        margin: 5px 0 5px 2px;
        color: #E0F2FF;
    }
    /*  */
    .item5Index {
        background-color: #256D89;
        font-size: 12px !important;          /* 文字大小 */
        width: 14px;               /* 圆圈宽度 */
        height: 14px;              /* 圆圈高度 */
        margin: 5px 0 5px 2px;
        color: #D0EBFF;
    }
    .itemIndex_class {
        font-weight: bold;        /* 加粗文字 */
        font-size: 14px;          /* 文字大小 */
        line-height: 1;           /* 行高设为1避免偏移 */
        border-radius: 50%;        /* 圆形 */
        display: inline-flex;       /* 使用 flex 布局方便居中 */
        align-items: center;       /* 垂直居中 */
        justify-content: center;   /* 水平居中 */
    }
    .select-block {
        display: flex;
        flex-direction: column;
    }

    .select-title {
        font-size: 14px;
        color: #333;
        margin-bottom: 4px;
        font-weight: bold;
    }

    .custom-select-wrapper {
        position: relative;
        width: 100%;
    }

    .custom-select :deep(.el-input__inner) {
        padding-right: 32px !important;
    }

    .fake-arrow {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
    }
    .btn-col {
        display: flex;
        padding-right: 10px;
        align-items: flex-end;
    }

</style>
<link rel="stylesheet" href="lib/elementPlus/index.css"/>
<script src="lib/third/vue3/vue.global.js"></script>
<script src="lib/elementPlus/index.full.min.js"></script>
<script src="lib/elementPlus/zh-cn.js"></script>

<script>
    console.log(require.cache)
    delete require.cache[require.resolve('./vue/dataQuery/MSG_PDBehaviorModelQuery.js')];
    require('./vue/dataQuery/MSG_PDBehaviorModelQuery.js');
    // $('#handlerForm').validator();
</script>
