<div class="main-content">
  <div class="main-content-inner">
    <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs">
      <div class="page-content">
        <div class="page-header">
          <h1>日志备份</h1>
        </div><!-- /.page-header -->
        <div class="row wa-mb10">
          <div class="col-sm-12">
            <form class="form-horizontal" role="form" id="log_audit_frm">
            <div class="form-group">
              <label class="col-sm-2 control-label no-padding-right">
                <b>日志来源:</b>
              </label>
              <div class="col-sm-2">
                <select class="form-control" id="log_source" name="log_source">
                  <option value="">请选择</option>
                  <option value="0">应用日志</option>
                  <option value="1">操作系统日志</option>
                </select>
              </div>
              <label class="col-sm-2 control-label no-padding-right">
                <b>日志级别:</b>
              </label>
              <div class="col-sm-2">
                <select class="form-control" id="log_level" name="log_level">
                  <option value="">请选择</option>
                  <option value="0">紧急</option>
                  <option value="1">重要</option>
                  <option value="2">次要</option>
                  <option value="3">一般</option>
                  <option value="4">告知</option>
                </select>
              </div>
            </div>
            </form>
          </div>
          <div class="col-sm-12 text-right">
            <a href="javascript:;" type="button" class="btn btn-sm btn-primary" id="backup"><i
                class="ace-icon fa fa-refresh"></i> 备份</a>
          </div>
        </div>
        <div class="row">
          <div class="col-xs-12">
            <table id="log-backup-tb" class="table table-bordered table-hover"></table>
            <div id="log-backup-pager"></div>
            <!-----用来计算单元格内容实际长度的--------->
            <div class="ui-jqgrid ui-widget ui-widget-content ui-corner-all" style="position:absolute;top:-9999px">
              <div class="ui-jqgrid-view">
                <div class="ui-jqgrid-bdiv">
                  <div style="position: relative;">
                    <table cellspacing="0" cellpadding="0" border="0">
                      <tr class="ui-widget-content jqgrow ui-row-ltr" style="table-layout:table">
                        <td id="tdCompute" style="background:#eee;width:auto"></td>
                      </tr>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div><!-- /.page-content -->
    </div>
  </div><!-- /.main-content -->
  <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
    <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
  </a>
  <!-- 确认对话框 -->
  <div class="modal fade" id="confirmDlg">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
            <span class="sr-only">Close</span>
          </button>
          <h4 class="modal-title" id="confirmDlgTitle" wa-data="export">备份</h4>
        </div>
        <div class="modal-body">
          <p>确定备份日志？</p>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
          <button type="button" class="btn btn-primary" data-dismiss="modal" id="confirmBtn">确定</button>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
  </div><!-- /.modal -->
  <!-- 验证用户身份-->
  <div class="modal" id="validateUserDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <form class="form-horizontal" id="validateUserForm" method="post" action="" data-toggle="validator" role="form">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header" style="color: white;background: deepskyblue">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            <h4 class="modal-title">验证用户身份</h4>
          </div>
          <div class="modal-body" style="padding: 10px 50px;">
            <p>请输入登录密码</p>
            <input type="hidden" id="opraType"/>
            <input type="hidden" id="log_file_name"/>
            <input type="password" id="password" class="form-control" placeholder="密码" required/>
            <div class="help-block with-errors"></div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
            <button type="submit" class="btn btn-primary" id="validateuser_btn">确定
            </button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </form>
  </div><!-- /.modal -->
  <!-- 导入导出选择对话框 -->
  <div class="modal fade" id="confirmPortDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
       aria-hidden="true">
    <form class="form-horizontal" id="confirmPortForm" method="post" action="" data-toggle="validator" role="form">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
              <span class="sr-only">Close</span>
            </button>
            <h4 class="modal-title" wa-name="import_tt">请选择文件路径</h4>
          </div>
          <div class="modal-body">
            <div class="form-group">
              <label class="col-sm-2 control-label text-right" for="select_folder">请选择:</label>
<!--              <div class="col-sm-9">-->
<!--                <label class="ace-file-input">-->
<!--                  <input type="file" id="select_folder" webkitdirectory directory required>-->
<!--                </label>-->
<!--                &lt;!&ndash;<div class="help-block with-errors"></div>&ndash;&gt;-->
<!--              </div>-->
              <div class="col-sm-9">
                 <div class="input-group">
                        <input type="text" id="export_file_path" class="form-control" readonly required>
                        <span class="input-group-btn">
                           <button type="button" id="export_selectPath_btn" class="btn btn-default">选择路径</button>
                        </span>
                    </div>
                    <!--<div class="help-block with-errors"></div>-->
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
            <button type="button" class="btn btn-primary" id="port_btn">确定</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.form-horizontal-->
    </form><!-- /.modal-dialog -->
  </div><!-- /.modal -->
  <div class="modal fade" id="confirmDelDlg">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
            <span class="sr-only">Close</span>
          </button>
          <h4 class="modal-title">删除</h4>
        </div>
        <div class="modal-body">
          <input type="hidden" id="file_name"/>
          确定删除日志文件？
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
          <button type="button" class="btn btn-primary" data-dismiss="modal" id="removeFileBtn">确定</button>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
  </div><!-- /.modal -->
  <script>
    delete require.cache[require.resolve('./script/view/log_backup.js')];
    require('./script/view/log_backup.js');
  </script>
</div>
