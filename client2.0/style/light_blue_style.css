
/* 首页样式渲染*/
html,body,body .main-container{
  height: 100%;
}
body{
  overflow: hidden;
}
body .navbar.navbar-default{
  width: 100%;
}
body .main-container{
  padding-bottom: 150px;
}
body .main-container>.sidebar{
  height: 100%;
}
body .main-container>.parent_content{
  height: 100%;
  overflow-y: scroll!important;
  overflow-x: hidden;
  background: #ffffff!important;
}
body .main-container .footer .footer-content{
  /*background-color: #ffffff!important;*/
  left: 10px;
  right: 23px;
}
/* 首页样式渲染結束*/
/* 公共样式渲染*/
.wa-w266{
  width: 266px;
}
.wa-w55{
  width: 55px;
}
.wa-h100{
  height: 100px;
}
.wa-m10{
  margin: 10px;
}
.wa-mb10{
  margin-bottom: 10px!important;
}
.wa-mb0{
  margin-bottom: 0!important;
}
.wa-pt12{
  padding-top: 12px;
}
.wa-ptb-10{
  padding-top: 10px!important;
  padding-bottom: 10px!important;
}
.wa-mlr5,.wa-mr5{
  margin-right: 5px!important;
}

.wa-floatL{
  float: left!important;
}
.wa-floatR{
  float: right!important;
}
.wa-fsize51{
  font-size: 51px;
}
.wa-fsize15{
  font-size: 15px;
}
.wa-fsize29{
  font-size: 29px;
  font-family: microsoft yahei;/* 首页展示框name专用*/
}
/* 首页样式渲染*/
.wa-infobox{
  width: 266px;
  height: 100px;
  margin: 10px;
  box-shadow: 8px 8px 11px #f0f0f0;
}
.wa-infobox:hover,.wa-infobox:active:hover{
  box-shadow: 8px 8px 11px #c0c0c0;
}
/* (公共)布局调整*/
.wa-breadcrumbs.breadcrumbs{
  padding-right: 0;
  border-bottom: 0;
  line-height: initial;
}
/* 左侧导航栏样式渲染*/
.nav.nav-list .submenu{
  transition: display .5s linear 0s;
  -webkit-transition: display .5s linear 0s; /* Safari */
}
/* 去除标签栏样式*/
.wa-tabs.wa-no-switch>li,.wa-tabs.wa-no-switch>li>a{
  cursor: default!important;
}
/* 字体、颜色、字号等样式渲染*/
.wa-breadcrumbs .text-warning{
  font-family: 'Microsoft yahei'!important;
}

/* 重置jqgridtable样式*/
.ui-jqgrid .ui-jqgrid-btable, .ui-jqgrid .ui-jqgrid-ftable, .ui-jqgrid .ui-jqgrid-htable{
  border-collapse: collapse;
}

.ui-jqgrid tr.jqgroup, .ui-jqgrid tr.jqgrow{
  border: inherit!important;
}
.ui-jqgrid .ui-jqgrid-btable{
  border-left:0!important;
}

.ui-jqgrid .ui-jqgrid-htable th,.ui-jqgrid tr.jqgrow td{
  text-align: center!important;
  /*white-space: normal!important;*/
  word-wrap: break-word;
  white-space: pre-wrap;
}
.ui-jqgrid .ui-jqgrid-htable th div{
  padding-bottom: 15px;
}
.ui-jqgrid-btable>tbody>tr>td{
  text-overflow: ellipsis;
}
.ui-jqgrid-htable>thead>tr>th,.table-bordered>tbody>tr>td{
  border-left: 0!important;
  border-right: 0!important;
}
.ui-jqgrid-btable>tbody>tr>td, .ui-jqgrid-btable>tbody>tr>th, .ui-jqgrid-btable>tfoot>tr>td,
.ui-jqgrid-btable>tfoot>tr>th, .ui-jqgrid-btable>thead>tr>td, .ui-jqgrid-btable>thead>tr>th{
  vertical-align: middle;
}
.ui-jqgrid tr.jqgrow td{
  height: 29px!important;
}
.ui-pg-table.ui-pager-table .ui-pg-selbox{
  display: none;
}
.ui-jqgrid-btable>tbody>tr>td>.btn-group{
  height: 20px!important;
  display: inline-flex;
}
@media (max-width: 991px) and (min-width: 768px){
  .ui-jqgrid-btable>tbody>tr>td>.btn-group{
    display: inline-flex!important;
  }
}
@media (max-width: 767px){
  .ui-jqgrid-btable>tbody>tr>td>.btn-group{
    display: inline-flex!important;
  }
}

/* 重置bootstrap-table*/
.bootstrap-table .fixed-table-loading{
  display: none!important;
}
.bootstrap-table .table-responsive.table>thead>tr>th {
  border-bottom: none!important;
}
.bootstrap-table .fixed-table-pagination .btn-group>.btn>.caret{
  margin-top: 0;
}
.bootstrap-table .pagination>ul.pagination{
  margin: 0 0 20px!important;
}
/* 重置bootstrap-table-page样式*/
.pagination-jump {
  margin: 0 0 20px!important;
}
.pagination-jump {
  display: inline-block;
  padding-left: 9px;
  border-radius: 4px;
}
.pagination-jump>li {
  display: inline;
}
.pagination-jump>li>a, .pagination-jump>li>input, .pagination-jump>li>span {
  position: relative;
  float: left;
  margin-left: -1px;
  line-height: 1.42857143;
  color: #337ab7;
  text-decoration: none;
  background-color: #fff;
}
.pagination-jump>li>a {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.pagination-jump>li>input {
  padding: 6px 0px;
  border: 1px solid #ddd;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  width: 36px;
  text-align: center;
}
.pagination-jump>li>span{
  padding: 9px 3px 6px 12px;
}
.pagination-jump>li>.jump-go {
  margin-left: 0;
  padding: 6px;
}

/* 重置date插件样式*/
.daterangepicker .ranges li{
  border-radius: 0!important;
}
.daterangepicker .ranges ul{
  width: 109px!important;
}

/* 界面整体样式渲染开始*/
.l-b-body{
  background-color: #eef7fe!important;
  background-image: none;
}
.l-b-logincont{
  width: 100%!important;
}
.l-b-loginhd{
  border-radius: 1px;
  box-shadow: 0 0 4px 1px rgba(0, 0, 0, .2);
  padding: 10px 28px 1px;
  background-image: linear-gradient(to bottom , #e9e9e9, #ffffff);
}
.l-b-loginhd img{
  float:left;
  height:63px;
  margin: 10px 10px 0 0;
}
.l-b-loginhd .l-b-loghd-span{
  color: #007A77!important;
  font-size: 25px;
  font-weight: bolder;
  font-family: cursive;
  line-height: 65px;
  margin-top: 15px;
  margin-bottom: 0;
}
.l-b-loginbox{
  width: 375px;
  margin: 150px auto 0;
}
.l-b-loginbox>.l-b-log-box{
  background: #fefefe;
  border: solid 2px #c7c7c7;
  box-shadow: 0 0 3px #eee;
}
.l-b-loginbox>.l-b-log-box .l-b-wtmain{
  background:none;
}
.l-b-loginbox>.l-b-log-box .l-b-wtmain>h4{
  font-size: 18px;
  color: #555!important;
  font-family: Simhei;
}
.l-b-loginbox>.l-b-log-box .l-b-wtmain input[type=text],.l-b-loginbox>.l-b-log-box .l-b-wtmain input[type=password]{
  background-color: rgb(232, 240, 254) !important;
  background-image: none !important;
  color: rgb(0, 0, 0) !important;
}
.l-b-loginbox>.l-b-log-box .l-b-wtmain .btn.btn-sm.btn-primary{
  background-color: #449d44!important;
  border-color: #398439!important;
  border-radius: 4px;
  border-width: 1px;
}
.l-b-loginbox>.l-b-log-box .l-b-wtmain .btn.btn-sm.btn-primary:hover,
.l-b-loginbox>.l-b-log-box .l-b-wtmain .btn.btn-sm.btn-primary:focus,
.l-b-loginbox>.l-b-log-box .l-b-wtmain .btn.btn-sm.btn-primary:active{
  background-color: #449d44!important;
  border-color: #398439!important;
}
.l-b-footer{
  padding: 0!important;
  left: 0!important;
  right: 0!important;
  bottom: 0!important;
  line-height: 29px!important;
  font-size: 11px;
  color: #ffffff;
  background-color: #007a77!important;
  z-index: 9999;
}
.l-b-sidebar{
  width: 139px!important;
  height: 100%;
  float: left!important;
  margin-right: 10px;
  border: none!important;
  background: #ffffff!important;
}
.l-b-sidebar>.l-b-nav{
  height: 100%;
  padding-bottom: 40px;
  margin: 0;
  padding: 0;
  list-style: none;
  overflow: auto;
}
.l-b-sidebar>.l-b-nav>li{
  width: 100px;
  float: left;
  margin-top: 10px;
  margin-left: 8px;
  border-top: none!important;
  border-bottom: solid 1px #eef7fe!important;
  cursor: pointer;
}
.l-b-sidebar>.l-b-nav>li.l-b-active .l-b-navbox-1>img{
  visibility: visible;
}
.l-b-sidebar>.l-b-nav>li:hover .l-b-navbox-1>img{
  visibility: visible;
}
.l-b-sidebar>.l-b-nav>li.l-b-active .l-b-navbox-2{
  background-color: rgb(250, 255, 232)
}
.l-b-nav img{
  width: 49px;
  height: 41px;
  margin: 3px 5px 3px 0px;
  padding-left: 5px;
  padding-right: 8px;
  vertical-align: middle;
}
.l-b-navbox-1{
  float: left;
  margin-left: 5px;
}
.l-b-navbox-1>img{
  height: 80px;
  width: 15px;
  margin-left: -12px;
  visibility: hidden;
}
.l-b-navbox-2{
  margin-top: 5px;
  padding-bottom: 12px;
}

.l-b-navbar{
  width: calc(100% - 150px)!important;
  float: right!important;
  background-color: #f8f8f8;
  border-color: #e7e7e7;
  border-bottom: 1px solid #e5e5e5;
}
.l-b-navbar .navbar-header>.navbar-brand{
  height: 50px;
  padding: 15px 15px;
  font-size: 18px;
  line-height: 20px;
  color: #777;
}
.l-b-navbar ul.navbar-nav>li{
  border: none;
}
.l-b-navbar ul.navbar-nav>li>a:hover,.l-b-navbar ul.navbar-nav>li>a:active{
  background-color: #f8f8f8!important;
  color: #555!important;
}
.l-b-navbar ul.navbar-nav>li.active>a,.l-b-navbar ul.navbar-nav>li.active>a:focus,.l-b-navbar ul.navbar-nav>li.active>a:hover{
  background-color: #e7e7e7!important;
  color: #555!important;
}
.l-b-navbar ul.navbar-nav>li>a{
  height: 50px;
  line-height: 25px;
  background-color: #f8f8f8;
  color: #777
}
.l-b-parent_content{
  width: calc(100% - 150px)!important;
  height: calc(100% - 51px)!important;
  float: right!important;
}
.l-b-parent_content .btn.btn-sm.btn-primary,
.l-b-parent_content .btn.btn-sm.btn-info,
.l-b-parent_content .btn.btn-sm.btn-danger,
.l-b-parent_content .btn.btn-sm.btn-success{
  background-color: #fff!important;
  border-color: #ccc!important;
  border-width: 1px;
  border-radius: 4px;
  color: #6ecefe!important;
  font-weight: bold;
}
.l-b-parent_content .btn.btn-sm.btn-success{
  color: #5cb85c!important;
}
.l-b-parent_content .btn.btn-sm.btn-danger{
  color: #d9534f!important;
}
.l-b-parent_content .btn.btn-sm.btn-primary:hover,
.l-b-parent_content .btn.btn-sm.btn-primary:focus,
.l-b-parent_content .btn.btn-sm.btn-primary:active,
.l-b-parent_content .btn.btn-sm.btn-info:hover,
.l-b-parent_content .btn.btn-sm.btn-info:focus,
.l-b-parent_content .btn.btn-sm.btn-info:active{
  background-color: #6ecefe!important;
  color: white!important;
}
.l-b-parent_content .btn.btn-sm.btn-success:hover,
.l-b-parent_content .btn.btn-sm.btn-success:focus,
.l-b-parent_content .btn.btn-sm.btn-success:active{
  background-color: #5cb85c!important;
  color: white!important;
}
.l-b-parent_content .btn.btn-sm.btn-danger:hover,
.l-b-parent_content .btn.btn-sm.btn-danger:focus,
.l-b-parent_content .btn.btn-sm.btn-danger:active{
  background-color: #d9534f!important;
  color: white!important;
}
.l-b-parent_content .ui-jqgrid .ui-jqgrid-hbox>table>thead>tr{
  background-color: #ffffff;
  background-image: none;
  border-left: none!important;
  border-right: none!important;
}
.l-b-parent_content .ui-jqgrid .ui-jqgrid-hbox>table>thead>tr>th{
  border-right: none!important;
}
.l-b-parent_content .ui-jqgrid .ui-jqgrid-bdiv table>tbody>tr:nth-child(odd){
  background-color: #f9f9f9;
}
.l-b-parent_content .ui-jqgrid .ui-jqgrid-hbox>table>thead>tr>th .ui-jqgrid-sortable,
.l-b-parent_content .ui-jqgrid .ui-jqgrid-bdiv table>tbody>tr>td{
  color: #333!important;
}
.l-b-parent_content .ui-jqgrid .ui-jqgrid-bdiv table>tbody .btn-success{
  background-color: #5cb85c!important;
  border-color: #4cae4c!important;
}
.l-b-parent_content .ui-jqgrid .ui-jqgrid-bdiv table>tbody .btn-success:hover,
.l-b-parent_content .ui-jqgrid .ui-jqgrid-bdiv table>tbody .btn-success:focus,
.l-b-parent_content .ui-jqgrid .ui-jqgrid-bdiv table>tbody .btn-success:active{
  background-color: #67ee67!important;
}
.l-b-parent_content .ui-jqgrid .ui-jqgrid-bdiv table>tbody .btn-inverse{
  background-color: #5bc0de!important;
  border-color: #46b8da!important;
}
.l-b-parent_content .ui-jqgrid .ui-jqgrid-bdiv table>tbody .btn-inverse:hover,
.l-b-parent_content .ui-jqgrid .ui-jqgrid-bdiv table>tbody .btn-inverse:focus,
.l-b-parent_content .ui-jqgrid .ui-jqgrid-bdiv table>tbody .btn-inverse:active{
  background-color: #6ecefe!important;
}
.l-b-parent_content .ui-jqgrid .ui-jqgrid-bdiv table>tbody .btn-danger{
  background-color: #d9534f!important;
  border-color: #d43f3a!important;
}
.l-b-parent_content .ui-jqgrid .ui-jqgrid-bdiv table>tbody .btn-danger:hover,
.l-b-parent_content .ui-jqgrid .ui-jqgrid-bdiv table>tbody .btn-danger:focus,
.l-b-parent_content .ui-jqgrid .ui-jqgrid-bdiv table>tbody .btn-danger:active{
  background-color: #fa5550!important;
}
.l-b-body .modal .btn.btn-default,.l-b-body .modal .btn.btn-primary{
  border-radius: 4px!important;
  background-color: #5bc0de!important;
  border-color: #46b8da!important;
  border-width: 1px;
}
.l-b-body .modal .btn.btn-default{
  background-color: #fff!important;
  border-color: #ccc!important;
  color: #333!important;
}
.l-b-body .modal .btn.btn-primary:hover,
.l-b-body .modal .btn.btn-primary:focus,
.l-b-body .modal .btn.btn-primary:active{
  background-color: #31b0d5!important;
  border-color: #269abc!important;
}
.l-b-body .modal .btn.btn-default:hover,
.l-b-body .modal .btn.btn-default:focus,
.l-b-body .modal .btn.btn-default:active{
  background-color: #e6e6e6!important;
  border-color: #adadad!important;
  color: #333!important;
}

/* 界面整体样式渲染结束*/