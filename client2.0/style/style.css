
/* 首页样式渲染*/
html,body,body>.main-container{
  height: 100%;
}
body{
  overflow: hidden;
}
body>.navbar.navbar-default{
  width: 100%;
}
body>.main-container{
  padding-bottom: 60px;
}
body>.main-container>.parent_content{
  height: 100%;
  overflow-y: scroll!important;
  overflow-x: hidden;
  background: #ffffff!important;
}
body>.main-container>.sidebar{
  height: 100%;
}
body>.main-container .footer .footer-content{
  background-color: #ffffff!important;
  left: 10px;
  right: 23px;
}
/* 首页样式渲染結束*/
/* 公共样式渲染*/
.wa-w266{
  width: 266px;
}
.wa-w55{
  width: 55px;
}
.wa-h100{
  height: 100px;
}
.wa-m10{
  margin: 10px;
}
.wa-mb10{
  margin-bottom: 10px!important;
}
.wa-mb0{
  margin-bottom: 0!important;
}
.wa-pt12{
  padding-top: 12px;
}
.wa-ptb-10{
  padding-top: 10px!important;
  padding-bottom: 10px!important;
}
.wa-mlr5,.wa-mr5{
  margin-right: 5px!important;
}

.wa-floatL{
  float: left!important;
}
.wa-floatR{
  float: right!important;
}
.wa-fsize51{
  font-size: 51px;
}
.wa-fsize15{
  font-size: 15px;
}
.wa-fsize29{
  font-size: 29px;
  font-family: microsoft yahei;/* 首页展示框name专用*/
}
/* 首页样式渲染*/
.wa-infobox{
  width: 266px;
  height: 100px;
  margin: 10px;
  box-shadow: 8px 8px 11px #f0f0f0;
}
.wa-infobox:hover,.wa-infobox:active:hover{
  box-shadow: 8px 8px 11px #c0c0c0;
}
/* (公共)布局调整*/
.wa-breadcrumbs.breadcrumbs{
  padding-right: 0;
  border-bottom: 0;
  line-height: initial;
}
/* 左侧导航栏样式渲染*/
.nav.nav-list .submenu{
  transition: display .5s linear 0s;
  -webkit-transition: display .5s linear 0s; /* Safari */
}
/* 去除标签栏样式*/
.wa-tabs.wa-no-switch>li,.wa-tabs.wa-no-switch>li>a{
  cursor: default!important;
}
/* 字体、颜色、字号等样式渲染*/
.wa-breadcrumbs .text-warning{
  font-family: 'Microsoft yahei'!important;
}

/* 重置jqgridtable样式*/
.ui-jqgrid .ui-jqgrid-btable, .ui-jqgrid .ui-jqgrid-ftable, .ui-jqgrid .ui-jqgrid-htable{
  border-collapse: collapse;
}

.ui-jqgrid tr.jqgroup, .ui-jqgrid tr.jqgrow{
  border: inherit!important;
}
.ui-jqgrid .ui-jqgrid-btable{
  border-left:0!important;
}

.ui-jqgrid .ui-jqgrid-htable th,.ui-jqgrid tr.jqgrow td{
  text-align: center!important;
  /*white-space: normal!important;*/
  word-wrap: break-word;
  white-space: pre-wrap;
}
.ui-jqgrid-btable>tbody>tr>td{
  text-overflow: ellipsis;
}
.ui-jqgrid-htable>thead>tr>th,.table-bordered>tbody>tr>td{
  border-left: 0!important;
  border-right: 0!important;
}
.ui-jqgrid-btable>tbody>tr>td, .ui-jqgrid-btable>tbody>tr>th, .ui-jqgrid-btable>tfoot>tr>td,
.ui-jqgrid-btable>tfoot>tr>th, .ui-jqgrid-btable>thead>tr>td, .ui-jqgrid-btable>thead>tr>th{
  vertical-align: middle;
}
.ui-jqgrid tr.jqgrow td{
  height: 29px!important;
}
.ui-pg-table.ui-pager-table .ui-pg-selbox{
  display: none;
}
.ui-jqgrid-btable>tbody>tr>td>.btn-group{
  height: 20px!important;
  display: inline-flex;
}
@media (max-width: 991px) and (min-width: 768px){
  .ui-jqgrid-btable>tbody>tr>td>.btn-group{
    display: inline-flex!important;
  }
}
@media (max-width: 767px){
  .ui-jqgrid-btable>tbody>tr>td>.btn-group{
    display: inline-flex!important;
  }
}
/* 重置bootstrap-table样式*/
.bootstrap-table .fixed-table-loading{
  display: none!important;
}
.bootstrap-table .table-responsive.table>thead>tr>th {
  border-bottom: none!important;
}
.bootstrap-table .fixed-table-pagination .btn-group>.btn>.caret{
  margin-top: 0;
}
.bootstrap-table .pagination>ul.pagination{
  margin: 0 0 20px!important;
}
/* 重置bootstrap-table-page样式*/
.pagination-jump {
  margin: 0 0 20px!important;
}
.pagination-jump {
  display: inline-block;
  padding-left: 9px;
  border-radius: 4px;
}
.pagination-jump>li {
  display: inline;
}
.pagination-jump>li>a, .pagination-jump>li>input, .pagination-jump>li>span {
  position: relative;
  float: left;
  margin-left: -1px;
  line-height: 1.42857143;
  color: #337ab7;
  text-decoration: none;
  background-color: #fff;
}
.pagination-jump>li>a {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.pagination-jump>li>input {
  padding: 6px 0px;
  border: 1px solid #ddd;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  width: 36px;
  text-align: center;
}
.pagination-jump>li>span{
  padding: 9px 3px 6px 12px;
}
.pagination-jump>li>.jump-go {
  margin-left: 0;
  padding: 6px;
}

/* 重置date插件样式*/
.daterangepicker .ranges li{
  border-radius: 0!important;
}
.daterangepicker .ranges ul{
  width: 109px!important;
}

