
<div class="main-content">
  <div class="main-content-inner">
    <div class="breadcrumbs ace-save-state wangan-breadcrumbs" id="breadcrumbs">
      <div class="page-content">
        <div class="page-header">
          <h1>账号管理
            <small>
              <i class="ace-icon fa fa-angle-double-right"></i>
              人员管理
            </small>
          </h1>
        </div><!-- /.page-header -->
        <div class="row">
          <div class="col-xs-12 wa-mb10">
            <div class="text-right">
              <button id="btn_add" class="btn btn-sm btn-primary"><i class="ace-icon fa fa-plus"></i>新增人员</button>
            </div>

          </div>
          <div class="col-xs-12">
            <!--<div id="user-table" class="table table-striped table-bordered table-condensed"></div>-->
            <table id="user-table" class="table table-bordered table-hover">
            </table>
            <div id="user-pager"></div>
            <!-----用来计算单元格内容实际长度的--------->
            <div class="ui-jqgrid ui-widget ui-widget-content ui-corner-all" style="position:absolute;top:-9999px">
              <div class="ui-jqgrid-view">
                <div class="ui-jqgrid-bdiv">
                  <div style="position: relative;">
                    <table cellspacing="0" cellpadding="0" border="0">
                      <tr class="ui-widget-content jqgrow ui-row-ltr" style="table-layout:table">
                        <td id="tdCompute" style="background:#eee;width:auto"></td>
                      </tr>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div><!-- /.col -->
      </div><!-- /.row -->
    </div><!-- /.page-content -->
  </div>
</div><!-- /.main-content -->
<a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
  <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
</a>
<!--提示-->
<div id="alertDanger" class="alert alert-danger"
     style="position:absolute;top:35%;left:35%;width:500px;height:50px;display:none;z-index:99999!important">
  <strong></strong>
</div>
<!-- 验证用户身份-->
<div class="modal" id="validateUserDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
  <form class="form-horizontal" id="validateUserForm" method="post" action="" data-toggle="validator" role="form">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header" style="color: white;background: deepskyblue">
          <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
          <h4 class="modal-title">验证用户身份</h4>
        </div>
        <div class="modal-body" style="padding: 10px 50px;">
          <p>请输入登录密码</p>
          <input type="hidden" id="opraType"/>
          <input type="hidden" id="opraStr"/>
          <input type="hidden" id="opraUserId"/>
          <input type="hidden" id="opraGroupId"/>
          <input type="hidden" id="opraUserName"/>
          <input type="hidden" id="opraLoginIp"/>
          <input type="hidden" id="opraDesc"/>
          <input type="hidden" id="opraRemoveId"/>
          <input type="password" id="_password" class="form-control" placeholder="密码" required/>
          <div class="help-block with-errors"></div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
          <button type="submit" class="btn btn-primary" id="validateuser_btn">确定
          </button>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
  </form>
</div><!-- /.modal -->
<!--新增or修改用户-->
<div class="modal fade" id="userDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
  <form class="form-horizontal" id="userForm" method="post" action="" data-toggle="validator" role="form">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header" style="color: white;background: deepskyblue">
          <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
          <h4 class="modal-title" id="userDlgTitle">用户</h4>
        </div>
        <div class="modal-body">
          <input type="hidden" id="app_user_id"/>

          <div class="form-group">
            <label class="col-sm-2 control-label text-right">角色<span style="color:red;">*</span></label>
            <div class="col-sm-10">
              <select class="form-control selectpicker show-tick show-menu-arrow" name="app_group_id" id="app_group_id" readonly="readonly" disabled="disabled"  required>
                <option value="1">管理员</option>
                  <!--<option value="2">安全员</option>-->
                  <option value="2">审计员</option>
                  <option value="3">操作员</option>
              </select>
              <div class="help-block with-errors"></div>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label text-right">用户名<span style="color:red;">*</span></label>
            <div class="col-sm-10">
              <input type="text" minlength="8" maxlength="20" class="form-control required" id="app_user_name" name="app_user_name"
                     placeholder="请输入用户名,长度8-20,不能重复"
                     pattern="^[\u4e00-\u9fa5_a-zA-Z0-9
                       \u3002|\uff1f|\uff01|\uff0c|\u3001|\uff1b|\uff1a|\u201c|\u201d|\u2018|\u2019|\uff08|\uff09|\u300a|\u300b|\u3008|\u3009|\u3010|\u3011|\u300e|\u300f|\u300c|\u300d|\ufe43|\ufe44|\u3014|\u3015|\u2026|\u2014|\uff5e|\ufe4f|\uffe5
                       ,.,@-]+$"
                     required/>
              <div class="help-block with-errors"></div>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label text-right">人员说明</label>
            <div class="col-sm-10">
              <input type="text" class="form-control" id="desc" name="desc"
                     placeholder="请输入人员说明(选填)"
                     pattern="^[\u4e00-\u9fa5_a-zA-Z0-9
                       \u3002|\uff1f|\uff01|\uff0c|\u3001|\uff1b|\uff1a|\u201c|\u201d|\u2018|\u2019|\uff08|\uff09|\u300a|\u300b|\u3008|\u3009|\u3010|\u3011|\u300e|\u300f|\u300c|\u300d|\ufe43|\ufe44|\u3014|\u3015|\u2026|\u2014|\uff5e|\ufe4f|\uffe5
                       ,.,@-]+$"/>
              <div class="help-block with-errors"></div>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label text-right">口令<span style="color:red;">*</span></label>
            <div class="col-sm-10">
              <input type="password" minlength="8" maxlength="20" class="form-control required" id="password"
                     name="remark"
                     onpaste="return false" oncontextmenu="return false" oncopy="return false" oncut="return false"
                     placeholder="最少8位,最大20位,需包含大&小写字母,数字,特殊字符"
                     pattern="(?=^.{8,}$)((?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$"
                     required/>
              <div class="help-block with-errors"></div>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label text-right">确认口令<span style="color:red;">*</span></label>
            <div class="col-sm-10">
              <input type="password" class="form-control required" id="confirmed_password"
                     name="confirmed_password" data-match="#password"
                     onpaste="return false" oncontextmenu="return false" oncopy="return false" oncut="return false"
                     placeholder="请确认口令" required/>
              <div class="help-block with-errors"></div>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label text-right">登录机IP<span style="color:red;">*</span></label>
            <div class="col-sm-10">
              <input type="text" data-minlength="7" class="form-control required" id="login_ip" name="remark"
                     placeholder="请输入登录机ip"
                     pattern="(?=(\b|\D))(((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))\.){3}((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))(?=(\b|\D))"
                     required/>
              <div class="help-block with-errors"></div>
            </div>
          </div>
<!--          <div class="form-group hidden" id="ukeySwitch">-->
<!--            <label class="col-sm-2 control-label text-right">是否同时修改UKey</label>-->
<!--            <div class="col-sm-10">-->
<!--              <label>-->
<!--                <input name="switch-field-1" class="ace ace-switch" type="checkbox">-->
<!--                <span class="lbl" title="是否同时修改UKey"></span>-->
<!--              </label>-->
<!--                <p style="color:red;font-size: 14px"><b>* 若同时修改UKey,需先拔下当前UKey再插入新UKey，否则会修改失败</b></p>-->
<!--            </div>-->
<!--          </div>-->
<!--          <div class="form-group hidden" id="ukeyDesc">-->
<!--            <label class="col-sm-2 control-label text-right"></label>-->
<!--            <div class="col-sm-10">-->
<!--                <p style="color:red;font-size: 14px"><b>* 请拔下当前管理员UKey并插入新UKey！</b></p>-->
<!--            </div>-->
<!--          </div>-->
        </div>
        <div class="modal-footer">
          <button type="submit" id="sbbtn" class="btn btn-primary">提交</button>
          <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
        </div>
      </div>
    </div>
  </form>
</div>
<!-- 查看权限对话框 -->
<div class="modal fade" id="authorityDlg">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
          <span class="sr-only">Close</span>
        </button>
        <h4 class="modal-title">查看此账号权限</h4>
      </div>
      <div class="modal-body">
        <ol class="dd-list">
          <li class="dd-item" id="authority_list" data-id="2">
            <button data-action="collapse" type="button" style="display: block;">Collapse</button>
            <button data-action="expand" type="button" style="display: none;">Expand</button>
            <div class="dd-handle">Item 2</div>

            <ol class="dd-list">
              <li class="dd-item" data-id="3">
                <div class="dd-handle">
                  Item 3
                  <a data-rel="tooltip" data-placement="left" title="" href="#"
                     class="pull-right tooltip-info btn btn-primary btn-mini btn-white btn-bold"
                     data-original-title="Change Date">
                    <i class="bigger-120 ace-icon fa fa-calendar"></i>
                  </a>
                </div>
              </li>
              <li class="dd-item" data-id="4">
                <div class="dd-handle">
                  <span class="orange">Item 4</span>
                  <span class="lighter grey">
                    &nbsp;with some description
                  </span>
                </div>
              </li>
            </ol>
          </li>
        </ol>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" data-dismiss="modal">确定</button>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<!-- 确认对话框 -->
<div class="modal fade" id="confirmDlg">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
          <span class="sr-only">Close</span>
        </button>
        <h4 class="modal-title">删除</h4>
      </div>
      <div class="modal-body">
        <input type="hidden" id="remove_user_id"/>
        确定删除用户？
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" data-dismiss="modal" id="removeUserBtn">确定</button>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<script>
  // console.log(require.cache)
  delete require.cache[require.resolve('./script/view/account_mgr/personal_mgr.js')];
  require('./script/view/account_mgr/personal_mgr.js');
  $('#userForm').validator()
</script>
