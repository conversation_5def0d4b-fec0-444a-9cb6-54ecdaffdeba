/**
 * Bootstrap Multiselect (https://github.com/davidstutz/bootstrap-multiselect)
 * 
 * Apache License, Version 2.0:
 * Copyright (c) 2012 - 2015 <PERSON>
 * 
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a
 * copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 * 
 * BSD 3-Clause License:
 * Copyright (c) 2012 - 2015 <PERSON>
 * All rights reserved.
 * 
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *    - Redistributions of source code must retain the above copyright notice,
 *      this list of conditions and the following disclaimer.
 *    - Redistributions in binary form must reproduce the above copyright notice,
 *      this list of conditions and the following disclaimer in the documentation
 *      and/or other materials provided with the distribution.
 *    - Neither the name of <PERSON> nor the names of its contributors may be
 *      used to endorse or promote products derived from this software without
 *      specific prior written permission.
 * 
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF
 * ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
!function(a){"use strict";function b(a,b){for(var c=0;c<a.length;++c)b(a[c],c)}function c(b,c){this.$select=a(b),this.$select.attr("data-placeholder")&&(c.nonSelectedText=this.$select.data("placeholder")),this.options=this.mergeOptions(a.extend({},c,this.$select.data())),this.originalOptions=this.$select.clone()[0].options,this.query="",this.searchTimeout=null,this.lastToggledInput=null,this.options.multiple="multiple"===this.$select.attr("multiple"),this.options.onChange=a.proxy(this.options.onChange,this),this.options.onDropdownShow=a.proxy(this.options.onDropdownShow,this),this.options.onDropdownHide=a.proxy(this.options.onDropdownHide,this),this.options.onDropdownShown=a.proxy(this.options.onDropdownShown,this),this.options.onDropdownHidden=a.proxy(this.options.onDropdownHidden,this),this.buildContainer(),this.buildButton(),this.buildDropdown(),this.buildSelectAll(),this.buildDropdownOptions(),this.buildFilter(),this.updateButtonText(),this.updateSelectAll(),this.options.disableIfEmpty&&a("option",this.$select).length<=0&&this.disable(),this.$select.hide().after(this.$container)}"undefined"!=typeof ko&&ko.bindingHandlers&&!ko.bindingHandlers.multiselect&&(ko.bindingHandlers.multiselect={after:["options","value","selectedOptions"],init:function(b,c,d,e,f){var g=a(b),h=ko.toJS(c());if(g.multiselect(h),d.has("options")){var i=d.get("options");ko.isObservable(i)&&ko.computed({read:function(){i(),setTimeout(function(){var a=g.data("multiselect");a&&a.updateOriginalOptions(),g.multiselect("rebuild")},1)},disposeWhenNodeIsRemoved:b})}if(d.has("value")){var j=d.get("value");ko.isObservable(j)&&ko.computed({read:function(){j(),setTimeout(function(){g.multiselect("refresh")},1)},disposeWhenNodeIsRemoved:b}).extend({rateLimit:100,notifyWhenChangesStop:!0})}if(d.has("selectedOptions")){var k=d.get("selectedOptions");ko.isObservable(k)&&ko.computed({read:function(){k(),setTimeout(function(){g.multiselect("refresh")},1)},disposeWhenNodeIsRemoved:b}).extend({rateLimit:100,notifyWhenChangesStop:!0})}ko.utils.domNodeDisposal.addDisposeCallback(b,function(){g.multiselect("destroy")})},update:function(b,c,d,e,f){var g=a(b),h=ko.toJS(c());g.multiselect("setOptions",h),g.multiselect("rebuild")}}),c.prototype={defaults:{buttonText:function(b,c){if(0===b.length)return this.nonSelectedText;if(this.allSelectedText&&b.length===a("option",a(c)).length&&1!==a("option",a(c)).length&&this.multiple)return this.selectAllNumber?this.allSelectedText+" ("+b.length+")":this.allSelectedText;if(b.length>this.numberDisplayed)return b.length+" "+this.nSelectedText;var d="",e=this.delimiterText;return b.each(function(){var b=void 0!==a(this).attr("label")?a(this).attr("label"):a(this).text();d+=b+e}),d.substr(0,d.length-2)},buttonTitle:function(b,c){if(0===b.length)return this.nonSelectedText;var d="",e=this.delimiterText;return b.each(function(){var b=void 0!==a(this).attr("label")?a(this).attr("label"):a(this).text();d+=b+e}),d.substr(0,d.length-2)},optionLabel:function(b){return a(b).attr("label")||a(b).text()},onChange:function(a,b){},onDropdownShow:function(a){},onDropdownHide:function(a){},onDropdownShown:function(a){},onDropdownHidden:function(a){},onSelectAll:function(){},enableHTML:!1,buttonClass:"btn btn-default",inheritClass:!1,buttonWidth:"auto",buttonContainer:'<div class="btn-group" />',dropRight:!1,selectedClass:"active",maxHeight:!1,checkboxName:!1,includeSelectAllOption:!1,includeSelectAllIfMoreThan:0,selectAllText:" Select all",selectAllValue:"multiselect-all",selectAllName:!1,selectAllNumber:!0,enableFiltering:!1,enableCaseInsensitiveFiltering:!1,enableClickableOptGroups:!1,filterPlaceholder:"Search",filterBehavior:"text",includeFilterClearBtn:!0,preventInputChangeEvent:!1,nonSelectedText:"None selected",nSelectedText:"selected",allSelectedText:"All selected",numberDisplayed:3,disableIfEmpty:!1,delimiterText:", ",templates:{button:'<button type="button" class="multiselect dropdown-toggle" data-toggle="dropdown"><span class="multiselect-selected-text"></span> <b class="caret"></b></button>',ul:'<ul class="multiselect-container dropdown-menu"></ul>',filter:'<li class="multiselect-item filter"><div class="input-group"><span class="input-group-addon"><i class="glyphicon glyphicon-search"></i></span><input class="form-control multiselect-search" type="text"></div></li>',filterClearBtn:'<span class="input-group-btn"><button class="btn btn-default multiselect-clear-filter" type="button"><i class="glyphicon glyphicon-remove-circle"></i></button></span>',li:'<li><a tabindex="0"><label></label></a></li>',divider:'<li class="multiselect-item divider"></li>',liGroup:'<li class="multiselect-item multiselect-group"><label></label></li>'}},constructor:c,buildContainer:function(){this.$container=a(this.options.buttonContainer),this.$container.on("show.bs.dropdown",this.options.onDropdownShow),this.$container.on("hide.bs.dropdown",this.options.onDropdownHide),this.$container.on("shown.bs.dropdown",this.options.onDropdownShown),this.$container.on("hidden.bs.dropdown",this.options.onDropdownHidden)},buildButton:function(){this.$button=a(this.options.templates.button).addClass(this.options.buttonClass),this.$select.attr("class")&&this.options.inheritClass&&this.$button.addClass(this.$select.attr("class")),this.$select.prop("disabled")?this.disable():this.enable(),this.options.buttonWidth&&"auto"!==this.options.buttonWidth&&(this.$button.css({width:this.options.buttonWidth,overflow:"hidden","text-overflow":"ellipsis"}),this.$container.css({width:this.options.buttonWidth}));var b=this.$select.attr("tabindex");b&&this.$button.attr("tabindex",b),this.$container.prepend(this.$button)},buildDropdown:function(){this.$ul=a(this.options.templates.ul),this.options.dropRight&&this.$ul.addClass("pull-right"),this.options.maxHeight&&this.$ul.css({"max-height":this.options.maxHeight+"px","overflow-y":"auto","overflow-x":"hidden"}),this.$container.append(this.$ul)},buildDropdownOptions:function(){this.$select.children().each(a.proxy(function(b,c){var d=a(c),e=d.prop("tagName").toLowerCase();d.prop("value")!==this.options.selectAllValue&&("optgroup"===e?this.createOptgroup(c):"option"===e&&("divider"===d.data("role")?this.createDivider():this.createOptionValue(c)))},this)),a("li input",this.$ul).on("change",a.proxy(function(b){var c=a(b.target),d=c.prop("checked")||!1,e=c.val()===this.options.selectAllValue;this.options.selectedClass&&(d?c.closest("li").addClass(this.options.selectedClass):c.closest("li").removeClass(this.options.selectedClass));var f=c.val(),g=this.getOptionByValue(f),h=a("option",this.$select).not(g),i=a("input",this.$container).not(c);return e&&(d?this.selectAll():this.deselectAll()),e||(d?(g.prop("selected",!0),this.options.multiple?g.prop("selected",!0):(this.options.selectedClass&&a(i).closest("li").removeClass(this.options.selectedClass),a(i).prop("checked",!1),h.prop("selected",!1),this.$button.click()),"active"===this.options.selectedClass&&h.closest("a").css("outline","")):g.prop("selected",!1)),this.$select.change(),this.updateButtonText(),this.updateSelectAll(),this.options.onChange(g,d),this.options.preventInputChangeEvent?!1:void 0},this)),a("li a",this.$ul).on("mousedown",function(a){return a.shiftKey?!1:void 0}),a("li a",this.$ul).on("touchstart click",a.proxy(function(b){b.stopPropagation();var c=a(b.target);if(b.shiftKey&&this.options.multiple){c.is("label")&&(b.preventDefault(),c=c.find("input"),c.prop("checked",!c.prop("checked")));var d=c.prop("checked")||!1;if(null!==this.lastToggledInput&&this.lastToggledInput!==c){var e=c.closest("li").index(),f=this.lastToggledInput.closest("li").index();if(e>f){var g=f;f=e,e=g}++f;var h=this.$ul.find("li").slice(e,f).find("input");h.prop("checked",d),this.options.selectedClass&&h.closest("li").toggleClass(this.options.selectedClass,d);for(var i=0,j=h.length;j>i;i++){var k=a(h[i]),l=this.getOptionByValue(k.val());l.prop("selected",d)}}c.trigger("change")}c.is("input")&&!c.closest("li").is(".multiselect-item")&&(this.lastToggledInput=c),c.blur()},this)),this.$container.off("keydown.multiselect").on("keydown.multiselect",a.proxy(function(b){if(!a('input[type="text"]',this.$container).is(":focus"))if(9===b.keyCode&&this.$container.hasClass("open"))this.$button.click();else{var c=a(this.$container).find("li:not(.divider):not(.disabled) a").filter(":visible");if(!c.length)return;var d=c.index(c.filter(":focus"));38===b.keyCode&&d>0?d--:40===b.keyCode&&d<c.length-1?d++:~d||(d=0);var e=c.eq(d);if(e.focus(),32===b.keyCode||13===b.keyCode){var f=e.find("input");f.prop("checked",!f.prop("checked")),f.change()}b.stopPropagation(),b.preventDefault()}},this)),this.options.enableClickableOptGroups&&this.options.multiple&&a("li.multiselect-group",this.$ul).on("click",a.proxy(function(b){b.stopPropagation();var c=a(b.target).parent(),d=c.nextUntil("li.multiselect-group"),e=d.filter(":visible:not(.disabled)"),f=!0,g=e.find("input");g.each(function(){f=f&&a(this).prop("checked")}),g.prop("checked",!f).trigger("change")},this))},createOptionValue:function(b){var c=a(b);c.is(":selected")&&c.prop("selected",!0);var d=this.options.optionLabel(b),e=c.val(),f=this.options.multiple?"checkbox":"radio",g=a(this.options.templates.li),h=a("label",g);h.addClass(f),this.options.enableHTML?h.html(" "+d):h.text(" "+d);var i=a("<input/>").attr("type",f).addClass("ace");this.options.checkboxName&&i.attr("name",this.options.checkboxName),h.prepend(i),i.after('<span class="lbl" />');var j=c.prop("selected")||!1;i.val(e),e===this.options.selectAllValue&&(g.addClass("multiselect-item multiselect-all"),i.parent().parent().addClass("multiselect-all")),h.attr("title",c.attr("title")),this.$ul.append(g),c.is(":disabled")&&i.attr("disabled","disabled").prop("disabled",!0).closest("a").attr("tabindex","-1").closest("li").addClass("disabled"),i.prop("checked",j),j&&this.options.selectedClass&&i.closest("li").addClass(this.options.selectedClass)},createDivider:function(b){var c=a(this.options.templates.divider);this.$ul.append(c)},createOptgroup:function(b){var c=a(b).prop("label"),d=a(this.options.templates.liGroup);this.options.enableHTML?a("label",d).html(c):a("label",d).text(c),this.options.enableClickableOptGroups&&d.addClass("multiselect-group-clickable"),this.$ul.append(d),a(b).is(":disabled")&&d.addClass("disabled"),a("option",b).each(a.proxy(function(a,b){this.createOptionValue(b)},this))},buildSelectAll:function(){"number"==typeof this.options.selectAllValue&&(this.options.selectAllValue=this.options.selectAllValue.toString());var b=this.hasSelectAll();if(!b&&this.options.includeSelectAllOption&&this.options.multiple&&a("option",this.$select).length>this.options.includeSelectAllIfMoreThan){this.options.includeSelectAllDivider&&this.$ul.prepend(a(this.options.templates.divider));var c=a(this.options.templates.li);a("label",c).addClass("checkbox"),this.options.enableHTML?a("label",c).html(" "+this.options.selectAllText):a("label",c).text(" "+this.options.selectAllText),this.options.selectAllName?a("label",c).prepend('<input type="checkbox" name="'+this.options.selectAllName+'" />'):a("label",c).prepend('<input type="checkbox" />');var d=a("input",c);d.val(this.options.selectAllValue),c.addClass("multiselect-item multiselect-all"),d.parent().parent().addClass("multiselect-all"),this.$ul.prepend(c),d.prop("checked",!1)}},buildFilter:function(){if(this.options.enableFiltering||this.options.enableCaseInsensitiveFiltering){var b=Math.max(this.options.enableFiltering,this.options.enableCaseInsensitiveFiltering);if(this.$select.find("option").length>=b){if(this.$filter=a(this.options.templates.filter),a("input",this.$filter).attr("placeholder",this.options.filterPlaceholder),this.options.includeFilterClearBtn){var c=a(this.options.templates.filterClearBtn);c.on("click",a.proxy(function(b){clearTimeout(this.searchTimeout),this.$filter.find(".multiselect-search").val(""),a("li",this.$ul).show().removeClass("filter-hidden"),this.updateSelectAll()},this)),this.$filter.find(".input-group").append(c)}this.$ul.prepend(this.$filter),this.$filter.val(this.query).on("click",function(a){a.stopPropagation()}).on("input keydown",a.proxy(function(b){13===b.which&&b.preventDefault(),clearTimeout(this.searchTimeout),this.searchTimeout=this.asyncFunction(a.proxy(function(){if(this.query!==b.target.value){this.query=b.target.value;var c,d;a.each(a("li",this.$ul),a.proxy(function(b,e){var f=a("input",e).length>0?a("input",e).val():"",g=a("label",e).text(),h="";if("text"===this.options.filterBehavior?h=g:"value"===this.options.filterBehavior?h=f:"both"===this.options.filterBehavior&&(h=g+"\n"+f),f!==this.options.selectAllValue&&g){var i=!1;this.options.enableCaseInsensitiveFiltering&&h.toLowerCase().indexOf(this.query.toLowerCase())>-1?i=!0:h.indexOf(this.query)>-1&&(i=!0),a(e).toggle(i).toggleClass("filter-hidden",!i),a(e).hasClass("multiselect-group")?(c=e,d=i):(i&&a(c).show().removeClass("filter-hidden"),!i&&d&&a(e).show().removeClass("filter-hidden"))}},this))}this.updateSelectAll()},this),300,this)},this))}}},destroy:function(){this.$container.remove(),this.$select.show(),this.$select.data("multiselect",null)},refresh:function(){a("option",this.$select).each(a.proxy(function(b,c){var d=a("li input",this.$ul).filter(function(){return a(this).val()===a(c).val()});a(c).is(":selected")?(d.prop("checked",!0),this.options.selectedClass&&d.closest("li").addClass(this.options.selectedClass)):(d.prop("checked",!1),this.options.selectedClass&&d.closest("li").removeClass(this.options.selectedClass)),a(c).is(":disabled")?d.attr("disabled","disabled").prop("disabled",!0).closest("li").addClass("disabled"):d.prop("disabled",!1).closest("li").removeClass("disabled")},this)),this.updateButtonText(),this.updateSelectAll()},select:function(b,c){a.isArray(b)||(b=[b]);for(var d=0;d<b.length;d++){var e=b[d];if(null!==e&&void 0!==e){var f=this.getOptionByValue(e),g=this.getInputByValue(e);void 0!==f&&void 0!==g&&(this.options.multiple||this.deselectAll(!1),this.options.selectedClass&&g.closest("li").addClass(this.options.selectedClass),g.prop("checked",!0),f.prop("selected",!0),c&&this.options.onChange(f,!0))}}this.updateButtonText(),this.updateSelectAll()},clearSelection:function(){this.deselectAll(!1),this.updateButtonText(),this.updateSelectAll()},deselect:function(b,c){a.isArray(b)||(b=[b]);for(var d=0;d<b.length;d++){var e=b[d];if(null!==e&&void 0!==e){var f=this.getOptionByValue(e),g=this.getInputByValue(e);void 0!==f&&void 0!==g&&(this.options.selectedClass&&g.closest("li").removeClass(this.options.selectedClass),g.prop("checked",!1),f.prop("selected",!1),c&&this.options.onChange(f,!1))}}this.updateButtonText(),this.updateSelectAll()},selectAll:function(b,c){var b="undefined"==typeof b?!0:b,d=a("li input[type='checkbox']:enabled",this.$ul),e=d.filter(":visible"),f=d.length,g=e.length;if(b?(e.prop("checked",!0),a("li:not(.divider):not(.disabled)",this.$ul).filter(":visible").addClass(this.options.selectedClass)):(d.prop("checked",!0),a("li:not(.divider):not(.disabled)",this.$ul).addClass(this.options.selectedClass)),f===g||b===!1)a("option:enabled",this.$select).prop("selected",!0);else{var h=e.map(function(){return a(this).val()}).get();a("option:enabled",this.$select).filter(function(b){return-1!==a.inArray(a(this).val(),h)}).prop("selected",!0)}c&&this.options.onSelectAll()},deselectAll:function(b){var b="undefined"==typeof b?!0:b;if(b){var c=a("li input[type='checkbox']:not(:disabled)",this.$ul).filter(":visible");c.prop("checked",!1);var d=c.map(function(){return a(this).val()}).get();a("option:enabled",this.$select).filter(function(b){return-1!==a.inArray(a(this).val(),d)}).prop("selected",!1),this.options.selectedClass&&a("li:not(.divider):not(.disabled)",this.$ul).filter(":visible").removeClass(this.options.selectedClass)}else a("li input[type='checkbox']:enabled",this.$ul).prop("checked",!1),a("option:enabled",this.$select).prop("selected",!1),this.options.selectedClass&&a("li:not(.divider):not(.disabled)",this.$ul).removeClass(this.options.selectedClass)},rebuild:function(){this.$ul.html(""),this.options.multiple="multiple"===this.$select.attr("multiple"),this.buildSelectAll(),this.buildDropdownOptions(),this.buildFilter(),this.updateButtonText(),this.updateSelectAll(),this.options.disableIfEmpty&&a("option",this.$select).length<=0?this.disable():this.enable(),this.options.dropRight&&this.$ul.addClass("pull-right")},dataprovider:function(c){var d=0,e=this.$select.empty();a.each(c,function(c,f){var g;a.isArray(f.children)?(d++,g=a("<optgroup/>").attr({label:f.label||"Group "+d,disabled:!!f.disabled}),b(f.children,function(b){g.append(a("<option/>").attr({value:b.value,label:b.label||b.value,title:b.title,selected:!!b.selected,disabled:!!b.disabled}))})):g=a("<option/>").attr({value:f.value,label:f.label||f.value,title:f.title,selected:!!f.selected,disabled:!!f.disabled}),e.append(g)}),this.rebuild()},enable:function(){this.$select.prop("disabled",!1),this.$button.prop("disabled",!1).removeClass("disabled")},disable:function(){this.$select.prop("disabled",!0),this.$button.prop("disabled",!0).addClass("disabled")},setOptions:function(a){this.options=this.mergeOptions(a)},mergeOptions:function(b){return a.extend(!0,{},this.defaults,this.options,b)},hasSelectAll:function(){return a("li.multiselect-all",this.$ul).length>0},updateSelectAll:function(){if(this.hasSelectAll()){var b=a("li:not(.multiselect-item):not(.filter-hidden) input:enabled",this.$ul),c=b.length,d=b.filter(":checked").length,e=a("li.multiselect-all",this.$ul),f=e.find("input");d>0&&d===c?(f.prop("checked",!0),e.addClass(this.options.selectedClass),this.options.onSelectAll()):(f.prop("checked",!1),e.removeClass(this.options.selectedClass))}},updateButtonText:function(){var b=this.getSelected();this.options.enableHTML?a(".multiselect .multiselect-selected-text",this.$container).html(this.options.buttonText(b,this.$select)):a(".multiselect .multiselect-selected-text",this.$container).text(this.options.buttonText(b,this.$select)),a(".multiselect",this.$container).attr("title",this.options.buttonTitle(b,this.$select))},getSelected:function(){return a("option",this.$select).filter(":selected")},getOptionByValue:function(b){for(var c=a("option",this.$select),d=b.toString(),e=0;e<c.length;e+=1){var f=c[e];if(f.value===d)return a(f)}},getInputByValue:function(b){for(var c=a("li input",this.$ul),d=b.toString(),e=0;e<c.length;e+=1){var f=c[e];if(f.value===d)return a(f)}},updateOriginalOptions:function(){this.originalOptions=this.$select.clone()[0].options},asyncFunction:function(a,b,c){var d=Array.prototype.slice.call(arguments,3);return setTimeout(function(){a.apply(c||window,d)},b)},setAllSelectedText:function(a){this.options.allSelectedText=a,this.updateButtonText()}},a.fn.multiselect=function(b,d,e){return this.each(function(){var f=a(this).data("multiselect"),g="object"==typeof b&&b;f||(f=new c(this,g),a(this).data("multiselect",f)),"string"==typeof b&&(f[b](d,e),"destroy"===b&&a(this).data("multiselect",!1))})},a.fn.multiselect.Constructor=c,a(function(){a("select[data-role=multiselect]").multiselect()})}(window.jQuery);