<div class="page-header" style="padding-left: 18%;">
  <h1>设置
    <small>
      <i class="ace-icon fa fa-angle-double-right"></i>
      修改密码
    </small>
  </h1>
</div><!-- /.page-header -->
<div class="row">
  <div class="col-xs-12 wa-mb10">
    <form class="form-horizontal" id="modifyPsdForm" role="form">
      <input type="hidden" id="app_user_id"/>
      <div class="form-group">
        <div class="col-md-8 text-right"></div>
      </div>
      <div class="form-group">
        <label class="col-sm-3 control-label text-right">用户名<span style="color:red;">*</span></label>
        <div class="col-sm-5">
          <input type="text" class="form-control required" id="app_user_name" name="app_user_name" readonly/>
        </div>
      </div>
      <div class="form-group">
        <label class="col-sm-3 control-label text-right">口令<span style="color:red;">*</span></label>
        <div class="col-sm-5">
          <input type="password" minlength="8" maxlength="20" class="form-control required" id="password"
                 name="remark"
                 placeholder="最少8位,最大20位,需包含大&小写字母,数字,特殊字符"
                 pattern="(?=^.{8,}$)((?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$"
                 required/>
          <div class="help-block with-errors"></div>
        </div>
      </div>
      <div class="form-group">
        <label class="col-sm-3 control-label text-right">确认口令<span style="color:red;">*</span></label>
        <div class="col-sm-5">
          <input type="password" class="form-control required" id="confirmed_password"
                 name="confirmed_password" data-match="#password"
                 placeholder="请确认口令" required/>
          <div class="help-block with-errors"></div>
        </div>
      </div>
      <div class="form-group " id="ukeySwitch">
            <label class="col-sm-3 control-label text-right">是否同时修改UKey</label>
            <div class="col-sm-5">
                <label>
                    <input id="buttonState" name="switch-field-1" class="ace ace-switch" type="checkbox">
                    <span class="lbl" title="是否同时修改UKey"></span>
                </label>
                <p style="color:red;font-size: 14px"><b>* 若同时修改UKey,需先拔下当前UKey再插入新UKey，否则会修改失败</b></p>
            </div>
      </div>
      <div class="col-md-offset-3 col-md-9">
        <button type="submit" class="btn btn-sm btn-primary" id="btn_modifypsd_save">
          <i class="ace-icon fa fa-check bigger-110"></i>保存
        </button>
      </div>
    </form>
  </div><!-- /.col -->
</div><!-- /.row -->
<a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
  <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
</a>
<script>
  delete require.cache[require.resolve('./script/view/modify_psd.js')];
  require('./script/view/modify_psd.js');
  $('#modifyPsdForm').validator()
</script>