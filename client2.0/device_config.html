<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta charset="utf-8" />
    <title>装置参数配置</title>

    <meta name="description" content="event param" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />

    <!-- bootstrap & fontawesome -->
    <link rel="stylesheet" href="assets/css/bootstrap.min.css" />
    <link rel="stylesheet" href="assets/font-awesome/4.5.0/css/font-awesome.min.css" />

    <!-- text fonts -->
    <link rel="stylesheet" href="assets/css/fonts.googleapis.com.css" />

    <!-- ace styles -->
    <link rel="stylesheet" href="assets/css/ace.min.css" />

    <script src="assets/js/jquery-2.1.4.min.js"></script>
</head>

<body class="no-skin">
<div class="main-content">
    <div class="main-content-inner">
        <div class="breadcrumbs ace-save-state" id="breadcrumbs">
            <div class="page-content">
                <div class="page-header">
                    <h1>
                        账号管理
                        <i class="ace-icon fa fa-angle-double-right"></i>
                        人员管理
                    </h1>
                </div><!-- /.page-header -->

                <div class="row">
                    <div class="col-xs-12">
                        <div class="pull-right">
                            <button id="btn_add" class="btn btn-primary">新增人员</button>
                        </div>
                        <div id="user-table" class="table table-striped table-bordered table-condensed"></div>
                    </div>
                </div><!-- /.col -->
            </div><!-- /.row -->
        </div><!-- /.page-content -->
    </div>
</div><!-- /.main-content -->

<div class="footer">
    <div class="footer-inner">
        <div class="footer-content">
						<span class="bigger-120">
							<span class="blue bolder">山东金煜电子</span>
							网络安全装置平台 &copy; 2018
						</span>
        </div>
    </div>
</div>

<!--提示-->
<div id="alertDanger" class="alert alert-danger"
     style="position:absolute;top:35%;left:35%;width:500px;height:50px;display:none">
    <strong></strong>
</div>

<!-- basic scripts -->
<script src="assets/js/jquery-2.1.4.min.js"></script>

<script type="text/javascript">
    if('ontouchstart' in document.documentElement) document.write("<script src='assets/js/jquery.mobile.custom.min.js'>"+"<"+"/script>");
</script>

<!-- inline scripts related to this page -->
<script type="text/javascript">

</script>
<script>
    // You can also require other files to run in this process
    require('./script/view/login.js');
</script>
</body>
</html>
