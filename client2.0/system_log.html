<div class="main-content">
    <div class="main-content-inner">
        <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs">
            <div class="page-content">
                <div class="page-header">
                    <h1>系统调试
                        <small>
                            <i class="ace-icon fa fa-angle-double-right"></i>
                            系统日志
                        </small>
                    </h1>
                </div><!-- /.page-header -->
                <div class="row wa-mb10">
                    <div class="col-xs-12 text-right">
                        <!--<button class="btn btn-sm btn-success" id="see_cert"><i class="ace-icon fa fa-eye"></i>查看</button>-->
                        <button class="btn btn-sm btn-primary wa-mr5" id="all_export">全部导出</button>
                        <button class="btn btn-sm btn-primary wa-mr5" id="all_clear">全部清空</button>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12">
                        <table id="system-log-tb" class="table table-bordered table-hover"></table>
                        <div id="system-log-pager"></div>
                        <!-----用来计算单元格内容实际长度的--------->
                        <div class="ui-jqgrid ui-widget ui-widget-content ui-corner-all" style="position:absolute;top:-9999px">
                            <div class="ui-jqgrid-view">
                                <div class="ui-jqgrid-bdiv">
                                    <div style="position: relative;">
                                        <table cellspacing="0" cellpadding="0" border="0">
                                            <tr class="ui-widget-content jqgrow ui-row-ltr" style="table-layout:table">
                                                <td id="tdCompute" style="background:#eee;width:auto"></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!-- /.page-content -->
        </div>
    </div><!-- /.main-content -->
    <!-- 验证用户身份-->
    <div class="modal" id="validateUserDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <form class="form-horizontal" id="validateUserForm" method="post" action="" data-toggle="validator" role="form">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header" style="color: white;background: deepskyblue">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                        <h4 class="modal-title">验证用户身份</h4>
                    </div>
                    <div class="modal-body" style="padding: 10px 50px;">
                        <p>请输入登录密码</p>
                        <input type="hidden" id="opraType"/>
                        <input type="hidden" id="log_file_name"/>
                        <input type="password" id="password" class="form-control" placeholder="密码" required/>
                        <div class="help-block with-errors"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary" id="validateuser_btn">确定
                        </button>
                    </div>
                </div><!-- /.modal-content -->
            </div><!-- /.modal-dialog -->
        </form>
    </div><!-- /.modal -->
    <div class="modal fade" id="confirmDelDlg">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                        <span class="sr-only">Close</span>
                    </button>
                    <h4 class="modal-title">清空</h4>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="file_name"/>
                    确定清空日志文件？
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" data-dismiss="modal" id="removeFileBtn">确定</button>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
    <!-- 选择文件压缩格式 -->
    <div class="modal fade" id="selectCompressFormatDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <form class="form-horizontal" id="selectCompressFormatForm" method="post" action="" data-toggle="validator" role="form">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                            <span class="sr-only">Close</span>
                        </button>
                        <h4 class="modal-title" wa-name="import_tt">请选择</h4>
                    </div>
                    <div class="modal-body">
                        <div style="margin-top: 2px;height: 30px;">
                            <div class="col-sm-9">
                                <label class="col-sm-2 control-label text-right" style="width: 130px;">
                                    <b>选择压缩格式:</b>
                                </label>
                                <div class="col-sm-2" style="width: 130px;">
                                    <select class="form-control" id="compress_log_format" name="compress_log_format">
                                        <option value="">请选择</option>
                                        <option id="nil" value="NIL">不压缩</option>
                                        <option value="ZIP">ZIP</option>
                                        <option value="GZIP">GZIP</option>
                                        <!--<option value="JAR">JAR</option>-->
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" id="file_Compress_btn">确定</button>
                    </div>
                </div><!-- /.modal-content -->
            </div><!-- /.form-horizontal-->
        </form><!-- /.modal-dialog -->
    </div><!-- /.modal -->
    <!-- 导入导出选择对话框 -->
    <div class="modal fade" id="confirmPortDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <form class="form-horizontal" id="confirmPortForm" method="post" action="" data-toggle="validator" role="form">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                            <span class="sr-only">Close</span>
                        </button>
                        <h4 class="modal-title" wa-name="import_tt">请选择</h4>
                    </div>
                    <div class="modal-body">
                        <div class="form-group  hidden">
                            <label class="col-sm-2 control-label text-right" for="select_file">请选择:</label>
                            <div class="col-sm-9">
                                <label class="ace-file-input">
                                    <input type="file" id="select_file" required>
                                </label>
                                <!--<div class="help-block with-errors"></div>-->
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label text-right" for="select_folder">请选择:</label>
                            <div class="col-sm-9">
                                <label class="ace-file-input">
                                    <input type="file" id="select_folder" webkitdirectory directory  required>
                                </label>
                                <!--<div class="help-block with-errors"></div>-->
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" id="port_btn">确定</button>
                    </div>
                </div><!-- /.modal-content -->
            </div><!-- /.form-horizontal-->
        </form><!-- /.modal-dialog -->
    </div><!-- /.modal -->
    <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
        <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
    </a>
    <script>
        // console.log(require.cache)
        delete require.cache[require.resolve('./script/view/system_setup/system_log.js')];
        require('./script/view/system_setup/system_log.js');
    </script>
</div>
