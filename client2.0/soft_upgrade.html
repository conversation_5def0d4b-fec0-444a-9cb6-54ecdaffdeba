<div class="main-content">
    <div class="main-content-inner">
        <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs">
            <div class="page-content">
                <div class="page-header">
                    <h1>系统设置
                        <small>
                            <i class="ace-icon fa fa-angle-double-right"></i>
                            软件升级
                        </small>
                    </h1>
                </div><!-- /.page-header -->
                <div class="row">
                    <div class="col-xs-12">
                        <form class="form-horizontal" id="certificateForm" role="form">
                            <input type="hidden" id="app_certificate_id"/>
                            <div class="form-group" style="margin-bottom: 20px;">
                                <label class="col-sm-3 control-label text-right">
                                    选择文件:
                                </label>
                                <div class="col-sm-6">
                                    <label class="ace-file-input ace-file-multiple">
                                        <input multiple="" type="file" id="id-input-file">
                                    </label>
                                </div>
                            </div>
                            <div class="col-sm-9 text-right" style="padding-right: 16px;">
                                <button type="button" class="btn btn-sm btn-info" id="btn_upgrade">
                                    <i class=" ace-icon ace-icon fa fa-cloud-upload"></i>升级
                                </button>
                            </div>
                        </form>
                    </div><!-- /.col -->
                </div><!-- /.row -->
            </div><!-- /.page-content -->
        </div>
    </div><!-- /.main-content -->
    <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
        <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
    </a>
    <!-- 验证用户身份-->
    <div class="modal" id="validateUserDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         aria-hidden="true">
        <form class="form-horizontal" id="validateUserForm" method="post" action="" data-toggle="validator" role="form">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header" style="color: white;background: deepskyblue">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                        <h4 class="modal-title">验证用户身份</h4>
                    </div>
                    <div class="modal-body" style="padding: 10px 50px;">
                        <p>校验码（请发送给厂家管理员）</p>
                        <input type="text" id="code" style="display: inline-block; width: 87%" readonly placeholder="机器码"
                               required/>
                        <button type="button" id="copyCode" class="btn btn-default" style="display: inline-block;">复制
                        </button>
                        <div class="help-block with-errors"></div>
                    </div>
                    <div class="modal-body" style="padding: 10px 50px;">
                        <p>请输入验证码</p>
                        <input type="hidden" id="opraMsgQueue"/>
                        <textarea rows="3" cols="65" id="machineCode" class="form-control" placeholder="激活码"
                                  required></textarea>
                        <div class="help-block with-errors"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary" id="validateuser_btn">确定
                        </button>
                    </div>
                </div><!-- /.modal-content -->
            </div><!-- /.modal-dialog -->
        </form>
    </div><!-- /.modal -->
    <script>
        // console.log(require.cache)
        delete require.cache[require.resolve('./script/view/system_setup/soft_upgrade.js')];
        require('./script/view/system_setup/soft_upgrade.js');
        // $('#certificateForm').validator();
    </script>
</div>
