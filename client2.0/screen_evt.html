<div class="main-content">
  <div class="main-content-inner">
    <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs">
      <div class="page-content">
        <div class="page-header">
          <h1>屏蔽事件</h1>
        </div><!-- /.page-header -->
        <div class="row wa-mb10">
          <div class="col-xs-12 text-right wa-mb10">
            <a href="javascript:;" type="button" class="btn btn-sm btn-primary" id="add_filted_evt">
              <i class="ace-icon fa fa-plus"></i>新增屏蔽事件</a>
          </div>
        </div>
        <div class="row">
          <div class="col-xs-12">
            <table id="evt-tb" class="table table-bordered table-hover"></table>
            <div id="evt-pager"></div>
            <!-----用来计算单元格内容实际长度的--------->
            <div class="ui-jqgrid ui-widget ui-widget-content ui-corner-all" style="position:absolute;top:-9999px">
              <div class="ui-jqgrid-view">
                <div class="ui-jqgrid-bdiv">
                  <div style="position: relative;">
                    <table cellspacing="0" cellpadding="0" border="0">
                      <tr class="ui-widget-content jqgrow ui-row-ltr" style="table-layout:table">
                        <td id="tdCompute" style="background:#eee;width:auto"></td>
                      </tr>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div><!-- /.page-content -->
    </div>
  </div><!-- /.main-content -->
  <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
    <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
  </a>
  <!-- 验证用户身份-->
  <div class="modal" id="validateUserDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <form class="form-horizontal" id="validateUserForm" method="post" action="" data-toggle="validator" role="form">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header" style="color: white;background: deepskyblue">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            <h4 class="modal-title">验证用户身份</h4>
          </div>
          <div class="modal-body" style="padding: 10px 50px;">
            <p>请输入登录密码</p>
            <input type="hidden" id="opraType"/>
            <input type="hidden" id="opra_evt_id"/>
            <input type="password" id="password" class="form-control" placeholder="密码" required/>
            <div class="help-block with-errors"></div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
            <button type="submit" class="btn btn-primary" id="validateuser_btn">确定
            </button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </form>
  </div><!-- /.modal -->
  <!-- 新增屏蔽事件-->
  <div class="modal" id="addFiltedEventDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <form class="form-horizontal" id="addFiltedEventForm" method="post" action="" data-toggle="validator" role="form">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header" style="color: white;background: deepskyblue">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
            <h4 class="modal-title" id="addFiltedEventTitle" wa-data="add">新增屏蔽事件</h4>
          </div>
          <div class="modal-body">
            <div class="form-group">
              <label class="col-sm-2 control-label no-padding-right"><b>防火墙事件:</b></label>
              <div class="col-sm-10" id="FWEvts"></div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label no-padding-right"><b>横向正向隔离装置事件:</b></label>
              <div class="col-sm-10" id="FIDEvts"></div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label no-padding-right"><b>横向反向隔离装置事件:</b></label>
              <div class="col-sm-10" id="BIDEvts"></div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label no-padding-right"><b>服务器事件:</b></label>
              <div class="col-sm-10" id="SVREvts"></div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label no-padding-right"><b>交换机事件:</b></label>
              <div class="col-sm-10" id="SWEvts"></div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label no-padding-right"><b>网络安全监测装置事件:</b></label>
              <div class="col-sm-10" id="DCDEvts"></div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="submit" id="save_evt_btn" class="btn btn-primary">保存</button>
            <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </form>
  </div><!-- /.modal -->
  <!-- 确认删除屏蔽事件对话框 -->
  <div class="modal fade" id="confirmDlg">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
            <span class="sr-only">Close</span>
          </button>
          <h4 class="modal-title">取消屏蔽事件</h4>
        </div>
        <div class="modal-body">
          <input type="hidden" id="remove_evt_id"/>
          确定取消屏蔽该事件？
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
          <button type="button" class="btn btn-primary" data-dismiss="modal" id="remove_evt_btn">确定</button>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
  </div><!-- /.modal -->
  <!-- 导入导出选择对话框 -->
  <script>
    delete require.cache[require.resolve('./script/view/system_setup/screen_evt.js')];
    require('./script/view/system_setup/screen_evt.js');
  </script>
</div>
