<!--&lt;!&ndash; element-plus &ndash;&gt;-->

<!--<script src="vue/admin/controller/port_param.js"></script>-->

<div class="main-content">
    <div class="main-content-inner">
        <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs" style="height: calc(100% - 100px);">
            <div class="page-content">
                <div class="page-header">
                    <h1>参数配置
                        <small>
                            <i class="ace-icon fa fa-angle-double-right"></i>
                            数据传输参数
                        </small>
                    </h1>
                </div>
                <!-- /.page-header -->
                <div class="row" style="height: 100%">
                    <div id="app" style="height: 100%">
                        <div ref="box" style="height: 100%">
                            <el-form ref="formRef" :model="formData" :rules="formRules" style="width: 100%;">
                                <el-table stripe :height="tableHeight" :data="formData.transferList">
                                    <el-table-column label="功能类型" prop="funcType">
                                        <template #default="scope">
                                            <el-form-item
                                                style="width: 100%"
                                                :prop="'transferList' + `[${scope.$index}]` + '.funcType'"
                                                :rules="formRules.funcType">
                                                    <el-select v-if="true" v-model="scope.row.funcType" :suffix-icon="null" disabled @change="val => changeSelect(scope.$index, val)">
                                                        <el-option v-for="item in Enum" :key="item.code" :label="item.name" :value="item.code" />
                                                    </el-select>
                                                    <span v-else>{{ getNameByCode(scope.row.funcType) }}</span>
                                            </el-form-item>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="报文类型" prop="logType">
                                        <template #default="scope">
                                            <el-form-item
                                                style="width: 100%"
                                                :prop="'transferList' + `[${scope.$index}]` + '.logType'"
                                                :rules="formRules.logType"
                                                >
                                                    <template  v-if="true">
                                                        <el-select v-model="scope.row.logType" :suffix-icon="null" disabled>
                                                            <el-option v-for="item in subEnumMap[scope.row.funcType]" :key="item.code" :label="item.name" :value="item.code" />
                                                        </el-select>
                                                    </template>
                                                    <span v-else>{{ getSubNameByCodeAndParentCode(scope.row.logType, scope.row.funcType) }}</span>

                                            </el-form-item>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="对称加密开关" prop="funcOpt0">
                                        <template #default="scope">
                                            <el-form-item
                                                style="width: 100%"
                                                :prop="'transferList' + `[${scope.$index}]` + '.funcOpt0'"
                                                >
                                                    <template  v-if="true">
                                                        <el-switch v-model="scope.row.funcOpt0" active-value="1" inactive-value="0"/>
                                                    </template>
                                                    <span v-else>{{ scope.row.funcOpt0 === '1' ? '开' : scope.row.funcOpt0 === '0' ? '关' : '' }}</span>
                                            </el-form-item>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="签名开关" prop="funcOpt1">
                                        <template #default="scope">
                                            <el-form-item
                                                    style="width: 100%"
                                                    :prop="'transferList' + `[${scope.$index}]` + '.funcOpt1'"
                                            >
                                                <template  v-if="true">
                                                    <el-switch v-model="scope.row.funcOpt1" active-value="1" inactive-value="0" />
                                                </template>
                                                <span v-else>{{ scope.row.funcOpt1 === '1' ? '开' : scope.row.funcOpt1 === '0' ? '关' : '' }}</span>
                                            </el-form-item>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="MAC开关" prop="funcOpt2">
                                        <template #default="scope">
                                            <el-form-item
                                                    style="width: 100%"
                                                    :prop="'transferList' + `[${scope.$index}]` + '.funcOpt2'"
                                            >
                                                <template  v-if="true">
                                                    <el-switch v-model="scope.row.funcOpt2" active-value="1" inactive-value="0" />
                                                </template>
                                                <span v-else>{{ scope.row.funcOpt2 === '1' ? '开' : scope.row.funcOpt2 === '0' ? '关' : '' }}</span>
                                            </el-form-item>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="压缩开关" prop="funcOpt3">
                                        <template #default="scope">
                                            <el-form-item
                                                    style="width: 100%"
                                                    :prop="'transferList' + `[${scope.$index}]` + '.funcOpt3'"
                                            >
                                                <template  v-if="true">
                                                    <el-switch v-model="scope.row.funcOpt3" active-value="1" inactive-value="0" />
                                                </template>
                                                <span v-else>{{ scope.row.funcOpt3 === '1' ? '开' : scope.row.funcOpt3 === '0' ? '关' : '' }}</span>
                                            </el-form-item>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </el-form>
                            <div style="display: flex;align-items: center;justify-content: center;margin-top: 30px;">
                                <!-- <el-button v-if="!isEdit" type="primary" @click="() => editFn('ON')">编辑</el-button> -->
                                <!-- <el-button v-if="isEdit" type="warning" plain @click="() => editFn('OFF')">取消</el-button> -->
                                <!--                            <el-button type="primary" @click="() => importExportStep1Handler('IMPORT')">导入</el-button>-->
                                <el-button v-if="true" type="success" @click="() => editFn('SAVE')">保存</el-button>
                                <!--                            <el-button type="primary" @click="() => importExportStep1Handler('EXPORT')">导出</el-button>-->
                            </div>
                        </div>

                    </div>
                    <!-- /.col -->
                </div>
                <!-- /.row -->
            </div>
            <!-- /.page-content -->
        </div>
    </div>
    <!-- /.main-content -->
    <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
        <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
    </a>
    <!-- 验证用户身份-->
   <div class="modal" id="validateUserDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
        aria-hidden="true">
       <form class="form-horizontal" id="validateUserForm" method="post" action="" data-toggle="validator" role="form">
           <div class="modal-dialog">
               <div class="modal-content">
                   <div class="modal-header" style="color: white;background: deepskyblue">
                       <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                       <h4 class="modal-title">验证用户身份</h4>
                   </div>
                   <div class="modal-body" style="padding: 10px 50px;">
                       <p>请输入登录密码</p>
                       <input type="hidden" id="opraType"/>
                       <input type="password" id="password" class="form-control" placeholder="密码" required/>
                       <div class="help-block with-errors"></div>
                   </div>
                   <div class="modal-footer">
                       <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                       <button type="submit" class="btn btn-primary" id="validateuser_btn">确定
                       </button>
                   </div>
               </div>
           </div>
       </form>
   </div>
    <!-- /.modal -->
    <!-- 导入导出选择对话框 -->
   <div class="modal fade" id="confirmPortDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
        aria-hidden="true">
       <form class="form-horizontal" id="confirmPortForm" method="post" action="" data-toggle="validator" role="form">
           <div class="modal-dialog" role="document">
               <div class="modal-content">
                   <div class="modal-header">
                       <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                           <span aria-hidden="true">&times;</span>
                           <span class="sr-only">Close</span>
                       </button>
                       <h4 class="modal-title" wa-name="import_tt">请选择</h4>
                   </div>
                   <div class="modal-body">
                       <div class="form-group">
                           <label class="col-sm-2 control-label text-right" for="select_file">请选择:</label>
                           <div class="col-sm-9">
                               <label class="ace-file-input">
                                   <input type="file" id="select_file" required/>
                               </label>
                           </div>
                       </div>
                       <div class="form-group hidden">
                           <label class="col-sm-2 control-label text-right" for="select_folder">请选择:</label>
                           <div class="col-sm-9">
                               <label class="ace-file-input">
                                   <input type="file" id="select_folder" webkitdirectory directory required/>
                               </label>
                           </div>
                       </div>
                   </div>
                   <div class="modal-footer">
                       <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                       <button type="button" class="btn btn-primary" id="port_btn">确定</button>
                   </div>
               </div>
           </div>
       </form>
   </div>
    <!-- /.modal -->
<!--    <script src="vue/admin/controller/port_param.js?t=<?=new Date().getTime()?>"></script>-->

</div>
<!-- 统一高度用 -->
<style>
    #app {
        display: flex;
        flex-direction: column;
        height: 100%;
        background-color: #f9f9f9;
    }
    .main-content {
        height: 100%;

    }
    .main-content-inner, .page-content, .row {
        height: 100%;
    }

    html, body {
        height: 100%;
        margin: 0;
        padding: 0;
    }

    #app {
        display: flex;
        flex-direction: column;
        height: 100%;
        background-color: #f9f9f9;
    }
</style>
<style>
    .el-icon-arrow-up{
        display: none !important;
    }
    /* 去掉el-input内置的边框 */

    .el-dialog {
        padding: 0 !important;
    }
    .el-dialog__header {
        background-color: #00bfff !important;
        color: white !important;
        padding-top: 10px !important;
        padding-left: 10px !important;
    }
    .el-dialog__title {
        color: white !important;
    }
    .dialog_content {
        padding: 20px !important;
    }
    .dialog-footer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 20px !important;
        background-color: #eff3f8;
    }
            /* 隐藏跳转框前后的文字 */
        .custom-pagination .el-pagination__jump {
            margin-left: 0;
            font-size: 0;
        }
        .custom-pagination .el-pagination__jump:before,
        .custom-pagination .el-pagination__jump:after {
            content: none;
        }
    .el-table__header th {
        border-bottom: 3px solid #e1e1e1 !important;
    }
    /* 表格最底部的线（蓝色，加粗） */
    .el-table::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        border-bottom: 2px solid #e1e1e1 !important;
    }
    .cell-input {
        height: 26px;
        margin-left: -10px;
    }

        .cell-select .el-select__wrapper {
            height: 26px;
            min-height: 26px;
            margin-left: -11px;
        }

.el-table .el-table__row {
  height: 50px;
}
.btn-prev {
    border-radius: 50% !important;
}
.btn-next {
    border-radius: 50% !important;
}
</style>
<link rel="stylesheet" href="lib/elementPlus/index.css"/>
<script src="lib/third/vue3/vue.global.js"></script>
<script src="lib/elementPlus/index.full.min.js"></script>
<script src="lib/elementPlus/zh-cn.js"></script>
<style>

    .el-input__inner {
        border: none !important;
    }
    .el-select__icon {
        border: none !important;
    }
    .el-select .el-input .el-select__caret {
        display: none !important;
    }
    .el-select .el-input .el-icon-arrow-up {
        display: none !important;
    }
</style>
<script>
    console.log(require.cache)
    delete require.cache[require.resolve('./vue/paramConf/DataTransferConfig')];
    require('./vue/paramConf/DataTransferConfig');
    // $('#handlerForm').validator();
</script>
