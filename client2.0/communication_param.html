<div class="main-content">
  <div class="main-content-inner">
    <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs">
      <div class="page-content">
        <div class="page-header">
          <h1>设备参数
            <small>
              <i class="ace-icon fa fa-angle-double-right"></i>
              通信与平台参数配置
            </small>
          </h1>
        </div><!-- /.page-header -->
        <div class="row">
          <div class="col-xs-12">
            <div class="widget-box transparent ui-sortable-handle">
              <div class="widget-header">
                <div class="widget-toolbar no-border wa-floatL">
                  <ul class="nav nav-tabs wa-tabs wa-no-switch" id="commu-tabs">
                    <li class="active">
                      <a href="#commu" data-toggle="tab">通信参数</a>
                    </li>
                    <li class="">
                      <a href="#platform" data-toggle="tab">平台参数</a>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="widget-body">
                <div class="widget-main padding-12 no-padding-left no-padding-right">
                  <div class="tab-content padding-4" id="commu-tab-cont">
                    <div id="commu" class="tab-pane active">
                      <form class="form-horizontal" id="commuForm" role="form">
                        <input type="hidden" id="app_commu_id"/>
                        <div class="form-group">
                          <label class="col-sm-3 control-label no-padding-right" for="tcp_port">
                            数据采集服务端口:<br/>(服务器、工作站)
                          </label>
                          <div class="col-sm-5">
                            <input type="text" id="tcp_port" name="tcp_port"
                                   placeholder="请输入范围1-65535端口号" class="col-xs-12 col-sm-12" style="margin-bottom: 5px;"
                                   maxlength="15"
                                   pattern="(^[6][0-4]\d{3}$)|(^[6][5][0-4]\d{2}$)|(^[6][5][5][0-2][0-9]$)|(^[6][5][5][3][0-5]$)|(^[1-5]\d{0,4}$)|10000|(^[1-9]\d{0,3}$)"
                                   required>
                            <h4 class="text-warning" style="font-size: 15px;">
                              <i class="ace-icon fa fa-exclamation-circle">注: 修改服务器、工作站采集服务端口需等待半分钟左右生效</i></h4>
                            <div class="help-block with-errors"></div>
                          </div>
                        </div>
                        <div class="form-group">
                          <label class="col-sm-3 control-label no-padding-right" for="syslog_port">
                            数据采集服务端口:<br/>(安全防护设备)
                          </label>
                          <div class="col-sm-5">
                            <input type="text" id="syslog_port" name="syslog_port"
                                   placeholder="请输入范围1-65535端口号" class="col-xs-12 col-sm-12" style="margin-bottom: 5px;"
                                   maxlength="15"
                                   pattern="(^[6][0-4]\d{3}$)|(^[6][5][0-4]\d{2}$)|(^[6][5][5][0-2][0-9]$)|(^[6][5][5][3][0-5]$)|(^[1-5]\d{0,4}$)|10000|(^[1-9]\d{0,3}$)"
                                   required>
                            <h4 class="text-warning" style="font-size: 15px;">
                              <i class="ace-icon fa fa-exclamation-circle">注: 修改安防设备采集服务端口需等待半分钟左右生效</i></h4>
                            <div class="help-block with-errors"></div>
                          </div>
                        </div>
                        <div class="form-group">
                          <label class="col-sm-3 control-label no-padding-right" for="snmp_port">
                            SNMP/TRAP端口:
                          </label>
                          <div class="col-sm-5">
                            <input type="text" id="snmp_port" name="snmp_port"
                                   placeholder="请输入范围1-65535端口号" class="col-xs-12 col-sm-12" style="margin-bottom: 5px;"
                                   maxlength="15"
                                   pattern="(^[6][0-4]\d{3}$)|(^[6][5][0-4]\d{2}$)|(^[6][5][5][0-2][0-9]$)|(^[6][5][5][3][0-5]$)|(^[1-5]\d{0,4}$)|10000|(^[1-9]\d{0,3}$)"
                                   required>
                            <h4 class="text-warning" style="font-size: 15px;">
                              <i class="ace-icon fa fa-exclamation-circle">注: 修改网络设备端口需等待半分钟左右生效</i></h4>
                            <div class="help-block with-errors"></div>
                          </div>
                        </div>
                        <div class="form-group">
                          <label class="col-sm-3 control-label no-padding-right" for="agent_port">
                            服务代理端口:<br/>
                          </label>
                          <div class="col-sm-5">
                            <input type="text" id="agent_port" name="agent_port"
                                   placeholder="请输入范围1-65535端口号" class="col-xs-12 col-sm-12" style="margin-bottom: 5px;"
                                   maxlength="15"
                                   pattern="(^[6][0-4]\d{3}$)|(^[6][5][0-4]\d{2}$)|(^[6][5][5][0-2][0-9]$)|(^[6][5][5][3][0-5]$)|(^[1-5]\d{0,4}$)|10000|(^[1-9]\d{0,3}$)"
                                   required>
                            <h4 class="text-warning" style="font-size: 15px;">
                              <i class="ace-icon fa fa-exclamation-circle">注: 修改服务代理端口需等待半分钟左右生效,会断开与人机客户端连接</i></h4>
                            <div class="help-block with-errors"></div>
                          </div>
                        </div>
                        <div class="col-md-offset-3 col-md-9">
                          <button class="btn btn-sm btn-primary" id="btn_import" type="reset">导入</button>
                          &nbsp;
                          <button class="btn btn-sm btn-primary" id="btn_export" type="reset">导出</button>
                          &nbsp;
                          <button type="submit" class="btn btn-sm btn-primary" id="next_page">
                            下一步 →
                          </button>
                        </div>
                      </form>
                    </div>
                    <div id="platform" class="tab-pane">
                      <div class="row wa-mb10">
                        <div class="col-xs-12 text-right">
                          <button class="btn btn-sm btn-primary" id="add_plat"><i class="ace-icon fa fa-plus"></i>新增</button>
                          <!--<button class="btn btn-sm btn-danger" id="del_plat"><i class='ace-icon fa fa-trash-o'></i>删除</button>-->
                        </div>
                      </div>
                      <div class="row" style="margin-bottom: 25px">
                        <div class="col-xs-12">
                          <table id="platform-tb" class="table table-bordered table-hover">
                          </table>
                          <!--<div id="platform-pager"></div>-->
                          <!-----用来计算单元格内容实际长度的--------->
                          <div class="ui-jqgrid ui-widget ui-widget-content ui-corner-all" style="position:absolute;top:-9999px">
                            <div class="ui-jqgrid-view">
                              <div class="ui-jqgrid-bdiv">
                                <div style="position: relative;">
                                  <table cellspacing="0" cellpadding="0" border="0">
                                    <tr class="ui-widget-content jqgrow ui-row-ltr" style="table-layout:table">
                                      <td id="tdCompute" style="background:#eee;width:auto"></td>
                                    </tr>
                                  </table>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="row">
                        <div class="col-xs-12 text-right">
                          <button class="btn btn-sm btn-primary" id="prev_page">← 上一步</button>
                          <button class="btn btn-sm btn-primary" id="save_all_data">保存通信&平台参数</button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div><!-- /.col -->
        </div><!-- /.row -->
      </div><!-- /.page-content -->
    </div>
  </div><!-- /.main-content -->
  <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
    <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
  </a>

  <!-- 验证用户身份-->
  <div class="modal" id="validateUserDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <form class="form-horizontal" id="validateUserForm" method="post" action="" data-toggle="validator" role="form">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header" style="color: white;background: deepskyblue">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            <h4 class="modal-title">验证用户身份</h4>
          </div>
          <div class="modal-body" style="padding: 10px 50px;">
            <p>请输入登录密码</p>
            <input type="hidden" id="opraType"/>
            <input type="password" id="password" class="form-control" placeholder="密码" required/>
            <div class="help-block with-errors"></div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
            <button type="submit" class="btn btn-primary" id="validateuser_btn">确定
            </button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </form>
  </div><!-- /.modal -->
  <!-- 导入导出选择对话框 -->
  <div class="modal fade" id="confirmPortDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <form class="form-horizontal" id="confirmPortForm" method="post" action="" data-toggle="validator" role="form">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
              <span class="sr-only">Close</span>
            </button>
            <h4 class="modal-title" wa-name="import_tt">请选择</h4>
          </div>
          <div class="modal-body">
            <div class="form-group">
              <label class="col-sm-2 control-label text-right" for="select_file">请选择:</label>
              <div class="col-sm-9">
                <label class="ace-file-input">
                  <input type="file" id="select_file" required>
                </label>
                <!--<div class="help-block with-errors"></div>-->
              </div>
            </div>
            <div class="form-group hidden">
              <label class="col-sm-2 control-label text-right" for="select_folder">请选择:</label>
              <div class="col-sm-9">
                <label class="ace-file-input">
                  <input type="file" id="select_folder" webkitdirectory directory  required>
                </label>
                <!--<div class="help-block with-errors"></div>-->
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
            <button type="button" class="btn btn-primary" id="port_btn">确定</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.form-horizontal-->
    </form><!-- /.modal-dialog -->
  </div><!-- /.modal -->
  <!-- 新增&修改平台参数-->
  <div class="modal" id="addPlatDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <form class="form-horizontal" id="addPlatForm" method="post" action="" data-toggle="validator" role="form">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header" style="color: white;background: deepskyblue">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
            <h4 class="modal-title" id="addPlatTitle" wa-data="add">新增平台参数</h4>
          </div>
          <div class="modal-body">
            <input type="hidden" id="app_plat_rn"/>
            <input type="hidden" id="app_plat_id"/>
            <input type="hidden" id="old_ip"/>
            <div class="form-group">
              <label class="col-sm-3 control-label text-right">平台ip地址：</label>
              <div class="col-sm-8">
                <input type="text" class="form-control required" id="platip" name="platip"
                       placeholder="请输入正确ip格式,格式为0.0.0.0~***************" maxlength="15"
                       pattern="(?=(\b|\D))(((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))\.){3}((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))(?=(\b|\D))"
                       required/>
                <div class="help-block with-errors"></div>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-3 control-label text-right">事件上传端口：</label>
              <div class="col-sm-8">
                <input type="text" class="form-control required" id="platport" name="platport"
                       placeholder="请输入正确端口号,范围0-65535" maxlength="15"
                       pattern="([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-4]\d{4}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])"
                       required/>
                <div class="help-block with-errors"></div>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-3 control-label text-right">平台ip权限：</label>
              <div class="col-sm-8">
                <!--<select class="form-control required" id="permissionType" name="permissionType" required>-->
                <!--</select>-->
                <select id="permissionType" class="multiselect" multiple="">
                  <!--<option value="0">没有选中任何项</option>-->
                  <option value="1">上传权限</option>
                  <option value="2">读权限</option>
                  <option value="4">对监测装置写权限</option>
                  <option value="8">对监测对象写权限</option>
                </select>
                <!--<div class="help-block with-errors"></div>-->
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-3 control-label text-right">分组号：</label>
              <div class="col-sm-8">
                <input type="text" class="form-control required" id="groupId" name="groupId"
                       placeholder="请输入0-15正整数" maxlength="15"
                       pattern="^([0-9]|(1[0-5]))$"
                       required/>
                <div class="help-block with-errors"></div>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-3 control-label text-right">组内优先级：</label>
              <div class="col-sm-8">
                <input type="text" class="form-control required" id="priority" name="priority"
                       placeholder="请输入0-15正整数" maxlength="15"
                       pattern="^([0-9]|(1[0-5]))$"
                       required/>
                <div class="help-block with-errors"></div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="submit" id="save_plat_btn" class="btn btn-primary">保存</button>
            <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </form>
  </div><!-- /.modal -->
  <!-- 确认删除对话框 -->
  <div class="modal fade" id="confirmDlg">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
            <span class="sr-only">Close</span>
          </button>
          <h4 class="modal-title">删除</h4>
        </div>
        <div class="modal-body">
          <input type="hidden" id="remove_plat_rn"/>
          确定删除此行平台参数信息？
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
          <button type="button" class="btn btn-primary" data-dismiss="modal" id="remove_plat_btn">确定</button>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
  </div><!-- /.modal -->
  <!-- 确认保存对话框 -->
  <div class="modal fade" id="confirmDlg2">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
            <span class="sr-only">Close</span>
          </button>
          <h4 class="modal-title">确认提示</h4>
        </div>
        <div class="modal-body">
          <span style="color:red;font-size: 14px">如果您修改了<b>服务代理端口</b>,将会<b>暂时断开与装置连接,需等待半分钟左右</b>,</span>
          确定保存通信&平台所有参数信息？
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
          <button type="button" class="btn btn-primary" id="save_all_btn" data-dismiss="modal">确定</button>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
  </div><!-- /.modal -->
  <script>
    // console.log(require.cache)
    delete require.cache[require.resolve('./script/view/device_mgr/communication_param.js')];
    require('./script/view/device_mgr/communication_param.js');
    $('#commuForm').validator();
    $('#addPlatForm').validator();
  </script>
</div>
