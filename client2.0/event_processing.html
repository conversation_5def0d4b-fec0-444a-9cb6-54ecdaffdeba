<div class="main-content">
  <div class="main-content-inner">
    <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs">
      <div class="page-content">
        <div class="page-header">
          <h1>设备参数
            <small>
              <i class="ace-icon fa fa-angle-double-right"></i>
              事件处理
            </small>
          </h1>
        </div><!-- /.page-header -->
        <div class="row">
          <div class="col-xs-12">
            <form class="form-horizontal" id="handlerForm" role="form">
              <div class="form-group">
                <label class="col-sm-3 control-label no-padding-right" for="cpu_limit">
                  CPU利用率上限阈值(%):
                </label>
                <div class="col-sm-9">
                  <input type="text" id="cpu_limit" name="cpu_limit" placeholder="请输入1-100正整数"
                         class="col-xs-10 col-sm-6"
                         maxlength="3"
                         pattern="^([1-9][0-9]{0,1}|100)$"
                         required>
                  <div class="help-block with-errors"></div>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-3 control-label no-padding-right" for="mem_limit">
                  内存使用率上限阈值(%):
                </label>
                <div class="col-sm-9">
                  <input type="text" id="mem_limit" name="mem_limit" placeholder="请输入1-100正整数"
                         class="col-xs-10 col-sm-6"
                         maxlength="3"
                         pattern="^([1-9][0-9]{0,1}|100)$"
                         required>
                  <div class="help-block with-errors"></div>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-3 control-label no-padding-right" for="trf_limit">
                  网口流量越限阈值(Kbps):
                </label>
                <div class="col-sm-9">
                  <input type="text" id="trf_limit" name="trf_limit" placeholder="请输入1-4294967295整数,单位Kbps"
                         class="col-xs-10 col-sm-6"
                         maxlength="10"
                         pattern="(^[4][0-1]\d{8}$)|(^[4][2][0-8]\d{7}$)|(^[4][2][9][0-3]\d{6}$)|(^[4][2][9][4][0-8]\d{5}$)|(^[4][2][9][4][9][0-5]\d{4}$)
                         |(^[4][2][9][4][9][6][0-6]\d{3}$)|(^[4][2][9][4][9][6][7][0-1]\d{2}$)|(^[4][2][9][4][9][6][7][2][0-8][0-9]$)
                         |(^[4][2][9][4][9][6][7][2][9][0-5]$)|(^[1-3]\d{9}$)|(^[1-9]\d{0,8}$)"
                         required>
                  <div class="help-block with-errors"></div>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-3 control-label no-padding-right" for="login_limit">
                  连续登录失败阈值(次):
                </label>
                <div class="col-sm-9">
                  <input type="text" id="login_limit" name="login_limit" placeholder="请输入1-255整数"
                         class="col-xs-10 col-sm-6"
                         maxlength="3"
                         pattern="(^[2][0-4][0-9]$)|(^[2][5][0-5]$)|(^[1]\d{2}$)|(^[1-9]\d{0,1}$)"
                         required>
                  <div class="help-block with-errors"></div>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-3 control-label no-padding-right" for="merg_time">
                  归并事件归并周期(s):
                </label>
                <div class="col-sm-9">
                  <input type="text" id="merg_time" name="merg_time" placeholder="请输入1-65535整数,单位s"
                         class="col-xs-10 col-sm-6"
                         maxlength="5"
                         pattern="(^[6][0-4]\d{3}$)|(^[6][5][0-4]\d{2}$)|(^[6][5][5][0-2][0-9]$)|(^[6][5][5][3][0-5]$)|(^[1-5]\d{0,4}$)|10000|(^[1-9]\d{0,3}$)"
                         required>
                  <div class="help-block with-errors"></div>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-3 control-label no-padding-right" for="disk_limit">
                  磁盘使用率上限阈值(%):
                </label>
                <div class="col-sm-9">
                  <input type="text" id="disk_limit" name="disk_limit" placeholder="请输入1-100正整数"
                         class="col-xs-10 col-sm-6"
                         maxlength="3"
                         pattern="^([1-9][0-9]{0,1}|100)$"
                         required>
                  <div class="help-block with-errors"></div>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-3 control-label no-padding-right" for="time_interval">
                  历史事件上报分界时间参数(分钟):
                </label>
                <div class="col-sm-9">
                  <input type="text" id="time_interval" name="time_interval" placeholder="请输入1-4294967295整数"
                         class="col-xs-10 col-sm-6"
                         maxlength="10"
                         pattern="(^[4][0-1]\d{8}$)|(^[4][2][0-8]\d{7}$)|(^[4][2][9][0-3]\d{6}$)|(^[4][2][9][4][0-8]\d{5}$)|(^[4][2][9][4][9][0-5]\d{4}$)
                         |(^[4][2][9][4][9][6][0-6]\d{3}$)|(^[4][2][9][4][9][6][7][0-1]\d{2}$)|(^[4][2][9][4][9][6][7][2][0-8][0-9]$)
                         |(^[4][2][9][4][9][6][7][2][9][0-5]$)|(^[1-3]\d{9}$)|(^[1-9]\d{0,8}$)"
                         required>
                  <div class="help-block with-errors"></div>
                </div>
              </div>
              <div class="col-md-offset-3 col-md-9">
                <button class="btn btn-sm btn-primary" id="btn_import" type="reset">导入</button>
                &nbsp;
                <button class="btn btn-sm btn-primary" id="btn_export" type="reset">导出</button>
                &nbsp;
                <button type="submit" class="btn btn-sm btn-primary" id="btn_handler_save">
                  <i class="ace-icon fa fa-check bigger-110"></i>保存
                </button>
              </div>
            </form>
          </div><!-- /.col -->
        </div><!-- /.row -->
      </div><!-- /.page-content -->
    </div>
  </div><!-- /.main-content -->
  <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
    <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
  </a>
  <!-- 验证用户身份-->
  <div class="modal" id="validateUserDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <form class="form-horizontal" id="validateUserForm" method="post" action="" data-toggle="validator" role="form">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header" style="color: white;background: deepskyblue">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            <h4 class="modal-title">验证用户身份</h4>
          </div>
          <div class="modal-body" style="padding: 10px 50px;">
            <p>请输入登录密码</p>
            <input type="hidden" id="opraType"/>
            <input type="password" id="password" class="form-control" placeholder="密码" required/>
            <div class="help-block with-errors"></div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
            <button type="submit" class="btn btn-primary" id="validateuser_btn">确定
            </button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </form>
  </div><!-- /.modal -->
  <!-- 导入导出选择对话框 -->
  <div class="modal fade" id="confirmPortDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <form class="form-horizontal" id="confirmPortForm" method="post" action="" data-toggle="validator" role="form">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
              <span class="sr-only">Close</span>
            </button>
            <h4 class="modal-title" wa-name="import_tt">请选择</h4>
          </div>
          <div class="modal-body">
            <div class="form-group">
              <label class="col-sm-2 control-label text-right" for="select_file">请选择:</label>
              <div class="col-sm-9">
                <label class="ace-file-input">
                  <input type="file" id="select_file" required>
                </label>
                <!--<div class="help-block with-errors"></div>-->
              </div>
            </div>
            <div class="form-group hidden">
              <label class="col-sm-2 control-label text-right" for="select_folder">请选择:</label>
              <div class="col-sm-9">
                <label class="ace-file-input">
                  <input type="file" id="select_folder" webkitdirectory directory  required>
                </label>
                <!--<div class="help-block with-errors"></div>-->
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
            <button type="button" class="btn btn-primary" id="port_btn">确定</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.form-horizontal-->
    </form><!-- /.modal-dialog -->
  </div><!-- /.modal -->
  <script>
    // console.log(require.cache)
    delete require.cache[require.resolve('./script/view/device_mgr/event_processing.js')];
    require('./script/view/device_mgr/event_processing.js');
    $('#handlerForm').validator();
  </script>
</div>
