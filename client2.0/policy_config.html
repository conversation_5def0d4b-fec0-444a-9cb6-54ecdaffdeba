<div class="main-content">
  <div class="main-content-inner">
    <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs">
      <div class="page-content">
        <div class="page-header">
          <h1>系统设置
            <small>
              <i class="ace-icon fa fa-angle-double-right"></i>
              策略配置
            </small>
          </h1>
        </div><!-- /.page-header -->
        <div class="row">
          <div class="col-xs-12">
            <form class="form-horizontal" id="policyForm" role="form">
              <input type="hidden" id="app_policy_id"/>
              <div class="form-group">
                <label class="col-sm-3 control-label text-right">失败阈值时间(分):</label>
                <div class="col-sm-5">
                  <input type="text" id="fail_threshold_time" name="fail_threshold_time" style="margin-bottom: 5px;"
                         placeholder="请输入>0整数" class="col-xs-12 col-sm-12"
                         pattern="^[0-9]*[1-9][0-9]*$" required/>
                  <h4 class="text-warning" style="font-size: 15px;">
                    <i class="ace-icon fa fa-exclamation-circle">注: 在失败阈值时间内连续登录失败超过设定阈值会锁定登录用户</i>
                    <br>
<!--                    <a href="javascript:;" id="hrefToSetEvt" style="text-decoration: underline;font-size: 13px;">设置登录失败阈值</a>-->
                  </h4>
                  <div class="help-block with-errors" style="margin: 0"></div>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-3 control-label text-right">多次登录失败后锁定时间(分):</label>
                <div class="col-sm-5">
                  <input type="text" id="log_cool_down" name="log_cool_down"
                         placeholder="请输入>20整数" class="col-xs-12 col-sm-12"
                         pattern="^2[1-9]{1}|^[3-9]\d{1}|^[1-9]\d{2,}$" required/>
                  <div class="help-block with-errors"></div>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-3 control-label text-right">口令历史重复次数(N):</label>
                <div class="col-sm-5">
                  <input type="text" id="repeat_psd_count" name="repeat_psd_count"
                         placeholder="请输入>0整数" class="col-xs-12 col-sm-12"
                         pattern="^[0-9]*[1-9][0-9]*$" required/>
                  <div class="help-block with-errors"></div>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-3 control-label text-right">口令过期时间(天):</label>
                <div class="col-sm-5">
                  <input type="text" id="expire_time" name="expire_time"
                         placeholder="请输入>0整数" class="col-xs-12 col-sm-12"
                         pattern="^[0-9]*[1-9][0-9]*$" required/>
                  <div class="help-block with-errors"></div>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-3 control-label text-right">超时时间(秒):</label>
                <div class="col-sm-5">
                  <input type="text" id="lock_timeout" name="expire_time"
                         placeholder="请输入>1整数" class="col-xs-12 col-sm-12"
                         pattern="^([1-9][0-9]{0,9})$" required/>
                  <div class="help-block with-errors"></div>
                </div>
              </div>
              <div class="col-sm-offset-3 col-sm-9">
                <button type="submit" class="btn btn-sm btn-info" id="btn_policy_save">
                  <i class="ace-icon fa fa-check bigger-110"></i>保存
                </button>
              </div>
            </form>
          </div><!-- /.col -->
        </div><!-- /.row -->
      </div><!-- /.page-content -->
    </div>
  </div><!-- /.main-content -->
  <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
    <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
  </a>
  <!-- 验证用户身份-->
  <div class="modal" id="validateUserDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <form class="form-horizontal" id="validateUserForm" method="post" action="" data-toggle="validator" role="form">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header" style="color: white;background: deepskyblue">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            <h4 class="modal-title">验证用户身份</h4>
          </div>
          <div class="modal-body" style="padding: 10px 50px;">
            <p>请输入登录密码</p>
            <input type="hidden" id="opraType"/>
            <input type="password" id="password" class="form-control" placeholder="密码" required/>
            <div class="help-block with-errors"></div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
            <button type="submit" class="btn btn-primary" id="validateuser_btn">确定
            </button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </form>
  </div><!-- /.modal -->
  <script>
    // console.log(require.cache)
    delete require.cache[require.resolve('./script/view/system_setup/policy_config.js')];
    require('./script/view/system_setup/policy_config.js');
    $('#policyForm').validator();
  </script>
</div>
