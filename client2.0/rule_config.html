<div class="main-content">
  <div class="main-content-inner">
    <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs">
      <div class="page-content">
        <div class="page-header">
          <h1>规则配置</h1>
        </div><!-- /.page-header -->
        <div class="row">
          <div class="col-xs-12">
            <div class="widget-box transparent ui-sortable-handle">
              <div class="widget-header">
                <div class="widget-toolbar no-border wa-floatL">
                  <ul class="nav nav-tabs" id="commu-tabs">
                    <!--<li class="" wa-name="device-tb" disabled="disabled">-->
                      <!--<a href="#alarm_rule" data-toggle="tab">告警规则</a>-->
                    <!--</li>-->
                    <li class="active" wa-name="switch-tb">
                      <a href="#sw_rule" data-toggle="tab">交换机规则</a>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="widget-body">
                <div class="widget-main padding-12 no-padding-left no-padding-right">
                  <div class="tab-content padding-4" id="commu-tab-cont">
                    <!--<div id="alarm_rule" class="tab-pane">-->
                      <!--&lt;!&ndash;<div class="row wa-mb10">&ndash;&gt;-->
                        <!--&lt;!&ndash;<div class="col-xs-12 text-right wa-mb10">&ndash;&gt;-->
                          <!--&lt;!&ndash;<a href="javascript:;" type="button" class="btn btn-sm btn-primary" id="add_device">&ndash;&gt;-->
                            <!--&lt;!&ndash;<i class="ace-icon fa fa-plus"></i>新增设备</a>&ndash;&gt;-->
                          <!--&lt;!&ndash;<a href="javascript:;" type="button" class="btn btn-sm btn-primary"&ndash;&gt;-->
                             <!--&lt;!&ndash;id="import">导入</a>&ndash;&gt;-->
                          <!--&lt;!&ndash;<a href="javascript:;" type="button" class="btn btn-sm btn-primary"&ndash;&gt;-->
                             <!--&lt;!&ndash;id="export">导出</a>&ndash;&gt;-->
                        <!--&lt;!&ndash;</div>&ndash;&gt;-->
                        <!--&lt;!&ndash;<div class="col-xs-12">&ndash;&gt;-->
                          <!--&lt;!&ndash;<form class="form-horizontal" role="form">&ndash;&gt;-->
                            <!--&lt;!&ndash;<div class="form-group wa-mb0">&ndash;&gt;-->
                              <!--&lt;!&ndash;<label class="col-sm-1 control-label no-padding-right">&ndash;&gt;-->
                                <!--&lt;!&ndash;<b>设备类型:</b>&ndash;&gt;-->
                              <!--&lt;!&ndash;</label>&ndash;&gt;-->
                              <!--&lt;!&ndash;<div class="col-sm-11" id="deviceTypeSelect">&ndash;&gt;-->
                                <!--&lt;!&ndash;<label class="radio-inline">&ndash;&gt;-->
                                  <!--&lt;!&ndash;<input type="radio" name="device-type" value="0" checked>不限&ndash;&gt;-->
                                <!--&lt;!&ndash;</label>&ndash;&gt;-->
                                <!--&lt;!&ndash;<label class="radio-inline">&ndash;&gt;-->
                                  <!--&lt;!&ndash;<input type="radio" name="device-type" value="FW">防火墙&ndash;&gt;-->
                                <!--&lt;!&ndash;</label>&ndash;&gt;-->
                                <!--&lt;!&ndash;<label class="radio-inline">&ndash;&gt;-->
                                  <!--&lt;!&ndash;<input type="radio" name="device-type" value="FID">横向正向隔离装置&ndash;&gt;-->
                                <!--&lt;!&ndash;</label>&ndash;&gt;-->
                                <!--&lt;!&ndash;<label class="radio-inline">&ndash;&gt;-->
                                  <!--&lt;!&ndash;<input type="radio" name="device-type" value="BID">横向反向隔离装置&ndash;&gt;-->
                                <!--&lt;!&ndash;</label>&ndash;&gt;-->
                                <!--&lt;!&ndash;<label class="radio-inline">&ndash;&gt;-->
                                  <!--&lt;!&ndash;<input type="radio" name="device-type" value="SVR">服务器&ndash;&gt;-->
                                <!--&lt;!&ndash;</label>&ndash;&gt;-->
                                <!--&lt;!&ndash;<label class="radio-inline">&ndash;&gt;-->
                                  <!--&lt;!&ndash;<input type="radio" name="device-type" value="SW">交换机&ndash;&gt;-->
                                <!--&lt;!&ndash;</label>&ndash;&gt;-->
                                <!--&lt;!&ndash;<label class="radio-inline">&ndash;&gt;-->
                                  <!--&lt;!&ndash;<input type="radio" name="device-type" value="DCD">网络安全监测装置&ndash;&gt;-->
                                <!--&lt;!&ndash;</label>&ndash;&gt;-->
                              <!--&lt;!&ndash;</div>&ndash;&gt;-->
                            <!--&lt;!&ndash;</div>&ndash;&gt;-->
                            <!--&lt;!&ndash;<div class="form-group wa-mb0">&ndash;&gt;-->
                              <!--&lt;!&ndash;<label class="col-sm-1 control-label no-padding-right">&ndash;&gt;-->
                                <!--&lt;!&ndash;<b>设备状态:</b>&ndash;&gt;-->
                              <!--&lt;!&ndash;</label>&ndash;&gt;-->
                              <!--&lt;!&ndash;<div class="col-sm-11" id="deviceStatisSelect">&ndash;&gt;-->
                                <!--&lt;!&ndash;<label class="radio-inline">&ndash;&gt;-->
                                  <!--&lt;!&ndash;<input type="radio" name="device-statis" value="3" checked>不限&ndash;&gt;-->
                                <!--&lt;!&ndash;</label>&ndash;&gt;-->
                                <!--&lt;!&ndash;<label class="radio-inline">&ndash;&gt;-->
                                  <!--&lt;!&ndash;<input type="radio" name="device-statis" value="0">未知&ndash;&gt;-->
                                <!--&lt;!&ndash;</label>&ndash;&gt;-->
                                <!--&lt;!&ndash;<label class="radio-inline">&ndash;&gt;-->
                                  <!--&lt;!&ndash;<input type="radio" name="device-statis" value="1">在线&ndash;&gt;-->
                                <!--&lt;!&ndash;</label>&ndash;&gt;-->
                                <!--&lt;!&ndash;<label class="radio-inline">&ndash;&gt;-->
                                  <!--&lt;!&ndash;<input type="radio" name="device-statis" value="2">离线&ndash;&gt;-->
                                <!--&lt;!&ndash;</label></div>&ndash;&gt;-->
                            <!--&lt;!&ndash;</div>&ndash;&gt;-->
                            <!--&lt;!&ndash;<div class="form-group">&ndash;&gt;-->
                              <!--&lt;!&ndash;<label class="col-sm-1 control-label no-padding-right">&ndash;&gt;-->
                                <!--&lt;!&ndash;<b>过滤条件:</b>&ndash;&gt;-->
                              <!--&lt;!&ndash;</label>&ndash;&gt;-->
                              <!--&lt;!&ndash;<div class="col-sm-11">&ndash;&gt;-->
                                <!--&lt;!&ndash;<input type="text" id="search" name="search"&ndash;&gt;-->
                                       <!--&lt;!&ndash;placeholder="请输入设备名称" class="col-sm-3">&ndash;&gt;-->
                              <!--&lt;!&ndash;</div>&ndash;&gt;-->
                            <!--&lt;!&ndash;</div>&ndash;&gt;-->
                          <!--&lt;!&ndash;</form>&ndash;&gt;-->
                        <!--&lt;!&ndash;</div>&ndash;&gt;-->
                        <!--&lt;!&ndash;<div class="col-sm-12 text-right">&ndash;&gt;-->
                          <!--&lt;!&ndash;<a href="javascript:;" type="button" class="btn btn-sm btn-success" id="query"><i&ndash;&gt;-->
                              <!--&lt;!&ndash;class="ace-icon fa fa-refresh"></i> 查询</a>&ndash;&gt;-->
                        <!--&lt;!&ndash;</div>&ndash;&gt;-->
                      <!--&lt;!&ndash;</div>&ndash;&gt;-->
                    <!--</div>-->
                    <div id="sw_rule" class="tab-pane active">
                      <div class="row wa-mb10">
                        <div class="col-xs-12 text-right">
                          <a href="javascript:;" type="button" class="btn btn-sm btn-primary disabled"
                             id="sw_import">导入</a>
                          <a href="javascript:;" type="button" class="btn btn-sm btn-primary disabled"
                             id="sw_export">导出</a>
                        </div>
                      </div>
                      <div class="row">
                        <div class="col-xs-12">
                          <table id="switch-tb" class="table table-bordered table-hover"></table>
                          <div id="switch-pager"></div>
                          <!-----用来计算单元格内容实际长度的--------->
                          <div class="ui-jqgrid ui-widget ui-widget-content ui-corner-all" style="position:absolute;top:-9999px">
                            <div class="ui-jqgrid-view">
                              <div class="ui-jqgrid-bdiv">
                                <div style="position: relative;">
                                  <table cellspacing="0" cellpadding="0" border="0">
                                    <tr class="ui-widget-content jqgrow ui-row-ltr" style="table-layout:table">
                                      <td id="tdCompute" style="background:#eee;width:auto"></td>
                                    </tr>
                                  </table>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div><!-- /.page-content -->
    </div>
  </div><!-- /.main-content -->
  <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
    <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
  </a>
  <!-- 修改交换机参数 -->
  <div class="modal" id="editSwParam" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <form class="form-horizontal" id="editSwParamForm" method="post" action="" data-toggle="validator" role="form">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header" style="color: white;background: deepskyblue">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
            <h4 class="modal-title" id="editSwParamTitle" wa-name="see_param">修改交换机参数</h4>
          </div>
          <div class="modal-body">
            <div class="form-group">
              <label class="col-sm-2 control-label text-right">编码:</label>
              <div class="col-sm-7">
                <input type="text" class="form-control" id="sw_code" name="sw_code"
                       placeholder="设备名称不超过64个字" maxlength="64" disabled="disabled"/>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label text-right">参数:</label>
              <div class="col-sm-7">
               <textarea class="form-control" id="sw_param" name="sw_param" rows="6" placeholder="请输入参数" required></textarea>
                <div class="help-block with-errors"></div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="submit" class="btn btn-primary" id="save_sw_btn">确定</button>
            <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </form>
  </div><!-- /.modal -->
  <script>
    delete require.cache[require.resolve('./script/view/rule_config.js')];
    require('./script/view/rule_config.js');
    $('#editSwParamForm').validator();
  </script>
</div>
