<div class="main-content">
  <div class="main-content-inner">
    <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs">
      <div class="page-content">
        <div class="page-header">
          <h1>监控对象参数管理</h1>
        </div><!-- /.page-header -->
        <div class="row">
          <div class="col-xs-12">
            <table id="device-tb" class="table table-bordered table-hover"></table>
            <div id="device-pager"></div>
            <!-----用来计算单元格内容实际长度的--------->
            <div class="ui-jqgrid ui-widget ui-widget-content ui-corner-all" style="position:absolute;top:-9999px">
              <div class="ui-jqgrid-view">
                <div class="ui-jqgrid-bdiv">
                  <div style="position: relative;">
                    <table cellspacing="0" cellpadding="0" border="0">
                      <tr class="ui-widget-content jqgrow ui-row-ltr" style="table-layout:table">
                        <td id="tdCompute" style="background:#eee;width:auto"></td>
                      </tr>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div><!-- /.page-content -->
    </div>
  </div><!-- /.main-content -->
  <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
    <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
  </a>
  <!-- 验证用户身份-->
  <div class="modal" id="validateUserDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <form class="form-horizontal" id="validateUserForm" method="post" action="" data-toggle="validator" role="form">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header" style="color: white;background: deepskyblue">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            <h4 class="modal-title">验证用户身份</h4>
          </div>
          <div class="modal-body" style="padding: 10px 50px;">
            <p>请输入登录密码</p>
            <input type="hidden" id="opraType"/>
            <input type="hidden" id="opraId"/>
            <input type="hidden" id="opraIp"/>
            <input type="hidden" id="opraName"/>
            <input type="hidden" id="opraTp"/>
            <input type="password" id="password" class="form-control" placeholder="密码" required/>
            <div class="help-block with-errors"></div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
            <button type="submit" class="btn btn-primary" id="validateuser_btn">确定
            </button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </form>
  </div><!-- /.modal -->
  <!-- 查看&设置监控对象参数-->
  <div class="modal" id="seeDeviceDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <form class="form-horizontal" id="seeDeviceForm" method="post" action="" data-toggle="validator" role="form">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header" style="color: white;background: deepskyblue">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
            <h4 class="modal-title" id="seeDeviceTitle" wa-name="see_param">查看监控对象参数</h4>
          </div>
          <div class="modal-body">
            <input type="hidden" id="see_device_id"/>
            <input type="hidden" id="see_device_ip"/>
            <div class="form-group">
              <label class="col-sm-2 control-label text-right">设备名称:</label>
              <div class="col-sm-7">
                <input type="text" class="form-control" id="device_name" name="device_name"
                       placeholder="设备名称不超过64个字" maxlength="64" disabled="disabled"
                       required/>
                <div class="help-block with-errors"></div>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label text-right">设备类型:</label>
              <div class="col-sm-7">
                <select class="form-control" id="device_type" name="device_type" disabled="disabled">
                  <option value="SVR">服务器</option>
                  <option value="DCD">网络安全监测装置</option>
                </select>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label text-right">请选择参数:</label>
              <div class="col-sm-7">
                <select class="form-control" id="sub_type" name="sub_type">
                  <!--<option value="0">请选择</option>-->
                  <option value="1">网络连接白名单</option>
                  <option value="2">服务端口白名单</option>
                  <option value="3">关键文件/目录清单</option>
                  <option value="4">存在光驱设备检测周期</option>
                  <option value="5">非法端口检测周期</option>
                  <option value="6">危险操作命令清单</option>
                </select>
              </div>
            </div>
            <div class="form-group">
              <div class="col-md-offset-2 col-sm-7" id="sub_container">
                <textarea class="form-control sub_val hidden" disabled="disabled" rows="5"
                          placeholder="请输入格式为\'tcp/udp(协议号),0.0.0.0~***************(ip),0~65535(端口号)\',可多条,按回车换行,例如:tcp,0.0.0.0,0"></textarea>
                <input class="form-control sub_val hidden" disabled="disabled" rows="5"
                       placeholder="请输入>0整数,如\'60\',单位秒">
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-primary" id="set_sub_type">确定</button>
            <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </form>
  </div><!-- /.modal -->
  <script>
    delete require.cache[require.resolve('./script/view/monitorobj_param_mgr.js')];
    require('./script/view/monitorobj_param_mgr.js');
    $('#seeDeviceForm').validator();
  </script>
</div>
