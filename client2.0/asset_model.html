<!--&lt;!&ndash; element-plus &ndash;&gt;-->

<!--<script src="vue/admin/controller/port_param.js"></script>-->

<div class="main-content">
    <div class="main-content-inner">
        <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs" style="height: calc(100% - 100px);">
            <div class="page-content">
                <div class="page-header">
                    <h1>参数配置
                        <small>
                            <i class="ace-icon fa fa-angle-double-right"></i>
                            资产参数
                        </small>
                    </h1>
                </div>
                <!-- /.page-header -->
                <div class="row">
                <div id="app">
                    <div class="app-top">
                        <div style="height: 100px;padding: 8px;">
                            <el-row :gutter="10">
                            <el-col
                                v-for="(item, key) in mainInfo"
                                :key="key"
                                :span="8"
                                style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"
                            >
                                <span style="color: #999;">{{ item.name }}:</span>
                                <span style="margin-left: 4px;">
                                    <template v-if="'value2Lable' in item">
                                        {{ item.value2Lable(item.value) }}
                                    </template>
                                    <template v-else>
                                        {{ item.value }}
                                    </template>
                                </span>
                            </el-col>
                            </el-row>
                        </div>
                    </div>
                    <div class="app-bottom">
                        <div class="app-bottom-left" style="overflow-y: auto;">
                                <el-tree
                                    :data="treeData"
                                    :props="defaultProps"
                                    default-expand-all
                                    :expand-on-click-node="false"
                                    node-key="value"
                                    highlight-current
                                    @node-click="handleNodeClick"
                                    >
                                    <!-- 自定义节点内容 -->
                                    <template #default="{ node, data }">
                                        <span class="custom-tree-node">
                                        <span>{{ node.label }}</span>
                                        </span>
                                    </template>
                                </el-tree>
                        </div>
                        <div class="app-bottom-right" style="overflow-y: auto;">
                            <template v-if="rightShowData.length">
                                <!-- 每项: 硬件 -->
                                <el-row :gutter="10" v-for="(item, key) in rightShowData" :key="'rightShowData' + key" style="margin-top: 10px;margin-bottom: 10px;border: 1px solid black;padding: 10px 0 10px 0;">
                                    <!-- 第一层：硬件标识 -->
                                    <template v-for="(field, itemKey) of Object.keys(item)">

                                        <!-- item[field]对应这一项的值 -->
                                        <el-col v-if="!(Array.isArray(item[field]))" :key="'itemKey' + key" :span="8" style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;" >
                                            <span class="field-label">{{  code2ChineseMap[field] }}:</span>{{ item[field]  }}
                                        </el-col>

                                        <!-- 数组 -->
                                        <template v-else>
                                            <el-col :key="'itemKey' + key" :span="24" style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;" >

                                                <el-row class="field-arr-label"><el-col :span="24">{{  code2ChineseMap[field] }}:</el-col></el-row>

                                                <el-row :gutter="10" style="padding: 10px 0 10px 0;border-bottom: 1px solid #ebeef5;border-top: 1px solid #ebeef5;" >
                                                    <template v-if="item[field].length">

                                                        <el-row v-for="(item2, index2) in item[field]" style="padding-bottom: 5px;padding-left: 5px;border-bottom: 1px solid #ebeef5;">
                                                            <template v-for="(field2, itemKey2) in Object.keys(item2)"  :key="key + itemKey + index2">

                                                                <el-col :span="8" v-if="!(Array.isArray(item2[field2]))">
                                                                    <span class="field-label" style="font-weight: lighter;">{{ code2ChineseMap[field2] }}:</span>
                                                                    <el-tooltip :content="item2[field2]" effect="light">
                                                                        <!-- item[field][index2][field2] -->
                                                                        <span class="ellipsis-span">{{ item2[field2] }}</span>
                                                                    </el-tooltip>
                                                                </el-col>

                                                                <template v-else>
                                                                    <el-col :span="24">
                                                                        <el-row class="field-arr-label-next"><el-col :span="24">*{{ code2ChineseMap[field2] }}:</el-col></el-row>
                                                                        <el-row :gutter="10" style="padding: 0 10px 10px 10px;">

                                                                            <template v-if="item[field][index2][field2].length">
                                                                                <template v-for="(item3, index3) in item[field][index2][field2]">
                                                                                    <template v-for="(field3, itemKey3) in Object.keys(item3)" :key="itemKey3+itemKey2+11">
                                                                                        <el-col :span="8" v-if="!(Array.isArray(item3[field3]))">
                                                                                            <span class="field-label">{{ code2ChineseMap[field3] }}:</span>
                                                                                            <el-tooltip :content="item3[field3]" effect="light">
                                                                                                <!-- item[field][index2][field3] -->
                                                                                                <span class="ellipsis-span">{{ item3[field3] }}</span>
                                                                                            </el-tooltip>
                                                                                        </el-col>
                                                                                    </template>
                                                                                </template>
                                                                            </template>
                                                                            <template v-else><el-col style="padding-left: 30px;">暂无数据</el-col></template>
                                                                        </el-row>
                                                                    </el-col>
                                                                </template>
                                                            </template>
                                                        </el-row>
                                                    </template>
                                                    <template v-else><el-col style="text-align: center;">暂无数据</el-col></template>
                                                </el-row>
                                            </el-col>
                                        </template>
                                    </template>

                                    </el-row>
                                </div>
                            </template>
                            <template v-else>
                                <div  style="height: 100%;width: 100%;display: flex;align-items: center;justify-content: center;">
                                    <span>暂无数据</span>
                                </div>
                            </template>

                    </div>
                </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /.main-content -->
    <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
        <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
    </a>
    <!-- 验证用户身份-->
   <div class="modal" style="z-index: 4000;" id="validateUserDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
        aria-hidden="true">
       <form class="form-horizontal" id="validateUserForm" method="post" action="" data-toggle="validator" role="form">
           <div class="modal-dialog">
               <div class="modal-content">
                   <div class="modal-header" style="color: white;background: deepskyblue">
                       <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                       <h4 class="modal-title">验证用户身份</h4>
                   </div>
                   <div class="modal-body" style="padding: 10px 50px;">
                       <p>请输入登录密码</p>
                       <input type="hidden" id="opraType"/>
                       <input type="password" id="password" class="form-control" placeholder="密码" required/>
                       <div class="help-block with-errors"></div>
                   </div>
                   <div class="modal-footer">
                       <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                       <button type="submit" class="btn btn-primary" id="validateuser_btn">确定
                       </button>
                   </div>
               </div>
           </div>
       </form>
   </div>
    <!-- /.modal -->
    <!-- 导入导出选择对话框 -->
   <div class="modal fade" id="confirmPortDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
        aria-hidden="true">
       <form class="form-horizontal" id="confirmPortForm" method="post" action="" data-toggle="validator" role="form">
           <div class="modal-dialog" role="document">
               <div class="modal-content">
                   <div class="modal-header">
                       <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                           <span aria-hidden="true">&times;</span>
                           <span class="sr-only">Close</span>
                       </button>
                       <h4 class="modal-title" wa-name="import_tt">请选择</h4>
                   </div>
                   <div class="modal-body">
                       <div class="form-group">
                           <label class="col-sm-2 control-label text-right" for="select_file">请选择:</label>
                           <div class="col-sm-9">
                               <label class="ace-file-input">
                                   <input type="file" id="select_file" required/>
                               </label>
                           </div>
                       </div>
                       <div class="form-group hidden">
                           <label class="col-sm-2 control-label text-right" for="select_folder">请选择:</label>
                           <div class="col-sm-9">
                               <label class="ace-file-input">
                                   <input type="file" id="select_folder" webkitdirectory directory required/>
                               </label>
                           </div>
                       </div>
                   </div>
                   <div class="modal-footer">
                       <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                       <button type="button" class="btn btn-primary" id="port_btn">确定</button>
                   </div>
               </div>
           </div>
       </form>
   </div>
    <!-- /.modal -->

</div>
<style>

    /* ------------------------------------------------------------------------------------------------- */
    /* 让 #app 占满剩余空间 */
    .main-content {
        height: 100%;

    }
    .main-content-inner, .page-content, .row {
        height: 100%;
    }

    html, body {
        height: 100%;
        margin: 0;
        padding: 0;
    }

    #app {
        display: flex;
        flex-direction: column;
        height: 100%;
        background-color: #f9f9f9;
    }
    .app-bottom {
        flex: 1;
        display: flex;
        overflow: hidden;
    }

.app-top {
    height: 100px;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    margin: 10px;
    padding: 10px;
    color: #333;
    font-weight: 500;
    background: transparent;
}

.app-bottom-left {
    width: 200px;
    height: calc(100% - 100px);
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    margin: 10px;
    padding: 10px;
    background: transparent;
    color: #444;
}

.app-bottom-right {
    flex: 1;
    height: calc(100% - 100px);
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    margin: 10px;
    padding: 16px;
    background: transparent;
    color: #444;
}



</style>

<style>
    .ellipsis-span {
  display: inline-block;
  max-width: 120px; /* 可根据 el-col 宽度灵活调整 */
  overflow: hidden;
  padding-bottom: 3px;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
}
    .field-arr-label {
        color: #9999a5;
        margin-top: 10px;
        margin-bottom: 10px;
        font-weight: bold;
    }
    .field-arr-label-next {
        color: black;
        margin-top: 10px;
        margin-bottom: 10px;
        font-weight: bold;
    }
    .field-label {
        font-weight: bold;
    }
    /* 隐藏跳转框前后的文字 */
    .custom-pagination .el-pagination__jump {
        margin-left: 0;
        font-size: 0;
    }
    .custom-pagination .el-pagination__jump:before,
    .custom-pagination .el-pagination__jump:after {
        content: none;
    }
    .el-table__header th {
        border-bottom: 3px solid #e1e1e1 !important;
    }
    /* 表格最底部的线（蓝色，加粗） */
    .el-table::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        border-bottom: 2px solid #e1e1e1 !important;
    }
    .cell-input {
        height: 26px;
        margin-left: -10px;
    }

        .cell-select .el-select__wrapper {
            height: 26px;
            min-height: 26px;
            margin-left: -11px;
        }

.el-table .el-table__row {
  height: 50px;
}
.btn-prev {
    border-radius: 50% !important;
}
.btn-next {
    border-radius: 50% !important;
}

</style>
<link rel="stylesheet" href="lib/elementPlus/index.css"/>
<script src="lib/third/vue3/vue.global.js"></script>
<script src="lib/elementPlus/index.full.min.js"></script>
<script src="lib/elementPlus/zh-cn.js"></script>

<script>
    console.log(require.cache)
    delete require.cache[require.resolve('./vue/paramConf/MSG_ASSET.js')];
    require('./vue/paramConf/MSG_ASSET.js');
    // $('#handlerForm').validator();
</script>
