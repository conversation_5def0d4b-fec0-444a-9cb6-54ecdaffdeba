<!--&lt;!&ndash; element-plus &ndash;&gt;-->

<!--<script src="vue/admin/controller/port_param.js"></script>-->

<div class="main-content">
    <div class="main-content-inner">
        <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs">
            <div class="page-content">
                <div class="page-header">
                    <h1>参数配置
                        <small>
                            <i class="ace-icon fa fa-angle-double-right"></i>
                            通信参数
                        </small>
                    </h1>
                </div>
                <!-- /.page-header -->
                <div class="row">
                    <div id="app">
                        <div class="process-container">
                            <div
                            class="step"
                            :class="{ active: activeTabs === 1 }"
                            >
                            <div class="circle">1</div>
                            <div class="label">通信参数</div>
                            </div>
                            <div
                            class="step"
                            :class="{ active: activeTabs === 2 }"
                            >
                            <div class="circle">2</div>
                            <div class="label">平台参数</div>
                            </div>
                        </div>
                        <div v-if="activeTabs === 1" style="padding: 5px;width: 100%;display: flex;align-items: center;justify-content: center;">
                            <el-form ref="formRef" :model="commParamForm" :rules="formRules" label-width="250px">
                                <el-row>
                                    <el-form-item label="数据采集服务端口(服务器、工作站)" prop="servPort">
                                        <el-input v-model="commParamForm.servPort" class="formItemWidth" placeholder="请输入范围1-65535端口号"></el-input>
                                    </el-form-item>
                                </el-row>
                                <el-row>
                                    <el-form-item label="装置与平台交互端口" prop="selfPort">
                                        <el-input v-model="commParamForm.selfPort" class="formItemWidth" placeholder="请输入范围1-65535端口号"></el-input>
                                    </el-form-item>
                                </el-row>
                                <el-row>
                                    <el-form-item label="数据采集服务端口(安全防护设备)" prop="SYSLOGPort">
                                        <el-input v-model="commParamForm.SYSLOGPort" class="formItemWidth" placeholder="请输入范围1-65535端口号"></el-input>
                                    </el-form-item>
                                </el-row>
                                <el-row>
                                    <el-form-item label="SNMP/TRAP端口(网络设备)" prop="TRAPPort">
                                        <el-input v-model="commParamForm.TRAPPort" class="formItemWidth" placeholder="请输入范围1-65535端口号"></el-input>
                                    </el-form-item>
                                </el-row>
                            </el-form>
                        </div>
                        <div v-else style="padding: 5px;width: 100%;display: flex;align-items: center;justify-content: center;flex-direction: column;">
                            <div style="display: flex;align-items: center;justify-content: flex-end;margin-top: 30px;padding-right: 5%;width: 100%;">
                                <el-button type="success" @click="() => operationFn('ADD')">新增</el-button>
                            </div>
                                <el-table stripe :max-height="300" :data="platInfoList">
                                    <el-table-column type="index" label="编号" width="60"></el-table-column>
                                    <el-table-column label="平台IP" prop="platIP"></el-table-column>
                                    <el-table-column label="平台管理权限" prop="permission">
                                        <template #default="scope">
                                            <span> {{ getAuthNameByCode(scope.row.permission) }} </span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="通信分组号" prop="groupId"></el-table-column>
                                    <el-table-column label="通信组内链路的优先级" prop="groupPriority"></el-table-column>
                                    <el-table-column label="操作" width="200">
                                        <template #default="scope">
                                            <el-button type="primary" size="small" @click="() => { operationFn('EDIT', scope.row, scope.$index) }">修改</el-button>
                                            <el-button type="danger" size="small" @click="() => { operationFn('DELETE', scope.row) }">删除</el-button>
                                        </template>
                                        </el-table-column>
                                </el-table>
                        </div>
                        <div style="display: flex;align-items: center;justify-content: center;margin-top: 30px;">
                            <el-button v-if="activeTabs === 1" type="success" @click="() => editFn('NEXT')">下一步</el-button>
                            <el-button v-else type="success" @click="() => editFn('PRE')">上一步</el-button>
                            <el-button v-if="activeTabs !== 1" type="success" @click="() => editFn('SAVE')">保存</el-button>
<!--                            <el-button type="primary" @click="() => importExportStep1Handler('IMPORT')">导入</el-button>-->
<!--                            <el-button type="primary" @click="() => importExportStep1Handler('EXPORT')">导出</el-button>-->
                        </div>
                        <el-dialog v-model="updateDialogVisible" @close="() => { operationFn('CLOSE') }" :title="updateDialogType === 'ADD' ? '新增' : '编辑'"  width="50%">
                            <div class="dialog_content">
                                <el-form ref="nextFormRef" :model="rowData" :rules="nextStepFormRules" label-width="140px">
                                    <el-row>
                                        <el-col>
                                            <el-form-item label="平台IP" prop="platIP">
                                                <el-input v-model="rowData.platIP" placeholder="请输入正确ip格式：0.0.0.0~***************"></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col>
                                            <el-form-item label="平台管理权限" prop="permission">
                                                <el-select v-model="rowData.permission" placeholder="请选择平台管理权限">
                                                    <el-option v-for="item in authEnum" :key="item.code"  :label="item.name" :value="item.code"></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col>
                                            <el-form-item label="通信分组号" prop="groupId">
                                                <el-input v-model="rowData.groupId" type="number" placeholder="请输入0-16正整数"></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col>
                                            <el-form-item label="通信组内链路的优先级" prop="groupPriority">
                                                <el-input v-model="rowData.groupPriority" type="number" placeholder="请输入0-15正整数"></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </el-form>
                            </div>
                            <div slot="footer" class="dialog-footer">
                                <el-button type="primary" @click="() => { operationFn('SAVE') }">提交</el-button>
                                <el-button @click="() => { operationFn('CLOSE') }">关闭</el-button>
                            </div>
                        </el-dialog>

                    </div>
                    <!-- /.col -->
                </div>
                <!-- /.row -->
            </div>
            <!-- /.page-content -->
        </div>
    </div>
    <!-- /.main-content -->
    <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
        <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
    </a>
    <!-- 验证用户身份-->
   <div class="modal" id="validateUserDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
        aria-hidden="true">
       <form class="form-horizontal" id="validateUserForm" method="post" action="" data-toggle="validator" role="form">
           <div class="modal-dialog">
               <div class="modal-content">
                   <div class="modal-header" style="color: white;background: deepskyblue">
                       <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                       <h4 class="modal-title">验证用户身份</h4>
                   </div>
                   <div class="modal-body" style="padding: 10px 50px;">
                       <p>请输入登录密码</p>
                       <input type="hidden" id="opraType"/>
                       <input type="password" id="password" class="form-control" placeholder="密码" required/>
                       <div class="help-block with-errors"></div>
                   </div>
                   <div class="modal-footer">
                       <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                       <button type="submit" class="btn btn-primary" id="validateuser_btn">确定
                       </button>
                   </div>
               </div>
           </div>
       </form>
   </div>
    <!-- /.modal -->
    <!-- 导入导出选择对话框 -->
   <div class="modal fade" id="confirmPortDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
        aria-hidden="true">
       <form class="form-horizontal" id="confirmPortForm" method="post" action="" data-toggle="validator" role="form">
           <div class="modal-dialog" role="document">
               <div class="modal-content">
                   <div class="modal-header">
                       <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                           <span aria-hidden="true">&times;</span>
                           <span class="sr-only">Close</span>
                       </button>
                       <h4 class="modal-title" wa-name="import_tt">请选择</h4>
                   </div>
                   <div class="modal-body">
                       <div class="form-group">
                           <label class="col-sm-2 control-label text-right" for="select_file">请选择:</label>
                           <div class="col-sm-9">
                               <label class="ace-file-input">
                                   <input type="file" id="select_file" required/>
                               </label>
                           </div>
                       </div>
                       <div class="form-group hidden">
                           <label class="col-sm-2 control-label text-right" for="select_folder">请选择:</label>
                           <div class="col-sm-9">
                               <label class="ace-file-input">
                                   <input type="file" id="select_folder" webkitdirectory directory required/>
                               </label>
                           </div>
                       </div>
                   </div>
                   <div class="modal-footer">
                       <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                       <button type="button" class="btn btn-primary" id="port_btn">确定</button>
                   </div>
               </div>
           </div>
       </form>
   </div>
    <!-- /.modal -->

</div>
<style>
    /* 去掉el-input内置的边框 */
    .el-input__inner {
        border: none !important;
    }
    .formItemWidth {
        width: 250px !important;
    }
    .el-table__header th {
        border-bottom: 3px solid #e1e1e1 !important;
    }
    /* 表格最底部的线（蓝色，加粗） */
    .el-table::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        border-bottom: 2px solid #e1e1e1 !important;
    }
     .dialog_content {
        padding: 20px !important;
    }
    .dialog-footer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 20px !important;
        background-color: #eff3f8;
    }
    .cell-input {
        height: 26px;
        margin-left: -10px;
    }

        .cell-select .el-select__wrapper {
            height: 26px;
            min-height: 26px;
            margin-left: -11px;
        }

    .el-table .el-table__row {
    height: 50px;
    }
    .process-container {
        margin-bottom: 10px;
        display: flex;
        justify-content: center; /* 横向居中 */
        align-items: center;
        gap: 30px;
        font-family: Arial, sans-serif;
        user-select: none;
        font-size: 12px;
    }

    .step {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #999;
    transition: color 0.3s ease;
    line-height: 1;
    }

    .step .circle {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 2px solid #999;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 600;
    margin-bottom: 4px;
    font-size: 12px;
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
    }

    .step.active {
    color: #409eff;
    }

    .step.active .circle {
    background-color: #409eff;
    border-color: #409eff;
    color: white;
    }

    /* 移除步骤间的连线 */
    .step:not(:last-child)::after {
        display: none;
    }
</style>
<link rel="stylesheet" href="lib/elementPlus/index.css"/>
<script src="lib/third/vue3/vue.global.js"></script>
<script src="lib/elementPlus/index.full.min.js"></script>
<script src="lib/elementPlus/zh-cn.js"></script>

<script>
    // console.log(require.cache)
    delete require.cache[require.resolve('./vue/paramConf/MSG_CommunicationConfig')];
    require('./vue/paramConf/MSG_CommunicationConfig');
    // $('#handlerForm').validator();
</script>
