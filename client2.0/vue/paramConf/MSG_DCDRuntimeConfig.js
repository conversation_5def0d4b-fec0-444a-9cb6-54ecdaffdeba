const DCDParamDefine_pb = require('../../script/pb/DCDParamDefine_pb.js')
const PublicDefine_pb = require('../../script/pb/PublicDefine_pb.js');
const PlatformProxyServer_pb = require('../../script/pb/PlatformProxyServer_pb.js');
const ProxyServer_pb = require('../../script/pb/ProxyServer_pb.js');
const util = require('../../lib/util.js');
const ui_util = require('../../script/view/ui_util.js');
const tcpInstance = require('../../lib/v2/tls_client.js');
const {createApp, ref, onMounted, reactive, onBeforeMount} = Vue;
const {ipcRenderer, dialog} = require('electron');
const { ElMessage } = ElementPlus;
const { cloneDeep } = require('lodash')
// 校验IP或IP段
const validate100 = (rule, value, callback) => {
    if (typeof value === 'string' && value.includes('.')) return callback(new Error('请在50-100之间取值整数'))
  if (!value || isNaN(value) || value < 50 || value > 100) {
    return callback(new Error('请在50-100之间取值'))
  } else {
    return callback()
  }
}
// 校验IP或IP段
const validate10 = (rule, value, callback) => {
    let num;
    if (typeof value === 'string') {
        const str = value.trim();
        // 只允许纯数字：禁止小数点、负号、空格、科学计数法等
        if (!/^\d+$/.test(str)) {
            return callback(new Error('请在1-10之间取值整数'));
        }
        num = Number(str);
    } else if (typeof value === 'number') {
        if (!Number.isFinite(value)) {
            return callback(new Error('请在1-10之间取值整数'));
        }
        if (!Number.isInteger(value)) {
            return callback(new Error('请在1-10之间取值整数'));
        }
        num = value;
    } else {
        return callback(new Error('请输入数字'));
    }
  if (!value || isNaN(value) || value < 1 || value > 10) {

      if (typeof value === 'string' && value.includes('.')) return callback(new Error('请在1-10之间取值整数'))
    return callback(new Error('请在1-10之间取值'))
  } else {
    return callback()
  }
}
// 校验IP或IP段
const validate600 = (rule, value, callback) => {
    if (typeof value === 'string' && value.includes('.')) return callback(new Error('请在10-600之间取值整数'))
  if (!value || isNaN(value) || value < 10 || value > 600) {
    return callback(new Error('请在10-600之间取值'))
  } else {
    return callback()
  }
}
const validate13600 = (rule, value, callback) => {
    if (typeof value === 'string' && value.includes('.')) return callback(new Error('请在1-3600之间取值整数'))
    if (!value || isNaN(value) || value < 1 || value > 3600) {
        return callback(new Error('请在1-3600之间取值'))
    } else {
        return callback()
    }
}
const rule = {
    cpuMax: [
        { required: false, message: '请输入', trigger: 'blur' },
        { required: false, message: validate100.Error, validator: validate100, trigger: ['blur', 'change'] },
    ],
    memMax: [
        { required: false, message: '请输入', trigger: 'blur' },
        { required: false, message: validate100.Error, validator: validate100, trigger: ['blur', 'change'] },
    ],
    diskMax: [
        { required: false, message: '请输入', trigger: 'blur' },
        { required: false, message: validate100.Error, validator: validate100, trigger: ['blur', 'change'] },
    ],
    logfailMax: [
        { required: false, message: '请输入', trigger: 'blur' },
        { required: false, message: validate10.Error, validator: validate10, trigger: ['blur', 'change'] },
    ],
    mergeCycle: [
        { required: false, message: '请输入', trigger: 'blur' },
        { required: false, message: validate600.Error, validator: validate600, trigger: ['blur', 'change'] },
    ],
    offTimeJudge: [
        { required: false, message: '请输入', trigger: 'blur' },
        { required: false, message: validate13600.Error, validator: validate13600, trigger: ['blur', 'change'] },
    ],
}
const app = createApp({
    setup() {
        // 前置获取用户及请求信息
        const requestMsgId = util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET)
        const user_name = $("body").attr("user_name_info");
        // 配置发起请求
        const tcpConfigPDSetting = (pageCode, functionCode, data = null) => {
            const msgPayload = {
                uuid: util.genTaskId(),
                type: String(pageCode),
                param: String(functionCode),
                addInfo: []
            }
            if (data) msgPayload.addInfo = [data]
            let sendMsg = JSON.stringify(msgPayload)
            console.log('send =>', sendMsg);
            // 配置管理,参数配置
            tcpInstance.sendJsonMsg(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET, sendMsg).then();
        }
        // 响应
        const getData = () => {
            tcpConfigPDSetting(
                PlatformProxyServer_pb.MSG_PDConfigParam.FunctionType.DEVICE_RUNNING,
                PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.QUERY
            )
        }
        onBeforeMount(() => {
            initOnMessage() // 前置注册响应事件
            getData()
        })
        const formatDataFn = (msg) => {
                let form =  {}
                const addInfoList = msg.getAddinfoList();
                addInfoList.forEach((anyItem) => {
                    try {
                        let DCD = DCDParamDefine_pb.MSG_DCDRuntimeConfig
                        console.log('DCD', DCD.prototype)
                        const bytes = anyItem.getValue(); // 获取Any中的二进制数据
                        const msg1 = DCD.deserializeBinary(bytes)
                        form = {
                            cpuMax: msg1.getCpumax(),
                            memMax: msg1.getMemmax(),
                            diskMax: msg1.getDiskmax(),
                            logfailMax: msg1.getLogfailmax(),
                            mergeCycle: msg1.getMergecycle(),
                            offTimeJudge: msg1.getOfftimejudge()
                        }
                        Object.keys(form).forEach(key => {
                            deviceParamList[key] = form[key];
                        })
                    } catch (error) {
                        console.log('error', error);
                    }
                })
            }
        const formRef = ref(null)
        const formRules = ref(rule)
        const addRedStar = (h, { column }) => {
            return [
                h('span', { style: 'color: red' }, '*'),
                h('span', ' ' + column.label)
            ]
        }
        // const copyNetInter = ref([])
        const copyDevice_param = ref([])
        // common
        const isEdit = ref(false)
        const reload = () => {
            ipcRenderer.send('loadPage');
        }
        const editFn = (type) => {
            switch (type) {
                case 'ON': {
                    copyDevice_param.value = cloneDeep(formData.deviceParamList)
                    isEdit.value = true
                    break;
                }
                case 'OFF': {
                    formData.deviceParamList = cloneDeep(copyDevice_param.value)
                    isEdit.value = false
                    break;
                }
                case 'SAVE': {
                    formRef.value.validate(valid => {
                        if (valid) {
                            onClickUserValidate('save')
                        } else {
                            ui_util.showFadeTip('请检查填写内容！')
                        }
                    })
                    break;
                }
                default:
                    break;
            }
        }
        // 监听事件注册
        const initOnMessage = () => {
            // 页面监听
            tcpInstance.onMessage(util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET), onMessageHandle)
            // 用户校验监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCVALIDATEUSER, (validatePb) => {
                const validateMsg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(validatePb);
                if (validateMsg.getErrorCode() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                    if (validateMsg.getRequestMsgId() === requestMsgId) {
                        switch ($('#opraType').val()) {
                            case 1:{
                                $('#confirmPortDlg').modal('show')
                                $('#confirmPortDlg .modal-title').attr("wa-name","import_tt")
                                $('#confirmPortDlg .modal-body .form-group:nth-child(1)').removeClass("hidden")
                                $('#confirmPortDlg .modal-body .form-group:nth-child(2)').addClass("hidden")
                                break
                            };
                            case 2:{
                                $('#confirmPortDlg').modal('show')
                                $('#confirmPortDlg .modal-title').attr("wa-name","export_tt")
                                $('#confirmPortDlg .modal-body .form-group:nth-child(2)').removeClass("hidden")
                                $('#confirmPortDlg .modal-body .form-group:nth-child(1)').addClass("hidden")
                                break
                            };
                            case 'save':{
                                tcpConfigPDSetting(
                                PlatformProxyServer_pb.MSG_PDConfigParam.FunctionType.DEVICE_RUNNING,
                                PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.MODIFY,
                                cloneDeep(deviceParamList)
                                )
                                // 配置管理：FUNCTIONTYPE_CONFIGMANAGE 参数配置：CONFIGMANAGEPACKETTYPE_PARAMSET
                                break
                            };
                            default:
                                break;
                        }
                    }
                } else {
                    ui_util.getErrorTips(validateMsg.getErrorCode(), '验证用户身份')
                }
            })
                        // 导入 监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCIMPORTDATA, (pb) => importExportDoneHandler('IMPORT', pb))
            // 导出 监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCEXPORTDATA, (pb) => importExportDoneHandler('EXPORT', pb))
        }
        // 导入导出文件操作1：监听按钮点击打开用户验证
        const importExportStep1Handler = (type) => {
            $('#validateUserDlg').modal('show');
            e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
            $('#validateUserForm').get(0).reset()
            $('#opraType').val(type)
        }
        // 导入导出文件操作2：发送请求  file 在导出时是filePath不是文件内容
        const importExportStep2Handler = (TYPE, file, fileName) => {
            const isImport = TYPE === 'IMPORT';
            const reqData = isImport ? new ProxyServer_pb.MSG_CPImportData() : new ProxyServer_pb.MSG_CPExportData();
            reqData.setType(PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_EVENT);
            isImport && reqData.setData(util.readFromFile(file, false).toString());
            tcpInstance.sendProtoMsg(isImport ? ProxyServer_pb.PSMessageId.PSMESSAGEID_CPIMPORTDATA : ProxyServer_pb.PSMessageId.PSMESSAGEID_CPEXPORTDATA, reqData);
            isImport && $('.parent_content').busyLoad("show", { background: "rgba(0, 0, 0, 0.59)", fontawesome: "fa fa-spinner fa-spin fa-3x fa-fw" });
        }
        // 接收请求后续操作
        const importExportDoneHandler = (type, pb) => {
            const isImport = type === 'IMPORT';
            const msg = ProxyServer_pb[isImport ? 'MSG_PCImportData' : 'MSG_PCExportData'].deserializeBinary(pb);
            isImport && $('.parent_content').busyLoad("hide");
            if (msg.getErrorCode() !== 0) return ui_util.getErrorTips(msg.getErrorCode(), `${isImport ? '导入' : '导出'}事件处理`);
            isImport ? ui_util.showLoading(30) : util.saveToCSV(msg.getData(), `${filePath}/${fileName}`);
            ui_util.showFadeTip(`${isImport ? '导入' : '导出'}事件处理成功`);
            getData();
        }
        let file="",filePath="",fileName="sntp.cfg";
        // 初始化选择文件夹路径&选择文件
        $('#select_folder').ace_file_input({
          no_file:'请选择文件夹',
          btn_choose:'选择',
          btn_change:null,
          droppable:false,
          onchange:null,
          thumbnail:false
        });
        document.querySelector('#select_folder').addEventListener('change', e => {
          for (let entry of e.target.files){
            console.log(entry.name, entry.path);
            filePath=entry.path
          }
        });
        $('#select_file').ace_file_input({
          no_file:'请选择文件',
          btn_choose:'选择',
          btn_change:null,
          droppable:false,
          onchange:null,
          thumbnail:false,
          allowExt: ['cfg'],
        }).on('change', function(){
          let fileDir = $(this).val();
          let suffix = fileDir.substr(fileDir.lastIndexOf("."));
          if ("" == fileDir||".cfg" != suffix) {
            ui_util.showFadeTip('请选择.cfg后缀文件!');
            return false;
          }
          // console.log($(this).data('ace_input_files'));
          file=$(this).data('ace_input_files')[0].path
        });
        // 注册导入导出按钮监听
        $('#port_btn').on('click',function (e) {
          if ($("#port_btn").hasClass("disabled")) return
          if($('#confirmPortDlg .modal-body .form-group:nth-child(1)').hasClass("hidden")){
            if(!$("#select_folder").next("span.ace-file-container").hasClass("selected")){
              ui_util.showFadeTip('请选择文件路径!')
              return;
            }
          }else if($('#confirmPortDlg .modal-body .form-group:nth-child(2)').hasClass("hidden")){
            if(!($("#select_file").next("span.ace-file-container").hasClass("selected"))){
              ui_util.showFadeTip('请选择文件!')
              return;
            }
          }
          $('#confirmPortDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
          e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
          if($('#confirmPortDlg .modal-title').attr("wa-name")=="import_tt"){
            importEvents('IMPORT', file, fileName)
          }else if($('#confirmPortDlg .modal-title').attr("wa-name")=="export_tt"){
            exportEvents('EXPORT', filePath, fileName)
          }
        })
        // 响应事件处理
        const onMessageHandle = (pb) => {
            const msg = PlatformProxyServer_pb.MSG_DPConfigParam.deserializeBinary(pb);
            if (msg.getResult() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                switch (Number(msg.getParam())) {
                    case PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.QUERY: {
                        $('#validateuser_btn').off('click'); // 用完一定要手动移除该事件，否则会积累触发点击事件
                        formatDataFn(msg)
                        break
                    }
                    case PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.MODIFY: {
                        $('#validateuser_btn').off('click'); // 用完一定要手动移除该事件，否则会积累触发点击事件
                        isEdit.value = false
                        ui_util.showFadeTip('操作成功！')
                        getData()
                        break
                    }
                    default:
                        break;
                }
            } else {
                ui_util.getErrorTips(msg.getResult(), '操作')
            }
        }
        const onClickUserValidate = (type) => {
            $('#validateUserDlg').modal('show'); // 打开用户密码校验
            $('#validateUserForm').get(0).reset() // 置空密码校验数据
            $('#opraType').val(type) // 存入保存标识
            // 弹出密码校验框
            $('#validateuser_btn').on('click',function (e) {
            if ($("#validateuser_btn").hasClass("disabled")) {
                return;
            }
            $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
            e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
            let username=user_name
            let psd=$('#validateUserDlg #password').val()
            const user_sign = $("body").attr("user_sign_info");
            // 发送消息
            let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
            sendMsg.setUserName(username)
            sendMsg.setPassword(psd)
            sendMsg.setRequestMsgId(requestMsgId)
            tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg).then();
        })
        }
        // netInter
        const broadcastEnum = ref([
            {code: 0, name: '非广播'},
            {code: 1, name: '广播'}
        ])
        const deviceParamList = reactive({
                cpuMax: 60,
                memMax: 20,
                diskMax: 3400,
                logfailMax: 60,
                mergeCycle: 60
        })
                // 网口
        const formData = reactive({
            deviceParamList: [
            {
                cpuMax: 60,
                memMax: 20,
                diskMax: 3400,
                logfailMax: 60,
                mergeCycle: 60
            },
            {
                cpuMax: 60,
                memMax: 20,
                diskMax: 3400,
                logfailMax: 60,
                mergeCycle: 60
            },
            {
                cpuMax: 60,
                memMax: 20,
                diskMax: 3400,
                logfailMax: 60,
                mergeCycle: 60
            },
        ]
        })
        // flowInter

        return {
            importExportStep1Handler,

            deviceParamList,
            broadcastEnum,
            formRef,
            formData,
            addRedStar,
            reload,
            formRules,

            isEdit,
            editFn,
        };
    }
})
// app.use(ElementPlus);
app.use(ElementPlus, {
    locale: ElementPlusLocaleZhCn,
  })
app.mount('#app');
