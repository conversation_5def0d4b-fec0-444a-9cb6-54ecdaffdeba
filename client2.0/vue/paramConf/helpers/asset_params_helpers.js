const DicDefine_pb = require('../../../script/pb/DicDefine_pb.js');
const DCDParamDefine_pb = require('../../../script/pb/DCDParamDefine_pb.js')
const AssetModelDefine_pb = require('../../../script/pb/AssetModelDefine_pb.js');
const asset_map = require('./asset_map.js');
const {cloneDeep} = require("lodash");
const ASSET_MAP_HELPS = require("../../ASSET_MAP/ASSET_MAP_HELPS");
const util = require('../../../lib/util.js');
const MAP=  {
    // 资产
    ASSET: {
        ASSET: '资产',
        GID: '资产 ID',
        ATAG: '资产标识',
        DSN: '设备序列号',
        DNAME: '设备名称',
        TYPE: '资产类型',
        CONAST: '已确认资产',
        LABEL: '资产标签集',
        SOURCE: '数据来源'
    },
    // 资产节点
    ASSET_NODE: {
        ASSET_NODE: '资产节点',
        GID: '资产 ID',
        IP: 'IP',
        MAC: 'MAC',
        IFID: '网络接口 ID',
        SOURCE: '数据来源'
    },
    // SNMP 交互配置
    INTERCONF: {
        INTERCONF: 'SNMP 交互配置',
        GID: '资产 ID',
        RMTPORT: '远程登录端口',
        RMTTP: '远程方式',
        RMTACT: '远程账户',
        RMTPSD: '远程账户密码',
        SNMPPORT: 'SNMP监听端口',
        SNMPVER: 'snmp版本',
        SNMPV3USER: 'snmp v3 用户名',
        SNMPAUTH: 'snmp v3 认证算法',
        SNMPENC: 'snmp v3 加密算法',
        SNMPREAD: 'snmp read 团体名/认证密码',
        SNMPWRITE: 'snmp write 团体名/认证密码',
        ANAMETH: '解析方法'
    },
    // 管理
    MANAGEMENT: {
        MANAGEMENT: '管理',
        GID: '资产 ID',
        DCLOUDID: '调控云资产标识',
        DCCID: '所属调度标识',
        STAID: '所属区域标识',
        FNAME: '资产全称',
        SNAME: '资产简称',
        ATYPE: '资产类型',
        ABRAND: '资产厂商',
        AMODEL: '资产型号',
        STATUS: '运行状态',
        ISCII: '关基设施',
        CLASS: '资产类别',
        MTYPE: '管辖类型',
        NTTYPE: '网络类型',
        AREA: '安全区',
        NOTE: '备注',
        NOTES: '备注'
    },
    // 应用
    APPLICATION: {
        APPLICATION: '应用',
        GID: '资产 ID',
        APPID: '应用软件标识',
        NAME: '名称',
        BRAND: '品牌',
        VER: '版本',
        TYPE: '应用类型',
        STYPE: '应用子类型',
        MDIPA: '被管 IP-A',
        MDIPB: '被管 IP-B',
        APPDESC: '应用描述',
        SOURCE: '数据来源',
        ENSTAT: '监测代理软件启用状态',
        ISCARD: '是否是加密卡',
        FBIDAPP: '隔离应用软件标识',
    }
}
const DATA_TEMP = {
    ASSET: ['GID', 'ATAG', 'DSN', 'DNAME', 'TYPE', 'CONAST', 'LABEL', 'SOURCE'],
    ASSET_NODE: ['GID', 'IP', 'MAC', 'IFID', 'SOURCE'],
    INTERCONF: ['GID', 'RMTPORT', 'RMTTP', 'RMTACT', 'RMTPSD', 'SNMPPORT', 'SNMPVER', 'SNMPV3USER', 'SNMPAUTH', 'SNMPENC', 'SNMPREAD', 'SNMPWRITE', 'ANAMETH'],
    MANAGEMENT: ['GID', 'DCLOUDID', 'DCCID', 'STAID', 'FNAME', 'SNAME', 'ATYPE', 'ABRAND', 'AMODEL', 'STATUS', 'ISCII', 'CLASS', 'MTYPE', 'NTTYPE', 'AREA', 'NOTES'],
    APPLICATION: ['GID', 'APPID', 'NAME', 'BRAND', 'VER', 'TYPE', 'STYPE', 'MDIPA', 'MDIPB', 'APPDESC', 'SOURCE', 'ENSTAT', 'ISCARD', 'FBIDAPP']
}
function getShowNameByMapWithValue(Enums, value) {
    if ([undefined, null, ''].includes(value)) return '--'
    if (Enums && Object.keys(Enums).length) {
        if (value in Enums)
            return Enums[value]
    } else {
        return '--'
    }
}
function formatTreeDataFn(msg){
    const data = []
    const addInfoList = msg.getAddinfoList();
    let ASSET_PB = DCDParamDefine_pb.AssetConfig
    console.log('11113wdw', ASSET_PB.prototype)
    addInfoList.forEach((anyItem, anyIndex) => {
        try {
            const bytes = anyItem.getValue(); // 获取Any中的二进制数据
            const deAnyItem = ASSET_PB.deserializeBinary(bytes) // 反序列化
            console.log('deAnyItem',bytes, deAnyItem)
            const ASSET_BUF = asset_map.getProtobufFieldValue(deAnyItem, 'ASSET')
            console.log('xxx', util.getProtobufFieldValue(ASSET_BUF, 'DNAME'))
            console.log('TYPE', util.getProtobufFieldValue(ASSET_BUF, 'TYPE'))
            const dataItem = {
                label: util.getProtobufFieldValue(ASSET_BUF, 'DNAME'),
                value: anyIndex,
                type: util.getProtobufFieldValue(ASSET_BUF, 'TYPE'),
                bufData: deAnyItem,
            }
            console.log('树节点', dataItem)
            data.push(dataItem)
        } catch (e) {
            console.log('解构 err', e)
        }
    })
    return data
}
function getEditData(bufData) {
    const data = { ASSET: {}, ASSET_NODE: [], INTERCONF: {}, MANAGEMENT: {}, APPLICATION: [] }
    try {
        const ASSET_BUF = asset_map.getProtobufFieldValue(bufData, 'ASSET')
        data.ASSET = util.buildObjectFromProto(ASSET_MAP_HELPS.ASSET_PARAM_MAP.ASSET, ASSET_BUF, false)

        const INTERCONF_BUF = asset_map.getProtobufFieldValue(bufData, 'INTERCONF')
        data.INTERCONF = util.buildObjectFromProto(ASSET_MAP_HELPS.ASSET_PARAM_MAP.INTERCONF, INTERCONF_BUF, false)
        const MANAGEMENT_BUF = asset_map.getProtobufFieldValue(bufData, 'MANAGEMENT')
        data.MANAGEMENT = util.buildObjectFromProto(ASSET_MAP_HELPS.ASSET_PARAM_MAP.MANAGEMENT, MANAGEMENT_BUF, false)
        const ASSET_NODE_BUF = asset_map.getProtobufFieldValue(bufData, 'ASSET_NODE', true)
        const APPLICATION_BUF = asset_map.getProtobufFieldValue(bufData, 'APPLICATION', true)
        ASSET_NODE_BUF.forEach(bufItem => {
            const ITEM = util.buildObjectFromProto(ASSET_MAP_HELPS.ASSET_PARAM_MAP.ASSET_NODE, bufItem, false)
            if (ITEM && Object.keys(ITEM).length) data.ASSET_NODE.push(ITEM)
        })
        APPLICATION_BUF.forEach(bufItem => {
            const ITEM = util.buildObjectFromProto(ASSET_MAP_HELPS.ASSET_PARAM_MAP.APPLICATION, bufItem, false)
            if (ITEM && Object.keys(ITEM).length) data.APPLICATION.push(ITEM)
        })
        console.log('返回数据_编辑用 =>', data)
        return data
    } catch (e) {
        console.log('edit: err', e)
        return data
    }
}
function handleNodeClick(clickData) {
    console.log('点击事件', clickData, clickData.bufData)
    const bufData = clickData.bufData
    try {
        const ASSET_BUF = asset_map.getProtobufFieldValue(bufData, 'ASSET')
        const header = util.buildObjectFromProto(ASSET_MAP_HELPS.ASSET_PARAM_MAP.ASSET, ASSET_BUF, true)
        const content = { ASSET_NODE: [], INTERCONF: [], MANAGEMENT: [], APPLICATION: [] }
        // 基本信息
        const ASSET_NODE_BUF = asset_map.getProtobufFieldValue(bufData, 'ASSET_NODE', true)
        const INTERCONF_BUF = asset_map.getProtobufFieldValue(bufData, 'INTERCONF')
        const MANAGEMENT_BUF = asset_map.getProtobufFieldValue(bufData, 'MANAGEMENT')
        const APPLICATION_BUF = asset_map.getProtobufFieldValue(bufData, 'APPLICATION', true)
        console.log(Array.isArray(ASSET_NODE_BUF), Array.isArray(INTERCONF_BUF), Array.isArray(MANAGEMENT_BUF), Array.isArray(APPLICATION_BUF))
        ASSET_NODE_BUF.forEach(bufItem => {
            console.log('bufItem111', bufItem)
            const data = util.buildObjectFromProto(ASSET_MAP_HELPS.ASSET_PARAM_MAP.ASSET_NODE, bufItem, true)
            if (data && Object.keys(data).length) content.ASSET_NODE.push(data)
        })
        INTERCONF_BUF ? content.INTERCONF.push(util.buildObjectFromProto(ASSET_MAP_HELPS.ASSET_PARAM_MAP.INTERCONF, INTERCONF_BUF, true)) : ''
        MANAGEMENT_BUF ? content.MANAGEMENT.push(util.buildObjectFromProto(ASSET_MAP_HELPS.ASSET_PARAM_MAP.MANAGEMENT, MANAGEMENT_BUF, true)) : ''
        APPLICATION_BUF.forEach(bufItem => {
            console.log('bufItem111222', bufItem.getAppmtype())
            const data = util.buildObjectFromProto(ASSET_MAP_HELPS.ASSET_PARAM_MAP.APPLICATION, bufItem, true)
            if (data && Object.keys(data).length) content.APPLICATION.push(data)
        })
        console.log('返回数据_展示用 =>', { headerData: header, contentData: [content] })
        return { headerData: header, contentData: [content] }
    } catch (e) {
        console.log('err', e)
        return { headerData: {}, contentData: [] }
    }
}
module.exports = {
    MAP,
    // 数据模板
    DATA_TEMP,
    // 配置树
    formatTreeDataFn,
    // 点击事件
    handleNodeClick,
    // 编辑用数据
    getEditData
}
/**
 [{
 ASSET: {
 GID: '730232010000000001',
 ATAG: 'temp31222NRFCMD0001',
 DSN: 'unidxxxxxxxxxxxxx',
 DNAME: '装置名',
 TYPE: '7305',
 LABEL: '',
 SOURCE: 1
 },
 ASSET_NODE: [{
 IP: '************',
 MAC: '48:7b:6b:39:83:d3',
 IFID: 'eth1',
 SOURCE: 1
 }],
 INTERCONF: [{
 RMTPORT: '22',
 RMTTP: '0',
 RMTACT: 'user',
 RMTPSD: 'password',
 SNMPPORT: '161',
 SNMPVER: '1',
 SNMPV3USER: 'snmpv3',
 SNMPAUTH: '1',
 SNMPENC: '1',
 SNMPREAD: 'public',
 SNMPWRITE: 'private',
 ANAMETH: '0'
 }],
 MANAGEMENT: [{
 DCLOUDID: '070199010600000001',
 FNAME: '河北.调度主站.II 区_FES1',
 SNAME: 'II 区_FES1',
 ATYPE: '7101',
 ABRAND: '北京科东',
 AMODEL: 'PSNTA-5000-ZK',
 STATUS: '1001',
 ISCII: true,
 CLASS: 1,
 MTYPE: true,
 NTTYPE: 1,
 AREA: '1',
 NOTE: ''
 }],
 APPLICATION: [{
 APPID: 'P001',
 NAME: 'veadcard',
 BRAND: 'KEDONG',
 VER: '5.3.3',
 TYPE: 1,
 STYPE: 'VEAD',
 MDIPA: '************',
 MDIPB: '***********',
 APPDESC: 'vead',
 SOURCE: 1
 }]
 }]
 * */
