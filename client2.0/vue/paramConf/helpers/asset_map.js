const DicDefine_pb = require('../../../script/pb/DicDefine_pb.js');

// 字典----------------------------------------------------------------------------------------------------
const Enums = {
  // 资产模型类型
  DEVICE_ASSET_ENUM: [
    { code: 1, name: '主机设备' },
    { code: 2, name: '监测装置' },
    { code: 3, name: '防火墙' },
    { code: 4, name: '纵向加密' },
    { code: 5, name: '隔离装置' },
    { code: 6, name: '交换机' },
    { code: 7, name: '路由器' },
    { code: 8, name: '运维网关' },
    { code: 9, name: '入侵检测' },
    { code: 10, name: '服务器密码机' }
  ],
  // 数据来源字典Enum
  SOURCE_Enum: [
    { code: DicDefine_pb.DataSource.DATA_SOURCE_HOST_MONITORING, name: '主机监测' },
    { code: DicDefine_pb.DataSource.DATA_SOURCE_SWITCH, name: '交换机' },
    { code: DicDefine_pb.DataSource.DATA_SOURCE_ROUTER, name: '路由器' },
    { code: DicDefine_pb.DataSource.DATA_SOURCE_ISOLATION_DEVICE, name: '隔离装置' },
    { code: DicDefine_pb.DataSource.DATA_SOURCE_VERTICAL_ENCRYPTION, name: '纵向加密' },
    { code: DicDefine_pb.DataSource.DATA_SOURCE_FIREWALL, name: '防火墙' },
    { code: DicDefine_pb.DataSource.DATA_SOURCE_MONITORING_DEVICE, name: '监测装置' },
    { code: DicDefine_pb.DataSource.DATA_SOURCE_MONITORING_PLATFORM, name: '监测平台' },
    { code: DicDefine_pb.DataSource.DATA_SOURCE_OPERATION_GATEWAY, name: '运维网关' },
    { code: DicDefine_pb.DataSource.DATA_SOURCE_ASSET_DETECTION, name: '资产探测' },
    { code: DicDefine_pb.DataSource.DATA_SOURCE_TRAFFIC_COLLECTION, name: '流量采集' },
    { code: DicDefine_pb.DataSource.DATA_SOURCE_PLUGIN, name: '插件' },
  ],
  // CPU架构
  ARCH: [
  { code: DicDefine_pb.CpuArchitecture.CPU_ARCH_UNKNOWN, name: '未知' },
  { code: DicDefine_pb.CpuArchitecture.CPU_ARCH_X86, name: 'X86' },
  { code: DicDefine_pb.CpuArchitecture.CPU_ARCH_ARM, name: 'ARM' },
  { code: DicDefine_pb.CpuArchitecture.CPU_ARCH_POWERPC, name: 'POWERPC' },
  { code: DicDefine_pb.CpuArchitecture.CPU_ARCH_RISC_V, name: 'RISC-V' },
  { code: DicDefine_pb.CpuArchitecture.CPU_ARCH_MIPS, name: 'MIPS' },
  { code: DicDefine_pb.CpuArchitecture.CPU_ARCH_LOONGARCH, name: 'LoongArch' },
  { code: DicDefine_pb.CpuArchitecture.CPU_ARCH_OTHER, name: '其他' }
  ],
  // 资产类型字典Enum
  TYPE_Enum: [
  { code: DicDefine_pb.AssetType.ASSET_TYPE_UNKNOWN, name: '未知' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_SERVER, name: '服务器' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_WORKSTATION, name: '工作站' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_STORAGE_DEVICE, name: '存储设备' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_BLADE_CHASSIS, name: '刀片服务器机箱' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_BLADE_SERVER, name: '刀片服务器' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_ROUTER, name: '路由器' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_SWITCH, name: '交换机' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_INDUSTRIAL_SWITCH, name: '工业交换机' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_MEDIA_CONVERTER, name: '光电转换器' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_SERIAL_SERVER, name: '串口服务器' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_LATERAL_ISOLATION, name: '横向隔离装置' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_LONGITUDINAL_ENCRYPTION, name: '纵向加密装置' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_FIREWALL, name: '防火墙' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_IDS, name: '入侵检测设备' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_NET_SEC_MONITOR, name: '网络安全监测装置' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_ENCRYPTION_CARD, name: '加密卡' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_LARGE_SCREEN, name: '大屏幕' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_PRECISION_AC, name: '精密空调' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_KVM, name: 'KVM' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_TIME_SYNC, name: '时间同步装置' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_PRINTER, name: '打印机' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_NETWORK_CABLE, name: '网络连接线' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_MERGING_UNIT, name: '合并单元' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_INTELLIGENT_TERMINAL, name: '智能终端' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_SPECIAL_TELEMETRY_GATEWAY, name: '专用远动网关机' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_TELEMETRY_DEVICE, name: '远动装置' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_MEASUREMENT_CONTROL, name: '测控装置' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_PMU, name: '相量测量装置' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_ENERGY_ACQUISITION, name: '电能量采集终端' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_NETWORK_ANALYZER, name: '网络分析仪' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_SMART_PD_TERMINAL, name: '智能配电终端' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_SMART_PD_MONITORING, name: '智能配电测控终端' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_TRANSFORMER_MONITORING, name: '配变监控终端' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_SMART_PD_SYNC_MEAS, name: '智能配电同步测量终端' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_OS, name: '操作系统' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_DATABASE, name: '数据库' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_MIDDLEWARE, name: '中间件' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_APP_SOFTWARE_PKG, name: '应用软件包' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_APP_SOFTWARE, name: '应用软件' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_APPLICATION, name: '应用程序' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_MALWARE_MONITOR, name: '恶意代码监测系统' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_ANTIVIRUS, name: '防病毒' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_OPS_GATEWAY, name: '运维网关' },
  { code: DicDefine_pb.AssetType.ASSET_TYPE_OTHER, name: '其他' }
  ],
  // 外设接口类型
  INTERFACE_TYPE: [
  { code: 0, name: '未知' },
  { code: DicDefine_pb.PeripheralInterfaceType.PERIPHERAL_INTERFACE_USB, name: 'USB接口' },
  { code: DicDefine_pb.PeripheralInterfaceType.PERIPHERAL_INTERFACE_BLUETOOTH, name: '蓝牙模块' },
  { code: DicDefine_pb.PeripheralInterfaceType.PERIPHERAL_INTERFACE_SERIAL, name: '串口' },
  { code: DicDefine_pb.PeripheralInterfaceType.PERIPHERAL_INTERFACE_PARALLEL, name: '并口' },
  { code: DicDefine_pb.PeripheralInterfaceType.PERIPHERAL_INTERFACE_OPTICAL_DRIVE, name: '光驱' },
  { code: DicDefine_pb.PeripheralInterfaceType.PERIPHERAL_INTERFACE_OTHER, name: '其他' }
  ],
  // 外设设备类型
  PERIPHERAL_TYPE: [
    { code: DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_UNKNOWN, name: '未知' },
    { code: DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_STORAGE, name: '存储类' },
    { code: DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_COMM, name: '通信设备' },
    { code: DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_INPUT, name: '键鼠设备' },
    { code: DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_OPTICAL_DISC, name: '光盘' },
    { code: DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_DISPLAY, name: '显示器' },
    { code: DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_PRINTER, name: '打印机' },
    { code: DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_WIRELESS_NIC, name: '无线网卡' },
    { code: DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_OTHER, name: '其他' }
  ],
  // 外设协议编码
  PERIPHERAL_IFPCODE:[
  { code: DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_DUMMY, name: '手动添加的虚拟值' },
  { code: DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_AUDIO, name: 'Audio (01)' },
  { code: DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_CDC_CTRL, name: 'Communications and CDC Control (02)' },
  { code: DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_HID, name: 'HID(Human Interface Device) (03)' },
  { code: DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_PHYSICAL, name: 'Physical (05)' },
  { code: DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_IMAGE, name: 'Image (06)' },
  { code: DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_PRINTER, name: 'Printer (07)' },
  { code: DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_MASS_STORAGE, name: 'Mass Storage (08)' },
  { code: DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_HUB, name: 'Hub (09)' },
  { code: DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_CDC_DATA, name: 'CDC-Data (0a)' },
  { code: DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_SMART_CARD, name: 'Smart Card (0b)' },
  { code: DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_CONTENT_SECURITY, name: 'Content Security (0d)' },
  { code: DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_VIDEO, name: 'Video (0e)' },
  { code: DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_HEALTHCARE, name: 'Personal Healthcare (0f)' },
  { code: DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_DIAGNOSTIC, name: 'Diagnostic Device (dc)' },
  { code: DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_WIRELESS_CTRL, name: 'Wireless Controller (e0)' },
  { code: DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_MISCELLANEOUS, name: 'Miscellaneous (ef)' },
  { code: DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_APP_SPECIFIC, name: 'Application Specific (fe)' },
  { code: DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_VENDOR_SPECIFIC, name: 'Vendor Specific (ff)' }
  ],
  // 通用
  COMMON_ENUMS: {
    // 数据来源
    SOURCE: {
      [DicDefine_pb.DataSource.DATA_SOURCE_DUMMY]: '手动添加的虚拟值',
      [DicDefine_pb.DataSource.DATA_SOURCE_HOST_MONITOR]: '主机监测',
      [DicDefine_pb.DataSource.DATA_SOURCE_NETWORK_COLLECT]: '网络采集',
      [DicDefine_pb.DataSource.DATA_SOURCE_ASSET_DISCOVERY]: '资产探测',
      [DicDefine_pb.DataSource.DATA_SOURCE_TRAFFIC_COLLECT]: '流量采集'
    }
  },
  // 硬件
  HARDWARE_ENUMS: {
    // 硬件异常字典
    HERR: {
      [DicDefine_pb.HardwareExceptionStatus.HW_EX_DUMMY]: '手动添加的虚拟值',
      [DicDefine_pb.HardwareExceptionStatus.HW_EX_POWER_OVERLOAD]: '电源过载',
      [DicDefine_pb.HardwareExceptionStatus.HW_EX_POWER_CTRL_FAILURE]: '电源控制器异常',
      [DicDefine_pb.HardwareExceptionStatus.HW_EX_MAIN_POWER_FAILURE]: '主电源故障',
      [DicDefine_pb.HardwareExceptionStatus.HW_EX_DISK_ALERT]: '存在磁盘告警',
      [DicDefine_pb.HardwareExceptionStatus.HW_EX_DISK_ABNORMAL]: '硬盘状态异常',
      [DicDefine_pb.HardwareExceptionStatus.HW_EX_MEM_ECC_ERROR]: '内存ECC错误',
      [DicDefine_pb.HardwareExceptionStatus.HW_EX_FAN_FAILURE]: '风扇存在故障',
      [DicDefine_pb.HardwareExceptionStatus.HW_EX_POWER_REDUNDANCY_ALERT]: '电源冗余告警'
    },
    // CPU架构
    ARCH: {
      [DicDefine_pb.CpuArchitecture.CPU_ARCH_UNKNOWN]: '未知',
      [DicDefine_pb.CpuArchitecture.CPU_ARCH_X86]: 'X86',
      [DicDefine_pb.CpuArchitecture.CPU_ARCH_ARM]: 'ARM',
      [DicDefine_pb.CpuArchitecture.CPU_ARCH_POWERPC]: 'POWERPC',
      [DicDefine_pb.CpuArchitecture.CPU_ARCH_RISC_V]: 'RISC-V',
      [DicDefine_pb.CpuArchitecture.CPU_ARCH_MIPS]: 'MIPS',
      [DicDefine_pb.CpuArchitecture.CPU_ARCH_LOONGARCH]: 'LoongArch',
      [DicDefine_pb.CpuArchitecture.CPU_ARCH_OTHER]: '其他'
    },
    // 外设接口类型
    PERIPHERAL_INTERFACE_TYPE: {
      [DicDefine_pb.PeripheralInterfaceType.PERIPHERAL_INTERFACE_DUMMY]: '手动添加的虚拟值',
      [DicDefine_pb.PeripheralInterfaceType.PERIPHERAL_INTERFACE_USB]: 'USB接口',
      [DicDefine_pb.PeripheralInterfaceType.PERIPHERAL_INTERFACE_BLUETOOTH]: '蓝牙模块',
      [DicDefine_pb.PeripheralInterfaceType.PERIPHERAL_INTERFACE_SERIAL]: '串口',
      [DicDefine_pb.PeripheralInterfaceType.PERIPHERAL_INTERFACE_PARALLEL]: '并口',
      [DicDefine_pb.PeripheralInterfaceType.PERIPHERAL_INTERFACE_OPTICAL_DRIVE]: '光驱',
      [DicDefine_pb.PeripheralInterfaceType.PERIPHERAL_INTERFACE_OTHER]: '其他'
    },
    // 外设设备类型
    PERIPHERAL_TYPE: {
      [DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_UNKNOWN]: '未知',
      [DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_STORAGE]: '存储类',
      [DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_COMM]: '通信设备',
      [DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_INPUT]: '键鼠设备',
      [DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_OPTICAL_DISC]: '光盘',
      [DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_DISPLAY]: '显示器',
      [DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_PRINTER]: '打印机',
      [DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_WIRELESS_NIC]: '无线网卡',
      [DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_OTHER]: '其他'
    },
    // 外设接口协议编码
    IFPCODE: {
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_DUMMY]: '手动添加的虚拟值',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_AUDIO]: 'Audio',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_CDC_CTRL]: 'Communications and CDC Control',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_HID]: 'HID(Human Interface Device)',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_PHYSICAL]: 'Physical',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_IMAGE]: 'Image',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_PRINTER]: 'Printer',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_MASS_STORAGE]: 'Mass Storage',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_HUB]: 'Hub',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_CDC_DATA]: 'CDC-Data',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_SMART_CARD]: 'Smart Card',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_CONTENT_SECURITY]: 'Content Security',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_VIDEO]: 'Video',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_HEALTHCARE]: 'Personal Healthcare',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_DIAGNOSTIC]: 'Diagnostic Device',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_WIRELESS_CTRL]: 'Wireless Controller',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_MISCELLANEOUS]: 'Miscellaneous',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_APP_SPECIFIC]: 'Application Specific',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_VENDOR_SPECIFIC]: 'Vendor Specific'
    },
    // 传感器异常
    OTHERERRSTA: {
      [DicDefine_pb.SensorExceptionStatus.SENSOR_EX_DUMMY]: '手动添加的虚拟值',
      [DicDefine_pb.SensorExceptionStatus.SENSOR_EX_FAN_SPEED]: '风扇转速异常',
      [DicDefine_pb.SensorExceptionStatus.SENSOR_EX_TEMPERATURE]: '温度异常'
    }
  },
  // 软件
  SOFTWARE_ENUMS: {
    // 登陆方式
    LMETHOD: {
      [DicDefine_pb.LoginMethod.LOGIN_METHOD_DUMMY]: '手动添加的虚拟值',
      [DicDefine_pb.LoginMethod.LOGIN_METHOD_FTP]: 'FTP',
      [DicDefine_pb.LoginMethod.LOGIN_METHOD_LOCAL]: 'LOCAL',
      [DicDefine_pb.LoginMethod.LOGIN_METHOD_RADIUS]: 'RADIUS',
      [DicDefine_pb.LoginMethod.LOGIN_METHOD_RDP]: 'RDP',
      [DicDefine_pb.LoginMethod.LOGIN_METHOD_SSH]: 'SSH',
      [DicDefine_pb.LoginMethod.LOGIN_METHOD_TELNET]: 'TELNET',
      [DicDefine_pb.LoginMethod.LOGIN_METHOD_TFTP]: 'TFTP',
      [DicDefine_pb.LoginMethod.LOGIN_METHOD_VNC]: 'VNC',
      [DicDefine_pb.LoginMethod.LOGIN_METHOD_X11]: 'X11',
      [DicDefine_pb.LoginMethod.LOGIN_METHOD_SERIAL]: '串口',
      [DicDefine_pb.LoginMethod.LOGIN_METHOD_OTHER]: '其他'
    },
    // 应用类型
    APPLICATION_TYPE: {
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_DUMMY]: '手动添加的虚拟值',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_VERTICAL_ENCRYPTION]: '纵向加密应用',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_HORIZONTAL_ISOLATION]: '横向隔离应用',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_FIREWALL]: '防火墙应用',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_SWITCH]: '交换机应用',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_ROUTER]: '路由器应用',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_HOST_MONITORING]: '主机监测应用',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_TRUSTED_VERIFICATION]: '可信验证应用',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_INTRUSION_DETECTION]: '入侵检测应用',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_OPERATION_GATEWAY]: '运维网关应用',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_MALICIOUS_CODE_MONITORING]: '恶意代码监测应用',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_MONITORING_DEVICE]: '监测装置应用',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_DATABASE]: '数据库应用',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_CRYPTOGRAPHIC_SERVER]: '服务器密码机应用',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_SECURITY_MONITORING_SYSTEM]: '安全监测系统应用',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_OTHER]: '其他应用'
    },
    // 应用子类型
    APPLICATION_STYPE: {
      // 纵向加密
      1: {'VEAD': '纵向加密装置', 'CARD': '纵向加密卡', 'VEAD-MINI': '微型纵向加密装置'},
      // 横向隔离
      2: {'FID': '正向隔离装置', 'BID': '反向隔离装置', 'TS': '隔离传输软件'},
      // 防火墙
      3: {'FW': '防火墙', 'WAF': 'WEB防火墙'},
      // 交换机
      4: {'SW-MAIN': '局域网核心交换机', 'SW-FRONT': '调度数据网交换机', 'SW-PT': '局域网接入交换机'},
      // 路由器
      5: {'RT': '路由器'},
      // 主机监测
      6: {'SVR-PT': '服务器', 'SVR-PC': '工作站'},
      // 可信验证
      7: {'TVM': '主机可信验证'},
      // 入侵检测
      8: {'IDS': '入侵监测系统', 'IPS': '入侵防御系统'},
      // 运维网关
      9: {'SASH': '主站运维网关', 'PMG': '便携式运维网关'},
      // 恶意代码监测
      10: {'ACC': '防恶意代码客户端', 'MCMS': '恶意代码监测', 'AV': '防病毒', 'FMCD': '恶意代码网络流量监测采集'},
      // 监测装置
      11: {'DCD-Ⅰ': 'Ⅰ型监测装置', 'DCD-Ⅱ': 'Ⅱ型监测装置', 'DCD-H': '高性能监测装置', 'DCD-N': '普通版监测装置'},
      // 数据库
      12: {'DB': '数据库'},
      // 服务器密码机
      13: {'CS': '密码机', 'CS-CARD': '密码卡'},
      // 安全监测系统
      14: {'MP': '调度安全监测平台', 'DMP': '配电安全监测平台'},
      // 其他应用
      99: {'OTHER': '其他'}
    }
  },
  // 网络
  NETWORK_ENUMS: {
    // 资产用途
    CTYPE: {
      [DicDefine_pb.AssetPurpose.ASSET_PURPOSE_DUMMY]: '手动添加的虚拟值',
      [DicDefine_pb.AssetPurpose.ASSET_PURPOSE_GATEWAY]: '网关',
      [DicDefine_pb.AssetPurpose.ASSET_PURPOSE_ROUTING]: '路由',
      [DicDefine_pb.AssetPurpose.ASSET_PURPOSE_SWITCHING]: '交换',
      [DicDefine_pb.AssetPurpose.ASSET_PURPOSE_LONGITUDINAL]: '纵向',
      [DicDefine_pb.AssetPurpose.ASSET_PURPOSE_ISOLATION]: '隔离',
      [DicDefine_pb.AssetPurpose.ASSET_PURPOSE_FIREWALL]: '防火墙'
    },
    // 接口绑定模式
    BONDM: {
      [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_DUMMY]: '手动添加的虚拟值',
      [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_DISABLED]: '未启用',
      [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_0]: 'Mode 0',
      [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_1]: 'Mode 1',
      [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_2]: 'Mode 2',
      [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_3]: 'Mode 3',
      [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_4]: 'Mode 4',
      [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_5]: 'Mode 5',
      [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_6]: 'Mode 6',
      [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_BAGG]: 'BAGG',
      [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_LAGG]: 'LAGG'
    },
    // 策略类型字典
    ACCESS_CONTROL_LIST_TYPE: {
      [DicDefine_pb.PolicyType.POLICY_TYPE_DUMMY]: '手动添加的虚拟值',
      [DicDefine_pb.PolicyType.POLICY_TYPE_FIREWALL]: '防火墙策略',
      [DicDefine_pb.PolicyType.POLICY_TYPE_LONGITUDINAL_ENCRYPT]: '纵向加密策略',
      [DicDefine_pb.PolicyType.POLICY_TYPE_ISOLATION_DEVICE]: '隔离设备策略',
      [DicDefine_pb.PolicyType.POLICY_TYPE_SWITCH]: '交换机策略',
      [DicDefine_pb.PolicyType.POLICY_TYPE_ROUTER]: '路由器策略',
      [DicDefine_pb.PolicyType.POLICY_TYPE_OTHER]: '其他'
    }
  },
  // 管理
  MANAGEMENT_ENUMS: {
    ATYPE: {
      [DicDefine_pb.AssetType.ASSET_TYPE_DUMMY]: '手动添加的虚拟值',
      [DicDefine_pb.AssetType.ASSET_TYPE_UNKNOWN]: '未知',
      [DicDefine_pb.AssetType.ASSET_TYPE_SERVER]: '服务器',
      [DicDefine_pb.AssetType.ASSET_TYPE_WORKSTATION]: '工作站',
      [DicDefine_pb.AssetType.ASSET_TYPE_STORAGE_DEVICE]: '存储设备',
      [DicDefine_pb.AssetType.ASSET_TYPE_BLADE_CHASSIS]: '刀片服务器机箱',
      [DicDefine_pb.AssetType.ASSET_TYPE_BLADE_SERVER]: '刀片服务器',
      [DicDefine_pb.AssetType.ASSET_TYPE_ROUTER]: '路由器',
      [DicDefine_pb.AssetType.ASSET_TYPE_SWITCH]: '交换机',
      [DicDefine_pb.AssetType.ASSET_TYPE_INDUSTRIAL_SWITCH]: '工业交换机',
      [DicDefine_pb.AssetType.ASSET_TYPE_MEDIA_CONVERTER]: '光电转换器',
      [DicDefine_pb.AssetType.ASSET_TYPE_SERIAL_SERVER]: '串口服务器',
      [DicDefine_pb.AssetType.ASSET_TYPE_LATERAL_ISOLATION]: '横向隔离装置',
      [DicDefine_pb.AssetType.ASSET_TYPE_LONGITUDINAL_ENCRYPTION]: '纵向加密装置',
      [DicDefine_pb.AssetType.ASSET_TYPE_FIREWALL]: '防火墙',
      [DicDefine_pb.AssetType.ASSET_TYPE_IDS]: '入侵检测设备',
      [DicDefine_pb.AssetType.ASSET_TYPE_NET_SEC_MONITOR]: '网络安全监测装置',
      [DicDefine_pb.AssetType.ASSET_TYPE_ENCRYPTION_CARD]: '加密卡',
      [DicDefine_pb.AssetType.ASSET_TYPE_LARGE_SCREEN]: '大屏幕',
      [DicDefine_pb.AssetType.ASSET_TYPE_PRECISION_AC]: '精密空调',
      [DicDefine_pb.AssetType.ASSET_TYPE_KVM]: 'KVM',
      [DicDefine_pb.AssetType.ASSET_TYPE_TIME_SYNC]: '时间同步装置',
      [DicDefine_pb.AssetType.ASSET_TYPE_PRINTER]: '打印机',
      [DicDefine_pb.AssetType.ASSET_TYPE_NETWORK_CABLE]: '网络连接线',
      [DicDefine_pb.AssetType.ASSET_TYPE_MERGING_UNIT]: '合并单元',
      [DicDefine_pb.AssetType.ASSET_TYPE_INTELLIGENT_TERMINAL]: '智能终端',
      [DicDefine_pb.AssetType.ASSET_TYPE_SPECIAL_TELEMETRY_GATEWAY]: '专用远动网关机',
      [DicDefine_pb.AssetType.ASSET_TYPE_TELEMETRY_DEVICE]: '远动装置',
      [DicDefine_pb.AssetType.ASSET_TYPE_MEASUREMENT_CONTROL]: '测控装置',
      [DicDefine_pb.AssetType.ASSET_TYPE_PMU]: '相量测量装置',
      [DicDefine_pb.AssetType.ASSET_TYPE_ENERGY_ACQUISITION]: '电能量采集终端',
      [DicDefine_pb.AssetType.ASSET_TYPE_NETWORK_ANALYZER]: '网络分析仪',
      [DicDefine_pb.AssetType.ASSET_TYPE_SMART_PD_TERMINAL]: '智能配电终端',
      [DicDefine_pb.AssetType.ASSET_TYPE_SMART_PD_MONITORING]: '智能配电测控终端',
      [DicDefine_pb.AssetType.ASSET_TYPE_TRANSFORMER_MONITORING]: '配变监控终端',
      [DicDefine_pb.AssetType.ASSET_TYPE_SMART_PD_SYNC_MEAS]: '智能配电同步测量终端',
      [DicDefine_pb.AssetType.ASSET_TYPE_OS]: '操作系统',
      [DicDefine_pb.AssetType.ASSET_TYPE_DATABASE]: '数据库',
      [DicDefine_pb.AssetType.ASSET_TYPE_MIDDLEWARE]: '中间件',
      [DicDefine_pb.AssetType.ASSET_TYPE_APP_SOFTWARE_PKG]: '应用软件包',
      [DicDefine_pb.AssetType.ASSET_TYPE_APP_SOFTWARE]: '应用软件',
      [DicDefine_pb.AssetType.ASSET_TYPE_APPLICATION]: '应用程序',
      [DicDefine_pb.AssetType.ASSET_TYPE_MALWARE_MONITOR]: '恶意代码监测系统',
      [DicDefine_pb.AssetType.ASSET_TYPE_ANTIVIRUS]: '防病毒',
      [DicDefine_pb.AssetType.ASSET_TYPE_OPS_GATEWAY]: '运维网关',
      [DicDefine_pb.AssetType.ASSET_TYPE_OTHER]: '其他'
    },
    // 资产投运字典
    STATUS: {
      [DicDefine_pb.AssetOperationStatus.ASSET_OPERATION_DUMMY]: '手动添加的虚拟值',
      [DicDefine_pb.AssetOperationStatus.ASSET_OPERATION_PRE_ACCESS]: '预接入',
      [DicDefine_pb.AssetOperationStatus.ASSET_OPERATION_IN_SERVICE]: '在运',
      [DicDefine_pb.AssetOperationStatus.ASSET_OPERATION_MAINTENANCE]: '检修',
      [DicDefine_pb.AssetOperationStatus.ASSET_OPERATION_DEACTIVATED]: '退运'
    },
    // 网络类型字典
    NTTYPE: {
      [DicDefine_pb.NetworkType.NETWORK_TYPE_DUMMY]: '手动添加的虚拟值',
      [DicDefine_pb.NetworkType.NETWORK_TYPE_BACKBONE_PLANE1]: '骨干网一平面',
      [DicDefine_pb.NetworkType.NETWORK_TYPE_BACKBONE_PLANE2]: '骨干网二平面',
      [DicDefine_pb.NetworkType.NETWORK_TYPE_NATIONAL_DISPATCH_ACCESS]: '国调接入网',
      [DicDefine_pb.NetworkType.NETWORK_TYPE_REGIONAL_DISPATCH_ACCESS]: '网调接入网',
      [DicDefine_pb.NetworkType.NETWORK_TYPE_PROVINCIAL_ACCESS]: '省级接入网',
      [DicDefine_pb.NetworkType.NETWORK_TYPE_PREFECTURAL_ACCESS]: '地级接入网',
      [DicDefine_pb.NetworkType.NETWORK_TYPE_DISPATCH_INTRANET]: '调度内网',
      [DicDefine_pb.NetworkType.NETWORK_TYPE_DISPATCH_INTRANET_B]: '调度内网B'
    },
    // 安全区
    AREA: {
      [DicDefine_pb.SecurityZone.SECURITY_ZONE_UNKNOWN]: '未知',
      [DicDefine_pb.SecurityZone.SECURITY_ZONE_I]: 'I区',
      [DicDefine_pb.SecurityZone.SECURITY_ZONE_II]: 'II区',
      [DicDefine_pb.SecurityZone.SECURITY_ZONE_III]: 'Ⅲ区',
      [DicDefine_pb.SecurityZone.SECURITY_ZONE_IV]: 'IV区',
      [DicDefine_pb.SecurityZone.SECURITY_ZONE_I_II]: 'I-II区',
      [DicDefine_pb.SecurityZone.SECURITY_ZONE_PROD_CTRL_MGMT]: '生产控制一管理信息大区',
      [DicDefine_pb.SecurityZone.SECURITY_ZONE_II_III]: 'II-III区',
      [DicDefine_pb.SecurityZone.SECURITY_ZONE_III_IV]: 'III-IV区',
      [DicDefine_pb.SecurityZone.SECURITY_ZONE_SEC_ACCESS]: '安全接入区',
      [DicDefine_pb.SecurityZone.SECURITY_ZONE_OTHER]: '其他'
    }
  }
}
// 中文对照----------------------------------------------------------------------------------------------------
const Map = {
// 资产节点对照
  ASSET_NODE_Map: {
  GID: '资产ID',
  IP: 'IP',
  MAC: 'MAC',
  IFID: '网络接口ID',
  CREATET: '数据生成时间',
  UPDATET: '数据更新时间',
  SOURCE: '数据来源'
},
// 硬件对照
HARDWARE_Map: {
  // 通用
  BRAND: '品牌',
  MODEL: '型号',
  VER: '版本',
  CREATET: '数据生成时间',
  UPDATET: '数据更新时间',
  SOURCE: '数据来源',
  WIDTH: '总线宽度',
  TYPE: '类型',

  // HARDWARE
  HID: '硬件标识',
  SN: '硬件序列号',

  // HARDWARE_STATUS
  HARDWARE_STATUS: '硬件状态',
  TCPUR: '总CPU利用率',
  TMEMR: '总物理内存利用率',
  DISKR: '总磁盘利用率',
  HERR: '硬件异常',
  MTIME: '时标',

  // CPU
  CPU: 'CPU',
  CPUID: '处理器标识',
  ARCH: 'CPU架构',
  CNUM: '物理核心数',

  // CPU_STATUS
  CPU_STATUS: '处理器状态',
  CPUR: 'CPU利用率',
  LCID: '逻辑核心标识',

  // LOGICAL_CORE
  LOGICAL_CORE: '逻辑核心',

  // LCPU_STATUS
  LCPU_STATUS: '逻辑核心状态',
  CPUTT: '运行总时长',
  CPUST: '系统使用时长',
  CPUUT: '用户使用时长',
  CPUHT: '硬中断时长',
  CPUIT: '空闲时长',
  CPUNT: 'NICE时长',
  CPUSFT: '软中断时长',
  CPULT: '丢失时长',
  IOT: 'IO等待时间',
  URATE: '逻辑核心利用率',

  // MEMORY
  MEMORY: '内存',
  MEMID: '内存标识',
  CY: '容量',
  MEMSNID: '内存序列号',

  // DISK
  DISK: '磁盘',
  WWID: '磁盘标识',
  DFILE: '设备文件',
  MBR: '主引导记录哈希值',
  RABLE: '移动存储设备',
  DERR: '磁盘健康度',

  // DISK_STATUS
  DISK_STATUS: '磁盘状态',
  DUSED: '磁盘总使用',

  // NETWORK_CARD
  NETWORK_CARD: '有线网卡',
  NCID: '网卡标识',
  BCMAC: '网卡MAC地址',
  IFCNT: '接口数量',

  // ETHERNET
  ETHERNET: '物理网口',
  ETHID: '网口标识',
  ETHTYP: '类型',
  NTYPE: '网络位置类型',
  QUERL: '队列规则',
  SPEED: '速率',
  WKMD: '工作模式',
  SLOTN: '插槽号',
  CONCSTA: '连接状态',

  // WIRELESS
  WIRELESS: '无线网卡',
  MAC: 'MAC地址',
  PROTO: '协议类型',

  // PERIPHERAL_INTERFACE
  PERIPHERAL_INTERFACE: '外设接口',
  PIID: '外设接口标识',
  PINM: '接口名称',
  ENABLE: '启用状态',
  CONNUM: '接入数量',
  AUTHS: '授权状态',

  // PERIPHERAL
  PERIPHERAL: '外设设备',
  PERID: '外设设备标识',
  NAME: '名称',
  IFPCODE: '协议编码',
  CODE: '设备编码',
  VENDOR: '厂商编号',

  // POWER
  POWER: '电源',
  PWRID: '电源标识',
  RP: '额定功率',

  // GPU
  GPU: '显卡',
  GPUID: 'GPU卡标识',

  // HBA
  HBA: '主机总线适配器',
  HBAID: 'HBA卡标识',

  // RAID
  RAID: '磁盘冗余阵列',
  RAIDID: 'RAID卡标识',

  // BMC
  BMC: '基板管理控制器',
  BMCV: 'IPMI版本',
  BMCSUP: 'BMC支持设备',
  BMCFIRV: 'BMC固件修订版本',

  // SENSOR
  SENSOR: '传感器',
  OTHERTMP: '数值',
  UNIT: '单位',
  STATE: '状态',
  OTHERERRSTA: '异常状态'
},
// 软件对照
SOFTWARE_Map: {
    // 通用
    BRAND: "品牌",
    VER: "版本",
    NAME: "名称",
    TYPE: "类型",
    CREATET: "数据生成时间",
    UPDATET: "数据更新时间",
    SOURCE: "数据来源",
    DESC: "描述",

    // SOFTWARE
    SOFTID: "软件标识",
    MOD: "型号",
    KINFO: "内核信息",
    KPATH: "内核路径",
    KPARAM: "内核参数",
    HNAME: "主机名",
    STIME: "启动时间",
    SLEVEL: "启动级别",
    TZ: "时区",
    EVAR: "环境变量",
    EVHASH: "环境变量哈希值",
    RSTAT: "超级用户启用",
    USTAT: "USB存储功能启用",
    MSTAT: "强制访问控制启用",
    LHOSTS: "本地域名",
    MAXTNUM: "系统最大线程数",
    MAXPNUM: "系统最大进程数",
    MAXFNUM: "系统最大句柄数",
    STERRTYPE: "对时方式",

    // SYSTEM_RUNTIME_STATUS
    SYSTEM_RUNTIME_STATUS: "系统运行状态",
    "15MLOAD": "系统15分钟负载",
    "5MLOAD": "系统5分钟负载",
    "1MLOAD": "系统1分钟负载",
    SUPRNUM: "系统已使用进程数",
    SZPRNUM: "僵尸进程个数",
    UNPRNUM: "不可中断进程数",
    SUTNUM: "系统已使用线程数",
    SUHNUM: "系统已使用句柄数",
    MUSED: "已使用内存",
    MFREE: "空闲内存",
    MSHARED: "共享内存",
    MCACHE: "内存缓存",
    MAVAI: "可用内存",
    SUSED: "已使用交换区",
    SFREE: "空闲交换区",
    SIRATE: "交换分区每秒换入",
    SORATE: "交换分区每秒换出",
    SERR: "软件异常",
    MTIME: "时标",

    // USER
    USER: "用户",
    UID: "用户标识",
    DUGRID: "默认用户组标识",
    USHELL: "默认SHELL",
    UHOME: "用户主目录",
    PCHGTIME: "密码变更时间",
    LKSTAT: "用户锁定状态",
    NPIP: "免密登录IP",
    UMAXPRNUM: "用户最大进程数",

    // USER_STATUS
    USER_STATUS: "用户状态",
    UPRNUM: "用户进程数",
    UZPRNUM: "用户僵尸进程个数",
    UUPRNUM: "用户不可中断进程个数",
    UTNUM: "用户线程数",

    // USER_GROUP
    USER_GROUP: "用户组",
    GRPID: "用户组标识",
    ULIST: "组内用户名清单",

    // SESSION
    SESSION: "会话",
    SESSID: "会话标识",
    FSESS: "上一级会话标识",
    LUID: "登录用户标识",
    LUNAME: "登录用户名",
    SGID: "源资产标识",
    SPID: "软件包标识",
    SPPATH: "源端进程全路径名",
    DGID: "目的资产标识",
    DPID: "进程标识",
    DPPATH: "目的端进程全路径名",
    LMETHOD: "登录方式",
    SIP: "源IP",
    SPORT: "源端口",
    DIP: "目的IP",
    DPORT: "目的端口",
    TNUM: "终端号",
    SSTAT: "当前状态",
    LTIME: "登录时间戳",
    ETIME: "退出时",

    // PARTITION
    PARTITION: "分区",
    PARTID: "分区标识",
    WWID: "磁盘标识",
    DFILE: "磁盘设备文件",
    CAP: "容量(MB)",
    FSTYPE: "文件格式",
    MPOINT: "挂载点",
    ITOT: "索引节点总数",
    RWPERM: "读写权限",
    CRTIME: "创建时间",

    // PARTITION_STATUS
    PARTITION_STATUS: "分区状态",
    PURATE: "分区使用率",
    PWRATE: "分区写数量",
    PRRATE: "分区读数量",
    PUSED: "分区已使用",
    PUNUSED: "分区未使用",
    PFRATE: "分区空闲率",
    PIWAIT: "分区IO等待",
    PUURATE: "分区IO使用率",
    PIURATE: "分区索引节点使用率",
    PIUSAGE: "分区索引节点使用数",
    PIFREE: "分区索引节点空闲数",
    PIFRATE: "分区索引节点空闲率",
    IOERR: "读写异常",

    // PROCESS
    PROCESS: "进程",
    PRID: "进程标识",
    EID: "可执行文件标识",
    PPID: "父进程标识",
    PFNAME: "父进程进程文件名",
    PID: "进程PID",
    FNAME: "文件名",
    SCMD: "启动命令",
    SPARAM: "启动参数",
    WDIR: "工作目录",
    STAT: "状态",
    STIME: "启动时间",
    QTYPE: "退出方式",
    THNUM: "线程数量",
    FDNUM: "文件句柄数",

    // PROCESS_CPU_USAGE_STATUS
    PROCESS_CPU_USAGE_STATUS: "进程CPU使用状态",
    CURATE: "CPU使用率",
    MURATE: "内存利用率",

    // SERVICE
    SERVICE: "服务",
    SVID: "服务标识",
    UNAME: "登录用户名",
    STYPE: "启动类型",
    SELFSSTAT: "自启动状态",
    STARTCMD: "启动命令",
    STOPCMD: "终止命令",
    RESCMD: "重启命令",

    // BOOT_TASK
    BOOT_TASK: "启动任务",
    BOOT: "启动任务",
    BID: "启动任务标识",
    BFNAME: "启动任务文件名",
    BFHASH: "启动任务文件哈希",
    MINUTE: "分钟",
    HOUR: "小时",
    DAY: "日",
    MONTH: "月",
    WEEKDAY: "周",

    // DRIVER
    DRIVER: "驱动",
    DID: "驱动标识",
    AUTHOR: "作者",
    SIG: "签名",
    LDSTAT: "加载状态",
    LDTIME: "加载时间",

    // FILE
    FILE: "文件",
    FID: "文件标识",
    CLASS: "类别",
    PATH: "文件目录",
    FHASH: "文件HASH",
    PERM: "文件权限",
    FSIZE: "文件大小",
    LTFILE: "链接目标文件",
    CTIME: "创建时间",
    LATIME: "最后访问时间",
    LMTIME: "最后修改时间",
    LCTIME: "最新属性变化时间",
    INO: "索引节点号",

    SO_FILE_LIST: "关联动态库文件列表",

    // EXECUTABLE_FILE
    EXECUTABLE_FILE: "可执行文件",
    CAPBIL: "权能列表",

    // SOFTWARE_PACKAGE
    SOFTWARE_PACKAGE: "软件包",
    PNAME: "安装包名",
    PTYPE: "安装包类型",
    ITIME: "安装时间",
    IPATH: "安装路径",

    // VULN_SCAN_RESULT
    VULN_SCAN_RESULT: "漏洞扫描结果",
    HOSTIPSTR: '扫描IP',
    PLATFORMS: '影响范围',
    VULNID: "漏洞ID",
    NODENAME: "漏洞名称",
    SHORTDESC: "漏洞简短描述",
    FULLDESC: "漏洞详细描述",
    REPAIRADVICE: "修复建议",
    RISKLEVEL: "危险级别",
    CVETAG: "CVE编号",
    CVSSSCORE: "CVSS评分",
    UPDATE: "修改时间",
    CREATE: "发布时间",
    SOFTWARE: "软件名称",
    VERSION: "影响版本",
    FINDTIME: "扫描时间",
    REPAIR: "修复状态",

    // CONFIG_CHECK_RESULT
    CONFIG_CHECK_RESULT: "基线核查结果",
    CIID: "核查项ID",
    IP: "设备IP",
    SUBTYPE: "核查对象类型",
    CHECKITEMTYPE: "核查项类型",
    PARSE: "检查点结果",
    ISCHECK: "检查结果",
    CHECKITEMNAME: "检查项名称",
    SYSCODE: "编号",
    FIXEDPRO: "加固方案",
    JUDGE: "判定依据",
    ITEMCONTENT: "配置要求",

    // APPLICATION
    APPLICATION: "应用实例",
    APP_EXECUTABLE_FILE: '可执行程序列表',
    APPID: "应用标识",
    RSTA: "运行状态",
    MDIPA: "被管IP-A",
    MDIPB: "被管IP-B",
    DCDLIST: "监测装置列表",
    APPDESC: "应用描述",

    // PLUGIN
    PLUGIN: "安全插件",
    PLUG_ID: "插件ID",
    FLIST: "插件功能码",
    PSTAT: "插件状态",
    EREASON: "异常原因",

    // PLUGIN_STATUS
    PLUGIN_STATUS: "安全插件状态",
    MEMR: "内存利用率",
    DISK: "使用磁盘大小",
    PRONUM: "进程或线程数量",
    FILENUM: "文件描述符数",
    PORTNUM: "监听端口数"
},
// 网络对照
NETWORK_Map: {
  // 通用字段
  CREATET: "数据生成时间",
  UPDATET: "数据更新时间",
  SOURCE: "数据来源",
  TYPE: "类型",
  DESC: "描述",
  DESCRIPTION: "描述",
  NAME: "名称",

  // 基础网络信息
  NETID: "网络标识",
  FWD: "网络转发功能启用",
  IPV6: "IPV6启用",
  FINGER: "网络协议指纹",
  CTYPE: "空间类型",

  // 网络状态
  NETWORK_STATUS: "网络状态",
  NERR: "网络异常",

  // 网络接口信息
  NETWORK_INTERFACE: "网络接口",
  IFID: "接口标识",
  ETHID: "网口标识",
  NCID: "无线网卡标识",
  IFNM: "接口名",
  CLASS: "型式分类",
  LINKSTA: "启用状态",
  SPEED: "协商速率",
  MTU: "最大传输单元",
  IP: "IP地址列表",
  MAC: "MAC地址",
  DHCP: "地址分配模式",
  DHCPS: "DHCP服务器地址",
  BONDM: "绑定模式",
  BONDIF: "绑定接口名",
  LIF: "逻辑接口名",
  IFIDX: "接口索引",
  AUTHS: "授权状态",
  VLIST: "接口所属VLAN列表",
  MACBIND: "绑定MAC地址列表",

  // 网络接口运行状态
  NETIF_RUNNING_STATUS: "网络接口运行状态",
  IN: "接收字节数",
  OUT: "发送字节数",
  INLOSE: "接收丢包率",
  OUTLOSE: "发送丢包率",
  INERR: "接收错包率",
  OUTERR: "发送错包率",
  RXPKGS: "接收包的数量",
  TXPKGS: "发送包的数量",
  RXERR: "收到出错包数",
  TXERR: "发送包出错数",
  FRAME: "接收到的帧错误总数",
  CARRIER: "发送时检测到的载波丢失次数",
  COLLIS: "发生冲突次数",
  TXQLEN: "传输队列长度",
  LOST: "丢失数",
  ERR: "错误数",

  // 网络路由
  ROUTE: "网络路由",
  RID: "路由标识",
  DSTNET: "目标网络",
  DSTMK: "目标掩码",
  NHA: "下一跳地址",
  METRIC: "度量值",
  RPROT: "路由协议类型",

  // 地址学习表
  ADDRESS: "地址学习表",
  VID: "VLAN标识",
  ARPTP: "ARP类型",

  // 网络监听
  LISTENNING_PORT: "网络监听",
  LISPORTID: "网络监听ID",
  PRID: "进程标识",
  PRNAME: "进程名",
  PORT: "监听端口",
  PROTO: "最高层协议",
  PROVER: "协议版本",
  CLIN: "客户端连接数",
  CWNUM: "关闭等待数",
  TWNUM: "等待关闭数",
  SRNUM: "同步接收数",

  // 网络邻居信息
  NEIGHBOR: "网络邻居",
  NBRID: "邻居信息标识",
  NBRIFID: "邻居接口标识",
  NBRIDX: "邻居索引号",
  UPTIME: "邻居信息更新时间",
  CHATYPE: "邻居标识类型",
  CHAID: "邻居标识",
  NBRIP: "邻居设备IP",
  PIDTYPE: "网口标识类型",
  PORTID: "网口标识",
  PORTDESC: "网口描述",
  SYSNM: "邻居系统名",
  SYSDESC: "邻居系统描述",

  // VLAN信息
  VLAN: "VLAN列表",
  VTYPE: "VLAN类型",
  MASK: "掩码",
  NIFLST: "接口列表",

  // 访问控制策略
  ACCESS_CONTROL_LIST: "访问控制策略",
  ACLID: "访问控制策略标识",
  ACLNUM: "策略编号",
  SRCMAC: "源MAC组",
  DSTMAC: "目的MAC组",
  FRTYPE: "帧类型",
  SRCBIP: "源起始IP",
  SRCEIP: "源终止IP",
  DSTBIP: "目的起始IP",
  DSTEIP: "目的终止IP",
  SRCPORT: "源端口范围",
  DSTPORT: "目的端口范围",
  DIRECTION: "传输方向",
  ACTION: "动作",
  SRCNIP: "源转换IP地址",
  DSTNIP: "目的转换IP地址",
  SRCNP: "源转换端口",
  DSTNP: "目的转换端口",
  NATPRO: "转换协议类型",
  EFCTIME: "生效时间段",
  ENABLE: "启用状态",

  // VRRP
  VRRP: "VRRP组",
  VRRPID: "VRRP组ID",
  VIP: "虚拟IP",
  VMASK: "掩码",
  MASTER: "主备状态",
},
// 管理对照
 MANAGEMENT_Map: {
  // 基本标识
  GID: "资产ID",

  // 组织归属
  DCLOUDID: "调控云资产标识",
  DCCID: "所属调度标识",
  STAID: "所属区域标识",

  // 资产属性
  FNAME: "资产全称",
  SNAME: "资产简称",
  ATYPE: "资产类型",
  ABRAND: "资产厂商",
  AMODEL: "资产型号",
  CLASS: "资产类别",
  MTYPE: "管辖类型",
  NTTYPE: "网络类型",
  AREA: "安全区",
  ISCII: "关基设施",

  // 运行状态
  STATUS: "运行状态",

  // 附加信息
  NOTES: "备注",
  CREATET: "数据生成时间",
  UPDATET: "数据更新时间"
},
}
// 数据模板----------------------------------------------------------------------------------------------------
const DATA_TEMP = {
   // 资产节点
  ASSET_NODE: {
    INFO: ['GID', 'IP', 'MAC', 'IFID', 'CREATET', 'UPDATET', 'SOURCE']
  },
  HARDWARE: {
    INFO: ['HID', 'BRAND', 'MODEL', 'VER', 'SN', 'CREATET', 'UPDATET', 'SOURCE'], // 硬件基础信息
    // --硬件状态   字典: HERR
    HARDWARE_STATUS: ['HID', 'TCPUR', 'TMEMR', 'DISKR', 'HERR', 'MTIME'],
    // CPU  字典：ARCH SOURCE  数组：LOGICAL_CORE
    CPU: ['CPUID', 'BRAND', 'MODEL', 'VER', 'WIDTH', 'ARCH', 'CNUM', 'CREATET', 'UPDATET', 'SOURCE'],
    // --处理器状态
    CPU_STATUS: ['CPUID', 'CPUR', 'MTIME', 'LCID', 'CPUID', 'CREATET', 'UPDATET', 'SOURCE'],
    // CPU => 逻辑核心  字典：SOURCE
    LOGICAL_CORE: ['LCID', 'CPUID', 'CREATET', 'UPDATET', 'SOURCE'],
    // CPU => 逻辑核心状态  字典：SOURCE
    LCPU_STATUS: ['LCID', 'CPUID', 'CPUTT', 'CPUSFT', 'CPUUT', 'CPUHT', 'CPUIT', 'CPUNT', 'CPUSFT', 'CPULT', 'IOT', 'URATE', 'MTIME', 'CREATET', 'UPDATET', 'SOURCE'],
    // 内存  字典：SOURCE
    MEMORY: ['MEMID', 'BRAND', 'MODEL', 'VER', 'CY', 'MEMSNID', 'CREATET', 'UPDATET', 'SOURCE'],
    // 磁盘  字典：SOURCE
    DISK: ['WWID', 'BRAND', 'MODEL', 'VER', 'CY', 'DFILE', 'MBR', 'RABLE', 'DERR', 'CREATET', 'UPDATET', 'SOURCE'],
    // --磁盘状态
    DISK_STATUS: ['WWID', 'DUSED', 'MTIME'],
    // *有线网卡  字典：SOURCE 数组：ETHERNET
    NETWORK_CARD: ['NCID', 'BRAND', 'MODEL', 'VER', 'BCMAC', 'IFCNT', 'WIDTH', 'CREATET', 'UPDATET', 'SOURCE'],
    // --物理网口  字典：SOURCE
    ETHERNET: ['ETHID', 'NCID', 'NAME', 'ETHTYP', 'NTYPE', 'QUERL', 'MAC', 'SPEED', 'WKMD', 'SLOTN', 'CONCSTA', 'CREATET', 'UPDATET', 'SOURCE'],
    // 无线网卡  字典：SOURCE
    WIRELESS: ['NCID', 'BRAND', 'MODEL', 'VER', 'MAC', 'PROTO', 'CREATET', 'UPDATET', 'SOURCE'],
    // 外设接口  字典：TYPE  SOURCE
    PERIPHERAL_INTERFACE: ['PIID', 'PINM', 'TYPE', 'ENABLE', 'CONNUM', 'AUTHS', 'WIDTH', 'CREATET', 'UPDATET', 'SOURCE'],
    // --外设设备 字典：TYPE IFPCODE  SOURCE
    PERIPHERAL: ['PERID', 'PIID', 'NAME', 'BRAND', 'MODEL', 'TYPE', 'IFPCODE', 'CODE', 'VENDOR', 'CREATET', 'UPDATET', 'SOURCE'],
    // 电源  字典：SOURCE
    POWER: ['PWRID', 'BRAND', 'MODEL', 'VER', 'RP', 'CREATET', 'UPDATET', 'SOURCE'],
    // 显卡  字典：SOURCE
    GPU: ['GPUID', 'BRAND', 'MODEL', 'VER', 'WIDTH', 'CREATET', 'UPDATET', 'SOURCE'],
    // 主机总线适配器  字典：SOURCE
    HBA: ['HBAID', 'BRAND', 'MODEL', 'VER', 'WIDTH', 'CREATET', 'UPDATET', 'SOURCE'],
    // 磁盘冗余阵列  字典：SOURCE
    RAID: ['RAIDID', 'BRAND', 'MODEL', 'VER', 'WIDTH', 'CREATET', 'UPDATET', 'SOURCE'],
    // 基板管理控制器  字典：SOURCE
    BMC: ['BRAND', 'MODEL', 'BMCV', 'BMCSUP', 'BMCFIRV', 'CREATET', 'UPDATET', 'SOURCE'],
    // *传感器  字典：SOURCE OTHERERRSTA
    SENSOR: ['NAME', 'OTHERTMP', 'UNIT', 'TYPE', 'STATE', 'OTHERERRSTA', 'MTIME', 'CREATET', 'UPDATET', 'SOURCE']
  },
  // 软件
  SOFTWARE: {
    INFO: ['SOFTID', 'BRAND', 'MOD', 'VER', 'KINFO', 'KPATH', 'KPARAM', 'HNAME', 'STIME', 'SLEVEL', 'TZ', 'EVAR', 'EVHASH', 'RSTAT', 'USTAT', 'MSTAT', 'LHOSTS', 'MAXTNUM', 'MAXPNUM', 'MAXFNUM', 'STERRTYPE', 'CREATET', 'UPDATET', 'SOURCE'],
    // --*系统运行状态  字典：SERR
    SYSTEM_RUNTIME_STATUS: ['SOFTID', '15MLOAD', '5MLOAD', '1MLOAD', 'SUPRNUM', 'SZPRNUM', 'UNPRNUM', 'SUTNUM', 'SUHNUM', 'MUSED', 'MFREE', 'MSHARED', 'MCACHE', 'MAVAI', 'SUSED', 'SFREE', 'SIRATE', 'SORATE', 'SERR', 'MTIME'],
    // 用户 字典：SOURCE
    USER: ['UID', 'DUGRID', 'NAME', 'USHELL', 'UHOME', 'PCHGTIME', 'LKSTAT', 'NPIP', 'UMAXPRNUM', 'MAXFNUM', 'MAXTNUM', 'CREATET', 'UPDATET', 'SOURCE'],
    // --用户状态
    USER_STATUS: ['UID', 'UPRNUM', 'UZPRNUM', 'UUPRNUM', 'UTNUM', 'MTIME'],
    // 用户组 字典：SOURCE
    USER_GROUP: ['GRPID', 'NAME', 'ULIST', 'CREATET', 'UPDATET', 'SOURCE'],
    // *会话  字典：SOURCE LMETHOD
    SESSION: ['SESSID', 'FSESS', 'LUID', 'LUNAME', 'SGID', 'SPID', 'SPPATH', 'DGID', 'DPID', 'DPPATH', 'LMETHOD', 'SIP', 'SPORT', 'DIP', 'DPORT', 'TNUM', 'SSTAT', 'EVAR', 'EVHASH', 'LTIME', 'ETIME', 'CREATET', 'UPDATET', 'SOURCE'],
    // 分区  字典：SOURCE
    PARTITION: ['PARTID', 'WWID', 'DFILE', 'NAME', 'CAP', 'TYPE', 'FSTYPE', 'MPOINT', 'ITOT', 'RWPERM', 'CRTIME', 'CREATET', 'UPDATET', 'SOURCE'],
    // --分区状态  字典：SOURCE
    PARTITION_STATUS: ['PARTID', 'PURATE', 'PWRATE', 'PRRATE', 'PUSED', 'PUNUSED', 'PFRATE', 'PIWAIT', 'PUURATE', 'PIURATE', 'PIUSAGE', 'PIFREE', 'PIFRATE', 'IOERR', 'MTIME'],
    // 进程  字典：SOURCE
    PROCESS: ['PRID', 'EID', 'PPID', 'PFNAME', 'SESSID', 'PID', 'FNAME', 'NAME', 'SCMD', 'SPARAM', 'WDIR', 'STAT', 'USER', 'STIME', 'ETIME', 'QTYPE', 'THNUM', 'FDNUM', 'DPID', 'CREATET', 'UPDATET', 'SOURCE'],
    // --进程资源使用状态
    PROCESS_CPU_USAGE_STATUS: ['PRID', 'FNAME', 'CURATE', 'MURATE', 'MTIME'],
    // 服务  字典：SOURCE
    SERVICE: ['SVID', 'UID', 'UNAME', 'EID', 'FNAME', 'NAME', 'DESC', 'STYPE', 'SELFSSTAT', 'STARTCMD', 'STOPCMD', 'RESCMD', 'STAT', 'CREATET', 'UPDATET', 'SOURCE'],
    // 启动任务  字典：SOURCE
    BOOT: ['BID', 'TYPE', 'UID', 'UNAME', 'BFNAME', 'BFHASH', 'MINUTE', 'HOUR', 'DAY', 'MONTH', 'WEEKDAY', 'CREATET', 'UPDATET', 'SOURCE'],
    // 驱动  字典：SOURCE
    DRIVER: ['DID', 'FID', 'FNAME', 'NAME', 'AUTHOR', 'VER', 'DESC', 'SIG', 'LDSTAT', 'LDTIME', 'CREATET', 'UPDATET', 'SOURCE'],
    // 文件  字典：SOURCE
    FILE: ['FID', 'UID', 'GRPID', 'SPID', 'NAME', 'TYPE', 'CLASS', 'PATH', 'PERM', 'FHASH', 'FSIZE', 'LTFILE', 'CTIME', 'LATIME', 'LMTIME', 'LCTIME', 'INO', 'CREATET', 'UPDATET', 'SOURCE'],
    // 可执行文件  字典：SOURCE FILE
    EXECUTABLE_FILE: ['EID', 'CAPBIL', 'CREATET', 'UPDATET', 'SOURCE'],
    // 软件包  字典：SOURCE
    SOFTWARE_PACKAGE: ['SPID', 'NAME', 'BRAND', 'VER', 'PNAME', 'PTYPE', 'ITIME', 'SIG', 'DESC', 'IPATH', 'CREATET', 'UPDATET', 'SOURCE'],
    // 漏洞扫描
    VULN_SCAN_RESULT: ['HOSTIPSTR', 'VULNID', 'NODENAME', 'SHORTDESC', 'FULLDESC', 'REPAIRADVICE', 'RISKLEVEL', 'PLATFORMS', 'CVETAG', 'CVSSSCORE', 'UPDATE', 'CREATE', 'SOFTWARE', 'VERSION', 'FINDTIME', 'REPAIR'],
    // 基线核查
    CONFIG_CHECK_RESULT: ['CIID', 'IP', 'SUBTYPE', 'CHECKITEMTYPE', 'CIID', 'PARSE', 'ISCHECK', 'CHECKITEMNAME', 'SYSCODE', 'FIXEDPRO', 'JUDGE', 'ITEMCONTENT', 'CTIME'],
    // *应用实例  字典：SOURCE TYPE STYPE  数组：EXECUTABLE_FILE
    APPLICATION: ['APPID', 'NAME', 'BRAND', 'VER', 'TYPE', 'STYPE', 'STIME', 'RSTA', 'MDIPA', 'MDIPB', 'DCDLIST', 'APPDESC', 'CREATET', 'UPDATET', 'SOURCE'],
    // *安全插件  字典：SOURCE *STYPE
    PLUGIN: ['PLUG_ID', 'APPID', 'TYPE', 'NAME', 'BRAND', 'VER', 'STYPE', 'FLIST', 'PSTAT', 'EREASON', 'CREATET', 'UPDATET', 'SOURCE'],
    // --安全插件状态
    PLUGIN_STATUS: ['APPID', 'PLUG_ID', 'CPUR', 'MEMR', 'DISK', 'PRONUM', 'FILENUM', 'PORTNUM', ]
  },
  // 网络
  NETWORK: {
    //  字典： CTYPE  SOURCE
    INFO: ['NETID', 'FWD', 'IPV6', 'FINGER', 'CTYPE', 'CREATET', 'UPDATET', 'SOURCE'],
    // --网络状态  字典： *NERR
    NETWORK_STATUS: ['NETID', 'NERR'],
    // *网络接口  字典：BONDM SOURCE
    NETWORK_INTERFACE: ['IFID', 'ETHID', 'NCID', 'IFNM', 'CLASS', 'LINKSTA', 'SPEED', 'MTU', 'IP', 'MAC', 'DHCP', 'DHCPS', 'BONDM', 'BONDIF', 'LIF', 'IFIDX', 'AUTHS', 'VLIST', 'TYPE', 'MACBIND', 'CREATET', 'UPDATET', 'SOURCE'],
    // --网络接口运行状态  字典：SOURCE
    NETIF_RUNNING_STATUS: ['IFID', 'IN', 'OUT', 'INLOSE', 'OUTLOSE', 'INERR', 'OUTERR', 'RXPKGS', 'TXPKGS', 'RXERR', 'TXERR', 'FRAME', 'CARRIER', 'COLLIS', 'TXQLEN', 'LOST', 'ERR', 'MTIME', 'CREATET', 'UPDATET', 'SOURCE'],
    // 网络路由  字典：SOURCE
    ROUTE: ['RID', 'IFID', 'DSTNET', 'DSTMK', 'NHA', 'TYPE', 'METRIC', 'RPROT', 'CREATET', 'UPDATET', 'SOURCE'],
    // 地址学习表  字典：SOURCE
    ADDRESS: ['IFID', 'VID', 'IP', 'MAC', 'ARPTP', 'CREATET', 'UPDATET', 'SOURCE'],
    // 网络监听  字典：SOURCE
    LISTENNING_PORT: ['LISPORTID', 'IFID', 'PRID', 'PRNAME', 'IP', 'PORT', 'PROTO', 'PROVER', 'CLIN', 'CWNUM', 'TWNUM', 'SRNUM', 'CREATET', 'UPDATET', 'SOURCE'],
    // 网络邻居  字典：SOURCE
    NEIGHBOR: ['NBRID', 'IFID', 'NBRIFID', 'NBRIDX', 'UPTIME', 'CHATYPE', 'CHAID', 'NBRIP', 'PIDTYPE', 'PORTID', 'PORTDESC', 'SYSNM', 'SYSDESC', 'CREATET', 'UPDATET', 'SOURCE'],
    // *VLAN  字典：SOURCE
    VLAN: ['VID', 'VTYPE', 'IP', 'MASK', 'DESCRIPTION', 'CREATET', 'UPDATET', 'SOURCE'],
    // *访问控制策略  字典：SOURCE TYPE
    ACCESS_CONTROL_LIST: ['ACLID', 'IFID', 'ACLNUM', 'TYPE', 'SRCMAC', 'DSTMAC', 'FRTYPE', 'SRCBIP', 'SRCEIP', 'DSTBIP', 'DSTEIP', 'PROTO', 'SRCPORT', 'DSTPORT', 'DIRECTION', 'ACTION', 'SRCNIP', 'DSTNIP', 'SRCNP', 'DSTNP', 'NATPRO', 'DESC', 'ENABLE', 'EFCTIME', 'CREATET', 'UPDATET', 'SOURCE'],
    // 网络设备虚拟路由协议组  字典：SOURCE
    VRRP: ['IFID', 'VRRPID', 'VIP', 'VMASK', 'MASTER', 'CREATET', 'UPDATET', 'SOURCE'],
  },
  // *管理
  MANAGEMENT: {
      // 字典：SOURCE ATYPE STATUS NTTYPE AREA
    INFO: ['GID','DCLOUDID','DCCID','STAID','FNAME','SNAME','ATYPE','ABRAND','AMODEL','STATUS','ISCII','CLASS','MTYPE','NTTYPE','AREA','NOTES','CREATET','UPDATET']
  }
}



// ----------------------------------------------------------------------------------------------------
// 方法
// 获取资产节点数据格式
function transformAssetNodeData (fieldMap, rawDataList) {
    const result = rawDataList.map(item => {
    const transformed = {};
    for (const key in fieldMap) {
    if (item.hasOwnProperty(key)) {
        transformed[key] = {
        value: item[key],
        name: fieldMap[key]
        };
    }
    }
    return transformed;
    });
    return result;
}
// get方式取字段名用到的方法  传入  protobuf对象及字段名 => protobuf对象.get对象名() 对象名会转驼峰
function getProtobufFieldValue(messageInstance, fieldName, isArray = false) {
  // 将下划线命名转换为驼峰命名
  const camelCaseName = fieldName.toLowerCase().replace(/_([a-z])/g, (match, p1) => p1.toUpperCase());
  let methodName = "get" + camelCaseName.charAt(0).toUpperCase() + camelCaseName.slice(1);
  isArray ? methodName = methodName + 'List' : '';
  if (typeof messageInstance[methodName] === "function") {

    return messageInstance[methodName]();
  }
  return undefined;
}
function getArrayDataByBufDataAndTemp(bufData, Temp) {
  const data = {}
  if (!Temp) console.log('template is null', bufData, Temp);
  Temp.forEach(itm => {
    data[itm] = getProtobufFieldValue(bufData, itm)
  })
  return data
}
function ArrayWithLengthValidateFn(arr) {
  return Array.isArray(arr) && arr.length
}
module.exports = {
  // 对照 ---------------------------------------------------------------------------------------------
    // 资产节点字段名2中文名对照
    ASSET_NODE_Map: Map.ASSET_NODE_Map,
    // 硬件对照
    HARDWARE_Map: Map.HARDWARE_Map,
    // 软件对照
    SOFTWARE_Map: Map.SOFTWARE_Map,
    // 网络对照
    NETWORK_Map: Map.NETWORK_Map,
    // 管理对照
    MANAGEMENT_Map: Map.MANAGEMENT_Map,
    // 字典枚举 Enum --------------------------------------------
    // 资产类型枚举
    DEVICE_ASSET_ENUM: Enums.DEVICE_ASSET_ENUM,
    // 数据来源
    SOURCE_ENUM: Enums.SOURCE_Enum,
    // cpu架构
    ARCH_ENUM: Enums.ARCH,
    INTERFACE_TYPE: Enums.INTERFACE_TYPE,
    PERIPHERAL_TYPE: Enums.PERIPHERAL_TYPE,
    TYPE_ENUM: Enums.TYPE_Enum,
    PERIPHERAL_IFPCODE: Enums.PERIPHERAL_IFPCODE,

    COMMON_ENUMS: Enums.COMMON_ENUMS, // 通用字典
    HARDWARE_ENUMS: Enums.HARDWARE_ENUMS, // 硬件字典
    SOFTWARE_ENUMS: Enums.SOFTWARE_ENUMS, // 软件字典
    NETWORK_ENUMS: Enums.NETWORK_ENUMS, // 网络字典
    MANAGEMENT_ENUMS: Enums.MANAGEMENT_ENUMS, // 管理字典
    // -----------------------------------------------------------------
    // 获取枚举值对应的显示名称
    getShowNameByEnumNameAndValue: function (enumName, value) {
    },

    // 数据模板 -------------------------------------------------------
    DATA_TEMP: DATA_TEMP,
    // 方法 -------------------------------------------------------
    transformAssetNodeData,
    getProtobufFieldValue,
    getArrayDataByBufDataAndTemp,
    ArrayWithLengthValidateFn, // 验证数组并且有length
}
