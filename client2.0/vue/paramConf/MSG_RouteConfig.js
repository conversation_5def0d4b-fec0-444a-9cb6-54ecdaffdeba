const DCDParamDefine_pb = require('../../script/pb/DCDParamDefine_pb.js')
const PublicDefine_pb = require('../../script/pb/PublicDefine_pb.js');
const PlatformProxyServer_pb = require('../../script/pb/PlatformProxyServer_pb.js');
const ProxyServer_pb = require('../../script/pb/ProxyServer_pb.js');
const util = require('../../lib/util.js');
const ui_util = require('../../script/view/ui_util.js');
const tcpInstance = require('../../lib/v2/tls_client.js');
const {createApp, ref, onMounted, reactive, onBeforeMount, onBeforeUnmount} = Vue;
const {ipcRenderer, dialog} = require('electron');
const { ElMessage, ElMessageBox } = ElementPlus;
const { cloneDeep } = require('lodash')
// 校验IP或IP段
const validateIp = (rule, value, callback) => {
  const reg = /^((\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.){3}(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])(-((\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.){3}(\d{1,2}|1\d\d|2[0-4]\d|25[0-5]))?$/
  if (value && reg.test(value) === false) {
    return callback(new Error('请确保格式正确'))
  } else {
    return callback()
  }
}
const areaEnums = [
    { code: 1000, name: 'I区' },
    { code: 100, name: 'II区' },
    { code: 10, name: 'III区' },
    { code: 1, name: 'IV区' },
]
const rule = {
    dstNetSeg: [
    { required: true, message: '请输入目的网段', trigger: 'blur' },
    { required: true, message: validateIp.Error, validator: validateIp, trigger: ['blur', 'change'] },
    ],
    dstNetMask: [
    { required: true, message: '请输入网口掩码', trigger: 'blur' },
    { required: true, message: validateIp.Error, validator: validateIp, trigger: ['blur', 'change'] },
    ],
    netGateway: [
    { required: true, message: '请输入网关地址', trigger: 'blur' },
    { required: true, message: validateIp.Error, validator: validateIp, trigger: ['blur', 'change'] },
    ],
}
const areaEnumMap = {
    1000: 'I区',
    100: 'II区',
    10: 'III区',
    1: 'IV区'
}
const app = createApp({
    setup() {
        // 前置获取用户及请求信息
        const requestMsgId = util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET)
        const user_name = $("body").attr("user_name_info");
        // 格式化proto数据
        const formatDataFn = (msg) => {
            const routeList1 = []
            const addInfoList = msg.getAddinfoList();
            addInfoList.forEach((anyItem) => {
                try {
                    let DCD = DCDParamDefine_pb.MSG_RouteConfig
                    const bytes = anyItem.getValue(); // 获取Any中的二进制数据
                    const msg1 = DCD.deserializeBinary(bytes)
                        routeList1.push({
                            id: msg1.getId(),
                            dstNetSeg: msg1.getDstnetseg(),
                            dstNetMask: msg1.getDstnetmask(),
                            netGateway: msg1.getNetgateway()
                        })

                    console.log('解构数据 =>', routeList1)
                } catch (error) {
                    console.log('error', error);
                }
            })
            routeList.value = routeList1
        }
        // 配置发起请求
        const tcpConfigPDSetting = (pageCode, functionCode, data = null) => {
            const msgPayload = {
                uuid: util.genTaskId(),
                type: String(pageCode),
                param: String(functionCode),
                addInfo: []
            }
            if (data) msgPayload.addInfo = data
            let sendMsg = JSON.stringify(msgPayload)
            console.log('send =>', sendMsg);

            // 配置管理,参数配置
            tcpInstance.sendJsonMsg(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET, sendMsg).then();
        }
        const getData = () => {
            tcpConfigPDSetting(
                PlatformProxyServer_pb.MSG_PDConfigParam.FunctionType.ROUTING,
                PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.QUERY
            )
            // 配置管理：FUNCTIONTYPE_CONFIGMANAGE 参数配置：CONFIGMANAGEPACKETTYPE_PARAMSET
        }
        onMounted(() => {
            resizeHeight()
            window.addEventListener('resize', resizeHeight)
            initOnMessage()
            getData()
        })
        onBeforeUnmount(() => {
            window.removeEventListener('resize', resizeHeight)
        })
        const box = ref(null)
        const tableHeight = ref(100)
        const resizeHeight = () => {
            console.log('高度', box.value.clientHeight)
            tableHeight.value = box.value.clientHeight * 0.8
        }
        const formRules = ref(rule)
        const formRef = ref(null)
        // common
        // 所有的网卡口
        const ethEnum = ref(Array.from({length: 24}, (_, i) => `eth${i + 1}`))
        const areaEnum = ref(areaEnums)
        const areaMap = ref(areaEnumMap)
        const updateDialogVisible = ref(false) // 操作的弹窗
        const updateDialogType = ref('ADD') // 操作弹窗类型
        const updateDialogIndex = ref('ADD') // 操作弹窗索引
        // routeList
        // 流量
        const routeList = ref([
        ])
        const rowData = reactive({
            id: null,
            dstNetSeg: '',
            dstNetMask: '',
            netGateway: ''
        })
        const cloneFn = (row) => {
            Object.keys(row).forEach(key => {
                rowData[key] = row[key]
            })
        }
        const operationFn = (type, row, index) => {
            switch (type) {
                case 'ADD': {
                    updateDialogIndex.value = null
                    rowData.value = cloneFn({
                        id: 0,
                        dstNetSeg: '',
                        dstNetMask: '',
                        netGateway: ''
                    })
                    rowData.id = 0
                    updateDialogVisible.value = true
                    updateDialogType.value = 'ADD'
                    break
                };
                case 'DELETE':
                    ElMessageBox.confirm('确定要删除吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        rowData.value = cloneFn(row)
                        onClickUserValidate('delete')
                    }).catch(() => {
                        ui_util.showFadeTip('已取消删除')
                    })
                    break;
                case 'EDIT':
                    updateDialogIndex.value = index
                    rowData.value = cloneFn(row)
                    updateDialogVisible.value = true
                    updateDialogType.value = 'EDIT'
                    break
                case 'SAVE': {
                    formRef.value.validate(valid => {
                        if (valid) {
                            // 校验重复网段
                            let flag = false
                            routeList.value.forEach((item, idx) => {
                                if (item.dstNetSeg === rowData.dstNetSeg) {
                                    if (!(updateDialogType.value === 'EDIT' && idx === updateDialogIndex.value)) {
                                        flag = true
                                    }
                                }
                            })
                            if (flag) return ui_util.showFadeTip('目的网段重复')
                            onClickUserValidate('save')
                        }
                    })
                    break
                }
                default:
                    break;
            }
        }
        // 监听事件注册
        const initOnMessage = () => {
            // 页面监听
            tcpInstance.onMessage(util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET), onMessageHandle)
            // 用户校验监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCVALIDATEUSER, (validatePb) => {
                const validateMsg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(validatePb);
                if (validateMsg.getErrorCode() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                    if (validateMsg.getRequestMsgId() === requestMsgId) {
                        switch ($('#opraType').val()) {
                            case 1:{
                                $('#confirmPortDlg').modal('show')
                                $('#confirmPortDlg .modal-title').attr("wa-name","import_tt")
                                $('#confirmPortDlg .modal-body .form-group:nth-child(1)').removeClass("hidden")
                                $('#confirmPortDlg .modal-body .form-group:nth-child(2)').addClass("hidden")
                                break
                            };
                            case 2:{
                                $('#confirmPortDlg').modal('show')
                                $('#confirmPortDlg .modal-title').attr("wa-name","export_tt")
                                $('#confirmPortDlg .modal-body .form-group:nth-child(2)').removeClass("hidden")
                                $('#confirmPortDlg .modal-body .form-group:nth-child(1)').addClass("hidden")
                                break
                            };
                            case 'save':{
                                tcpConfigPDSetting(
                                PlatformProxyServer_pb.MSG_PDConfigParam.FunctionType.ROUTING,
                                PlatformProxyServer_pb.MSG_PDConfigParam.OperationType[updateDialogType.value === 'ADD' ? 'ADD' : 'MODIFY'],
                                [cloneDeep(rowData)]
                                )
                                // 配置管理：FUNCTIONTYPE_CONFIGMANAGE 参数配置：CONFIGMANAGEPACKETTYPE_PARAMSET
                                break
                            };
                            case 'delete':{
                                tcpConfigPDSetting(
                                PlatformProxyServer_pb.MSG_PDConfigParam.FunctionType.ROUTING,
                                PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.DELETE,
                                [cloneDeep(rowData)],
                                )
                                // 配置管理：FUNCTIONTYPE_CONFIGMANAGE 参数配置：CONFIGMANAGEPACKETTYPE_PARAMSET
                                break
                            };

                            default:
                                break;
                        }
                    }
                } else {
                    ui_util.getErrorTips(validateMsg.getErrorCode(), '验证用户身份')
                }
            })
            // 导入 监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCIMPORTDATA, (pb) => importExportDoneHandler('IMPORT', pb))
            // 导出 监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCEXPORTDATA, (pb) => importExportDoneHandler('EXPORT', pb))
        }
        // 导入导出文件操作1：监听按钮点击打开用户验证
        const importExportStep1Handler = (type) => {
            $('#validateUserDlg').modal('show');
            e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
            $('#validateUserForm').get(0).reset()
            $('#opraType').val(type)
        }
        // 导入导出文件操作2：发送请求  file 在导出时是filePath不是文件内容
        const importExportStep2Handler = (TYPE, file, fileName) => {
            const isImport = TYPE === 'IMPORT';
            const reqData = isImport ? new ProxyServer_pb.MSG_CPImportData() : new ProxyServer_pb.MSG_CPExportData();
            reqData.setType(PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_EVENT);
            isImport && reqData.setData(util.readFromFile(file, false).toString());
            tcpInstance.sendProtoMsg(isImport ? ProxyServer_pb.PSMessageId.PSMESSAGEID_CPIMPORTDATA : ProxyServer_pb.PSMessageId.PSMESSAGEID_CPEXPORTDATA, reqData);
            isImport && $('.parent_content').busyLoad("show", { background: "rgba(0, 0, 0, 0.59)", fontawesome: "fa fa-spinner fa-spin fa-3x fa-fw" });
        }
        // 接收请求后续操作
        const importExportDoneHandler = (type, pb) => {
            const isImport = type === 'IMPORT';
            const msg = ProxyServer_pb[isImport ? 'MSG_PCImportData' : 'MSG_PCExportData'].deserializeBinary(pb);
            isImport && $('.parent_content').busyLoad("hide");
            if (msg.getErrorCode() !== 0) return ui_util.getErrorTips(msg.getErrorCode(), `${isImport ? '导入' : '导出'}事件处理`);
            isImport ? ui_util.showLoading(30) : util.saveToCSV(msg.getData(), `${filePath}/${fileName}`);
            ui_util.showFadeTip(`${isImport ? '导入' : '导出'}事件处理成功`);
            getData();
        }
        let file="",filePath="",fileName="sntp.cfg";
        // 初始化选择文件夹路径&选择文件
        $('#select_folder').ace_file_input({
          no_file:'请选择文件夹',
          btn_choose:'选择',
          btn_change:null,
          droppable:false,
          onchange:null,
          thumbnail:false
        });
        document.querySelector('#select_folder').addEventListener('change', e => {
          for (let entry of e.target.files){
            console.log(entry.name, entry.path);
            filePath=entry.path
          }
        });
        $('#select_file').ace_file_input({
          no_file:'请选择文件',
          btn_choose:'选择',
          btn_change:null,
          droppable:false,
          onchange:null,
          thumbnail:false,
          allowExt: ['cfg'],
        }).on('change', function(){
          let fileDir = $(this).val();
          let suffix = fileDir.substr(fileDir.lastIndexOf("."));
          if ("" == fileDir||".cfg" != suffix) {
            ui_util.showFadeTip('请选择.cfg后缀文件!');
            return false;
          }
          // console.log($(this).data('ace_input_files'));
          file=$(this).data('ace_input_files')[0].path
        });
        // 注册导入导出按钮监听
        $('#port_btn').on('click',function (e) {
          if ($("#port_btn").hasClass("disabled")) return
          if($('#confirmPortDlg .modal-body .form-group:nth-child(1)').hasClass("hidden")){
            if(!$("#select_folder").next("span.ace-file-container").hasClass("selected")){
              ui_util.showFadeTip('请选择文件路径!')
              return;
            }
          }else if($('#confirmPortDlg .modal-body .form-group:nth-child(2)').hasClass("hidden")){
            if(!($("#select_file").next("span.ace-file-container").hasClass("selected"))){
              ui_util.showFadeTip('请选择文件!')
              return;
            }
          }
          $('#confirmPortDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
          e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
          if($('#confirmPortDlg .modal-title').attr("wa-name")=="import_tt"){
            importEvents('IMPORT', file, fileName)
          }else if($('#confirmPortDlg .modal-title').attr("wa-name")=="export_tt"){
            exportEvents('EXPORT', filePath, fileName)
          }
        })
        // 响应事件处理
        const onMessageHandle = (pb) => {
            const msg = PlatformProxyServer_pb.MSG_DPConfigParam.deserializeBinary(pb);
            if (msg.getResult() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                switch (Number(msg.getParam())) {
                    case PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.QUERY: {
                        formatDataFn(msg)
                        break
                    }
                    case PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.ADD: {
                        $('#validateuser_btn').off('click'); // 用完一定要手动移除该事件，否则会积累触发点击事件
                        ui_util.showFadeTip('操作成功')
                        updateDialogVisible.value = false
                        updateDialogType.value = ''
                        getData()
                        break
                    }
                    case PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.DELETE: {
                        $('#validateuser_btn').off('click'); // 用完一定要手动移除该事件，否则会积累触发点击事件
                        ui_util.showFadeTip('操作成功')
                        getData()
                        break
                    }
                    case PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.MODIFY: {
                        $('#validateuser_btn').off('click'); // 用完一定要手动移除该事件，否则会积累触发点击事件
                        updateDialogVisible.value = false
                        updateDialogType.value = ''
                        ui_util.showFadeTip('操作成功')
                        getData()
                        break
                    }
                    default:
                        break;
                }
            } else {
                ui_util.getErrorTips(msg.getResult(), '操作')
            }
        }
        const onClickUserValidate = (type) => {
            $('#validateUserDlg').modal('show'); // 打开用户密码校验
            $('#validateUserForm').get(0).reset() // 置空密码校验数据
            $('#opraType').val(type) // 存入保存标识
            // 弹出密码校验框
            $('#validateuser_btn').on('click',function (e) {
            if ($("#validateuser_btn").hasClass("disabled")) {
                return;
            }
            $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
            e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
            let username=user_name
            let psd=$('#validateUserDlg #password').val()
            validateUserGenerator(username,psd);
        })
        }
                /**
         * 发送验证密码参数
         * @param psd
         */

        const validateUserGenerator = (username,psd) => {
            const user_sign = $("body").attr("user_sign_info");
            console.log('用户验证', user_sign, username, psd);
            // 发送消息
            let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
            sendMsg.setUserName(username)
            sendMsg.setPassword(psd)
            sendMsg.setRequestMsgId(requestMsgId)
            tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg).then();


        }

        return {
            box,
            tableHeight,
            importExportStep1Handler,
            formRef,
            formRules,
            updateDialogVisible,
            updateDialogType,
            rowData,
            routeList,
            ethEnum,
            areaEnum,
            areaMap,
            operationFn
        };
    }
})
// app.use(ElementPlus);
app.use(ElementPlus, {
    locale: ElementPlusLocaleZhCn,
  })
app.mount('#app');
