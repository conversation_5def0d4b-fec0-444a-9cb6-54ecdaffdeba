const util = require('../../lib/util.js');
const tcpInstance = require('../../lib/v2/tls_client.js');
const PublicDefine_pb = require('../../script/pb/PublicDefine_pb.js')
const PlatformProxyServer_pb = require('../../script/pb/PlatformProxyServer_pb.js');
const asset_map = require('./helpers/asset_params_helpers');
const asset_form_helpers = require('../dataQuery/helps/asset_param_helps');
const { cloneDeep } = require('lodash')
const {ipcRenderer, dialog} = require('electron');
const ui_util = require("../../script/view/ui_util");
const {createApp, ref, onMounted, reactive, onBeforeMount, onBeforeUnmount} = Vue;
const ASSET_MAP_HELPS = require("../ASSET_MAP/ASSET_MAP_HELPS");
const ASSET_ENUMS = require("../ASSET_MAP/ASSET_ENUMS");
const ProxyServer_pb = require("../../script/pb/ProxyServer_pb");
const {ASSET_NODE} = require("../ASSET_MAP/ASSET_MAP_HELPS");
const { ElMessage, ElMessageBox } = ElementPlus;

const app = createApp({
    setup() {
        const infoBoxRef = ref(null) // 基本信息
        const selectRef = ref(null)
        const user_name = $("body").attr("user_name_info");
        const requestMsgId = util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET)
        const formItem = ref(asset_form_helpers.formItemMap)
        const formItemRules = asset_form_helpers.formItemRules
        const treeRef = ref(null)
        // 资产树
        const treeData = ref([])
        let showHeader = reactive({
            GID: '--',
            ATAG: '--',
            DNAME: '--',
            TYPE: '--',
            LABEL: '--',
            SOURCE: '--'
        })
        // 展示数据
        const showContent = ref({})
        // 中文对照用
        let code2ChineseMap  =ref(ASSET_MAP_HELPS.ASSET_PARAM_MAP)
        // 表头中文对照
        let code2ChineseHeaderMap  =ref(asset_map.MAP.ASSET)
        const box = ref(null)
        const dialogHeight = ref(0)

        onMounted(() => {
            resizeHeight()
            window.addEventListener('resize', resizeHeight)
            initOnMessage()
            getData()
        })
        onBeforeUnmount(() => {
            window.removeEventListener('resize', resizeHeight)
        })
        const handleScroll = () => {
            if (!updateModalVisible.value) return
            try {
                if (selectRef.value && Array.isArray(selectRef.value)) {
                    selectRef.value.forEach(item => {
                        if ('blur' in item) item.blur()
                    })
                }
            } catch (e) {
                console.log('去除下拉框 err', e)
            }
        }
        const resizeHeight = () => {
            dialogHeight.value = box.value.clientHeight * 0.7
        }
        const onClickUserValidate = (type) => {
            $('#validateUserDlg').modal('show'); // 打开用户密码校验
            $('#validateUserForm').get(0).reset() // 置空密码校验数据
            $('#opraType').val(type) // 存入保存标识
            // 弹出密码校验框
            $('#validateuser_btn').on('click',function (e) {
                if ($("#validateuser_btn").hasClass("disabled")) return
                $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
                e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
                let username=user_name
                let psd=$('#validateUserDlg #password').val()
                const user_sign = $("body").attr("user_sign_info");
                // 发送消息
                let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
                sendMsg.setUserName(username)
                sendMsg.setPassword(psd)
                sendMsg.setRequestMsgId(requestMsgId)
                console.log('send userValidate =>', sendMsg)
                tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg).then();
            })
        }
        const tcpConfigPDSetting = (pageCode, functionCode, data = null) => {
            const msgPayload = {
                uuid: util.genTaskId(),
                type: String(pageCode),
                param: String(functionCode),
                addInfo: []
            }
            if (data) msgPayload.addInfo = [data]
            let sendMsg = JSON.stringify(msgPayload)
            console.log('send => ', sendMsg, msgPayload);
            // 配置管理,参数配置
            tcpInstance.sendJsonMsg(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET, sendMsg).then();
        }
        const getData = () => {
            tcpConfigPDSetting(
                PlatformProxyServer_pb.MSG_PDConfigParam.FunctionType.ASSET,
                PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.QUERY
            )
        }
        const queryBuf = null
        const showEditButton = ref(false)
        const showDeleteButton = ref(false)
        const initOnMessage = () => {
            // 用户校验监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCVALIDATEUSER, (validatePb) => {
                const validateMsg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(validatePb);
                console.log('validateMsg.getErrorCode()', validateMsg.getErrorCode())
                if (validateMsg.getErrorCode() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                    if (validateMsg.getRequestMsgId() === requestMsgId) {
                        updateModalVisibleFn($('#opraType').val())
                    } else {
                        ui_util.getErrorTips(validateMsg.getErrorCode(), '操作')
                    }
                } else {
                    ui_util.getErrorTips(validateMsg.getErrorCode(), '验证用户身份')
                }
            })
            // 页面操作
            tcpInstance.onMessage(util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET), (pb) => {
                const msg = PlatformProxyServer_pb.MSG_DPConfigParam.deserializeBinary(pb);
                $('#validateuser_btn').off('click'); // 用完一定要手动移除该事件，否则会积累触发点击事件
                if (msg.getResult() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                    switch (Number(msg.getParam())) {
                        case PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.QUERY: {
                            console.log('阀门', pb.toString())
                            treeData.value = asset_map.formatTreeDataFn(msg) // 配置树节点
                            setTimeout(() => {
                                if (treeData.value.length) {
                                    console.log('树节点', treeData.value)
                                    treeRef.value.setCurrentKey(treeData.value[0].value, true)
                                    showEditButton.value = treeData.value[0].type != 7305
                                    showDeleteButton.value = treeData.value[0].type != 7305
                                    handleNodeClick(treeData.value[0])
                                }
                            }, 500)
                            break
                        }
                        case PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.ADD: {
                            ui_util.showFadeTip('操作成功！')
                            resetUpdateModelFn()
                            getData()
                            break
                        }
                        case PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.MODIFY: {
                            ui_util.showFadeTip('操作成功！')
                            resetUpdateModelFn()
                            getData()
                            break
                        }
                        case PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.DELETE: {
                            ui_util.showFadeTip('操作成功！')
                            getData()
                            break
                        }
                        default:
                            break;
                    }
                } else {
                    ui_util.getErrorTips(msg.getResult(), '数据获取')
                }
            })
        }
        const resetUpdateModelFn = () => {
            Object.assign(formData, {ASSET: {}, ASSET_NODE: [], INTERCONF: {}, MANAGEMENT: {}, APPLICATION: []})
            asset_node_deleteData.value = []
            application_deleteData.value = []
            window.removeEventListener('scroll', handleScroll)

            updateModalVisible.value = false

        }
        const defaultProps = { children: 'children', label: 'label', value: 'value', type: 'type' }
        const handleNodeClick = (data, node) => {
            showEditButton.value = data.type != 7305
            showDeleteButton.value = data.type != 7305
            const showData = asset_map.handleNodeClick(data)
            showHeader = Object.assign(showHeader, showData.headerData)
            showContent.value = showData.contentData
            console.log('展示数据', showHeader, showContent.value)
        }
        const updateModalVisible = ref(false)
        const updateModalType = ref('ADD')
        const formData = reactive({
            ASSET: {},
            ASSET_NODE: [],
            INTERCONF: {},
            MANAGEMENT: {},
            APPLICATION: [],
        })
        const asset_node_deleteData = ref([]) // 资产节点编辑时删除的旧数据
        const application_deleteData = ref([]) // 应用编辑时删除的旧数据
        const updateModalVisibleFn = (Type) => {
            updateModalType.value = Type
            switch (Type) {
                case 'ADD': {
                    updateModalVisible.value = true
                    setTimeout(() => {
                        const container = document.querySelector(".info_box")
                        console.log('弹窗容器', container)
                        container.addEventListener("scroll", handleScroll, true);
                    }, 500)
                    break
                }
                case 'EDIT': {
                    const node = treeRef.value.getCurrentNode()
                    const data = asset_map.getEditData(node.bufData)
                    // 标记旧数据
                    if ('ASSET_NODE' in data && Array.isArray(data['ASSET_NODE'])) {
                        data['ASSET_NODE'].forEach(item => item.NDMTYPE = 2)
                    }
                    if ('APPLICATION' in data && Array.isArray(data['APPLICATION'])) {
                        data['APPLICATION'].forEach(item => item.APPMTYPE = 2)
                    }
                    Object.assign(formData, data)
                    console.log('EDIT =>', data)
                    updateModalVisible.value = true
                    setTimeout(() => {
                        const container = document.querySelector(".info_box")
                        container.addEventListener("scroll", handleScroll, true);
                    }, 500)
                    break
                }
                case 'DELETE': {
                    console.log('删除')
                    ElMessageBox.confirm('确定要删除吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        const node = treeRef.value.getCurrentNode()
                        const data = asset_map.getEditData(node.bufData)
                        console.log('DELETE =>', data)
                        // 标记旧数据
                        if ('ASSET_NODE' in data && Array.isArray(data['ASSET_NODE'])) {
                            data['ASSET_NODE'].forEach(item => item.NDMTYPE = 3)
                        }
                        if ('APPLICATION' in data && Array.isArray(data['APPLICATION'])) {
                            data['APPLICATION'].forEach(item => item.APPMTYPE = 3)
                        }
                        tcpConfigPDSetting(
                            PlatformProxyServer_pb.MSG_PDConfigParam.FunctionType.ASSET,
                            PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.DELETE,
                            data
                        )
                    }).catch(() => {
                        ui_util.showFadeTip('已取消删除')
                    })

                }
            }
        }
        const ASSET_REF = ref(null)
        const ASSET_NODE_REF = ref(null)
        const INTERCONF_REF = ref(null)
        const MANAGEMENT_REF = ref(null)
        const APPLICATION_REF = ref(null)
        const updateModalSaveFn = async (Type) => {
            if (Type === 'CLOSE') {
                resetUpdateModelFn()
                return
            }
            try {
                await Promise.all([
                    ASSET_REF.value.validate(),
                    ASSET_NODE_REF.value.validate(),
                    INTERCONF_REF.value.validate(),
                    MANAGEMENT_REF.value.validate(),
                    APPLICATION_REF.value.validate(),
                ])
                ElMessageBox.confirm('确定要提交吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    // 后续为校验成功逻辑
                    const data = cloneDeep(formData)
                    data.INTERCONF['RMTPORT'] = Number(data.INTERCONF['RMTPORT'])
                    data.INTERCONF['SNMPPORT'] = Number(data.INTERCONF['SNMPPORT'])
                    if (updateModalType.value === 'ADD') {
                        // 统一赋值
                        data.ASSET_NODE.forEach(item => item.NDMTYPE = 1)
                        data.APPLICATION.forEach(item => item.APPMTYPE = 1)
                    } else if (updateModalType.value === 'EDIT') {
                        // 拼接被删除的旧数据
                        const asset_node_deleteArr = cloneDeep(asset_node_deleteData.value)
                        const application_deleteArr = cloneDeep(application_deleteData.value)
                        data.ASSET_NODE = asset_node_deleteArr.concat(data.ASSET_NODE)
                        data.APPLICATION = application_deleteArr.concat(data.APPLICATION)
                    }
                    formData.ASSET_NODE.forEach(item => item.GID = formData.ASSET.GID) // 统一GID
                    console.log('validate: success', formData)
                    tcpConfigPDSetting(
                        PlatformProxyServer_pb.MSG_PDConfigParam.FunctionType.ASSET,
                        PlatformProxyServer_pb.MSG_PDConfigParam.OperationType[updateModalType.value === 'ADD' ? 'ADD' : 'MODIFY'],
                        cloneDeep(formData)
                    )
                })
            } catch (e) {
                console.log('validate: err', e)
            }
        }
        // 操作表格数据
        const operationTableDataByIndex = (tableName, TYPE, index) => {
            switch (TYPE) {
                case 'ADD': {
                    const data = tableName === 'ASSET_NODE' ? {
                        NDMTYPE: 1, IP: '', MAC: '', IFID: '', SOURCE: null, GID: formData['ASSET']['GID']
                    } : {
                        APPMTYPE: 1, APPID: '', NAME: '', BRAND: '', VER: '', TYPE: null, STYPE: null, MDIPA: '', MDIPB: '', APPDESC: '', SOURCE: null, ENSTAT: false}
                    formData[tableName].push(data)
                    break
                }
                case 'DELETE': {
                    const deleteData = cloneDeep(formData[tableName][index])
                    // 旧数据被删除需要记录
                    const managementType = tableName === 'ASSET_NODE' ? deleteData['NDMTYPE'] : deleteData['APPMTYPE']
                    if (deleteData.NDMTYPE === 2) {
                        if (tableName === 'ASSET_NODE') {
                            asset_node_deleteData.value.push(Object.assign(deleteData, { NDMTYPE: 3 }))
                        } else {
                            asset_node_deleteData.value.push(Object.assign(deleteData, { APPMTYPE: 3 }))
                        }
                    }

                    if (!formData[tableName] || !Array.isArray(formData[tableName])) return
                    formData[tableName].splice(index, 1)
                    break
                }
            }

        }
        const changeSelect = (tableName, index, field, val) => {
            if (tableName === 'APPLICATION' && field === 'TYPE') formData[tableName][index]['STYPE'] = null // 和子类型联动
        }
        const getSTYPE = (val) => {
            if (!val) return []
            const data = []
            if (!(val in ASSET_ENUMS.SOFTWARE.APPLICATION_STYPE)) return []
            Object.keys(ASSET_ENUMS.SOFTWARE.APPLICATION_STYPE[val]).forEach(key => {
                data.push({ code: key, name: ASSET_ENUMS.SOFTWARE.APPLICATION_STYPE[val][key] })
            })
            return data
        }
        return {
            treeData,
            showHeader,
            showContent,
            dialogHeight,
            code2ChineseMap,
            code2ChineseHeaderMap,
            defaultProps,
            handleNodeClick,
            updateModalType,
            updateModalVisible,
            updateModalSaveFn,
            updateModalVisibleFn,
            formData,
            formItem,
            formItemRules,
            operationTableDataByIndex,

            // ref
            ASSET_REF,
            ASSET_NODE_REF,
            INTERCONF_REF,
            MANAGEMENT_REF,
            APPLICATION_REF,
            box,
            treeRef,
            changeSelect,
            getSTYPE,
            onClickUserValidate,
            showEditButton,
            showDeleteButton,
            selectRef,
            infoBoxRef
        }
    }
})
app.use(ElementPlus, { locale: ElementPlusLocaleZhCn })
app.mount('#app');
