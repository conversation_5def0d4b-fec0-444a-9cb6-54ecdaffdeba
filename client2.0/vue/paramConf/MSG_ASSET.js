const AssetModelCodeDefine_pb = require('../../script/pb/AssetModelCodeDefine_pb.js');
const AssetModelDefine_pb = require('../../script/pb/AssetModelDefine_pb.js');
const util = require('../../lib/util.js');
const tcpInstance = require('../../lib/v2/tls_client.js');
const DCDParamDefine_pb = require('../../script/pb/DCDParamDefine_pb.js')
const PublicDefine_pb = require('../../script/pb/PublicDefine_pb.js')
const PlatformProxyServer_pb = require('../../script/pb/PlatformProxyServer_pb.js');
const ui_util = require('../../script/view/ui_util.js');
const asset_map = require('./helpers/asset_map.js');
const {createApp, ref, onMounted, reactive, onBeforeMount} = Vue;
const { cloneDeep } = require('lodash')
const {ipcRenderer, dialog} = require('electron');

const app = createApp({
    setup() {
        const tcpConfigPDSetting = (pageCode, functionCode, data = null) => {
            const msgPayload = {
                uuid: util.genTaskId(),

                type: pageCode,
                param: functionCode,
                addInfo: []
            }
            if (data) msgPayload.addInfo = [data]
            let sendMsg = JSON.stringify(msgPayload)
            console.log('send => ', sendMsg);
            // 配置管理,参数配置
            tcpInstance.sendJsonMsg(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET, sendMsg).then();
        }
        onBeforeMount(() => {
            initOnMessage()
            getData()
        })
        const getData = () => {
            tcpConfigPDSetting(
                PlatformProxyServer_pb.MSG_PDConfigParam.FunctionType.ASSET,
                PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.QUERY
            )
        }
        const initOnMessage = () => {
            tcpInstance.onMessage(util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET), (pb) => {
                const msg = PlatformProxyServer_pb.MSG_DPConfigParam.deserializeBinary(pb);
                if (msg.getResult() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                    switch (Number(msg.getParam())) {
                        case PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.QUERY: {
                            formatDataFn(msg)
                        }
                        default:
                            break;
                    }
                }
            })

        }
        // 格式化返回数据
        const formatDataFn = (msg) => {
            const addInfoList = msg.getAddinfoList();
            let ASSET_PB = AssetModelDefine_pb.MSG_ASSET
            console.log('addInfoList', addInfoList, ASSET_PB.prototype);
            const getArrayDataByBufDataAndTemp = asset_map.getArrayDataByBufDataAndTemp // 遍历模板取对应值
            addInfoList.forEach(anyItem => {
                try {
                    console.log('anyItem', anyItem);
                    const bytes = anyItem.getValue(); // 获取Any中的二进制数据
                    console.log('bytes', bytes, 'deserializeBinary' in ASSET_PB, ASSET_PB.deserializeBinary(bytes));
                    const msg1 = ASSET_PB.deserializeBinary(bytes)
                    console.log('msg1', msg1);

                    // 资产主要信息
                    Object.keys(mainInfo).forEach(key => {
                        if (key !== 'DEVICE_ASSET') {
                            mainInfo[key].value = asset_map.getProtobufFieldValue(msg1, key);
                        }
                    })
                    // 资产模型
                    // const assetModal = asset_map.getProtobufFieldValue(msg1, 'DEVICE_ASSET', true);
                    // console.log('资产模型', assetModal);
                    // // 资产节点
                    const TEMP = asset_map.DATA_TEMP
                    // 资产节点
                    const ASSET_NODE_BUF = asset_map.getProtobufFieldValue(msg1, 'ASSET_NODE', true) || []
                    asset_map.ArrayWithLengthValidateFn(ASSET_NODE_BUF) ? ASSET_NODE_BUF.forEach(item => {
                        const assetNodeItem = getArrayDataByBufDataAndTemp(item, TEMP.ASSET_NODE.INFO)
                        if ('SOURCE' in assetNodeItem) assetNodeItem.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, assetNodeItem.SOURCE) // 数据来源
                        ASSET_NODE.push(assetNodeItem)
                    })
                     : ''

                    // 硬件
                    const HARDWARE_BUF = asset_map.getProtobufFieldValue(msg1, 'HARDWARE', true)
                    console.log('硬件', HARDWARE_BUF);
                    HARDWARE_BUF.forEach(item => {
                        // 基础信息
                        const InfoData = asset_map.getArrayDataByBufDataAndTemp(item, TEMP['HARDWARE']['INFO'])
                        if ('SOURCE' in InfoData) InfoData.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, InfoData.SOURCE) // 数据来源
                        // 硬件信息

                        InfoData['CPU'] = asset_map.getProtobufFieldValue(item, 'CPU', true).map(bufItem => {
                            const cpuData = asset_map.getArrayDataByBufDataAndTemp(bufItem, TEMP['HARDWARE']['CPU']);
                            if ('ARCH' in cpuData) cpuData.ARCH = getShowNameByMapWithValue(asset_map.HARDWARE_ENUMS.ARCH, cpuData.ARCH) // 架构
                            if ('SOURCE' in cpuData) cpuData.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, cpuData.SOURCE) // 架构
                            cpuData['LOGICAL_CORE'] = asset_map.getProtobufFieldValue(bufItem, 'LOGICAL_CORE', true).map(coreItem => {
                                    const LOGICAL_CORE_ITEM = asset_map.getArrayDataByBufDataAndTemp(coreItem, TEMP.HARDWARE.LOGICAL_CORE)
                                    if ('SOURCE' in LOGICAL_CORE_ITEM) LOGICAL_CORE_ITEM.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, LOGICAL_CORE_ITEM.SOURCE) // 架构
                                    return cpuData
                            })
                            return cpuData
                        }) // CPU
                        InfoData['NETWORK_CARD'] = asset_map.getProtobufFieldValue(item, 'NETWORK_CARD', true).map(bufItem => {
                            const NETWORK_CARD_ITEM = getArrayDataByBufDataAndTemp(bufItem, TEMP.HARDWARE.NETWORK_CARD);
                            if ('SOURCE' in NETWORK_CARD_ITEM) NETWORK_CARD_ITEM.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, NETWORK_CARD_ITEM.SOURCE) // 架构
                            NETWORK_CARD_ITEM['ETHERNET'] = asset_map.getProtobufFieldValue(bufItem, 'ETHERNET', true).map(coreItem => {
                                const ETHERNET_ITEM = getArrayDataByBufDataAndTemp(coreItem, TEMP.HARDWARE.ETHERNET)
                                if ('SOURCE' in ETHERNET_ITEM) ETHERNET_ITEM.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, ETHERNET_ITEM.SOURCE) // 架构
                                return ETHERNET_ITEM
                            })
                            return NETWORK_CARD_ITEM
                        }) // 有线网卡
                        ;['MEMORY', 'DISK', 'WIRELESS', 'PERIPHERAL_INTERFACE', 'PERIPHERAL', 'POWER', 'GPU', 'HBA', 'RAID', 'BMC', 'SENSOR'].forEach(key => {
                            if (asset_map.ArrayWithLengthValidateFn(asset_map.getProtobufFieldValue(item, key, true))) {
                                InfoData[key] = asset_map.getProtobufFieldValue(item, key, true).map(bufItem => {
                                    const data = getArrayDataByBufDataAndTemp(bufItem, TEMP.HARDWARE[key])
                                    if ('SOURCE' in data) data.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, data.SOURCE) // 架构
                                    return data
                                })
                            }
                        })
                        InfoData['PERIPHERAL_INTERFACE'].forEach(item => {
                            if ('TYPE' in item) item.TYPE = getShowNameByMapWithValue(asset_map.HARDWARE_ENUMS.PERIPHERAL_INTERFACE_TYPE, item.TYPE) // 架构
                        })
                        InfoData['SENSOR'].forEach(item => {
                            if ('OTHERERRSTA' in item) item.OTHERERRSTA = getShowNameByMapWithValue(asset_map.HARDWARE_ENUMS.OTHERERRSTA, item.OTHERERRSTA) // 架构
                        })
                        Object.keys(InfoData).length ? HARDWARE.push(InfoData) : ''
                    })

                    // 软件
                    const SOFTWARE_BUF = asset_map.getProtobufFieldValue(msg1, 'SOFTWARE', true) || []
                    SOFTWARE_BUF.forEach(item => {
                        console.log('软件', SOFTWARE_BUF);
                        // 基础信息
                        const InfoData = getArrayDataByBufDataAndTemp(item, TEMP.SOFTWARE.INFO)
                        // 软件信息
                        ;['USER', 'USER_GROUP', 'SESSION', 'PARTITION', 'PROCESS', 'SERVICE',
                             'BOOT', 'DRIVER', 'FILE', 'SOFTWARE_PACKAGE', 'VULN_SCAN_RESULT', 'CONFIG_CHECK_RESULT', 'PLUGIN'].forEach(key => {
                            console.log('软件基础信息', key, TEMP.SOFTWARE[key]);
                            const bufData = asset_map.getProtobufFieldValue(item, key, true)
                            if (asset_map.ArrayWithLengthValidateFn(bufData)) {
                                InfoData[key] = bufData.map(bufItem => {
                                    const data = getArrayDataByBufDataAndTemp(bufItem, TEMP.SOFTWARE[key])
                                    if ('SOURCE' in data) data.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, data.SOURCE) // 架构
                                    if (key === 'SESSION') {
                                        if ('LMETHOD' in data) data.LMETHOD = getShowNameByMapWithValue(asset_map.SOFTWARE_ENUMS.LMETHOD, data.LMETHOD) // 登陆方式
                                    }
                                    return data
                                })
                            }
                        })
                        // 可执行文件
                        const EXECUTABLE_FILE_BUF = asset_map.getProtobufFieldValue(item, 'EXECUTABLE_FILE', true)
                        if(asset_map.ArrayWithLengthValidateFn(EXECUTABLE_FILE_BUF)) {
                            console.log('11121');
                            InfoData['EXECUTABLE_FILE'] = EXECUTABLE_FILE_BUF.map(bufItem => {
                                const EXECUTABLE_FILE_ITEM = getArrayDataByBufDataAndTemp(bufItem, TEMP.SOFTWARE.EXECUTABLE_FILE);
                                if ('SOURCE' in EXECUTABLE_FILE_ITEM) EXECUTABLE_FILE_ITEM.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, EXECUTABLE_FILE_ITEM.SOURCE) // 架构
                                const SO_FILE_LISTL_BUF = asset_map.getProtobufFieldValue(bufItem, 'SO_FILE_LIST', true)
                                console.log('SO_FILE_LISTL_BUF', SO_FILE_LISTL_BUF);
                                EXECUTABLE_FILE_ITEM['SO_FILE_LIST'] = asset_map.ArrayWithLengthValidateFn(SO_FILE_LISTL_BUF) ? SO_FILE_LISTL_BUF.map(coreItem => {
                                    const data = getArrayDataByBufDataAndTemp(coreItem, TEMP.SOFTWARE.FILE)
                                    if ('SOURCE' in data) data.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, data.SOURCE) // 架构
                                    return data
                                }) : []
                                return EXECUTABLE_FILE_ITEM
                            }) // 可执行文件
                        }
                        // 应用
                        const APPLICATION_BUF = asset_map.getProtobufFieldValue(item, 'APPLICATION', true)
                        if (asset_map.ArrayWithLengthValidateFn(APPLICATION_BUF)) {
                           InfoData['APPLICATION'] = APPLICATION_BUF.map(bufItem => {
                                const APPLICATION_ITEM = getArrayDataByBufDataAndTemp(bufItem, TEMP.SOFTWARE.APPLICATION);
                                if ('STYPE' in APPLICATION_ITEM && 'TYPE' in APPLICATION_ITEM && APPLICATION_ITEM.STYPE) {
                                    APPLICATION_ITEM.STYPE = APPLICATION_ITEM.STYPE in asset_map.SOFTWARE_ENUMS.APPLICATION_STYPE[APPLICATION_ITEM.TYPE] ? asset_map.SOFTWARE_ENUMS.APPLICATION_STYPE[APPLICATION_ITEM.TYPE][APPLICATION_ITEM.STYPE]
                                        : '--'
                                }
                               if ('TYPE' in APPLICATION_ITEM) APPLICATION_ITEM.TYPE = getShowNameByMapWithValue(asset_map.SOFTWARE_ENUMS.APPLICATION_TYPE, APPLICATION_ITEM.TYPE) // 架构
                               if ('SOURCE' in APPLICATION_ITEM) APPLICATION_ITEM.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, APPLICATION_ITEM.SOURCE) // 架构
                               APPLICATION_ITEM['APP_EXECUTABLE_FILE'] = asset_map.getProtobufFieldValue(bufItem, 'APP_EXECUTABLE_FILE', true).map(coreItem => {
                                   const data = getArrayDataByBufDataAndTemp(coreItem, TEMP.SOFTWARE.EXECUTABLE_FILE)
                                   // const SO_FILE_LISTL_BUF = asset_map.getProtobufFieldValue(coreItem, 'SO_FILE_LIST', true)

                                   if ('SOURCE' in data) data.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, data.SOURCE) // 架构
                                   return data
                               })
                                return APPLICATION_ITEM
                            }) // 应用实例
                        }
                        Object.keys(InfoData).length ? SOFTWARE.push(InfoData) : ''
                    })
                    // 网络
                    const NETWORK_BUF = asset_map.getProtobufFieldValue(msg1, 'NETWORK', true) || []
                    NETWORK_BUF.forEach(item => {
                        console.log('网络', NETWORK_BUF);
                        // 基础信息
                        const InfoData = getArrayDataByBufDataAndTemp(item, TEMP.NETWORK.INFO)
                        // 网络信息
                        ;['NETWORK_INTERFACE', 'ROUTE', 'ADDRESS', 'LISTENNING_PORT', 'NEIGHBOR', 'ACCESS_CONTROL_LIST', 'VRRP'].forEach(key => {
                            console.log('网络基础信息', key, TEMP.SOFTWARE[key]);
                            const bufData = asset_map.getProtobufFieldValue(item, key, true)
                            if (asset_map.ArrayWithLengthValidateFn(bufData)) {
                                InfoData[key] = bufData.map(bufItem =>
                                    getArrayDataByBufDataAndTemp(bufItem, TEMP.NETWORK[key])
                                )
                            }

                        })
                        console.log('围栏');

                        const VLAN_BUF = asset_map.getProtobufFieldValue(item, 'VLAN', true)
                        if (asset_map.ArrayWithLengthValidateFn(VLAN_BUF)) {
                            InfoData['VLAN'] = VLAN_BUF.map(bufItem => {
                                const cpuData = getArrayDataByBufDataAndTemp(bufItem, TEMP.NETWORK.VLAN);
                                const NIFLST_BUF = asset_map.getProtobufFieldValue(bufItem, 'NIFLST', true)
                                cpuData['NIFLST'] = asset_map.ArrayWithLengthValidateFn(NIFLST_BUF) ? NIFLST_BUF.map(coreItem =>
                                    getArrayDataByBufDataAndTemp(coreItem, TEMP.NETWORK.NETWORK_INTERFACE)
                                ) : []
                                return cpuData
                            }) // VLAN
                        }
                        Object.keys(InfoData).length ? NETWORK.push(InfoData) : ''
                    })
                    // 管理
                    const MANAGEMENT_BUF = asset_map.getProtobufFieldValue(msg1, 'MANAGEMENT', true) || []
                    MANAGEMENT_BUF.forEach(item => {
                        const InfoData = getArrayDataByBufDataAndTemp(item, TEMP.MANAGEMENT.INFO)
                        Object.keys(InfoData).length ? MANAGEMENT.push(InfoData) : ''
                    })

                } catch (error) {
                    console.log('解构数据失败 =>', error)
                }
            })
            // 字典数据
            formatEnumFn()
            // 格式化树
            formatTreeDataFn()

            // console.log('解构参数', MSG_ASSET.prototype);
        }
        const formatEnumFn = () => {
            // 匹配字典
            console.log('匹配字典', { HARDWARE: cloneDeep(HARDWARE), SOFTWARE: cloneDeep(SOFTWARE), NETWORK: cloneDeep(NETWORK), MANAGEMENT: cloneDeep(MANAGEMENT)});
            setTimeout(() => {
                // 软件
                SOFTWARE.forEach(item => {
                    if ('SOURCE' in item) item.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, item.SOURCE) // 数据来源
                    // if ('SENSOR' in item) item.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, item.SOURCE) // 数据来源
                    // 用户
                    if ('USER' in item) item.USER.forEach(itm => itm.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, itm.SOURCE))
                    if ('USER_GROUP' in item) item.USER_GROUP.forEach(itm => itm.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, itm.SOURCE))
                    if ('SESSION' in item) { item.SESSION.forEach(itm => {
                        itm.LMETHOD = getShowNameByMapWithValue(asset_map.SOFTWARE_ENUMS.LMETHOD, itm.LMETHOD) // 登陆方式
                        itm.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, itm.SOURCE)
                    }) }
                    if ('PARTITION' in item)item.PARTITION.forEach(itm => itm.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, itm.SOURCE))
                    if ('PROCESS' in item)item.PROCESS.forEach(itm => itm.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, itm.SOURCE))
                    if ('SERVICE' in item)item.SERVICE.forEach(itm => itm.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, itm.SOURCE))
                    if ('BOOT' in item)item.BOOT.forEach(itm => itm.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, itm.SOURCE))
                    // if ('BOOT_TASK' in item)item.BOOT_TASK.forEach(itm => itm.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, itm.SOURCE))
                    if ('DRIVER' in item)item.DRIVER.forEach(itm => itm.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, itm.SOURCE))
                    if ('FILE' in item)item.FILE.forEach(itm => itm.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, itm.SOURCE))
                    if ('SOFTWARE_PACKAGE' in item)item.SOFTWARE_PACKAGE.forEach(itm => itm.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, itm.SOURCE))
                    if ('EXECUTABLE_FILE' in item) item.EXECUTABLE_FILE.forEach(itm => {
                        itm.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, itm.SOURCE)
                        'SO_FILE_LIST' in itm && Array.isArray(itm.SO_FILE_LIST) && itm.SO_FILE_LIST.length ?
                            itm.SO_FILE_LIST.forEach(t => {
                                t.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, t.SOURCE)
                        }) : ''
                    })
                    if ('SESSION' in item) { item.SESSION.forEach(itm => {
                        'SO_FILE_LIST' in itm && Array.isArray(itm.SO_FILE_LIST) && itm.SO_FILE_LIST.length ?
                            itm.SO_FILE_LIST.forEach(it => {
                                it.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, it.SOURCE)
                            })
                          : ''
                        itm.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, itm.SOURCE)
                    })
                }
                    if ('APPLICATION' in item) { item.APPLICATION.forEach(itm => {
                        itm.STYPE = itm.TYPE && itm.STYPE ?
                            itm.STYPE in asset_map.SOFTWARE_ENUMS.APPLICATION_STYPE[itm.TYPE] ? asset_map.SOFTWARE_ENUMS.APPLICATION_STYPE[itm.TYPE][itm.STYPE]
                                : '--' : '--'
                        itm.TYPE = getShowNameByMapWithValue(asset_map.SOFTWARE_ENUMS.APPLICATION_TYPE, itm.TYPE)
                        itm.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, itm.SOURCE)
                        'APP_EXECUTABLE_FILE' in itm && Array.isArray(itm.APP_EXECUTABLE_FILE) && itm.APP_EXECUTABLE_FILE.length ?
                            itm.APP_EXECUTABLE_FILE.forEach(it => {
                                it.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, it.SOURCE)
                                'SO_FILE_LIST' in it && Array.isArray(it.SO_FILE_LIST) && it.SO_FILE_LIST.length ?
                                    it.SO_FILE_LIST.forEach(t => {
                                        t.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, t.SOURCE)
                                })
                            : ''
                            })
                          : ''
                    }) }
                    if ('PLUGIN' in item) { item.PLUGIN.forEach(itm => {
                        itm.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, itm.SOURCE)
                    }) }
                    // 'USER' in item && Array.isArray(item.USER) && item.USER.length ? '' : ''
                })
                // 网络
                NETWORK.forEach(item => {
                    if ('CTYPE' in item) item.CTYPE = getShowNameByMapWithValue(asset_map.NETWORK_ENUMS.CTYPE, item.CTYPE) // 资产用途
                    if ('SOURCE' in item) item.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, item.SOURCE) // 数据来源
                    if ('NETWORK_INTERFACE' in item) { item.NETWORK_INTERFACE.forEach(itm => {
                        itm.BONDM = getShowNameByMapWithValue(asset_map.NETWORK_ENUMS.BONDM, itm.BONDM) // 外设设备类型
                        itm.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, itm.SOURCE) // 数据来源
                    }) }
                    if ('ROUTE' in item) item.ROUTE.forEach(itm => itm.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, itm.SOURCE))
                    if ('ADDRESS' in item) item.ADDRESS.forEach(itm => itm.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, itm.SOURCE))
                    if ('LISTENNING_PORT' in item) item.LISTENNING_PORT.forEach(itm => itm.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, itm.SOURCE))
                    if ('NEIGHBOR' in item) item.NEIGHBOR.forEach(itm => itm.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, itm.SOURCE))
                    if ('VLAN' in item) item.VLAN.forEach(itm => {
                        itm.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, itm.SOURCE)
                        if ('NIFLST' in itm) {
                            console.log('xxqee');
                            itm.NIFLST.forEach(it => {
                                it.BONDM = getShowNameByMapWithValue(asset_map.NETWORK_ENUMS.BONDM, it.BONDM) // 外设设备类型
                                it.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, it.SOURCE) // 数据来源
                            })
                        }
                    })
                    if ('ACCESS_CONTROL_LIST' in item){ item.ACCESS_CONTROL_LIST.forEach(itm => {
                        itm.TYPE = getShowNameByMapWithValue(asset_map.NETWORK_ENUMS.ACCESS_CONTROL_LIST_TYPE, itm.TYPE) //
                        itm.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, itm.SOURCE) // 数据来源
                    }) }
                    if ('VRRP' in item) item.VRRP.forEach(itm => itm.SOURCE = getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, itm.SOURCE))
                })
                // 管理
                MANAGEMENT.forEach(item => {
                    item.ATYPE = getShowNameByMapWithValue(asset_map.MANAGEMENT_ENUMS.ATYPE, item.ATYPE)
                    item.STATUS = getShowNameByMapWithValue(asset_map.MANAGEMENT_ENUMS.STATUS, item.STATUS)
                    item.NTTYPE = getShowNameByMapWithValue(asset_map.MANAGEMENT_ENUMS.NTTYPE, item.NTTYPE)
                    item.AREA = getShowNameByMapWithValue(asset_map.MANAGEMENT_ENUMS.AREA, item.AREA)
                })
            })
        }
        // 格式化树
        const formatTreeDataFn = () => {
            // 添加资产节点
            treeData.value.push({
                label: '资产节点',
                value: 'ASSET_NODE',
                type: 'ASSET_NODE',
                children: ASSET_NODE.map((item, index) => ({
                    label: item.GID,
                    value: util.genTaskId(),
                    type: `ASSET_NODE-${index}`
                }))
            })
            // 添加硬件节点
            treeData.value.push({
            label: '硬件',
            value: 'HARDWARE',
            type: 'HARDWARE',
            children: HARDWARE.map((item, index) => {
                // 处理硬件子项
                const children = Object.keys(item)
                .filter(key => Array.isArray(item[key]))
                .map(key => ({
                    label: asset_map.HARDWARE_Map[key],
                    value: util.genTaskId(),
                    type: `HARDWARE-${index}-${key}`,
                    children: null
                }));

                // 返回硬件标识节点
                return {
                label: item.HID,
                value: util.genTaskId(),
                type: `HARDWARE-${index}`,
                children
                };
            })
            })
            // 添加软件节点
            treeData.value.push({
            label: '软件',
            value: 'SOFTWARE',
            type: 'SOFTWARE',
            children: SOFTWARE.map((item, index) => {
                // 处理子项
                const children = Object.keys(item)
                .filter(key => Array.isArray(item[key]))
                .map(key => ({
                    label: asset_map.SOFTWARE_Map[key],
                    value: util.genTaskId(),
                    type: `SOFTWARE-${index}-${key}`,
                    children: null
                }));

                // 返回标识节点
                return {
                label: item.SOFTID,
                value: util.genTaskId(),
                type: `SOFTWARE-${index}`,
                children
                };
            })
            })
            // 添加网络节点
            treeData.value.push({
            label: '网络',
            value: 'NETWORK',
            type: 'NETWORK',
            children: NETWORK.map((item, index) => {
                // 处理子项
                const children = Object.keys(item)
                .filter(key => Array.isArray(item[key]))
                .map(key => ({
                    label: asset_map.NETWORK_Map[key],
                    value: util.genTaskId(),
                    type: `NETWORK-${index}-${key}`,
                    children: null
                }));

                // 返回标识节点
                return {
                label: item.NETID,
                value: util.genTaskId(),
                type: `NETWORK-${index}`,
                children
                };
            })
            })
            // 添加管理节点
            treeData.value.push({
            label: '管理',
            value: 'MANAGEMENT',
            type: 'MANAGEMENT',
            children: MANAGEMENT.map((item, index) => {
                // 处理子项
                const children = Object.keys(item)
                .filter(key => Array.isArray(item[key]))
                .map(key => ({
                    label: asset_map.MANAGEMENT_Map[key],
                    value: util.genTaskId(),
                    type: `MANAGEMENT-${index}-${key}`,
                    children: null
                }));

                // 返回标识节点
                return {
                label: item.GID,
                value: util.genTaskId(),
                type: `MANAGEMENT-${index}`,
                children
                };
            })
            })
        }
        const getShowNameByEnumNameAndValue = (EnumName, value) => {
            return getShowNameByEnumAndValue(asset_map[`${EnumName}_ENUM`], value)
        }
        // 根据字典和值获取数据展示
        const getShowNameByEnumAndValue = (enumList, value) => {
            let name = '--'
            if (!enumList) return name
            console.log('字典匹配', enumList, value);
            if (value === null || value === undefined) {
                name = '--';
            }
            enumList.forEach(item => {
                if (item.code === value) {
                    name = item.name;
                }
            })
            return name;
        }
        // 通过value从对象取值
        const getShowNameByMapWithValue = (Enums, value) => {
            if ([undefined, null, ''].includes(value)) return '--'
            if (Enums && Object.keys(Enums).length) {
                if (value in Enums)
                return Enums[value]
            } else {
                return '--'
            }
        }
        // trueOrFalse
        const trueOrFalseEnum2Name = (type, value) => {
            if ([null, undefined].includes(value)) return '--'
            return {
                onlineEnum: {
                    true: '在线',
                    false: '离线',
                }
                }[type][value]
        }
        // 资产属性
        const mainInfo = reactive({
            GID: { value: '', name: '资产ID' },
            ATAG: { value: '', name: '资产标识' },
            DSN: { value: '', name: '设备序列号' },
            DNAME: { value: '', name: '设备名称' },
             TYPE: { value: null, name: '资产类型', value2Lable: (val) => getShowNameByEnumAndValue(asset_map.TYPE_ENUM, val) },
            CONAST: { value: false, name: '已确认资产' },
            ONLSTA: { value: false, name: '在线状态', value2Lable: (val) => trueOrFalseEnum2Name('onlineEnum', val) },
            LABEL: { value: '', name: '资产标签集' },
            CREATET: { value: '', name: '数据生成时间' },
            UPDATET: { value: '', name: '数据更新时间' },
            SOURCE: { value: null, name: '数据来源', value2Lable: (val) => getShowNameByMapWithValue(asset_map.COMMON_ENUMS.SOURCE, val) },
            // DEVICE_ASSET: { value: null, name: '资产模型实例', value2Lable: (val) => getShowNameByEnumAndValue(asset_map.DEVICE_ASSET_ENUM, val) },
        })
        const treeData = ref([])
        // 资产节点
        const ASSET_NODE = []
        // 硬件
        const HARDWARE = []
        // 软件
        const SOFTWARE = []
        // 网络
        const NETWORK = []
        // 管理
        const MANAGEMENT = []

        // 需要翻译下拉值的数据字段
        const needToTransformData = ref(['SOURCE', 'ARCH'])
        // 中文对照表
        let code2ChineseMap  =ref({})
        let rightShowData = ref([])
        const defaultProps = {
            children: 'children',
            label: 'label',
            value: 'value',
            type: 'type'
        }
        // 点击时如果没有获取到该项
        const handleNodeClick = (data, node) => {
            // 截取选中属性
            const splitArr = data.type.split('-')
            if (!splitArr.length) return
                code2ChineseMap.value = asset_map[`${splitArr[0]}_Map`] // 获取中文对照表
                const showData = { ASSET_NODE, HARDWARE, SOFTWARE, NETWORK, MANAGEMENT }[splitArr[0]] // 匹配到关键项
                if (splitArr.length === 1) {
                    rightShowData.value = showData
                }
                if (splitArr.length === 2) {
                    rightShowData.value =  Array.isArray(showData[Number(splitArr[1])]) ? showData[Number(splitArr[1])] : [showData[Number(splitArr[1])]]
                    console.log('返回点击', cloneDeep(rightShowData.value));

                }
                if (splitArr.length === 3) {
                    rightShowData.value = Array.isArray(showData[Number(splitArr[1])][splitArr[2]]) ? showData[Number(splitArr[1])][splitArr[2]] : [showData[Number(splitArr[1])][splitArr[2]]]
                    console.log('返回点击', cloneDeep(rightShowData.value));

                }
        };
        const reload = () => {
                    ipcRenderer.send('loadPage');
        }
        return {
            reload,
            defaultProps,
            handleNodeClick,
            // 字典获取类
            trueOrFalseEnum2Name,
            // 数据类
            mainInfo,
            needToTransformData,
            getShowNameByEnumNameAndValue,
            getShowNameByEnumAndValue,
            treeData,
            rightShowData,
            code2ChineseMap
        }
    }
})
app.use(ElementPlus, {
    locale: ElementPlusLocaleZhCn,
  })
app.mount('#app');
