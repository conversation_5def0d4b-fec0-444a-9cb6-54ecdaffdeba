const DCDParamDefine_pb = require('../../script/pb/DCDParamDefine_pb.js')
const PublicDefine_pb = require('../../script/pb/PublicDefine_pb.js');
const PlatformProxyServer_pb = require('../../script/pb/PlatformProxyServer_pb.js');
const ProxyServer_pb = require('../../script/pb/ProxyServer_pb.js');
const util = require('../../lib/util.js');
const ui_util = require('../../script/view/ui_util.js');
const tcpInstance = require('../../lib/v2/tls_client.js');
const {createApp, ref, onMounted, reactive, onBeforeMount } = Vue;
const {ipcRenderer, dialog} = require('electron');
const { ElMessage } = ElementPlus;
const { cloneDeep } = require('lodash')
// 校验IP或IP段
const validate3600 = (rule, value, callback) => {
  if (typeof value === 'string' && value.includes('.')) return callback(new Error('请在10-3600之间取值整数'))
  if (!value || isNaN(value) || value < 10 || value > 3600) {
    return callback(new Error('请在10-3600之间取值'))
  } else {
    return callback()
  }
}
// 校验IP或IP段
const validate360 = (rule, value, callback) => {
    if (typeof value === 'string' && value.includes('.')) return callback(new Error('请在0-360之间取值整数'))

  if ((!value && value !== 0) || isNaN(value) || value < 0 || value > 360) {
    return callback(new Error('请在0-360之间取值'))
  } else {
    return callback()
  }
}
// 校验IP或IP段
const validate5099 = (rule, value, callback) => {
    if (typeof value === 'string' && value.includes('.')) return callback(new Error('请在50-99之间取值整数'))

  if (!value || isNaN(value) || value < 50 || value > 99) {
    return callback(new Error('请在50-99之间取值'))
  } else {
    return callback()
  }
}
const validate099 = (rule, value, callback) => {
    if (typeof value === 'string' && value.includes('.')) return callback(new Error('请在0-99之间取值整数'))

    if ((!value && value !== 0) || isNaN(value) || value < 0 || value > 99) {
        return callback(new Error('请在0-99之间取值'))
    } else {
        return callback()
    }
}
const rule = {
    actionMerCyc: [
        { required: false, message: '请输入', trigger: 'blur' },
        { required: false, message: validate3600.Error, validator: validate3600, trigger: ['blur', 'change'] },
    ],
    deviceNoticeCyc: [
        { required: false, message: '请输入', trigger: 'blur' },
        { required: false, message: validate3600.Error, validator: validate3600, trigger: ['blur', 'change'] },
    ],
    hisEventUploadTime: [
        { required: false, message: '请输入', trigger: 'blur' },
        { required: false, message: validate360.Error, validator: validate360, trigger: ['blur', 'change'] },
    ],
    // 装置存储最大容量比例
    maxStorRat: [
        { required: false, message: '请输入', trigger: 'blur' },
        { required: false, message: validate5099.Error, validator: validate5099, trigger: ['blur', 'change'] },
    ],
    // 模型数据磁盘空间使用率上限阈值
    modelStoMAX: [
        { required: false, message: '请输入', trigger: 'blur' },
        { required: false, message: validate099.Error, validator: validate099, trigger: ['blur', 'change'] },
    ],
    // 采集日志控件使用率上限阈值
    collectStoMAX: [
        { required: false, message: '请输入', trigger: 'blur' },
        { required: false, message: validate099.Error, validator: validate099, trigger: ['blur', 'change'] },
    ],
    flowStoMAX: [
        { required: false, message: '请输入', trigger: 'blur' },
        { required: false, message: validate099.Error, validator: validate099, trigger: ['blur', 'change'] },
    ],
    otherStoMax: [
        { required: false, message: '请输入', trigger: 'blur' },
        { required: false, message: validate099.Error, validator: validate099, trigger: ['blur', 'change'] },
    ],
}
const app = createApp({
    setup() {
        // 前置获取用户及请求信息
        const requestMsgId = util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET)
        const user_name = $("body").attr("user_name_info");
        // 配置发起请求
        const tcpConfigPDSetting = (pageCode, functionCode, data = null) => {
            const msgPayload = {
                uuid: util.genTaskId(),
                type: String(pageCode),
                param: String(functionCode),
                addInfo: []
            }
            if (data) msgPayload.addInfo = [data]
            let sendMsg = JSON.stringify(msgPayload)
            console.log('send =>', sendMsg);
            // 配置管理,参数配置
            tcpInstance.sendJsonMsg(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET, sendMsg).then();
        }
        // 响应
        const getData = () => {
            tcpConfigPDSetting(
                PlatformProxyServer_pb.MSG_PDConfigParam.FunctionType.SELF_RUNNING,
                PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.QUERY
            )
        }
        onBeforeMount(() => {
            initOnMessage()
            getData()
        })
        const formatDataFn = (msg) => {
            let form =  {}
            const addInfoList = msg.getAddinfoList();
            addInfoList.forEach((anyItem) => {
                try {
                    let DCD = DCDParamDefine_pb.MSG_EventConfig
                    const bytes = anyItem.getValue(); // 获取Any中的二进制数据
                    const msg1 = DCD.deserializeBinary(bytes)
                    form = {
                        // 行为归并
                        actionMerCyc: msg1.getActionmercyc(),
                        // 资产运行状态
                        deviceNoticeCyc: msg1.getDevicenoticecyc(),
                        // 历史数据
                        hisEventUploadTime: msg1.getHiseventuploadtime(),
                        // 模型数据磁盘
                        maxStorRat: msg1.getMaxstorrat(),
                        modelStoMAX: msg1.getModelstomax(),
                        collectStoMAX: msg1.getCollectstomax(),
                        flowStoMAX: msg1.getFlowstomax(),
                        otherStoMax: msg1.getOtherstomax()
                    }
                    console.log('解析数据 => ', form)
                    Object.keys(form).forEach(key => {
                        selfParamForm[key] = form[key];
                    })
                } catch (error) {
                    console.log('error', error);
                }
            })
        }
        const formRef = ref(null)
        const formRules = ref(rule)
        const addRedStar = (h, { column }) => {
            return [
                h('span', { style: 'color: red' }, '*'),
                h('span', ' ' + column.label)
            ]
        }
        // const copyNetInter = ref([])
        const copySelf_param = ref([])
        // common
        const isEdit = ref(false)
        const reload = () => {
            ipcRenderer.send('loadPage');
        }
        const editFn = (type) => {
            switch (type) {
                case 'ON': {
                    copySelf_param.value = cloneDeep(formData.selfParamList)
                    isEdit.value = true
                    break;
                }
                case 'OFF': {
                    formData.selfParamList = cloneDeep(copySelf_param.value)
                    isEdit.value = false
                    break;
                }
                case 'SAVE': {
                    formRef.value.validate(valid => {
                        if (valid) {
                            onClickUserValidate('save')
                        } else {
                            ui_util.showFadeTip('请检查填写内容！')
                        }
                    })
                    break;
                }
                default:
                    break;
            }
        }
        // 监听事件注册
        const initOnMessage = () => {
            // 页面监听
            tcpInstance.onMessage(util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET), onMessageHandle)
            // 用户校验监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCVALIDATEUSER, (validatePb) => {
                const validateMsg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(validatePb);
                if (validateMsg.getErrorCode() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                    if (validateMsg.getRequestMsgId() === requestMsgId) {
                        switch ($('#opraType').val()) {
                            case 1:{
                                $('#confirmPortDlg').modal('show')
                                $('#confirmPortDlg .modal-title').attr("wa-name","import_tt")
                                $('#confirmPortDlg .modal-body .form-group:nth-child(1)').removeClass("hidden")
                                $('#confirmPortDlg .modal-body .form-group:nth-child(2)').addClass("hidden")
                                break
                            };
                            case 2:{
                                $('#confirmPortDlg').modal('show')
                                $('#confirmPortDlg .modal-title').attr("wa-name","export_tt")
                                $('#confirmPortDlg .modal-body .form-group:nth-child(2)').removeClass("hidden")
                                $('#confirmPortDlg .modal-body .form-group:nth-child(1)').addClass("hidden")
                                break
                            };
                            case 'save':{
                                tcpConfigPDSetting(
                                PlatformProxyServer_pb.MSG_PDConfigParam.FunctionType.SELF_RUNNING,
                                PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.MODIFY,
                                cloneDeep(selfParamForm)
                                )
                                // 配置管理：FUNCTIONTYPE_CONFIGMANAGE 参数配置：CONFIGMANAGEPACKETTYPE_PARAMSET
                                break
                            };
                            default:
                                break;
                        }
                    }
                } else {
                    ui_util.getErrorTips(validateMsg.getErrorCode(), '验证用户身份')
                }
            })
            // 导入 监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCIMPORTDATA, (pb) => importExportDoneHandler('IMPORT', pb))
            // 导出 监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCEXPORTDATA, (pb) => importExportDoneHandler('EXPORT', pb))
        }
        // 导入导出文件操作1：监听按钮点击打开用户验证
        const importExportStep1Handler = (type) => {
            $('#validateUserDlg').modal('show');
            e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
            $('#validateUserForm').get(0).reset()
            $('#opraType').val(type)
        }
        // 导入导出文件操作2：发送请求  file 在导出时是filePath不是文件内容
        const importExportStep2Handler = (TYPE, file, fileName) => {
            const isImport = TYPE === 'IMPORT';
            const reqData = isImport ? new ProxyServer_pb.MSG_CPImportData() : new ProxyServer_pb.MSG_CPExportData();
            reqData.setType(PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_EVENT);
            isImport && reqData.setData(util.readFromFile(file, false).toString());
            tcpInstance.sendProtoMsg(isImport ? ProxyServer_pb.PSMessageId.PSMESSAGEID_CPIMPORTDATA : ProxyServer_pb.PSMessageId.PSMESSAGEID_CPEXPORTDATA, reqData);
            isImport && $('.parent_content').busyLoad("show", { background: "rgba(0, 0, 0, 0.59)", fontawesome: "fa fa-spinner fa-spin fa-3x fa-fw" });
        }
        // 接收请求后续操作
        const importExportDoneHandler = (type, pb) => {
            const isImport = type === 'IMPORT';
            const msg = ProxyServer_pb[isImport ? 'MSG_PCImportData' : 'MSG_PCExportData'].deserializeBinary(pb);
            isImport && $('.parent_content').busyLoad("hide");
            if (msg.getErrorCode() !== 0) return ui_util.getErrorTips(msg.getErrorCode(), `${isImport ? '导入' : '导出'}事件处理`);
            isImport ? ui_util.showLoading(30) : util.saveToCSV(msg.getData(), `${filePath}/${fileName}`);
            ui_util.showFadeTip(`${isImport ? '导入' : '导出'}事件处理成功`);
            getData();
        }
        let file="",filePath="",fileName="sntp.cfg";
        // 初始化选择文件夹路径&选择文件
        $('#select_folder').ace_file_input({
          no_file:'请选择文件夹',
          btn_choose:'选择',
          btn_change:null,
          droppable:false,
          onchange:null,
          thumbnail:false
        });
        document.querySelector('#select_folder').addEventListener('change', e => {
          for (let entry of e.target.files){
            console.log(entry.name, entry.path);
            filePath=entry.path
          }
        });
        $('#select_file').ace_file_input({
          no_file:'请选择文件',
          btn_choose:'选择',
          btn_change:null,
          droppable:false,
          onchange:null,
          thumbnail:false,
          allowExt: ['cfg'],
        }).on('change', function(){
          let fileDir = $(this).val();
          let suffix = fileDir.substr(fileDir.lastIndexOf("."));
          if ("" == fileDir||".cfg" != suffix) {
            ui_util.showFadeTip('请选择.cfg后缀文件!');
            return false;
          }
          // console.log($(this).data('ace_input_files'));
          file=$(this).data('ace_input_files')[0].path
        });
        // 注册导入导出按钮监听
        $('#port_btn').on('click',function (e) {
          if ($("#port_btn").hasClass("disabled")) return
          if($('#confirmPortDlg .modal-body .form-group:nth-child(1)').hasClass("hidden")){
            if(!$("#select_folder").next("span.ace-file-container").hasClass("selected")){
              ui_util.showFadeTip('请选择文件路径!')
              return;
            }
          }else if($('#confirmPortDlg .modal-body .form-group:nth-child(2)').hasClass("hidden")){
            if(!($("#select_file").next("span.ace-file-container").hasClass("selected"))){
              ui_util.showFadeTip('请选择文件!')
              return;
            }
          }
          $('#confirmPortDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
          e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
          if($('#confirmPortDlg .modal-title').attr("wa-name")=="import_tt"){
            importEvents('IMPORT', file, fileName)
          }else if($('#confirmPortDlg .modal-title').attr("wa-name")=="export_tt"){
            exportEvents('EXPORT', filePath, fileName)
          }
        })
        // 响应事件处理
        const onMessageHandle = (pb) => {
            const msg = PlatformProxyServer_pb.MSG_DPConfigParam.deserializeBinary(pb);
            if (msg.getResult() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                switch (Number(msg.getParam())) {
                    case PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.QUERY: {
                        $('#validateuser_btn').off('click'); // 用完一定要手动移除该事件，否则会积累触发点击事件
                        formatDataFn(msg)
                        break
                    }
                    case PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.MODIFY: {
                        $('#validateuser_btn').off('click'); // 用完一定要手动移除该事件，否则会积累触发点击事件
                        isEdit.value = false
                        ui_util.showFadeTip('操作成功！')
                        getData()
                        break
                    }
                    default:
                        break;
                }
            } else {
                console.log('error => ', msg.getResult())
                ui_util.getErrorTips(msg.getResult(), '操作', '(检查是否因各空间使用率上限阈值之和超过磁盘空间使用率上限阈值导致)')
            }
        }
        const onClickUserValidate = (type) => {
            $('#validateUserDlg').modal('show'); // 打开用户密码校验
            $('#validateUserForm').get(0).reset() // 置空密码校验数据
            $('#opraType').val(type) // 存入保存标识
            // 弹出密码校验框
            $('#validateuser_btn').on('click',function (e) {
            if ($("#validateuser_btn").hasClass("disabled")) {
                return;
            }
            $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
            e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
            let username=user_name
            let psd=$('#validateUserDlg #password').val()
            const user_sign = $("body").attr("user_sign_info");
            // 发送消息
            let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
            sendMsg.setUserName(username)
            sendMsg.setPassword(psd)
            sendMsg.setRequestMsgId(requestMsgId)
            tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg).then();
        })
        }
        // netInter
        const broadcastEnum = ref([
            {code: 0, name: '非广播'},
            {code: 1, name: '广播'}
        ])
        const selfParamForm = reactive({
            actionMerCyc: 60,
            deviceNoticeCyc: 20,
            hisEventUploadTime: 3400,
            maxStorRat: 50,
            modelStoMAX: 30,
            collectStoMAX: 30,
            flowStoMAX: 30,
            otherStoMax: 10,
        })
                // 网口
        const formData = reactive({
            selfParamList: []
        })
        // flowInter

        return {
            importExportStep1Handler,

            selfParamForm,
            broadcastEnum,
            formRef,
            formData,
            addRedStar,
            reload,
            formRules,

            isEdit,
            editFn,
        };
    }
})
// app.use(ElementPlus);
app.use(ElementPlus, {
    locale: ElementPlusLocaleZhCn,
  })
app.mount('#app');
