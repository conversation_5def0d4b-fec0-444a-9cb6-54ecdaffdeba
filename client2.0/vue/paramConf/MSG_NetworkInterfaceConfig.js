const DCDParamDefine_pb = require('../../script/pb/DCDParamDefine_pb.js')
const PublicDefine_pb = require('../../script/pb/PublicDefine_pb.js');
const PlatformProxyServer_pb = require('../../script/pb/PlatformProxyServer_pb.js');
const ProxyServer_pb = require('../../script/pb/ProxyServer_pb.js');
const util = require('../../lib/util.js');
const ui_util = require('../../script/view/ui_util.js');
// 新版请求方式
const config = require("../../config.json");
const tcpInstance = require('../../lib/v2/tls_client.js');
// 旧版
const tcp_client = require('../../lib/tcp_client.js');

const path = require('path');
const {createApp, ref, onBeforeMount, onMounted, reactive, onBeforeUnmount} = Vue;
const {ipcRenderer, dialog} = require('electron');
const { ElMessage } = ElementPlus;
const { cloneDeep } = require('lodash')
// 校验IP或IP段
const validate1500 = (rule, value, callback) => {

  // 非数字
  if (isNaN(value)) return callback(new Error('请在68-9568之间取值整数'))
  // 小数
  if (!Number.isInteger(Number(value))) return callback(new Error('请在68-9568之间取值整数'))
  // 小数点存在
  if(typeof value === 'string' && value.includes('.')) return callback(new Error('请在68-9568之间取值整数'))
  // 范围
  if (value < 68 || value > 9568) {
    return callback(new Error('请在68-9568之间取值整数'))
  } else {
    return callback()
  }
}
// 校验IP或IP段
const validateIp = (rule, value, callback) => {
  const reg = /^((\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.){3}(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])(-((\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.){3}(\d{1,2}|1\d\d|2[0-4]\d|25[0-5]))?$/
  if (value && reg.test(value) === false) {
    return callback(new Error('请确保格式正确'))
  } else {
    return callback()
  }
}
const changeMtu = (val) => {

}
const rule = {
    netIp: [
    { required: false, message: '请输入网口IP', trigger: 'blur' },
    { required: false, message: validateIp.Error, validator: validateIp, trigger: ['blur', 'change'] },
    ],
    netmask: [
    { required: false, message: '请输入子网掩码', trigger: 'blur' },
    { required: false, message: validateIp.Error, validator: validateIp, trigger: ['blur', 'change'] },
    ],
    netMtu: [
    { required: true, message: '请输入网卡mtu值', trigger: 'blur' },
    { required: true, message: validate1500.Error, validator: validate1500, trigger: ['blur', 'change'] },
    ]

}

const areaEnums = [
    { code: 1000, name: 'I区' },
    { code: 100, name: 'II区' },
    { code: 10, name: 'III区' },
    { code: 1, name: 'IV区' },
    { code: 0, name: '无' },
]
const areaEnumMap = {
    1000: 'I区',
    100: 'II区',
    10: 'III区',
    1: 'IV区',
    0: '无'
}
const app = createApp({
    setup() {
        const copyNetInter = ref([])
        const copyFlowInter = ref([])
        // common
        // 所有的网卡口
        const ethEnum = ref(Array.from({length: 24}, (_, i) => `eth${i + 1}`))
        const areaEnum = ref(areaEnums)
        const areaMap = ref(areaEnumMap)
        const isEdit = ref(false)
        const formRules = ref(rule)
        const formData = reactive({
            netInter: [],
            flowInter: []
        })
        onMounted(async () => {
            resizeHeight()
            window.addEventListener('resize', resizeHeight)
            initOnMessage()
            getData() // 获取数据
        })
        onBeforeUnmount(() => {
            window.removeEventListener('resize', resizeHeight)
        })
        const box = ref(null)
        const tableHeight = ref(100)
        const resizeHeight = () => {
            console.log('高度', box.value.clientHeight)
            tableHeight.value = box.value.clientHeight * 0.4
        }
        const formRef = ref(null)
        // 编辑/取消/保存 按钮相关逻辑
        const editFn = (type) => {
            switch (type) {
                case 'ON': {
                    copyNetInter.value = cloneDeep(formData.netInter)
                    copyFlowInter.value = cloneDeep(formData.flowInter)
                    isEdit.value = true
                    break;
                }
                case 'OFF': {
                    formData.netInter = cloneDeep(copyNetInter.value)
                    formData.value = cloneDeep(copyFlowInter.value)
                    isEdit.value = false
                    break;
                }
                case 'SAVE': {
                    formRef.value.validate(valid => {
                        if (valid) {
                            $('#validateUserDlg').modal('show'); // 打开用户密码校验
                            $('#validateUserForm').get(0).reset() // 置空密码校验数据
                            $('#opraType').val("save") // 存入保存标识
                            onClickUserValidate()
                        } else {
                            ui_util.showFadeTip('请检查填写内容！')
                        }
                    })


                    break;
                }
                default:
                    break;
            }
        }
        // 导入导出文件操作1：监听按钮点击打开用户验证
        const importExportStep1Handler = (type) => {
            $('#validateUserDlg').modal('show');
            e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
            $('#validateUserForm').get(0).reset()
            $('#opraType').val(type)
        }
        // 导入导出文件操作2：发送请求  file 在导出时是filePath不是文件内容
        const importExportStep2Handler = (TYPE, file, fileName) => {
            const isImport = TYPE === 'IMPORT';
            const reqData = isImport ? new ProxyServer_pb.MSG_CPImportData() : new ProxyServer_pb.MSG_CPExportData();
            reqData.setType(PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_EVENT);
            isImport && reqData.setData(util.readFromFile(file, false).toString());
            tcpInstance.sendProtoMsg(isImport ? ProxyServer_pb.PSMessageId.PSMESSAGEID_CPIMPORTDATA : ProxyServer_pb.PSMessageId.PSMESSAGEID_CPEXPORTDATA, reqData);
            isImport && $('.parent_content').busyLoad("show", { background: "rgba(0, 0, 0, 0.59)", fontawesome: "fa fa-spinner fa-spin fa-3x fa-fw" });
        }
        // 接收请求后续操作
        const importExportDoneHandler = (type, pb) => {
            const isImport = type === 'IMPORT';
            const msg = ProxyServer_pb[isImport ? 'MSG_PCImportData' : 'MSG_PCExportData'].deserializeBinary(pb);
            isImport && $('.parent_content').busyLoad("hide");
            if (msg.getErrorCode() !== 0) return ui_util.getErrorTips(msg.getErrorCode(), `${isImport ? '导入' : '导出'}事件处理`);
            isImport ? ui_util.showLoading(30) : util.saveToCSV(msg.getData(), `${filePath}/${fileName}`);
            ui_util.showFadeTip(`${isImport ? '导入' : '导出'}事件处理成功`);
            getData();
        }

        let file="",filePath="",fileName="sntp.cfg";
        // 初始化选择文件夹路径&选择文件
        $('#select_folder').ace_file_input({
          no_file:'请选择文件夹',
          btn_choose:'选择',
          btn_change:null,
          droppable:false,
          onchange:null,
          thumbnail:false
        });
        document.querySelector('#select_folder').addEventListener('change', e => {
          for (let entry of e.target.files){
            console.log(entry.name, entry.path);
            filePath=entry.path
          }
        });
        $('#select_file').ace_file_input({
          no_file:'请选择文件',
          btn_choose:'选择',
          btn_change:null,
          droppable:false,
          onchange:null,
          thumbnail:false,
          allowExt: ['cfg'],
        }).on('change', function(){
          let fileDir = $(this).val();
          let suffix = fileDir.substr(fileDir.lastIndexOf("."));
          if ("" == fileDir||".cfg" != suffix) {
            ui_util.showFadeTip('请选择.cfg后缀文件!');
            return false;
          }
          // console.log($(this).data('ace_input_files'));
          file=$(this).data('ace_input_files')[0].path
        });
        // 注册导入导出按钮监听
        $('#port_btn').on('click',function (e) {
          if ($("#port_btn").hasClass("disabled")) return
          if($('#confirmPortDlg .modal-body .form-group:nth-child(1)').hasClass("hidden")){
            if(!$("#select_folder").next("span.ace-file-container").hasClass("selected")){
              ui_util.showFadeTip('请选择文件路径!')
              return;
            }
          }else if($('#confirmPortDlg .modal-body .form-group:nth-child(2)').hasClass("hidden")){
            if(!($("#select_file").next("span.ace-file-container").hasClass("selected"))){
              ui_util.showFadeTip('请选择文件!')
              return;
            }
          }
          $('#confirmPortDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
          e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
          if($('#confirmPortDlg .modal-title').attr("wa-name")=="import_tt"){
            importEvents('IMPORT', file, fileName)
          }else if($('#confirmPortDlg .modal-title').attr("wa-name")=="export_tt"){
            exportEvents('EXPORT', filePath, fileName)
          }
        })
        const initOnMessage = () => {
            // 页面数据事件监听
            tcpInstance.onMessage(util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET), onMessageHandler)
            // 用户验证事件监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCVALIDATEUSER, (validatePb) => {
                const validateMsg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(validatePb);
                if (validateMsg.getErrorCode() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                    if (validateMsg.getRequestMsgId() === requestMsgId) {
                        if($('#opraType').val()=="save"){
                            const netInter = cloneDeep(formData.netInter)
                            const flowInter = cloneDeep(formData.flowInter)
                            const data = {
                                netInter: [],
                                flowInter: []
                            }
                            netInter.forEach(item => {
                                if (!item.area) {
                                    delete item.area
                                    data.netInter.push(item)
                                } else {
                                    delete item.netIp
                                    delete item.netmask
                                    delete item.netMtu
                                    data.flowInter.push(item)
                                }
                            })
                            flowInter.forEach(item => {
                                if (!item.area) {
                                    item.netIp = ''
                                    item.netmask = ''
                                    item.netMtu = 0
                                    delete item.area
                                    data.netInter.push(item)
                                } else {
                                    data.flowInter.push(item)
                                }
                            })
                            // tcpInstance.onMessage(util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET), onMessageHandler)
                            tcpConfigPDSetting(
                                PlatformProxyServer_pb.MSG_PDConfigParam.FunctionType.NETWORK_INTERFACE,
                                PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.MODIFY,
                                cloneDeep(data)
                            )
                        }
                    }
                } else {
                    ui_util.getErrorTips(validateMsg.getErrorCode(), '验证用户身份')
                }
            })
            // 导入 监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCIMPORTDATA, (pb) => importExportDoneHandler('IMPORT', pb))
            // 导出 监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCEXPORTDATA, (pb) => importExportDoneHandler('EXPORT', pb))
        }
        const requestMsgId = util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET)
        const user_name = $("body").attr("user_name_info");
        const onClickUserValidate = () => {
            // 弹出密码校验框
            $('#validateuser_btn').on('click',function (e) {
            if ($("#validateuser_btn").hasClass("disabled")) {
                return;
            }
            $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
            e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
            let username=user_name
            let psd=$('#validateUserDlg #password').val()
            validateUserGenerator(username,psd);
            })
        }
        /**
         * 发送验证密码参数
         * @param psd
         */
        const validateUserGenerator = (username,psd) => {
        const user_sign = $("body").attr("user_sign_info");

        // 发送消息
        let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
        sendMsg.setUserName(username)
        sendMsg.setPassword(psd)
        sendMsg.setRequestMsgId(requestMsgId)
        tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg).then();
    }

        // 格式化返回数据
        const formatDataFn = (msg) => {
                const data = {
                    netInter: [],
                    flowInter: []
                }
                // 获取appInfo
                const addInfoList = msg.getAddinfoList();
                addInfoList.forEach((anyItem) => {
                    try {
                        let DCD = DCDParamDefine_pb.MSG_NetworkInterfaceConfig
                        // 获取addInfo
                        const bytes = anyItem.getValue(); // 获取Any中的二进制数据
                        // 解构addInfo
                        const msg1 = DCDParamDefine_pb.MSG_NetworkInterfaceConfig.deserializeBinary(bytes)
                        // 获取NetInter 和 Flowinter
                        const Netinter = msg1.getNetinterList()
                        const Flowinter = msg1.getFlowinterList()
                        console.log('Netinter', Netinter, Flowinter)
                        Netinter.forEach(item => {
                            data.netInter.push({
                                interface: item.getInterface(),
                                netIp: item.getNetip(),
                                netmask: item.getNetmask(),
                                netMtu: item.getNetmtu(),
                                area: 0
                            })
                        })
                        Flowinter.forEach(item => {
                            data.flowInter.push({
                                interface: item.getInterface(),
                                area: item.getArea()
                            })
                        })
                    } catch (error) {
                        console.log('error', error);
                    }
                });
                    formData.netInter = cloneDeep(data.netInter)
                    formData.flowInter = cloneDeep(data.flowInter)
        }
        // 发送
        const tcpConfigPDSetting = (pageCode, functionCode, data = null) => {
            const msgPayload = {
                uuid: util.genTaskId(),
                type: String(pageCode),
                param: String(functionCode),
                addInfo: []
            }
            if (data) msgPayload.addInfo = [data]
            console.log('send =>', JSON.stringify(msgPayload), msgPayload);

            // 配置管理,参数配置
            // tcpInstance.sendPlatformMsg(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET, buffer).then();
            tcpInstance.sendJsonMsg(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET, JSON.stringify(msgPayload)).then();
        }
        // 响应
        const getData = () => {
            tcpConfigPDSetting(
                PlatformProxyServer_pb.MSG_PDConfigParam.FunctionType.NETWORK_INTERFACE,
                PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.QUERY
            )
        }
        const onMessageHandler = (pb) => {
            console.log('响应', pb)
                const msg = PlatformProxyServer_pb.MSG_DPConfigParam.deserializeBinary(pb);
            console.log('响应1', msg)
                if (msg.getResult() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                    switch (Number(msg.getParam())) {
                        case PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.QUERY: {
                            $('#validateuser_btn').off('click'); // 用完一定要手动移除该事件，否则会积累触发点击事件
                            formatDataFn(msg)
                            break
                        }
                        case PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.MODIFY: {
                            $('#validateuser_btn').off('click'); // 用完一定要手动移除该事件，否则会积累触发点击事件
                            ui_util.showFadeTip('操作成功')
                            isEdit.value = false
                            getData() // 重新请求当前页数据
                            break
                        }
                        default:
                            break;
                    }
                } else {
                    ui_util.getErrorTips(msg.getResult(), '操作')
                }
        }
        const changeAreaToFlow = (val, index) => {
            if (val) {
                const removedItem = formData.netInter.splice(index, 1)[0]
                const newItem = {...removedItem};
                delete newItem.netIp
                delete newItem.netmask
                delete newItem.netMtu
                formData.flowInter.push(newItem)
            }
        }
        const changeAreaToNet = (val, index) => {
            if (!val) {
                const removedItem = formData.flowInter.splice(index, 1)[0]
                const newItem = {...removedItem};
                newItem.netIp = ''
                newItem.netmask = ''
                newItem.netMtu = ''
                formData.netInter.push(newItem)
            }
        }

        return {
            box,
            tableHeight,
            importExportStep1Handler,
            formRef,
            formRules,
            formData,
            isEdit,
            ethEnum,
            areaEnum,
            areaMap,
            editFn,
            changeMtu,
            changeAreaToFlow,
            changeAreaToNet
        };
    }
})
// app.use(ElementPlus);
app.use(ElementPlus, {
    locale: ElementPlusLocaleZhCn,
  })
app.mount('#app');
