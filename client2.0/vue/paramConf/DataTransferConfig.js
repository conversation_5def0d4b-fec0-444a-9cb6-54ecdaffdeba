const DCDParamDefine_pb = require('../../script/pb/DCDParamDefine_pb.js')
const PublicDefine_pb = require('../../script/pb/PublicDefine_pb.js');
const PlatformProxyServer_pb = require('../../script/pb/PlatformProxyServer_pb.js');
const ProxyServer_pb = require('../../script/pb/ProxyServer_pb.js');
const util = require('../../lib/util.js');
const ui_util = require('../../script/view/ui_util.js');
const tcpInstance = require('../../lib/v2/tls_client.js');
const {createApp, ref, onMounted, reactive, onBeforeMount, onBeforeUnmount} = Vue;
const {ipcRenderer, dialog} = require('electron');
const { ElMessage, ElMessageBox } = ElementPlus;
const { cloneDeep } = require('lodash')
// 校验IP或IP段
const validateIp = (rule, value, callback) => {
  const reg = /^((\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.){3}(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])(-((\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.){3}(\d{1,2}|1\d\d|2[0-4]\d|25[0-5]))?$/
  if (value && reg.test(value) === false) {
    return callback(new Error('请确保格式正确'))
  } else {
    return callback()
  }
}
const areaEnums = [
    { code: 1000, name: 'I区' },
    { code: 100, name: 'II区' },
    { code: 10, name: 'III区' },
    { code: 1, name: 'IV区' },
]
const rule = {
    // funcType: [
    // { required: true, message: '请输入目的网段', trigger: 'blur' },
    // { required: true, message: validateIp.Error, validator: validateIp, trigger: ['blur', 'change'] },
    // ],
    // dstNetMask: [
    // { required: true, message: '请输入网口掩码', trigger: 'blur' },
    // { required: true, message: validateIp.Error, validator: validateIp, trigger: ['blur', 'change'] },
    // ],
    // netGateway: [
    // { required: true, message: '请输入网关地址', trigger: 'blur' },
    // { required: true, message: validateIp.Error, validator: validateIp, trigger: ['blur', 'change'] },
    // ],
}
const areaEnumMap = {
    1000: 'I区',
    100: 'II区',
    10: 'III区',
    1: 'IV区'
}
const app = createApp({
    setup() {
        // 前置获取用户及请求信息
        const requestMsgId = util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET)
        const user_name = $("body").attr("user_name_info");
        const trueOrFalse = ref([
            { code: '0', name: '关' },
            { code: '1', name: '开' }
        ])
        const Enum = ref([
            { code: PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_BEHAVIORUPLOAD, name: '行为上送' },
            { code: PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_MESSAGEPUSH, name: '消息通知' },
            { code: PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_DATAQUERY, name: '数据调阅' },
            { code: PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_DATAEXCHANGE, name: '数据交互' },
            { code: PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_COMMANDCONTROL, name: '命令控制' },
            { code: PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, name: '配置管理' },
            { code: PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_ASSETREGISTER, name: '资产注册' },
            { code: PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_COMMANDCONTROL, name: '命令控制' },
        ])
        const subEnumMap = ref({
            // 行为上送
            [PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_BEHAVIORUPLOAD]: [
                { code: PlatformProxyServer_pb.BehaviorUploadPaketType.BEHAVIORUPLOADPAKETTYPE_BEHAVIOR, name: '行为模型' }
            ],
            // 消息通知
            [PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_MESSAGEPUSH]: [
                { code: PlatformProxyServer_pb.MessagePushPacketType.MESSAGEPUSHPACKETTYPE_ASSETRUNNING, name: '资产运行状态通知' },
                { code: PlatformProxyServer_pb.MessagePushPacketType.MESSAGEPUSHPACKETTYPE_CONNECTIONCHANGE, name: '连接模型变更通知' },
                // { code: PlatformProxyServer_pb.MessagePushPacketType.MESSAGEPUSHPACKETTYPE_SECURITYDEVICEWARNING, name: '安防设备告警通知' },
                { code: PlatformProxyServer_pb.MessagePushPacketType.MESSAGEPUSHPACKETTYPE_PLUGINMESSAGE, name: '插件通知' },
            ],
            // 数据调阅
            [PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_DATAQUERY]: [
                { code: PlatformProxyServer_pb.DataQueryPacketType.DATAQUERYPACKETTYPE_ASSET, name: '资产模型调阅' },
                { code: PlatformProxyServer_pb.DataQueryPacketType.DATAQUERYPACKETTYPE_CONNECTION, name: '连接模型调阅' },
                { code: PlatformProxyServer_pb.DataQueryPacketType.DATAQUERYPACKETTYPE_BEHAVIOR, name: '行为模型调阅' },
                { code: PlatformProxyServer_pb.DataQueryPacketType.DATAQUERYPACKETTYPE_LOG, name: '日志信息调阅' },
                { code: PlatformProxyServer_pb.DataQueryPacketType.DATAQUERYPACKETTYPE_PCAP, name: '原始流量调阅' },
                { code: PlatformProxyServer_pb.DataQueryPacketType.DATAQUERYPACKETTYPE_DCDINFO, name: '装置自身信息' },
                // { code: PlatformProxyServer_pb.DataQueryPacketType.DATAQUERYPACKETTYPE_MONITOROBJSTATUS, name: '监测对象状态' },
                { code: PlatformProxyServer_pb.DataQueryPacketType.DATAQUERYPACKETTYPE_ASSETSTATUS, name: '资产状态调阅' },
                { code: PlatformProxyServer_pb.DataQueryPacketType.DATAQUERYPACKETTYPE_PLATFORMCOMMSTATUS, name: '平台通信状态调阅' },
            ],
            // 数据交互
            [PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_DATAEXCHANGE]: [
                { code: PlatformProxyServer_pb.DataExchangePacketType.DATAEXCHANGEPACKETTYPE_ACTIVELINK, name: '激活链路' },
                { code: PlatformProxyServer_pb.DataExchangePacketType.DATAEXCHANGEPACKETTYPE_ACTIVELINKRESPONSE, name: '激活应答' },
                { code: PlatformProxyServer_pb.DataExchangePacketType.DATAEXCHANGEPACKETTYPE_KEEPLINK, name: '链路保持' },
                { code: PlatformProxyServer_pb.DataExchangePacketType.DATAEXCHANGEPACKETTYPE_KEEPLINKRESPONSE, name: '保持响应' },
                { code: PlatformProxyServer_pb.DataExchangePacketType.DATAEXCHANGEPACKETTYPE_STOPLINK, name: '链路停止' },
                { code: PlatformProxyServer_pb.DataExchangePacketType.DATAEXCHANGEPACKETTYPE_STOPLINKCONFIRM, name: '停止确认' },
            ],
            // 资产注册
            [PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_ASSETREGISTER]: [
                { code: PlatformProxyServer_pb.AssetRegisterPacketType.ASSETREGISTERPACKETTYPE_DCDREGISTER, name: '装置自身注册' },
                { code: PlatformProxyServer_pb.AssetRegisterPacketType.ASSETREGISTERPACKETTYPE_DCDREGISTERRESULT, name: '装置自身注册结果' },
                { code: PlatformProxyServer_pb.AssetRegisterPacketType.ASSETREGISTERPACKETTYPE_DCDREGISTERED, name: '装置自身已注册' },
                { code: PlatformProxyServer_pb.AssetRegisterPacketType.ASSETREGISTERPACKETTYPE_DCDREGISTEREDCONFIRM, name: '装置自身注册确认' },
            ],
            // 配置管理
            [PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE]: [
                { code: PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET, name: '参数配置' },
                { code: PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_CERTSET, name: '证书配置' },
                { code: PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_ASSETREGISTER, name: '资产注册' },
                { code: PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_ASSETCONCAT, name: '资产拼接' },
                { code: PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_BASELINEMANAGEMENT, name: '基线管理' },
                // { code: PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_BASELINE_STATISTIC, name: '基线管理-基线统计设置' },
                // { code: PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_BASELINE_ANALYSIS, name: '基线管理-基线分析设置' },
                { code: PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_BACKUPPARAM, name: '参数备份' },
                { code: PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_RESTOREPARAM, name: '参数恢复' },
                { code: PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_UPGRADE, name: '装置升级' },
                { code: PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PLUGINMANAGEMENT, name: '插件管理' },
                { code: PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_VERSIONCHECK, name: '版本校验' },
            ],
            // 命令控制
            [PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_COMMANDCONTROL]: [
                { code: PlatformProxyServer_pb.CommandControlPacketType.COMMANDCONTROLPACKETTYPE_DETECTNET, name: '网段探测' },
                { code: PlatformProxyServer_pb.CommandControlPacketType.COMMANDCONTROLPACKETTYPE_FINGERPRINT, name: '指纹信息获取' },
                { code: PlatformProxyServer_pb.CommandControlPacketType.COMMANDCONTROLPACKETTYPE_SERVER, name: '主机管控' },
                { code: PlatformProxyServer_pb.CommandControlPacketType.COMMANDCONTROLPACKETTYPE_SECURITYDEVICE, name: '设备管控' },
                { code: PlatformProxyServer_pb.CommandControlPacketType.COMMANDCONTROLPACKETTYPE_BASELINE, name: '基线核查' },
                { code: PlatformProxyServer_pb.CommandControlPacketType.COMMANDCONTROLPACKETTYPE_LEAKSCAN, name: '漏洞扫描' },
                { code: PlatformProxyServer_pb.CommandControlPacketType.COMMANDCONTROLPACKETTYPE_PLUGIN, name: '插件通道' },
                { code: PlatformProxyServer_pb.CommandControlPacketType.COMMANDCONTROLPACKETTYPE_ADAPTV1, name: '代理兼容' },
            ]
        })
        const getNameByCode = (code) => {
            if (code) {
                const data = Enum.value.find(item => item.code == code)
                if (data) return data.name
                return ''
            } else {
                return ''
            }
        }
        const getSubNameByCodeAndParentCode = (code, parentCode) => {
            if (parentCode) {
                if (code) {
                    const data = subEnumMap.value[parentCode].find(item => item.code == code)
                    if (data) return data.name
                    return code
                } else {
                return code
                }
            } else {
                return code
            }
        }
        const changeSelect = (index, val) => {
            formData.transferList[index].logType = null
        }
        const getCharAtN = (str, n) => {
            // 检查字符串是否为空或n是否为负数
            if (!str || n < 0) {
                return '0';
            }
            // 检查n是否超出字符串长度
            if (n >= str.length) {
                return '0';
            }
            // 返回第n个字符
            return String(str.charAt(n));
        }
        // 格式化proto数据
        const formatDataFn = (msg) => {
            const routeList1 = []
            const addInfoList = msg.getAddinfoList();
            addInfoList.forEach((anyItem) => {
                try {
                    let DCD = DCDParamDefine_pb.DataTransferConfig
                    const bytes = anyItem.getValue(); // 获取Any中的二进制数据
                    const msg1 = DCD.deserializeBinary(bytes)
                        routeList1.push({
                            funcType: msg1.getFunctype(),
                            logType: msg1.getLogtype(),
                            funcOpt0: getCharAtN(msg1.getFuncopt(), 0),
                            funcOpt1: getCharAtN(msg1.getFuncopt(), 1),
                            funcOpt2: getCharAtN(msg1.getFuncopt(), 2),
                            funcOpt3: getCharAtN(msg1.getFuncopt(), 3),
                        })
                } catch (error) {
                    console.log('error', error);
                }
            })
            formData.transferList = routeList1
            console.log('返回数据', formData.transferList)
        }
        // 配置发起请求
        const tcpConfigPDSetting = (pageCode, functionCode, data = null) => {
            const msgPayload = {
                uuid: util.genTaskId(),
                type: String(pageCode),
                param: String(functionCode),
                addInfo: []
            }
            if (data) msgPayload.addInfo = data
            let sendMsg = JSON.stringify(msgPayload)
            console.log('send =>', sendMsg);

            // 配置管理,参数配置
            tcpInstance.sendJsonMsg(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET, sendMsg).then();
        }
        const getData = () => {
            tcpConfigPDSetting(
                PlatformProxyServer_pb.MSG_PDConfigParam.FunctionType.DATA_TRANSFER,
                PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.QUERY
            )
        }
        onMounted(() => {
            resizeHeight()
            window.addEventListener('resize', resizeHeight)
            initOnMessage() // 前置注册响应事件
            getData()
        })
        onBeforeUnmount(() => {
            window.removeEventListener('resize', resizeHeight)
        })
        const box = ref(null)
        const tableHeight = ref(100)
        const resizeHeight = () => {
            console.log('高度', box.value.clientHeight)
            tableHeight.value = box.value.clientHeight * 0.8
        }
        const isEdit = ref(false)
        // 数据传输列表
        // const transferList = ref([
        //     {
        //         funcType: 1,
        //         logType: 1,
        //         funcOpt: 1
        //     },
        //     {
        //         funcType: 1,
        //         logType: 2,
        //         funcOpt: 1
        //     }
        // ])
        const copyTransferList = ref([])

        const editFn = (type) => {
            switch (type) {
                case 'ON': {
                    copyTransferList.value = cloneDeep(formData.transferList)
                    isEdit.value = true
                    break;
                }
                case 'OFF': {
                    formData.transferList = cloneDeep(copyTransferList.value)
                    isEdit.value = false
                    break;
                }
                case 'SAVE': {
                    formRef.value.validate(valid => {
                        if (valid) {
                            onClickUserValidate('save')
                        } else {
                            ui_util.showFadeTip('请检查填写内容！')
                        }
                    })
                    break;
                }
                default:
                    break;
            }
        }
                // 监听事件注册
        const initOnMessage = () => {
            // 页面监听
            tcpInstance.onMessage(util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET), onMessageHandle)
            // 用户校验监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCVALIDATEUSER, (validatePb) => {
                const validateMsg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(validatePb);
                if (validateMsg.getErrorCode() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                    if (validateMsg.getRequestMsgId() === requestMsgId) {
                        switch ($('#opraType').val()) {
                            case 1:{
                                $('#confirmPortDlg').modal('show')
                                $('#confirmPortDlg .modal-title').attr("wa-name","import_tt")
                                $('#confirmPortDlg .modal-body .form-group:nth-child(1)').removeClass("hidden")
                                $('#confirmPortDlg .modal-body .form-group:nth-child(2)').addClass("hidden")
                                break
                            };
                            case 2:{
                                $('#confirmPortDlg').modal('show')
                                $('#confirmPortDlg .modal-title').attr("wa-name","export_tt")
                                $('#confirmPortDlg .modal-body .form-group:nth-child(2)').removeClass("hidden")
                                $('#confirmPortDlg .modal-body .form-group:nth-child(1)').addClass("hidden")
                                break
                            };
                            case 'save':{
                                const data = cloneDeep(formData.transferList)
                                data.forEach(item => {
                                    item.funcOpt = item.funcOpt0 + item.funcOpt1 + item.funcOpt2 + item.funcOpt3
                                    delete item.funcOpt0
                                    delete item.funcOpt1
                                    delete item.funcOpt2
                                    delete item.funcOpt3
                                })
                                tcpConfigPDSetting(
                                PlatformProxyServer_pb.MSG_PDConfigParam.FunctionType.DATA_TRANSFER,
                                PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.MODIFY,
                                data
                                )
                                // 配置管理：FUNCTIONTYPE_CONFIGMANAGE 参数配置：CONFIGMANAGEPACKETTYPE_PARAMSET
                                break
                            };
                            default:
                                break;
                        }
                    }
                } else {
                    ui_util.getErrorTips(validateMsg.getErrorCode(), '验证用户身份')
                }
            })
            // 导入 监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCIMPORTDATA, (pb) => importExportDoneHandler('IMPORT', pb))
            // 导出 监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCEXPORTDATA, (pb) => importExportDoneHandler('EXPORT', pb))
        }
        // 导入导出文件操作1：监听按钮点击打开用户验证
        const importExportStep1Handler = (type) => {
            $('#validateUserDlg').modal('show');
            e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
            $('#validateUserForm').get(0).reset()
            $('#opraType').val(type)
        }
        // 导入导出文件操作2：发送请求  file 在导出时是filePath不是文件内容
        const importExportStep2Handler = (TYPE, file, fileName) => {
            const isImport = TYPE === 'IMPORT';
            const reqData = isImport ? new ProxyServer_pb.MSG_CPImportData() : new ProxyServer_pb.MSG_CPExportData();
            reqData.setType(PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_EVENT);
            isImport && reqData.setData(util.readFromFile(file, false).toString());
            tcpInstance.sendProtoMsg(isImport ? ProxyServer_pb.PSMessageId.PSMESSAGEID_CPIMPORTDATA : ProxyServer_pb.PSMessageId.PSMESSAGEID_CPEXPORTDATA, reqData);
            isImport && $('.parent_content').busyLoad("show", { background: "rgba(0, 0, 0, 0.59)", fontawesome: "fa fa-spinner fa-spin fa-3x fa-fw" });
        }
        // 接收请求后续操作
        const importExportDoneHandler = (type, pb) => {
            const isImport = type === 'IMPORT';
            const msg = ProxyServer_pb[isImport ? 'MSG_PCImportData' : 'MSG_PCExportData'].deserializeBinary(pb);
            isImport && $('.parent_content').busyLoad("hide");
            if (msg.getErrorCode() !== 0) return ui_util.getErrorTips(msg.getErrorCode(), `${isImport ? '导入' : '导出'}事件处理`);
            isImport ? ui_util.showLoading(30) : util.saveToCSV(msg.getData(), `${filePath}/${fileName}`);
            ui_util.showFadeTip(`${isImport ? '导入' : '导出'}事件处理成功`);
            getData();
        }
        let file="",filePath="",fileName="sntp.cfg";
        // 初始化选择文件夹路径&选择文件
        $('#select_folder').ace_file_input({
          no_file:'请选择文件夹',
          btn_choose:'选择',
          btn_change:null,
          droppable:false,
          onchange:null,
          thumbnail:false
        });
        document.querySelector('#select_folder').addEventListener('change', e => {
          for (let entry of e.target.files){
            console.log(entry.name, entry.path);
            filePath=entry.path
          }
        });
        $('#select_file').ace_file_input({
          no_file:'请选择文件',
          btn_choose:'选择',
          btn_change:null,
          droppable:false,
          onchange:null,
          thumbnail:false,
          allowExt: ['cfg'],
        }).on('change', function(){
          let fileDir = $(this).val();
          let suffix = fileDir.substr(fileDir.lastIndexOf("."));
          if ("" == fileDir||".cfg" != suffix) {
            ui_util.showFadeTip('请选择.cfg后缀文件!');
            return false;
          }
          // console.log($(this).data('ace_input_files'));
          file=$(this).data('ace_input_files')[0].path
        });
        // 注册导入导出按钮监听
        $('#port_btn').on('click',function (e) {
          if ($("#port_btn").hasClass("disabled")) return
          if($('#confirmPortDlg .modal-body .form-group:nth-child(1)').hasClass("hidden")){
            if(!$("#select_folder").next("span.ace-file-container").hasClass("selected")){
              ui_util.showFadeTip('请选择文件路径!')
              return;
            }
          }else if($('#confirmPortDlg .modal-body .form-group:nth-child(2)').hasClass("hidden")){
            if(!($("#select_file").next("span.ace-file-container").hasClass("selected"))){
              ui_util.showFadeTip('请选择文件!')
              return;
            }
          }
          $('#confirmPortDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
          e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
          if($('#confirmPortDlg .modal-title').attr("wa-name")=="import_tt"){
            importEvents('IMPORT', file, fileName)
          }else if($('#confirmPortDlg .modal-title').attr("wa-name")=="export_tt"){
            exportEvents('EXPORT', filePath, fileName)
          }
        })
        // 响应事件处理
        const onMessageHandle = (pb) => {
            const msg = PlatformProxyServer_pb.MSG_DPConfigParam.deserializeBinary(pb);
            if (msg.getResult() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                switch (Number(msg.getParam())) {
                    case PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.QUERY: {
                        $('#validateuser_btn').off('click'); // 用完一定要手动移除该事件，否则会积累触发点击事件
                        formatDataFn(msg)
                        break
                    }
                    case PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.MODIFY: {
                        $('#validateuser_btn').off('click'); // 用完一定要手动移除该事件，否则会积累触发点击事件
                        isEdit.value = false
                        ui_util.showFadeTip('操作成功！')
                        getData()
                        break
                    }
                    default:
                        break;
                }
            } else {
                ui_util.getErrorTips(msg.getResult(), '操作')
            }
        }
        const onClickUserValidate = (type) => {
            $('#validateUserDlg').modal('show'); // 打开用户密码校验
            $('#validateUserForm').get(0).reset() // 置空密码校验数据
            $('#opraType').val(type) // 存入保存标识
            // 弹出密码校验框
            $('#validateuser_btn').on('click',function (e) {
            if ($("#validateuser_btn").hasClass("disabled")) {
                return;
            }
            $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
            e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
            let username=user_name
            let psd=$('#validateUserDlg #password').val()
            const user_sign = $("body").attr("user_sign_info");
            // 发送消息
            let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
            sendMsg.setUserName(username)
            sendMsg.setPassword(psd)
            sendMsg.setRequestMsgId(requestMsgId)
            tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg).then();
        })
        }
        const formRules = ref(rule)
        const formRef = ref(null)
        const formData = reactive({
            transferList: [
                // {
                //     funcType: 2,
                //     logType: 1,
                //     funcOpt: 1
                // }
            ]
        })
        // routeList
        // 流量
        const operationFn = (type, row) => {
        }

        return {
            box,
            tableHeight,
            importExportStep1Handler,
            trueOrFalse,
            changeSelect,
            formData,
            getNameByCode,
            getSubNameByCodeAndParentCode,
            // transferList,
            editFn,
            isEdit,
            Enum,
            subEnumMap,
            formRef,
            formRules,
            operationFn
        };
    }
})
// app.use(ElementPlus);
app.use(ElementPlus, {
    locale: ElementPlusLocaleZhCn,
  })
app.mount('#app');
