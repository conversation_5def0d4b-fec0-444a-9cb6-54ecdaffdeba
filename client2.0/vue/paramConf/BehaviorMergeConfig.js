const DCDParamDefine_pb = require('../../script/pb/DCDParamDefine_pb.js')
const PublicDefine_pb = require('../../script/pb/PublicDefine_pb.js');
const BehaviorCodeDefine_pb = require('../../script/pb/BehaviorCodeDefine_pb.js');
const PlatformProxyServer_pb = require('../../script/pb/PlatformProxyServer_pb.js');
const ProxyServer_pb = require('../../script/pb/ProxyServer_pb.js');
const util = require('../../lib/util.js');
const ui_util = require('../../script/view/ui_util.js');
const tcpInstance = require('../../lib/v2/tls_client.js');
const {createApp, ref, onMounted, reactive, onBeforeMount, onBeforeUnmount} = Vue;
const {ipc<PERSON><PERSON><PERSON>, dialog} = require('electron');
const { ElMessage, ElMessageBox } = ElementPlus;
const { cloneDeep } = require('lodash')
// 校验IP或IP段
const validateIp = (rule, value, callback) => {
  const reg = /^((\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.){3}(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])(-((\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.){3}(\d{1,2}|1\d\d|2[0-4]\d|25[0-5]))?$/
  if (value && reg.test(value) === false) {
    return callback(new Error('请确保格式正确'))
  } else {
    return callback()
  }
}
const areaEnums = [
    { code: 1000, name: 'I区' },
    { code: 100, name: 'II区' },
    { code: 10, name: 'III区' },
    { code: 1, name: 'IV区' },
]
const rule = {
    // funcType: [
    // { required: true, message: '请输入目的网段', trigger: 'blur' },
    // { required: true, message: validateIp.Error, validator: validateIp, trigger: ['blur', 'change'] },
    // ],
    // dstNetMask: [
    // { required: true, message: '请输入网口掩码', trigger: 'blur' },
    // { required: true, message: validateIp.Error, validator: validateIp, trigger: ['blur', 'change'] },
    // ],
    // netGateway: [
    // { required: true, message: '请输入网关地址', trigger: 'blur' },
    // { required: true, message: validateIp.Error, validator: validateIp, trigger: ['blur', 'change'] },
    // ],
}
const areaEnumMap = {
    1000: 'I区',
    100: 'II区',
    10: 'III区',
    1: 'IV区'
}
const app = createApp({
    setup() {
        // 前置获取用户及请求信息
        const requestMsgId = util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET)
        const user_name = $("body").attr("user_name_info");
        const props = ref({
            multiple: false,
            emitPath: false,
        })
        const trueOrFalse = ref([
            { code: 0, name: '无' },
            { code: 1, name: '有' }
        ])
        const getNameByValue = (value) => {
            if (value) {
                let label = ''
                Enum.value.forEach(item => {
                    item.children.forEach(itm => {
                        if (itm.value == value) {
                            label = itm.label
                        }
                    })
                })
                return label
            } else {
                return ''
            }
        }
        const Enum = ref(
            [
                {
                    value: 1,
                    label: '硬件类行为枚举',
                    children: [
                    { value: BehaviorCodeDefine_pb.HardwareBehaviorCode.DISK_ADD_DEL, label: '磁盘增删' },
                    { value: BehaviorCodeDefine_pb.HardwareBehaviorCode.DISK_CHANGE, label: '磁盘变更' },
                    { value: BehaviorCodeDefine_pb.HardwareBehaviorCode.NC_ADD_DEL, label: '有线网卡增删' },
                    { value: BehaviorCodeDefine_pb.HardwareBehaviorCode.WIRELESS_ADD_DEL, label: '无线网卡增删' },
                    { value: BehaviorCodeDefine_pb.HardwareBehaviorCode.PERI_INT_ADD_DEL, label: '外设接口增删' },
                    { value: BehaviorCodeDefine_pb.HardwareBehaviorCode.PERI_INT_DISABLE_ENABLE, label: '外设接口禁用启用' },
                    { value: BehaviorCodeDefine_pb.HardwareBehaviorCode.PERI_CONNECT_UNPLUG, label: '外设接入拔出' },
                    { value: BehaviorCodeDefine_pb.HardwareBehaviorCode.NET_INT_CONN_DISCONN, label: '物理网口通断' }
                    ]
                },
                {
                    value: 2,
                    label: '软件类行为枚举',
                    children: [
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.SOFTWARE_CHANGE, label: '系统变更' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.SOFTWARE_CONFIG_SWITCH, label: '系统配置开关' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.USER_GRP_ADD_DEL, label: '用户组增删' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.USER_GRP_NAME_CHANGE, label: '用户组名变更' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.USER_ADD_DEL, label: '用户增删' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.USER_ATTR_CHANGE, label: '用户属性变更' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.USER_LOCK_UNLOCK, label: '用户锁定解锁' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.PART_ADD_DEL, label: '分区增删' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.PART_FORMAT, label: '分区格式化' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.PART_MOUNT_UNM, label: '分区挂载卸载' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.BOOT_TASK_ADD_DEL, label: '启动任务增删' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.BOOT_TASK_CHANGE, label: '启动任务变更' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.DRV_LOAD_UNL, label: '驱动加载卸载' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.SOFTPAC_INSTALL_UNINST, label: '软件安装删除' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.SOFTPAC_UPGRADE, label: '软件升级' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.SERV_INSTALL_UNINST, label: '服务安装卸载' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.SERV_PARAM_MOD, label: '服务参数修改' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.SERV_START_STOP, label: '服务启停' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.FILE_ADD_DEL, label: '文件增删' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.FILE_CHANGE, label: '文件变更' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.CORE_FILE_CHANGE, label: '核心文件内容变更' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.FILE_ACCESS, label: '文件访问' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.EXEC_FILE_CHANGE, label: '可执行文件变更' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.PROC_START_STOP, label: '进程启停' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.PROC_SYS_CALL, label: '进程系统调用' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.SESSION_LOGIN, label: '会话登录' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.SESSION_LOGOUT, label: '会话退出' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.SESSION_OPER, label: '会话操作' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.APP_START_STOP, label: '应用启停' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.APP_OPER, label: '应用业务操作' },
                    { value: BehaviorCodeDefine_pb.SoftwareBehaviorCode.AGENT_SRCPLUG_CHANGE, label: 'Agent插件变更' }
                    ]
                },
                {
                    value: 3,
                    label: '网络类行为枚举',
                    children: [
                    { value: BehaviorCodeDefine_pb.NetworkBehaviorCode.NET_CONFIG_SWITCH, label: '网络配置开关' },
                    { value: BehaviorCodeDefine_pb.NetworkBehaviorCode.NET_INT_ADD_DEL, label: '网络接口增删' },
                    { value: BehaviorCodeDefine_pb.NetworkBehaviorCode.NET_INT_CHANGE, label: '网络接口变更' },
                    { value: BehaviorCodeDefine_pb.NetworkBehaviorCode.NET_INT_START_STOP, label: '网络接口启停' },
                    { value: BehaviorCodeDefine_pb.NetworkBehaviorCode.ROUTE_ADD_DEL, label: '路由增删' },
                    { value: BehaviorCodeDefine_pb.NetworkBehaviorCode.ACC_CTRL_POL_CHANGE, label: '访问控制策略变更' },
                    { value: BehaviorCodeDefine_pb.NetworkBehaviorCode.ADDR_CHANGE, label: '地址学习表变更' },
                    { value: BehaviorCodeDefine_pb.NetworkBehaviorCode.LISTEN_PORT_SWITCH, label: '监听端口开关' }
                    ]
                },
                {
                    value: 4,
                    label: '连接关系类行为枚举',
                    children: [
                    { value: BehaviorCodeDefine_pb.ConnectionBehaviorCode.NET_ACCESS, label: '网络访问' },
                    { value: BehaviorCodeDefine_pb.ConnectionBehaviorCode.NET_FILE_TRANSFER, label: '网络文件传输' }
                    ]
                }
            ])
        // 格式化proto数据
        const formatDataFn = (msg) => {
            const routeList1 = []
            const addInfoList = msg.getAddinfoList();
            addInfoList.forEach((anyItem) => {
                try {
                    let DCD = DCDParamDefine_pb.BehaviorMergeConfig
                    console.log('解构=>getAddinfoList => item', DCD.prototype);

                    const bytes = anyItem.getValue(); // 获取Any中的二进制数据
                    const msg1 = DCD.deserializeBinary(bytes)
                    // const routes = msg1.getRulesList()
                    // routes.forEach(item => {
                        routeList1.push({
                            // id: item.getId(),
                            modelType: msg1.getModeltype(),
                            flag: msg1.getFlag(),
                        })
                    // })
                    console.log('数据调阅 => ', routeList1);

                    formData.mergeList = routeList1

                    // msg1.getDstNetSeg

                } catch (error) {
                    console.log('error', error);
                }
            })
        }
        const getData = () => {
            tcpConfigPDSetting(
                PlatformProxyServer_pb.MSG_PDConfigParam.FunctionType.BEHAVIOR_MERGE,
                PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.QUERY
            )
        }
        // 配置发起请求
        const tcpConfigPDSetting = (pageCode, functionCode, data = null) => {
            const msgPayload = {
                uuid: util.genTaskId(),
                type: String(pageCode),
                param: String(functionCode),
                addInfo: []
            }
            if (data) msgPayload.addInfo = data
            let sendMsg = JSON.stringify(msgPayload)
            console.log('发送=>', sendMsg);

            // 配置管理,参数配置
            tcpInstance.sendJsonMsg(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET, sendMsg).then();
        }
        const onMessageHandler = (pb) => {
            const msg = PlatformProxyServer_pb.MSG_DPConfigParam.deserializeBinary(pb);
            console.log('参数配置结果 =>', '操作类型:', `${ {0: '无', 1: '调阅', 2: '添加', 3: '修改', 4: '删除'}[msg.getParam()] }-${msg.getParam()}`, '操作结果:', msg.getResult());
            if (msg.getResult() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                switch (Number(msg.getParam())) {
                    case PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.QUERY: {
                        formatDataFn(msg)
                        break
                    }
                    case PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.MODIFY: {
                        ElMessage.success('保存成功！')
                        isEdit.value = false
                        getData()
                        break
                    }
                    default:
                    break;
                }
            }
        }
        onMounted(() => {
            resizeHeight()
            window.addEventListener('resize', resizeHeight)
            initOnMessage() // 前置注册响应事件
            getData()
        })
        onBeforeUnmount(() => {
            window.removeEventListener('resize', resizeHeight)
        })
        const box = ref(null)
        const tableHeight = ref(100)
        const resizeHeight = () => {
            console.log('高度', box.value.clientHeight)
            tableHeight.value = box.value.clientHeight * 0.8
        }
        const isEdit = ref(false)
        // 数据传输列表
        const copyTransferList = ref([])

        const editFn = (type) => {
            switch (type) {
                case 'ON': {
                    copyTransferList.value = cloneDeep(formData.mergeList)
                    isEdit.value = true
                    break;
                }
                case 'OFF': {
                    formData.mergeList = cloneDeep(copyTransferList.value)
                    isEdit.value = false
                    break;
                }
                case 'SAVE': {
                    formRef.value.validate(valid => {
                        if (valid) {
                            onClickUserValidate('save')
                        } else {
                            ElMessage.error('请检查填写内容！')
                        }
                    })
                    break;
                }
                default:
                    break;
            }
        }
                        // 监听事件注册
        const initOnMessage = () => {
            // 页面监听
            tcpInstance.onMessage(util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET), onMessageHandle)
            // 用户校验监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCVALIDATEUSER, (validatePb) => {
                console.log('后续逻辑');
                const validateMsg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(validatePb);
                    console.log('用户验证0', validateMsg.getRequestMsgId(), requestMsgId);
                    console.log('用户验证1', validateMsg, ProxyServer_pb.MSG_PCValidateUser.prototype);
                if (validateMsg.getErrorCode() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                    if (validateMsg.getRequestMsgId() === requestMsgId) {
                        switch ($('#opraType').val()) {
                            case 1:{
                                $('#confirmPortDlg').modal('show')
                                $('#confirmPortDlg .modal-title').attr("wa-name","import_tt")
                                $('#confirmPortDlg .modal-body .form-group:nth-child(1)').removeClass("hidden")
                                $('#confirmPortDlg .modal-body .form-group:nth-child(2)').addClass("hidden")
                                break
                            };
                            case 2:{
                                $('#confirmPortDlg').modal('show')
                                $('#confirmPortDlg .modal-title').attr("wa-name","export_tt")
                                $('#confirmPortDlg .modal-body .form-group:nth-child(2)').removeClass("hidden")
                                $('#confirmPortDlg .modal-body .form-group:nth-child(1)').addClass("hidden")
                                break
                            };
                            case 'save':{
                                tcpConfigPDSetting(
                                PlatformProxyServer_pb.MSG_PDConfigParam.FunctionType.BEHAVIOR_MERGE,
                                PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.MODIFY,
                                cloneDeep(formData.mergeList)
                                )
                                // 配置管理：FUNCTIONTYPE_CONFIGMANAGE 参数配置：CONFIGMANAGEPACKETTYPE_PARAMSET
                                break
                            };
                            default:
                                break;
                        }
                    }
                } else {
                    ui_util.getErrorTips(validateMsg.getErrorCode(), '验证用户身份')
                }
            })
            // 导入 监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCIMPORTDATA, (pb) => importExportDoneHandler('IMPORT', pb))
            // 导出 监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCEXPORTDATA, (pb) => importExportDoneHandler('EXPORT', pb))
        }
        // 导入导出文件操作1：监听按钮点击打开用户验证
        const importExportStep1Handler = (type) => {
            $('#validateUserDlg').modal('show');
            e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
            $('#validateUserForm').get(0).reset()
            $('#opraType').val(type)
        }
        // 导入导出文件操作2：发送请求  file 在导出时是filePath不是文件内容
        const importExportStep2Handler = (TYPE, file, fileName) => {
            const isImport = TYPE === 'IMPORT';
            const reqData = isImport ? new ProxyServer_pb.MSG_CPImportData() : new ProxyServer_pb.MSG_CPExportData();
            reqData.setType(PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_EVENT);
            isImport && reqData.setData(util.readFromFile(file, false).toString());
            tcpInstance.sendProtoMsg(isImport ? ProxyServer_pb.PSMessageId.PSMESSAGEID_CPIMPORTDATA : ProxyServer_pb.PSMessageId.PSMESSAGEID_CPEXPORTDATA, reqData);
            isImport && $('.parent_content').busyLoad("show", { background: "rgba(0, 0, 0, 0.59)", fontawesome: "fa fa-spinner fa-spin fa-3x fa-fw" });
        }
        // 接收请求后续操作
        const importExportDoneHandler = (type, pb) => {
            const isImport = type === 'IMPORT';
            const msg = ProxyServer_pb[isImport ? 'MSG_PCImportData' : 'MSG_PCExportData'].deserializeBinary(pb);
            isImport && $('.parent_content').busyLoad("hide");
            if (msg.getErrorCode() !== 0) return ui_util.getErrorTips(msg.getErrorCode(), `${isImport ? '导入' : '导出'}事件处理`);
            isImport ? ui_util.showLoading(30) : util.saveToCSV(msg.getData(), `${filePath}/${fileName}`);
            ui_util.showFadeTip(`${isImport ? '导入' : '导出'}事件处理成功`);
            getData();
        }
        let file="",filePath="",fileName="sntp.cfg";
        // 初始化选择文件夹路径&选择文件
        $('#select_folder').ace_file_input({
          no_file:'请选择文件夹',
          btn_choose:'选择',
          btn_change:null,
          droppable:false,
          onchange:null,
          thumbnail:false
        });
        document.querySelector('#select_folder').addEventListener('change', e => {
          for (let entry of e.target.files){
            console.log(entry.name, entry.path);
            filePath=entry.path
          }
        });
        $('#select_file').ace_file_input({
          no_file:'请选择文件',
          btn_choose:'选择',
          btn_change:null,
          droppable:false,
          onchange:null,
          thumbnail:false,
          allowExt: ['cfg'],
        }).on('change', function(){
          let fileDir = $(this).val();
          let suffix = fileDir.substr(fileDir.lastIndexOf("."));
          if ("" == fileDir||".cfg" != suffix) {
            ui_util.showFadeTip('请选择.cfg后缀文件!');
            return false;
          }
          // console.log($(this).data('ace_input_files'));
          file=$(this).data('ace_input_files')[0].path
        });
        // 注册导入导出按钮监听
        $('#port_btn').on('click',function (e) {
          if ($("#port_btn").hasClass("disabled")) return
          if($('#confirmPortDlg .modal-body .form-group:nth-child(1)').hasClass("hidden")){
            if(!$("#select_folder").next("span.ace-file-container").hasClass("selected")){
              ui_util.showFadeTip('请选择文件路径!')
              return;
            }
          }else if($('#confirmPortDlg .modal-body .form-group:nth-child(2)').hasClass("hidden")){
            if(!($("#select_file").next("span.ace-file-container").hasClass("selected"))){
              ui_util.showFadeTip('请选择文件!')
              return;
            }
          }
          $('#confirmPortDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
          e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
          if($('#confirmPortDlg .modal-title').attr("wa-name")=="import_tt"){
            importEvents('IMPORT', file, fileName)
          }else if($('#confirmPortDlg .modal-title').attr("wa-name")=="export_tt"){
            exportEvents('EXPORT', filePath, fileName)
          }
        })
        // 响应事件处理
        const onMessageHandle = (pb) => {
            const msg = PlatformProxyServer_pb.MSG_DPConfigParam.deserializeBinary(pb);
            console.log('参数配置结果 =>', '操作类型:', `${ {0: '无', 1: '调阅', 2: '添加', 3: '修改', 4: '删除'}[msg.getParam()] }-${msg.getParam()}`, '操作结果:', msg.getResult());
            if (msg.getResult() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                switch (Number(msg.getParam())) {
                    case PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.QUERY: {
                        $('#validateuser_btn').off('click'); // 用完一定要手动移除该事件，否则会积累触发点击事件
                        formatDataFn(msg)
                        break
                    }
                    case PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.MODIFY: {
                        $('#validateuser_btn').off('click'); // 用完一定要手动移除该事件，否则会积累触发点击事件
                        isEdit.value = false
                        ui_util.showFadeTip('操作成功！')
                        getData()
                        break
                    }
                    default:
                        break;
                }
            } else {
                ui_util.getErrorTips(msg.getResult(), '操作')
            }
        }
        const onClickUserValidate = (type) => {
            $('#validateUserDlg').modal('show'); // 打开用户密码校验
            $('#validateUserForm').get(0).reset() // 置空密码校验数据
            $('#opraType').val(type) // 存入保存标识
            // 弹出密码校验框
            $('#validateuser_btn').on('click',function (e) {
            if ($("#validateuser_btn").hasClass("disabled")) {
                return;
            }
            $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
            e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
            let username=user_name
            let psd=$('#validateUserDlg #password').val()
            const user_sign = $("body").attr("user_sign_info");
            console.log('用户验证', user_sign, username, psd);
            // 发送消息
            let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
            sendMsg.setUserName(username)
            sendMsg.setPassword(psd)
            sendMsg.setRequestMsgId(requestMsgId)
            tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg).then();
        })
        }
        const formRules = ref(rule)
        const formRef = ref(null)
        const formData = reactive({
            mergeList: [
                // {
                //     modelType: 1025,
                //     flag: 1
                // }
            ]
        })
        // routeList
        // 流量
        const operationFn = (type, row) => {
        }

        return {
            box,
            tableHeight,
            importExportStep1Handler,
            props,
            getNameByValue,
            formData,
            // getNameByCode,
            // mergeList,
            editFn,
            isEdit,
            Enum,
            formRef,
            formRules,
            operationFn
        };
    }
})
// app.use(ElementPlus);
app.use(ElementPlus, {
    locale: ElementPlusLocaleZhCn,
  })
app.mount('#app');
