const DCDParamDefine_pb = require('../../script/pb/DCDParamDefine_pb.js')
const PublicDefine_pb = require('../../script/pb/PublicDefine_pb.js');
const PlatformProxyServer_pb = require('../../script/pb/PlatformProxyServer_pb.js');
const ProxyServer_pb = require('../../script/pb/ProxyServer_pb.js');
const util = require('../../lib/util.js');
const ui_util = require('../../script/view/ui_util.js');
const tcpInstance = require('../../lib/v2/tls_client.js');
const {createApp, ref, onMounted, reactive, onBeforeMount} = Vue;
const {ipcRenderer, dialog} = require('electron');
const { ElMessage } = ElementPlus;
const { cloneDeep } = require('lodash')
// 校验IP或IP段
const validateIp = (rule, value, callback) => {
  const reg = /^((\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.){3}(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])(-((\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.){3}(\d{1,2}|1\d\d|2[0-4]\d|25[0-5]))?$/
  if (value && reg.test(value) === false) {
    return callback(new Error('请确保格式正确'))
  } else {
    return callback()
  }
}
const validate65535 = (rule, value, callback) => {
  console.log(rule, value,'校验');
  if (!value && value !== 0) return callback()
  if (isNaN(value)) return callback(new Error('请在1 - 65535之间取值'))
  if (value < 1 || value > 65535) {
    return callback(new Error('请在0 - 65535之间取值'))
  } else {
    return callback()
  }
}
const validate0to65535 = (rule, value, callback) => {
    console.log(rule, value, '校验');

    // 允许空值直接通过
    if (value === '' || value === null || value === undefined) {
        return callback();
    }

    let num;

    if (typeof value === 'string') {
        const str = value.trim();
        // 只允许纯数字：禁止小数点、负号、空格、科学计数法等
        if (!/^\d+$/.test(str)) {
            return callback(new Error('只能输入非负整数，不能有小数点'));
        }
        num = Number(str);
    } else if (typeof value === 'number') {
        if (!Number.isFinite(value)) {
            return callback(new Error('请输入有效数字'));
        }
        if (!Number.isInteger(value)) {
            return callback(new Error('不能输入小数'));
        }
        num = value;
    } else {
        return callback(new Error('请输入数字'));
    }

    if (num < 0 || num > 65535) {
        return callback(new Error('请在 0 - 65535 之间取值'));
    }

    return callback();
};


const rule = {
    mainClockMainNet: [
        { required: false, message: '请输入', trigger: 'blur' },
        { required: false, message: validateIp.Error, validator: validateIp, trigger: ['blur', 'change'] },
    ],
    mainClockSubNet: [
        { required: false, message: '请输入', trigger: 'blur' },
        { required: false, message: validateIp.Error, validator: validateIp, trigger: ['blur', 'change'] },
    ],
    subClockMainNet: [
        { required: false, message: '请输入', trigger: 'blur' },
        { required: false, message: validateIp.Error, validator: validateIp, trigger: ['blur', 'change'] },
    ],
    subClockSubNet: [
        { required: false, message: '请输入', trigger: 'blur' },
        { required: false, message: validateIp.Error, validator: validateIp, trigger: ['blur', 'change'] },
    ],
    ports: [
        { required: false, message: '请输入', trigger: 'blur' },
        { required: false, message: validate65535.Error, validator: validate65535, trigger: ['blur', 'change'] },
    ],
    synPeriod: [
        { required: true, message: '请输入', trigger: 'blur' },
        { required: true, message: validate0to65535.Error, validator: validate0to65535, trigger: ['blur', 'change'] },
    ]
}
const app = createApp({
    setup() {
        // 前置获取用户及请求信息
        const requestMsgId = util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET)
        const user_name = $("body").attr("user_name_info");
        // 配置发起请求
        const tcpConfigPDSetting = (pageCode, functionCode, data = null) => {
            const msgPayload = {
                uuid: util.genTaskId(),
                type: String(pageCode),
                param: String(functionCode),
                addInfo: []
            }
            if (data) msgPayload.addInfo = [data]
            let sendMsg = JSON.stringify(msgPayload)
            console.log('发送 => ', sendMsg);

            // 配置管理,参数配置
            tcpInstance.sendJsonMsg(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET, sendMsg).then();
        }
        // 响应
        const getData = () => {
            tcpConfigPDSetting(
                PlatformProxyServer_pb.MSG_PDConfigParam.FunctionType.SNTP,
                PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.QUERY
            )
            // 配置管理：FUNCTIONTYPE_CONFIGMANAGE 参数配置：CONFIGMANAGEPACKETTYPE_PARAMSET
            tcpInstance.onMessage(util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET), onMessageHandle)
        }
        onBeforeMount(() => {
            initOnMessage()
            getData()
        })
        const formatDataFn = (msg) => {
            let form =  {}
            const addInfoList = msg.getAddinfoList();
            addInfoList.forEach((anyItem) => {
                try {
                    let DCD = DCDParamDefine_pb.MSG_SNTPConfig
                    console.log('解构=>getAddinfoList => item', DCD.prototype);

                    const bytes = anyItem.getValue(); // 获取Any中的二进制数据
                    const msg1 = DCD.deserializeBinary(bytes)
                    form = {
                        mainClockMainNet: msg1.getMainclockmainnet(),
                        mainClockSubNet: msg1.getMainclocksubnet(),
                        subClockMainNet: msg1.getSubclockmainnet(),
                        subClockSubNet: msg1.getSubclocksubnet(),
                        ports: msg1.getPorts(), // 默认123 不可修改
                        synPeriod: msg1.getSynperiod(), // 默认0
                        broadcast: msg1.getBroadcast(), // 是否采用广播 广播1，非广播0 默认0
                    }
                    Object.keys(form).forEach(key => {
                        sntpForm[key] = form[key];
                    })
                } catch (error) {
                    console.log('error', error);
                }
            })
        }
        const formRef = ref(null)
        const formRules = ref(rule)
        const addRedStar = (h, { column }) => {
            return [
                h('span', { style: 'color: red' }, '*'),
                h('span', ' ' + column.label)
            ]
        }
        // const copyNetInter = ref([])
        let copySntpList = {}
        // common
        const isEdit = ref(false)
        const cloneForm = (row) => {
            Object.keys(sntpForm).forEach(key => {
                sntpForm[key] = row[key]
            })
        }
        const editFn = (type) => {
            switch (type) {
                case 'ON': {
                    console.log('数据', cloneDeep(sntpForm));

                    copySntpList = cloneDeep(sntpForm)
                    isEdit.value = true
                    break;
                }
                case 'OFF': {
                    sntpForm = cloneForm(copySntpList)
                    isEdit.value = false
                    break;
                }
                case 'SAVE': {
                    formRef.value.validate(valid => {
                        if (valid) {
                            onClickUserValidate('save')
                        } else {
                            ui_util.showFadeTip('请检查填写内容！')
                        }
                    })
                    break;
                }
                default:
                    break;
            }
        }
                // 监听事件注册
        const initOnMessage = () => {
            // 页面监听
            tcpInstance.onMessage(util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET), onMessageHandle)
            // 用户校验监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCVALIDATEUSER, (validatePb) => {
                console.log('后续逻辑');
                const validateMsg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(validatePb);
                    console.log('用户验证0', validateMsg.getRequestMsgId(), requestMsgId);
                    console.log('用户验证1', validateMsg, ProxyServer_pb.MSG_PCValidateUser.prototype);
                if (validateMsg.getErrorCode() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                    if (validateMsg.getRequestMsgId() === requestMsgId) {
                        switch ($('#opraType').val()) {
                            case 1:{
                                $('#confirmPortDlg').modal('show')
                                $('#confirmPortDlg .modal-title').attr("wa-name","import_tt")
                                $('#confirmPortDlg .modal-body .form-group:nth-child(1)').removeClass("hidden")
                                $('#confirmPortDlg .modal-body .form-group:nth-child(2)').addClass("hidden")
                                break
                            };
                            case 2:{
                                $('#confirmPortDlg').modal('show')
                                $('#confirmPortDlg .modal-title').attr("wa-name","export_tt")
                                $('#confirmPortDlg .modal-body .form-group:nth-child(2)').removeClass("hidden")
                                $('#confirmPortDlg .modal-body .form-group:nth-child(1)').addClass("hidden")
                                break
                            };
                            case 'save':{
                                tcpConfigPDSetting(
                                PlatformProxyServer_pb.MSG_PDConfigParam.FunctionType.SNTP,
                                PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.MODIFY,
                                cloneDeep(sntpForm)
                                )
                                // 配置管理：FUNCTIONTYPE_CONFIGMANAGE 参数配置：CONFIGMANAGEPACKETTYPE_PARAMSET
                                break
                            };
                            default:
                                break;
                        }
                    }
                } else {
                    ui_util.getErrorTips(validateMsg.getErrorCode(), '验证用户身份')
                }
            })
            // 导入 监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCIMPORTDATA, (pb) => importExportDoneHandler('IMPORT', pb))
            // 导出 监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCEXPORTDATA, (pb) => importExportDoneHandler('EXPORT', pb))
        }
                // 导入导出文件操作1：监听按钮点击打开用户验证
        const importExportStep1Handler = (type) => {
            $('#validateUserDlg').modal('show');
            e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
            $('#validateUserForm').get(0).reset()
            $('#opraType').val(type)
        }
        // 导入导出文件操作2：发送请求  file 在导出时是filePath不是文件内容
        const importExportStep2Handler = (TYPE, file, fileName) => {
            const isImport = TYPE === 'IMPORT';
            const reqData = isImport ? new ProxyServer_pb.MSG_CPImportData() : new ProxyServer_pb.MSG_CPExportData();
            reqData.setType(PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_EVENT);
            isImport && reqData.setData(util.readFromFile(file, false).toString());
            tcpInstance.sendProtoMsg(isImport ? ProxyServer_pb.PSMessageId.PSMESSAGEID_CPIMPORTDATA : ProxyServer_pb.PSMessageId.PSMESSAGEID_CPEXPORTDATA, reqData);
            isImport && $('.parent_content').busyLoad("show", { background: "rgba(0, 0, 0, 0.59)", fontawesome: "fa fa-spinner fa-spin fa-3x fa-fw" });
        }
        // 接收请求后续操作
        const importExportDoneHandler = (type, pb) => {
            const isImport = type === 'IMPORT';
            const msg = ProxyServer_pb[isImport ? 'MSG_PCImportData' : 'MSG_PCExportData'].deserializeBinary(pb);
            isImport && $('.parent_content').busyLoad("hide");
            if (msg.getErrorCode() !== 0) return ui_util.getErrorTips(msg.getErrorCode(), `${isImport ? '导入' : '导出'}事件处理`);
            isImport ? ui_util.showLoading(30) : util.saveToCSV(msg.getData(), `${filePath}/${fileName}`);
            ui_util.showFadeTip(`${isImport ? '导入' : '导出'}事件处理成功`);
            getData();
        }
        let file="",filePath="",fileName="sntp.cfg";
        // 初始化选择文件夹路径&选择文件
        $('#select_folder').ace_file_input({
          no_file:'请选择文件夹',
          btn_choose:'选择',
          btn_change:null,
          droppable:false,
          onchange:null,
          thumbnail:false
        });
        document.querySelector('#select_folder').addEventListener('change', e => {
          for (let entry of e.target.files){
            console.log(entry.name, entry.path);
            filePath=entry.path
          }
        });
        $('#select_file').ace_file_input({
          no_file:'请选择文件',
          btn_choose:'选择',
          btn_change:null,
          droppable:false,
          onchange:null,
          thumbnail:false,
          allowExt: ['cfg'],
        }).on('change', function(){
          let fileDir = $(this).val();
          let suffix = fileDir.substr(fileDir.lastIndexOf("."));
          if ("" == fileDir||".cfg" != suffix) {
            ui_util.showFadeTip('请选择.cfg后缀文件!');
            return false;
          }
          // console.log($(this).data('ace_input_files'));
          file=$(this).data('ace_input_files')[0].path
        });
        // 注册导入导出按钮监听
        $('#port_btn').on('click',function (e) {
          if ($("#port_btn").hasClass("disabled")) return
          if($('#confirmPortDlg .modal-body .form-group:nth-child(1)').hasClass("hidden")){
            if(!$("#select_folder").next("span.ace-file-container").hasClass("selected")){
              ui_util.showFadeTip('请选择文件路径!')
              return;
            }
          }else if($('#confirmPortDlg .modal-body .form-group:nth-child(2)').hasClass("hidden")){
            if(!($("#select_file").next("span.ace-file-container").hasClass("selected"))){
              ui_util.showFadeTip('请选择文件!')
              return;
            }
          }
          $('#confirmPortDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
          e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
          if($('#confirmPortDlg .modal-title').attr("wa-name")=="import_tt"){
            importEvents('IMPORT', file, fileName)
          }else if($('#confirmPortDlg .modal-title').attr("wa-name")=="export_tt"){
            exportEvents('EXPORT', filePath, fileName)
          }
        })
        // 响应事件处理
        const onMessageHandle = (pb) => {
            const msg = PlatformProxyServer_pb.MSG_DPConfigParam.deserializeBinary(pb);
            console.log('参数配置结果 =>', '操作类型:', `${ {0: '无', 1: '调阅', 2: '添加', 3: '修改', 4: '删除'}[msg.getParam()] }-${msg.getParam()}`, '操作结果:', msg.getResult());
            if (msg.getResult() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                switch (Number(msg.getParam())) {
                    case PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.QUERY: {
                        $('#validateuser_btn').off('click'); // 用完一定要手动移除该事件，否则会积累触发点击事件
                        formatDataFn(msg)
                        break
                    }
                    case PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.MODIFY: {
                        $('#validateuser_btn').off('click'); // 用完一定要手动移除该事件，否则会积累触发点击事件
                        isEdit.value = false
                        ui_util.showFadeTip('操作成功！')
                        getData()
                        break
                    }
                    default:
                        break;
                }
            } else {
                ui_util.getErrorTips(msg.getResult(), '操作')
            }
        }
        const onClickUserValidate = (type) => {
            $('#validateUserDlg').modal({backdrop: 'static'}).modal('show'); // 打开用户密码校验
            $('#validateUserForm').get(0).reset() // 置空密码校验数据
            $('#opraType').val(type) // 存入保存标识
            // 弹出密码校验框
            $('#validateuser_btn').on('click',function (e) {
            if ($("#validateuser_btn").hasClass("disabled")) {
                return;
            }
            // 监听弹窗关闭事件
            $('#validateUserDlg').on('hidden.bs.modal', function (e) {
            console.log('弹窗已关闭，触发后续逻辑');
            // 可以在这里执行你的回调逻辑
            });
            $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
            e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
            let username=user_name
            let psd=$('#validateUserDlg #password').val()
            const user_sign = $("body").attr("user_sign_info");
            console.log('用户验证', user_sign, username, psd);
            // 发送消息
            let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
            sendMsg.setUserName(username)
            sendMsg.setPassword(psd)
            sendMsg.setRequestMsgId(requestMsgId)
            tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg).then();
            })
        }
        // netInter
        const broadcastEnum = ref([
            {code: 0, name: '非广播'},
            {code: 1, name: '广播'}
        ])
        let sntpForm = reactive({
                mainClockMainNet: '',
                mainClockSubNet: '',
                subClockMainNet: '',
                subClockSubNet: '',
                ports: 123, // 默认123 不可修改
                synPeriod: 0, // 默认0
                broadcast: 0, // 是否采用广播 广播1，非广播0 默认0
        })
        const netInterItem = {
            interface: '',
            netIp: '',
            netmask: '',
            netMtu: 0
        }
        // flowInter

        return {
            importExportStep1Handler,
            sntpForm,
            broadcastEnum,
            formRef,
            addRedStar,
            formRules,
            isEdit,
            editFn,
        };
    }
})
// app.use(ElementPlus);
app.use(ElementPlus, {
    locale: ElementPlusLocaleZhCn,
  })
app.mount('#app');
