const DCDParamDefine_pb = require('../../script/pb/DCDParamDefine_pb.js')
const PublicDefine_pb = require('../../script/pb/PublicDefine_pb.js');
const PlatformProxyServer_pb = require('../../script/pb/PlatformProxyServer_pb.js');
const ProxyServer_pb = require('../../script/pb/ProxyServer_pb.js');
const util = require('../../lib/util.js');
const ui_util = require('../../script/view/ui_util.js');
const tcpInstance = require('../../lib/v2/tls_client.js');
const {createApp, ref, onMounted, reactive, onBeforeMount } = Vue;
const {ipcRenderer, dialog} = require('electron');
const { cloneDeep } = require('lodash')
const { ElMessage, ElMessageBox } = ElementPlus;

const validate65535 = (rule, value, callback) => {
  if (!value && value !== 0) return callback()
  if (isNaN(value)) return callback(new Error('请在1 - 65535之间取值'))
    let num;
    if (typeof value === 'string') {
        const str = value.trim();
        // 只允许纯数字：禁止小数点、负号、空格、科学计数法等
        if (!/^\d+$/.test(str)) {
            return callback(new Error('只能输入非负整数，不能有小数点'));
        }
        num = Number(str);
    } else if (typeof value === 'number') {
        if (!Number.isFinite(value)) {
            return callback(new Error('请输入有效数字'));
        }
        if (!Number.isInteger(value)) {
            return callback(new Error('不能输入小数'));
        }
        num = value;
    } else {
        return callback(new Error('请输入数字'));
    }
  if (value < 1 || value > 65535) {
    return callback(new Error('请在1 - 65535之间取值'))
  } else {
    return callback()
  }
}
// 校验IP或IP段
const validateIp = (rule, value, callback) => {
  const reg = /^((\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.){3}(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])(-((\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.){3}(\d{1,2}|1\d\d|2[0-4]\d|25[0-5]))?$/
  if (value && reg.test(value) === false) {
    return callback(new Error('请确保IP格式正确'))
  } else {
    return callback()
  }
}
const validate15 = (rule, value, callback) => {
  if ((!value && value !== 0) || isNaN(value) || value < 0 || value > 15) {
    return callback(new Error('请输入0-15正整数'))
  } else {
      const reg = /^(0|[1-9]\d*)$/
      if (!reg.test(value)) {
          return callback(new Error('请输入0-16正整数'))
      }
    return callback()
  }
}
const validate16 = (rule, value, callback) => {
    if ((!value && value !== 0) || isNaN(value) || value < 0 || value > 16) {
        return callback(new Error('请输入0-16正整数'))
    } else {
        const reg = /^(0|[1-9]\d*)$/
        if (!reg.test(value)) {
            return callback(new Error('请输入0-16正整数'))
        }
        return callback()
    }
}
const rule = {
    servPort: [
        { required: false, message: '请输入范围1-65535端口号', trigger: 'blur' },
        { required: false, message: validate65535.Error, validator: validate65535, trigger: ['blur', 'change'] },
    ],
    selfPort: [
        { required: false, message: '请输入范围1-65535端口号', trigger: 'blur' },
        { required: false, message: validate65535.Error, validator: validate65535, trigger: ['blur', 'change'] },
    ],
    SYSLOGPort: [
        { required: false, message: '请输入范围1-65535端口号', trigger: 'blur' },
        { required: false, message: validate65535.Error, validator: validate65535, trigger: ['blur', 'change'] },
    ],
    TRAPPort: [
        { required: false, message: '请输入范围1-65535端口号', trigger: 'blur' },
        { required: false, message: validate65535.Error, validator: validate65535, trigger: ['blur', 'change'] },
    ]
}
const rule1 = {
    platIP: [
        { required: true, message: '请输入平台IP', trigger: 'blur' },
        { required: true, message: validateIp.Error, validator: validateIp, trigger: ['blur', 'change'] },
    ],
    groupId: [
        { required: true, message: '请输入0-16正整数', trigger: 'blur' },
        { required: true, message: validate16.Error, validator: validate16, trigger: ['blur', 'change'] },
    ],
    groupPriority: [
        { required: true, message: '请输入0-15正整数', trigger: 'blur' },
        { required: true, message: validate15.Error, validator: validate15, trigger: ['blur', 'change'] },
    ]
}
const app = createApp({
    setup() {
        // 前置获取用户及请求信息
        const requestMsgId = util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET)
        const user_name = $("body").attr("user_name_info");
        const authEnum = [
            { code: DCDParamDefine_pb.MSG_CommunicationConfig.PermissionLevel.EVENT_UPLOAD, name: '上传事件信息' },
            { code: DCDParamDefine_pb.MSG_CommunicationConfig.PermissionLevel.DCD_PARAM_READ, name: '读取装置信息' },
            { code: DCDParamDefine_pb.MSG_CommunicationConfig.PermissionLevel.DCD_PARAM_WRITE, name: '装置参数设置' },
            { code: DCDParamDefine_pb.MSG_CommunicationConfig.PermissionLevel.OBJECT_CONTROL, name: '监测对象控制' },
            { code: DCDParamDefine_pb.MSG_CommunicationConfig.PermissionLevel.REGISTER, name: '有权限对监测装置进行注册' }
        ]
        const getAuthNameByCode = (code) => {
            console.log('code', code)
            if (!code) {
                console.log('111')
                return '--'
            }
            const data = authEnum.find(item => item.code == code)
            console.log('匹配', data, authEnum)
            if (!data) return code
            return data.name
        }
        // 配置发起请求
        const tcpConfigPDSetting = (pageCode, functionCode, data = null) => {
            const msgPayload = {
                uuid: util.genTaskId(),
                type: String(pageCode),
                param: String(functionCode),
                addInfo: []
                }
            if (data) msgPayload.addInfo = [data]
            let sendMsg = JSON.stringify(msgPayload)
            console.log('send =>', sendMsg);

            // 配置管理,参数配置
            tcpInstance.sendJsonMsg(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET, sendMsg).then();
        }
        const getData = () => {
            tcpConfigPDSetting(
                PlatformProxyServer_pb.MSG_PDConfigParam.FunctionType.COMMUNICATION,
                PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.QUERY
            )
        }
        onBeforeMount(() => {
            initOnMessage()
            getData()
        })
                        // 监听事件注册
        const initOnMessage = () => {
            // 页面监听
            tcpInstance.onMessage(util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PARAMSET), onMessageHandle)
            // 用户校验监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCVALIDATEUSER, (validatePb) => {
                const validateMsg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(validatePb);
                if (validateMsg.getErrorCode() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                    if (validateMsg.getRequestMsgId() === requestMsgId) {
                        switch ($('#opraType').val()) {
                            case 1:{
                                $('#confirmPortDlg').modal('show')
                                $('#confirmPortDlg .modal-title').attr("wa-name","import_tt")
                                $('#confirmPortDlg .modal-body .form-group:nth-child(1)').removeClass("hidden")
                                $('#confirmPortDlg .modal-body .form-group:nth-child(2)').addClass("hidden")
                                break
                            };
                            case 2:{
                                $('#confirmPortDlg').modal('show')
                                $('#confirmPortDlg .modal-title').attr("wa-name","export_tt")
                                $('#confirmPortDlg .modal-body .form-group:nth-child(2)').removeClass("hidden")
                                $('#confirmPortDlg .modal-body .form-group:nth-child(1)').addClass("hidden")
                                break
                            };
                            case 'save':{
                                const data = Object.assign(cloneDeep(commParamForm), { platInfo: cloneDeep(platInfoList.value) })
                                tcpConfigPDSetting(
                                PlatformProxyServer_pb.MSG_PDConfigParam.FunctionType.COMMUNICATION,
                                PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.MODIFY,
                                data
                                )
                                break
                            };
                            default:
                                break;
                        }
                    }
                } else {
                    ui_util.getErrorTips(validateMsg.getErrorCode(), '验证用户身份')
                }
            })
                        // 导入 监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCIMPORTDATA, (pb) => importExportDoneHandler('IMPORT', pb))
            // 导出 监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCEXPORTDATA, (pb) => importExportDoneHandler('EXPORT', pb))
        }
                        // 导入导出文件操作1：监听按钮点击打开用户验证
        const importExportStep1Handler = (type) => {
            $('#validateUserDlg').modal('show');
            e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
            $('#validateUserForm').get(0).reset()
            $('#opraType').val(type)
        }
        // 导入导出文件操作2：发送请求  file 在导出时是filePath不是文件内容
        const importExportStep2Handler = (TYPE, file, fileName) => {
            const isImport = TYPE === 'IMPORT';
            const reqData = isImport ? new ProxyServer_pb.MSG_CPImportData() : new ProxyServer_pb.MSG_CPExportData();
            reqData.setType(PublicDefine_pb.ConfigPacketSubType.CONFIG_PACKET_SUB_TYPE_EVENT);
            isImport && reqData.setData(util.readFromFile(file, false).toString());
            tcpInstance.sendProtoMsg(isImport ? ProxyServer_pb.PSMessageId.PSMESSAGEID_CPIMPORTDATA : ProxyServer_pb.PSMessageId.PSMESSAGEID_CPEXPORTDATA, reqData);
            isImport && $('.parent_content').busyLoad("show", { background: "rgba(0, 0, 0, 0.59)", fontawesome: "fa fa-spinner fa-spin fa-3x fa-fw" });
        }
        // 接收请求后续操作
        const importExportDoneHandler = (type, pb) => {
            const isImport = type === 'IMPORT';
            const msg = ProxyServer_pb[isImport ? 'MSG_PCImportData' : 'MSG_PCExportData'].deserializeBinary(pb);
            isImport && $('.parent_content').busyLoad("hide");
            if (msg.getErrorCode() !== 0) return ui_util.getErrorTips(msg.getErrorCode(), `${isImport ? '导入' : '导出'}事件处理`);
            isImport ? ui_util.showLoading(30) : util.saveToCSV(msg.getData(), `${filePath}/${fileName}`);
            ui_util.showFadeTip(`${isImport ? '导入' : '导出'}事件处理成功`);
            getData();
        }
        let file="",filePath="",fileName="sntp.cfg";
        // 初始化选择文件夹路径&选择文件
        $('#select_folder').ace_file_input({
          no_file:'请选择文件夹',
          btn_choose:'选择',
          btn_change:null,
          droppable:false,
          onchange:null,
          thumbnail:false
        });
        document.querySelector('#select_folder').addEventListener('change', e => {
          for (let entry of e.target.files){
            console.log(entry.name, entry.path);
            filePath=entry.path
          }
        });
        $('#select_file').ace_file_input({
          no_file:'请选择文件',
          btn_choose:'选择',
          btn_change:null,
          droppable:false,
          onchange:null,
          thumbnail:false,
          allowExt: ['cfg'],
        }).on('change', function(){
          let fileDir = $(this).val();
          let suffix = fileDir.substr(fileDir.lastIndexOf("."));
          if ("" == fileDir||".cfg" != suffix) {
            ui_util.showFadeTip('请选择.cfg后缀文件!');
            return false;
          }
          // console.log($(this).data('ace_input_files'));
          file=$(this).data('ace_input_files')[0].path
        });
        // 注册导入导出按钮监听
        $('#port_btn').on('click',function (e) {
          if ($("#port_btn").hasClass("disabled")) return
          if($('#confirmPortDlg .modal-body .form-group:nth-child(1)').hasClass("hidden")){
            if(!$("#select_folder").next("span.ace-file-container").hasClass("selected")){
              ui_util.showFadeTip('请选择文件路径!')
              return;
            }
          }else if($('#confirmPortDlg .modal-body .form-group:nth-child(2)').hasClass("hidden")){
            if(!($("#select_file").next("span.ace-file-container").hasClass("selected"))){
              ui_util.showFadeTip('请选择文件!')
              return;
            }
          }
          $('#confirmPortDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
          e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
          if($('#confirmPortDlg .modal-title').attr("wa-name")=="import_tt"){
            importEvents('IMPORT', file, fileName)
          }else if($('#confirmPortDlg .modal-title').attr("wa-name")=="export_tt"){
            exportEvents('EXPORT', filePath, fileName)
          }
        })
                // 响应事件处理
        const onMessageHandle = (pb) => {
            const msg = PlatformProxyServer_pb.MSG_DPConfigParam.deserializeBinary(pb);
            if (msg.getResult() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                switch (Number(msg.getParam())) {
                    case PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.QUERY: {
                        $('#validateuser_btn').off('click'); // 用完一定要手动移除该事件，否则会积累触发点击事件
                        formatDataFn(msg)
                        break
                    }
                    case PlatformProxyServer_pb.MSG_PDConfigParam.OperationType.MODIFY: {
                        console.log('修改')
                        $('#validateuser_btn').off('click'); // 用完一定要手动移除该事件，否则会积累触发点击事件
                        ui_util.showFadeTip('操作成功！')
                        getData()
                        break
                    }
                    default:
                        break;
                }
            } else {
                ui_util.getErrorTips(msg.getResult(), '操作执行')
            }
        }
        const onClickUserValidate = (type) => {
            $('#validateUserDlg').modal('show'); // 打开用户密码校验
            $('#validateUserForm').get(0).reset() // 置空密码校验数据
            $('#opraType').val(type) // 存入保存标识
            // 弹出密码校验框
            $('#validateuser_btn').on('click',function (e) {
            if ($("#validateuser_btn").hasClass("disabled")) {
                return;
            }
            $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
            e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
            let username=user_name
            let psd=$('#validateUserDlg #password').val()
            const user_sign = $("body").attr("user_sign_info");
            // 发送消息
            let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
            sendMsg.setUserName(username)
            sendMsg.setPassword(psd)
            sendMsg.setRequestMsgId(requestMsgId)
                console.log('send =>', sendMsg)
            tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg).then();
        })
        }
        const formatDataFn = (msg) => {
            let form =  {}
            const addInfoList = msg.getAddinfoList();
            addInfoList.forEach((anyItem) => {
                try {
                    let DCD = DCDParamDefine_pb.MSG_CommunicationConfig
                    const bytes = anyItem.getValue(); // 获取Any中的二进制数据
                    const msg1 = DCD.deserializeBinary(bytes)
                    form = {
                        servPort: msg1.getServport(),
                        selfPort: msg1.getSelfport(),
                        SYSLOGPort: msg1.getSyslogport(),
                        TRAPPort: msg1.getTrapport(),
                        platInfo: []
                    }
                    const platInfo_pb = msg1.getPlatinfoList()
                    platInfo_pb.forEach(itm => {
                        form.platInfo.push({
                            platIP: itm.getPlatip(),
                            permission: itm.getPermission(),
                            groupId: itm.getGroupid(),
                            groupPriority: itm.getGrouppriority(),
                        })
                    })
                    platInfoList.value = form.platInfo
                    Object.keys(form).forEach(key => {
                        commParamForm[key] = form[key];
                    })
                } catch (error) {
                    console.log('error', error);
                }
            })
        }
        const formRef = ref(null)
        const formRules = ref(rule)
        const nextStepFormRules = ref(rule1)
        const nextFormRef = ref(null)
        const addRedStar = (h, { column }) => {
            return [
                h('span', { style: 'color: red' }, '*'),
                h('span', ' ' + column.label)
            ]
        }
        // const copyNetInter = ref([])
        const copySelf_param = ref([])

        const editFn = (type) => {
            switch (type) {
                case 'PRE': {
                    activeTabs.value = 1
                    break;
                }
                case 'NEXT': {
                    formRef.value.validate(valid => {
                        if (valid) {
                            activeTabs.value = 2
                        } else {
                            ui_util.showFadeTip('请检查填写内容！')
                        }
                    })
                    break;
                }
                case 'SAVE': {
                    onClickUserValidate('save')
                    break;
                }
                default:
                    break;
            }
        }
        // netInter
        const broadcastEnum = ref([
            {code: 0, name: '非广播'},
            {code: 1, name: '广播'}
        ])
        const commParamForm = reactive({
                servPort: null, // 服务器、工作站数据采集的服务端口
                selfPort: null, // 装置与平台交互端口
                SYSLOGPort: null, // 安全防护设备数据采集的服务端口
                TRAPPort: null, // 网络设备SNMP TRAP端口
            })
        let rowData = reactive({
            platIP: null,
            permission: null,
            groupId: null,
            groupPriority: null,
        })
        const cloneFn = (row) => {
            Object.keys(row).forEach(key => {
                rowData[key] = row[key]
            })
        }
        const updateDialogIndex = ref(0)
        const updateDialogVisible = ref(false)
        const updateDialogType = ref('ADD')
        const operationFn = (type, row, index) => {
            switch (type) {
                case 'ADD':
                    cloneFn({
                        platIP: null,
                        permission: null,
                        groupId: null,
                        groupPriority: null,
                    })
                    updateDialogVisible.value = true
                    updateDialogType.value = 'ADD'
                    break;
                case 'CLOSE': {
                    updateDialogVisible.value = false
                    break
                }
                case 'DELETE':
                    ElMessageBox.confirm('确定要删除吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        platInfoList.value.splice(index, 1)
                    }).catch(() => {
                        ElMessage({
                            type: 'info',
                            message: '已取消删除'
                        })
                    })
                    break;
                case 'EDIT':
                    updateDialogIndex.value = index
                    cloneFn(row)
                    updateDialogVisible.value = true
                    updateDialogType.value = 'EDIT'
                    break
                case 'SAVE': {
                    nextFormRef.value.validate(valid => {
                        if (valid) {
                            let ipFlag = true
                            let groupPriorityFlag = true
                            platInfoList.value.forEach((item, idx) => {
                                if (updateDialogType.value === 'ADD') {
                                    if (item.groupId == rowData.groupId && item.groupPriority == rowData.groupPriority) {
                                        groupPriorityFlag = false
                                    }
                                    if (rowData.platIP == item.platIP) ipFlag = false
                                } else {
                                    if (idx !== updateDialogIndex.value) {
                                        console.log('item.platIP', item.platIP, rowData.platIP)
                                        if (rowData.platIP == item.platIP) {
                                            ipFlag = false
                                        }
                                        if (item.groupId == rowData.groupId && item.groupPriority == rowData.groupPriority) {
                                            groupPriorityFlag = false
                                        }
                                    }
                                }
                            })
                            if(!ipFlag) return ui_util.showFadeTip('IP已存在')
                            if(!groupPriorityFlag) return ui_util.showFadeTip('同组内不允许等级相同！')
                            if (updateDialogType.value === 'ADD') {
                                platInfoList.value.push(cloneDeep(rowData))
                            } else {
                                platInfoList.value[updateDialogIndex.value] = cloneDeep(rowData)
                            }
                            updateDialogVisible.value = false
                        }
                    })
                    break
                }
                default:
                    break;
            }
        }
        // 平台参数表格
        const platInfoList = ref([])
        // platIP   平台的IP地址
        // permission 平台管理权限
        // groupId 通信分组号
        // groupPriority 通信组内链路的优先级

        // flowInter
        const activeTabs = ref(1)
        return {
            importExportStep1Handler,

            authEnum,
            activeTabs,
            commParamForm,
            broadcastEnum,
            formRef,
            addRedStar,
            formRules,
            nextStepFormRules,
            platInfoList,
            nextFormRef,
            copySelf_param,

            getAuthNameByCode,
            updateDialogVisible,
            updateDialogType,
            rowData,
            operationFn,
            editFn,
        };
    }
})
// app.use(ElementPlus);
app.use(ElementPlus, {
    locale: ElementPlusLocaleZhCn,
  })
app.mount('#app');
