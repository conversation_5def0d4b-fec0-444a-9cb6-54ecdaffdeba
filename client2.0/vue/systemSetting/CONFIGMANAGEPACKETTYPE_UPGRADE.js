const DCDParamDefine_pb = require('../../script/pb/DCDParamDefine_pb.js')
const PublicDefine_pb = require('../../script/pb/PublicDefine_pb.js');
const BehaviorCodeDefine_pb = require('../../script/pb/BehaviorCodeDefine_pb.js');
// 平台
const PlatformProxyServer_pb = require('../../script/pb/PlatformProxyServer_pb.js');
const ProxyServer_pb = require('../../script/pb/ProxyServer_pb.js');
const util = require('../../lib/util.js');
const ui_util = require('../../script/view/ui_util.js');
const tcpInstance = require('../../lib/v2/tls_client.js');
const {createApp, ref, onMounted, reactive, onBeforeMount, onBeforeUnmount} = Vue;
const {ipc<PERSON><PERSON>er} = require('electron');
const { ElMessage, ElMessageBox } = ElementPlus;
const { cloneDeep } = require('lodash')

const fs = require('fs')
const path = require("path");
const crypto = require('crypto')
function formatProgress(done, total, step = 5, isFormat = true) {
    let p = Math.round((done / total) * 100 / step) * step;
    return isFormat ? `上传中，进度${p}%` : `${p}%`;
}
// 按偏移量和长度读取文件一段
function readChunk(filePath, offset, length, fileSize) {
    if (offset + length > fileSize) length = fileSize - offset
    const fd = fs.openSync(filePath, "r"); // 打开文件
    const buffer = Buffer.alloc(length);   // 分配缓冲区
    fs.readSync(fd, buffer, 0, length, offset); // 读取
    fs.closeSync(fd); // 关闭文件
    return buffer;
}

// 根据标识位获取一字节buffer
function getBuffer8(num) {
    let buffer1 = Buffer.alloc(1)
    buffer1.writeUInt8(num)
    return buffer1
}
// 读取文件大小
function getFileSize(filePath) {
    const stats = fs.statSync(filePath);
    return stats.size;
}

// 流式获取MD5
function getFileMD5(filePath) {
    return new Promise((resolve, reject) => {
        const hash = crypto.createHash("md5");
        const stream = fs.createReadStream(filePath);
        stream.on("data", (chunk) => hash.update(chunk)); // 边读边计算
        stream.on("end", () => resolve(hash.digest("hex"))); // 完成
        stream.on("error", reject);
    });
}
// 校验路径下文件是否存在
function checkFileAccess(filePath) {
    try {
        // 先获取文件所在的目录
        const dirPath = path.dirname(filePath);
        // 判断目录是否存在
        if (!fs.existsSync(dirPath)) {
            return { status: false, reason: '上级文件夹不存在或无法访问' };
        }
        // 判断文件是否存在
        if (!fs.existsSync(filePath)) {
            return { status: false, reason: '文件不存在' };
        }
        return { status: true, reason: '文件可访问' };
    } catch (err) {
        return { status: false, reason: '访问时发生错误: ' + err.message };
    }
}
const app = createApp({
    setup() {
        const subType = ref('1') // 任务子类型
        const subTypeEnum = ref({
            '1': '监测装置软件',
            '2': '监测装置插件',
            '3': 'AGENT 升级',
            '4': 'AGENT 插件升级',
        })
        // 待办列表分页相关
        const unDonePageNum = ref(1)
        const unDonePageSize = ref(100)
        const unDoneTotalCount = ref(0)
        const unDoneCurrentChangeFn = () => {}
        const user_sign = $("body").attr("user_sign_info");
        const user_name = $("body").attr("user_name_info");
        const box = ref(null)
        const queryModelHeight = ref(0)
        const queryModelTableHeight = ref(0)
        const updateTableHeight = ref(0)
        // 工具函数，格式化文件大小
        const formatFileSize = (size) => {
            if (size < 1024) return size + ' B'
            if (size < 1024 * 1024) return (size / 1024).toFixed(2) + ' KB'
            return (size / (1024 * 1024)).toFixed(2) + ' MB'
        }
        const unDoneTableList = ref([])
        const uploadRequestMsgId = util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_UPGRADE)

        onMounted(() => {
            resizeHeight() // 重置高度
            window.addEventListener('resize', resizeHeight)
            initOnMessage() // 注册响应事件监听
            initValidateEvent() // 用户校验监听
            getAssetListFn() // 获取所有资产
            getSoftPacketListFn() // 获取升级包列表
        })
        const initValidateEvent = () => {
            $('#validateuser_btn').on('click',function (e) {
                if ($("#validateuser_btn").hasClass("disabled")) return;
                $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
                e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
                let username=user_name
                let psd=$('#validateUserDlg #password').val()
                // 发送消息
                let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
                sendMsg.setUserName(username)
                sendMsg.setPassword(psd)
                sendMsg.setRequestMsgId(uploadRequestMsgId)
                tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg).then();
            })
        }
        const resizeHeight = () => {
            queryModelHeight.value = box.value.clientHeight ? box.value.clientHeight : 100
            queryModelTableHeight.value = queryModelHeight.value * 0.8
            updateTableHeight.value = queryModelHeight.value * 0.6
        }
        onBeforeUnmount(() => {
            window.removeEventListener('resize', resizeHeight)
        })
        // 未上传的列表
        const unDoneVisible = ref(false)
        const validateToNext = (Type, row) => {
            $('#validateUserDlg').modal('show'); // 打开用户密码校验
            $('#validateUserForm').get(0).reset() // 置空密码校验数据
            $('#opraType').val(Type)
            if (row) {
                updateSoftPacketId.value = row.id
                console.log('用户前置校验 =>', 'id => ', updateSoftPacketId.value, row)
            }
        }
        const initOnMessage = () => {
            // 加载待办列表
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCLISTRESUMEFILETASK, (pb) => {
                const msg = ProxyServer_pb.MSG_PCListResumeFileTask.deserializeBinary(pb);
                if (util.getProtobufFieldValue(msg, 'error_code') == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                    try {
                        console.log('总条目', util.getProtobufFieldValue(msg, 'total_count'), msg)
                        const table = []
                        unDoneTotalCount.value = util.getProtobufFieldValue(msg, 'total_count') // 总条目
                        if (unDoneTotalCount.value > 0) {
                            const tasksBufferArr = util.getProtobufFieldValue(msg, 'tasks', true)
                            Buffer.isBuffer(tasksBufferArr)
                            console.log('获取到buf', tasksBufferArr)
                            tasksBufferArr.forEach(task => {
                                const tableItem = util.getArrayDataByBufDataAndTemp(task, ['uuid', 'sub_type', 'plugin_id', 'file_name', 'file_path', 'total_size', 'transferred_size', 'md5', 'last_transfer_time'])
                                console.log('单条待办数据', util.getArrayDataByBufDataAndTemp(task, ['uuid', 'sub_type', 'plugin_id', 'file_name', 'file_path', 'total_size', 'transferred_size', 'md5', 'last_transfer_time']))
                                tableItem['subTypeName'] = subTypeEnum.value[String(tableItem.sub_type)] // 任务子类型中文
                                tableItem['totalSizeTransfer'] = formatFileSize(tableItem['total_size'])
                                tableItem['process'] = formatProgress(tableItem['transferred_size'], tableItem['total_size'], 5, false)
                                tableItem['process'] === '100%' ? tableItem['process'] = '99%' : '' // 100%的是终止接口没调用
                                tableItem['lastTransferTimeName'] = String(moment(tableItem['last_transfer_time']).format('YYYY-MM-DD HH:mm:ss'))
                                table.push(tableItem)
                            })
                        }
                        unDoneTableList.value = table
                    } catch (e) {
                        unDoneTotalCount.value = 0
                        unDoneTableList.value = []
                        console.log('解构 err => undone_list', e)
                    }
                } else {
                    console.log('有报错', util.getProtobufFieldValue(msg, 'error_message'))
                    ui_util.getErrorTips(msg.getErrorCode(), '获取待办列表')
                }

            })
            // 删除待办列表
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCDELETERESUMEFILETASK, (pb) => {
                const msg = ProxyServer_pb.MSG_PCDeleteResumeFileTask.deserializeBinary(pb);
                if (util.getProtobufFieldValue(msg, 'error_code') == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                    ui_util.showFadeTip('删除成功！')
                    UnDoneTcpSendFn('SEARCH')
                } else {
                    console.log('有报错', util.getProtobufFieldValue(msg, 'error_message'))
                    ui_util.getErrorTips(msg.getErrorCode(), '删除')
                }
            })
            // 添加待办列表
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCADDRESUMEFILETASK, (pb) => {
                const msg = ProxyServer_pb.MSG_PCAddResumeFileTask.deserializeBinary(pb);
                if (util.getProtobufFieldValue(msg, 'error_code') == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                    // 如果分片都已经传完
                    if (fileBufferUploadOffset === fileSize.value) return stopFn()
                    uploadingFn() // 分片未传完
                } else {
                    console.log('有报错', util.getProtobufFieldValue(msg, 'error_message'))
                    ui_util.getErrorTips(msg.getErrorCode(), '删除')
                }
            })

            // 用户校验监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCVALIDATEUSER, (validatePb) => {
                const validateMsg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(validatePb);
                if (validateMsg.getErrorCode() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                    // 文件上传
                    if (validateMsg.getRequestMsgId() === uploadRequestMsgId) {
                        switch ($('#opraType').val()) {
                            // 上传启动
                            case 'START': {
                                startFn()
                                break
                            }
                            // 待办列表
                            case 'UNDONE': {
                                openUnDoneVisible()
                                break
                            }
                            case 'RESULT': {
                                getUpdateResultFn()
                                break
                            }
                            // 升级
                            case 'UPDATE': {
                                assetValue.value = []
                                updateVisible.value = true
                                break
                            }
                            // 删除
                            case 'DELETE': {
                                deleteSoftPacketFn()
                                break
                            }
                        }
                    }
                } else {
                    ui_util.getErrorTips(validateMsg.getErrorCode(), '验证用户身份')
                }
            })
            // 响应
            tcpInstance.onMessage(util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_UPGRADE), (pbBuffer) => {
                try {
                    let bytes = Number(pbBuffer.readUInt8(0)) // 读前一个字节
                    console.log('响应接口类型', bytes, isNaN(Number(bytes)) ? '不确定类型' :
                        { 1: '上传开始', 2: '上传中', 3: '上传结束', 4: '执行升级', 5: '升级结果查询', 6: '升级包查询', 7: '升级包删除' }[Number(bytes)])
                    const bufferData = pbBuffer.slice(1) // 一字节后的所有数据
                    console.log('响应bufferData', bufferData)
                    let msg = null
                    let Fn = () => {}
                    switch (Number(bytes)) {
                        // 上传开始
                        case 1: {
                            msg = PlatformProxyServer_pb.MSG_DPStartDownloadUpgradePackage.deserializeBinary(bufferData); // 先临时使用开始的
                            Fn = startDealFn
                            break
                        }
                        // 上传中
                        case 2: {
                            msg = PlatformProxyServer_pb.MSG_DPDownloadingUpgradePackage.deserializeBinary(bufferData); // 先临时使用开始的
                            Fn = uploadingDealFn
                            break
                        }
                        // 上传完成
                        case 3: {
                            msg = PlatformProxyServer_pb.MSG_DPCompleteDownloadUpgradePackage.deserializeBinary(bufferData); // 先临时使用开始的
                            Fn = stopDealFn
                            break
                        }
                        // 执行升级
                        case 4: {
                            msg = PlatformProxyServer_pb.MSG_DPExecuteUpgrade.deserializeBinary(bufferData); // 先临时使用开始的
                            Fn = updateDealFn
                            break
                        }
                        // 升级结果查询
                        case 5: {
                            msg = PlatformProxyServer_pb.MSG_DPQueryUpgradeResult.deserializeBinary(bufferData); // 先临时使用开始的
                            Fn = getUpdateResultDealFn
                            break
                        }
                        // 升级包查询
                        case 6: {
                            msg = PlatformProxyServer_pb.MSG_DPQueryUpgradePackage.deserializeBinary(bufferData); // 先临时使用开始的
                            Fn = getSoftPacketListDealFn
                            break
                        }
                        // 升级包删除
                        case 7: {
                            msg = PlatformProxyServer_pb.MSG_DPDeleteUpgradePackage.deserializeBinary(bufferData); // 先临时使用开始的
                            Fn = deleteSoftPacketDealFn
                            break
                        }
                    }
                    if (!msg) {
                        if (uploadStatus.value === 1) return uploadingErrorCatchFn()
                    } else {
                        console.log('结果', msg.getResult(), msg.getResult() != PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS)
                        if (msg.getResult() != PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                            if (uploadStatus.value === 1) {
                                uploadingErrorCatchFn()
                                return
                            }
                        }
                        Fn(msg)
                    }
                } catch (e) {
                    console.log('catch', e)
                    if (uploadStatus.value === 1) uploadingErrorCatchFn()
                }
            })
            // 获取资产
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCLOADASSETS, (pb) => {
                const msg = ProxyServer_pb.MSG_PCLoadAssets.deserializeBinary(pb);
                getAssetListDealFn(msg)
            })
        }
        const fileUuid = ref('')
        const fileName = ref('')
        const fileSize = ref(0)
        const fileBufferMD5 = ref('') // MD5校验值
        let filePath = ref('') // 文件路径
        let fileBufferUploadOffset = 0 // 分片位置

        // 文件上传ref
        const fileInput = ref(null)
        // 点击数据恢复框
        const uploadHandleClick = () => {
            if (!fileName.value) {
                fileInput.value?.click()
            }
        }
        /*
        *
        *
        * */

        // 数据恢复文件上传
        const onFileChange = async (event) => {
            const files = event.target.files
            if (!files || files.length === 0) return
            const file = files[0]
            filePath.value = file.path
            try {
                fileUuid.value = util.genTaskId() // 获取唯一标识
                fileName.value = path.basename(filePath.value) // 文件名
                fileSize.value = getFileSize(filePath.value) // 文件大小
                fileBufferMD5.value = await getFileMD5(filePath.value) // 文件MD5
                fileBufferUploadOffset = 0 // 重置偏移量

                console.log(
                    '同步读取文件大小 =>', getFileSize(filePath.value),
                    '文件路径 => ', filePath.value,
                    '文件名 => ', path.basename(filePath.value),
                    'MD5 => ', await getFileMD5(filePath.value),
                    '第一个分片 => ', readChunk(filePath.value, 0, offsetSplitNumber.value * 1024, fileSize.value),
                )
                uploadStatus.value = 0
            } catch (err) {
                console.error('读取文件失败:', err)
            }
            event.target.value = ''
        }

        const openUnDoneVisible = () => {
            UnDoneTcpSendFn('SEARCH')
            unDoneVisible.value = true
        }
        const closeUnDoneVisibleFn = () => {
            unDoneVisible.value = false
            unDonePageNum.value = 1
            unDoneTotalCount.value = 0
        }
        // 删除
        const deleteUnDoneFn = async (row) => {
            ElMessageBox.confirm('确定要删除吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    UnDoneTcpSendFn('DELETE', row)
                })
                .catch(() => {
                    ui_util.showFadeTip('已取消删除')
                })
        }
        // 继续
        const continueUnDoneFn = async (row) => {
            console.log('继续', cloneDeep(row))
            // 源文件校验
            const pathCheck = checkFileAccess(row['file_path'])
            console.log('校验路径', pathCheck)
            if (!pathCheck.status) {
                ui_util.showFadeTip(pathCheck.reason) // 提示
                return
            }
            const fileMD5 = await getFileMD5(row['file_path']) // 文件MD5
            const fSize = getFileSize(row['file_path']) // 文件大小
            console.log('对比源文件', fileMD5, row.md5, '大小', fSize, row['total_size'])
            if (fileMD5 !== row.md5 || fSize !== row['total_size']) return ui_util.showFadeTip('源文件内容发生变更，无法续传！')
            subType.value = String(row['sub_type']) // 任务子类型
            fileUuid.value = row.uuid // 唯一id
            fileName.value = row['file_name'] // 文件名
            fileSize.value = row['total_size'] // 文件大小
            fileBufferMD5.value = row['md5']
            filePath.value = row['file_path']
            fileBufferUploadOffset = 0
            unDoneVisible.value = false
            setTimeout(() => {
                startFn() // 直接传
            }, 100)
        }
        // 发送未完成列表相关消息
        const UnDoneTcpSendFn = (TYPE, row) => {
            const messageIdeNum = {
                'SEARCH': ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLISTRESUMEFILETASK,
                'DELETE': ProxyServer_pb.PSMessageId.PSMESSAGEID_CPDELETERESUMEFILETASK,
                'ADD': ProxyServer_pb.PSMessageId.PSMESSAGEID_CPADDRESUMEFILETASK
            }
            let sendMsg = null
            switch (TYPE) {
                case 'SEARCH': {
                    sendMsg = new ProxyServer_pb.MSG_CPListResumeFileTask();
                    sendMsg.setPageNum(unDonePageNum.value)
                    sendMsg.setPageSize(unDonePageSize.value)
                    sendMsg.setSubType(-1)
                    break
                }
                case 'ADD': {
                    sendMsg = new ProxyServer_pb.MSG_CPAddResumeFileTask();
                    sendMsg.setUuid(fileUuid.value)
                    sendMsg.setSubType(subType.value)
                    // sendMsg.setPluginId(row['plugin_id'])
                    sendMsg.setFileName(fileName.value)
                    sendMsg.setFilePath(filePath.value)
                    sendMsg.setTotalSize(fileSize.value)
                    sendMsg.setMd5(fileBufferMD5.value)
                    break
                }
                case 'DELETE': {
                    sendMsg = new ProxyServer_pb.MSG_CPDeleteResumeFileTask();
                    sendMsg.setUuidsList([row.uuid])
                    break
                }
            }
            tcpInstance.sendProtoMsg(messageIdeNum[TYPE], sendMsg).then();
        }

        // 删除数据恢复已经上传的文件
        const removeFile = (event) => {
            event.stopPropagation() // 阻止冒泡避免触发 restore-box 的 click
            fileName.value = ''
            fileSize.value = 0
            fileBufferMD5.value = ''
            filePath.value = ''
            fileBufferUploadOffset = 0 // 分片位置
            uploadStatus.value = 0 // 回归初始状态
        }
        const uploadStatus = ref(0)
        const getMsgData = (type, row) => {
            const data = {}
            switch (type) {
                case 'START': { // 开始
                    data.uuid = fileUuid.value
                    data.subtype = subType.value
                    data.addInfo = { id: '', pkgname: fileName.value, pkgsize: String(fileSize.value), pkgmd5: fileBufferMD5.value }
                    break
                }
                case 'UPLOADING': { // 上传中
                    data.uuid = fileUuid.value
                    data.subtype = subType.value
                    data.addInfo = { offset: fileBufferUploadOffset }
                    break
                }
                case 'END': {
                    Object.assign(data, {
                        uuid: fileUuid.value,
                        subtype: '1',
                    })
                    break
                }
                // 升级
                case 'UPDATE': {
                    data.uuid = util.genTaskId()
                    data.subtype = subType2.value
                    data.addInfo = row
                    break
                }
                case 'RESULT': {
                    data.subtype = subType2.value
                    data.uuid = util.genTaskId()
                    data.id = row.id
                    break
                }
                case 'DELETE': {
                    data.subtype = subType2.value
                    data.uuid = util.genTaskId()
                    data.id = row.id
                    break
                }
                case 'UPDATE_SEARCH': {
                    data.uuid = util.genTaskId()
                    data.subtype = subType2.value
                }
            }
            console.log('调用通用接口请求参数', data)
            return data
        }
        const tcpSendFn = (payload) => {
            tcpInstance.sendJsonMsg(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_UPGRADE, payload).then();
        }

        // 开始/续传
        const startFn = () => {
            if (!subType.value) return ui_util.showFadeTip('请先选择任务子类型！')
            console.log(`*****开始 => ${uploadStatus.value === 2 ? '续传' : '上传'}`, `${fileBufferUploadOffset}/${fileSize.value}`)
            uploadStatus.value = 1 // 上传中状态
            uploadProcessStr.value = formatProgress(fileBufferUploadOffset, fileSize.value)
            uploadDoneVisible.value = true
            // 接口类型标识位
            let buffer1 = getBuffer8(0x1)
            const bufferPart2 = Buffer.from(JSON.stringify(getMsgData('START')), 'utf8');
            tcpSendFn(Buffer.concat([buffer1, bufferPart2]))
        }
        // 开始上传处理响应用
        const startDealFn = (msg) => {
            console.log('启动111', msg)
            const addInfoBuf = msg.getAddinfo()
            console.log('-----启动响应 => ', `偏移：${addInfoBuf.getOffset()} ${typeof addInfoBuf.getOffset()}  进度： ${fileBufferUploadOffset}/${fileSize.value}::${formatProgress(fileBufferUploadOffset, fileSize.value)}`)
            console.log('偏移')
            fileBufferUploadOffset = addInfoBuf.getOffset()
            fileBufferUploadOffset = Number(fileBufferUploadOffset)
            UnDoneTcpSendFn('ADD')
        }
        const offsetSplitNumber = ref(16) // 分包单位
        // 上传中函数
        const uploadingFn = () => {
            if (uploadStatus.value !== 1) return // 非上传中状态不走
            console.log(`*****上传中 => `, `${fileBufferUploadOffset}/${fileSize.value}`)
            // 接口类型标识位
            let buffer1 = getBuffer8(0x2)
            let sendMsg = JSON.stringify(getMsgData('UPLOADING'))
            // json长度
            const jsonLength = Buffer.from(sendMsg).length;
            let buffer2 = Buffer.alloc(2)
            buffer2.writeInt16BE(jsonLength); // 存入数据长度
            // 文件
            console.log('文件路径1', filePath.value, fileBufferUploadOffset, fileSize.value)
            let buffer3 = readChunk(filePath.value, fileBufferUploadOffset, (offsetSplitNumber.value * 1024), fileSize.value)
            console.log('文件路径2', filePath.value, buffer3)
            const bufferPart2 = Buffer.from(sendMsg, 'utf8');

            tcpSendFn(Buffer.concat([buffer1, buffer2, bufferPart2, buffer3]))
        }
        // 开始上传处理响应用
        const uploadingDealFn =(msg) => {
            console.log('-----上传中响应 =>', `${fileBufferUploadOffset}/${fileSize.value}`, '上传的分片范围 =>', `${fileBufferUploadOffset}-${(fileBufferUploadOffset + (offsetSplitNumber.value * 1024)) - 1}`)
            const addInfoBuf = msg.getAddinfo()
            fileBufferUploadOffset = addInfoBuf.getOffset()
            fileBufferUploadOffset = Number(fileBufferUploadOffset)
            console.log('-----上传中响应 偏移量', addInfoBuf.getOffset())
            uploadProcessStr.value = formatProgress(fileBufferUploadOffset, fileSize.value)
            console.log(`*****上传分片 => 进度：${fileBufferUploadOffset}/${fileSize.value}::${formatProgress(fileBufferUploadOffset, fileSize.value)}`)
            if (fileBufferUploadOffset === fileSize.value) {
                stopFn()
            } else {
                uploadingFn()
            }
        }
        // 上传结束
        const stopFn = () => {
            console.log('*****终止 => ', `${fileBufferUploadOffset}/${fileSize.value}`)
            // 接口类型标识位
            let buffer1 = getBuffer8(0x3)
            const bufferPart2 = Buffer.from(JSON.stringify(getMsgData('END')), 'utf8');
            tcpSendFn(Buffer.concat([buffer1, bufferPart2]))
        }
        // 终止上传处理响应用
        const stopDealFn = (msg) => {
            console.log('-----终止响应 => ', `${fileBufferUploadOffset}/${fileSize.value}`)
            // 正常的情况
            uploadStatus.value = 0
            showUploadStatusTips.value = '上传成功！'
            uploadDoneVisible.value = true
            subType2.value = cloneDeep(subType.value)
            removeFile({ stopPropagation: () => {} }) // 清空文件框回归初始状态
            getSoftPacketListFn()
        }
        // 终止
        const shutdownFn = () => {
            console.log('*****上传手动终止 => ', `${fileBufferUploadOffset}/${fileSize.value}`)
            showUploadStatusTips.value = '上传终止！'
            uploadDoneVisible.value = true
            uploadStatus.value = 0
            removeFile({ stopPropagation: () => {} }) // 清空文件框回归初始状态
        }
        const uploadingErrorCatchFn = () => {
            console.log('*****出错终止 => ', `${fileBufferUploadOffset}/${fileSize.value}::${formatProgress(fileBufferUploadOffset, fileSize.value)}`)
            showUploadStatusTips.value = '上传失败，已暂停，请检查连接！'
            uploadStatus.value = 2
        }
        const uploadProcessStr = ref('') // 上传进度
        const closeUploadVisibleModelFn = () => {
            // 上传完,异常暂停，结尾上传出错
            if ([0, 2, 3].includes(uploadStatus.value)) {
                console.log('只是关闭弹窗')
                uploadDoneVisible.value = false
            } else {
                console.log('终止上传')
                shutdownFn()
            }
        }
        const showUploadStatusTips = ref('')
        const uploadDoneVisible = ref(false)
        // 0: 初始状态(上传) 1: 上传中(暂停，终止) 2: 暂停时(继续，终止) 3：全部传完后出错时(上传，终止) 4: 启动失败

        // TODO 执行升级相关
        const updateVisible = ref(false)
        const subType2 = ref('1') // 执行升级用
        const assetList = ref([]) // 资产列表
        const assetValue = ref([]) // 资产选中值
        const updateSoftPacketId = ref('') // 选中要升级的升级包
        const softPacketList = ref([]) // 升级包列表
        // 获取资产
        const getAssetListFn = () => {
            let sendMsg = new ProxyServer_pb.MSG_CPLoadAssets();
            tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADASSETS, sendMsg).then()
        }
        const closeUpdateVisibleFn = () => {
            updateVisible.value = false
            assetValue.value = []
            updateSoftPacketId.value = ''
        }
        const getAssetListDealFn = (msg) => {
            // 资产列表
                const data = []
                const assetsBufList = msg.getAssetsList()
                assetsBufList.forEach(bufItem => {
                    try {// 已确认资产
                        if (util.getProtobufFieldValue(bufItem, 'CONAST')) {
                            data.push(util.getArrayDataByBufDataAndTemp(bufItem, ['GID', 'DNAME', 'CONAST']))
                        }
                    } catch (e) {
                        console.log('解构err', e)
                    }
                })
                assetList.value = data
        }
        // 执行升级
        const updateFn = () => {
            if (!subType2.value) return ui_util.showFadeTip('请先选择任务子类型！')
            if (!assetValue.value) return ui_util.showFadeTip('请先选择资产！')
            console.log('^^^^^执行升级', subType2.value, assetValue.value)
            let buffer1 = getBuffer8(0x4)
            const bufferPart2 = Buffer.from(JSON.stringify(getMsgData('UPDATE', { gid: cloneDeep(assetValue.value), id: updateSoftPacketId.value })), 'utf8');
            tcpSendFn(Buffer.concat([buffer1, bufferPart2]))
        }
        const updateDealFn = (msg) => {
            setTimeout(() => {
                if (msg.getResult() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                    updateSoftPacketId.value = ''
                    assetValue.value = []
                    getSoftPacketListFn()
                } else {
                    ui_util.getErrorTips(msg.getResult(), '升级')
                }
            }, 300)
        }
        // 执行升级结果查询
        const getUpdateResultFn = () => {
            let buffer1 = getBuffer8(0x5)
            const bufferPart2 = Buffer.from(JSON.stringify(getMsgData('RESULT', { id: updateSoftPacketId.value })), 'utf8');
            tcpSendFn(Buffer.concat([buffer1, bufferPart2]))
        }
        const getUpdateResultDealFn = (msg) => {
            if (msg.getResult() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                ui_util.showFadeTip('升级成功！')
            } else {
                ui_util.getErrorTips(msg.getResult(), '查询升级结果')
            }
        }
        // 获取软件包列表
        const getSoftPacketListFn = (msg) => {
            let buffer1 = getBuffer8(0x6)
            const bufferPart2 = Buffer.from(JSON.stringify(getMsgData('UPDATE_SEARCH')), 'utf8');
            tcpSendFn(Buffer.concat([buffer1, bufferPart2]))
        }
        const getSoftPacketListDealFn = (msg) => {
            const table = []
            if (msg.getResult() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                const addInfoBuf = msg.getAddinfoList()
                addInfoBuf.forEach(bufItem => {
                    const tableItem = util.getArrayDataByBufDataAndTemp(bufItem, ['id', 'btype', 'status', 'pkgname', 'pkgsize', 'loadtime'])
                    tableItem['statusName'] = { 1: '升级中...', 2: '下载成功', 3: '异常' }[tableItem['status']]
                    tableItem['pkgsizeName'] = formatFileSize(tableItem['pkgsize'])
                    tableItem['loadtimeName'] = String(moment(tableItem['loadtime']).format('YYYY-MM-DD HH:mm:ss'))
                    table.push(tableItem)
                })
            } else {
                ui_util.getErrorTips(msg.getResult(), '升级包加载')
            }
            softPacketList.value = table
        }
        // 升级包删除
        const deleteSoftPacketFn = () => {
            let buffer1 = getBuffer8(0x7)
            const bufferPart2 = Buffer.from(JSON.stringify(getMsgData('DELETE', { id: updateSoftPacketId.value })), 'utf8');
            tcpSendFn(Buffer.concat([buffer1, bufferPart2]))
        }
        const deleteSoftPacketDealFn = (msg) => {
            if (msg.getResult() === PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                ui_util.showFadeTip('删除成功')
                getSoftPacketListFn()
            } else {
                ui_util.getErrorTips(msg.getResult(), '删除')
            }
        }
        const changeAssetValue = (val) => {
            if(val.length > 20) {
                ui_util.showFadeTip('最多选择20个资产！')
                assetValue.value.pop()
            }
        }
        return {
            subType,
            subTypeEnum,
            unDoneVisible, // 待办列表
            box,
            unDonePageNum,
            unDonePageSize,
            unDoneTotalCount,
            unDoneCurrentChangeFn,
            unDoneTableList,
            queryModelHeight,
            queryModelTableHeight,
            updateTableHeight,
            fileInput,
            fileName,
            fileSize,
            uploadStatus,
            openUnDoneVisible,
            closeUnDoneVisibleFn,
            continueUnDoneFn,
            deleteUnDoneFn,
            formatFileSize,
            uploadHandleClick,
            onFileChange,
            removeFile,
            startFn,
            uploadingFn,
            stopFn,
            shutdownFn,
            closeUploadVisibleModelFn,
            validateToNext,
            uploadProcessStr,
            uploadDoneVisible,
            showUploadStatusTips,

            // 执行升级相关
            closeUpdateVisibleFn,
            updateVisible,
            assetList,
            assetValue,
            softPacketList,
            subType2,
            updateFn,
            getUpdateResultFn,
            getSoftPacketListFn,
            deleteSoftPacketFn,
            changeAssetValue,
        }
    }
})
app.use(ElementPlus, { locale: ElementPlusLocaleZhCn })
app.mount('#app');