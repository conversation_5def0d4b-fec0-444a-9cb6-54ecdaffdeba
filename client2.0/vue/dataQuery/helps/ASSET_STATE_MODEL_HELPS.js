const ENUMS = {
    ASSET: {
        INFO: {
            GID: '资产ID',
            ATAG: '资产标识',
            HARDWARE_STATUS: '硬件状态',
            SYSTEM_RUNTIME_STATUS: '系统运行时状态',
            USER: '用户',
            USER_STATUS: '用户状态',
            PLUGIN: '插件',
            PLUGIN_STATUS: '插件状态',
            NETWORK_STATUS: '网络状态'
        },
        // 硬件状态
        HARDWARE_STATUS: {
            INFO: {
                TCPUR: '总CPU利用率',
                TMEMR: '总物理内存利用率',
                DISKR: '总磁盘利用率',
                HERR: '硬件异常计数',
            }

        },
        // 系统运行时状态
        SYSTEM_RUNTIME_STATUS: {
            INFO: {
                SOFTID: '软件标识',
                LOAD15M: '15分钟负载',
                LOAD5M: '5分钟负载',
                LOAD1M: '1分钟负载',
                SUPRNUM: '已使用进程数',
                SZPRNUM: '僵尸进程数',
                UNPRNUM: '不可中断进程数',
                SUTNUM: '已使用线程数',
                SUHNUM: '已使用句柄数',
                MUSED: '已使用内存(MB)',
                MFREE: '空闲内存(MB)',
                MSHARED: '共享内存(MB)',
                MCACHE: '内存缓存(MB)',
                MAVAI: '可用内存(MB)',
                SUSED: '已使用交换区(MB)',
                SFREE: '空闲交换区(MB)',
                SIRATE: '交换分区每秒换入(KB/s)',
                SORATE: '交换分区每秒换出(KB/s)',
                SERR: '软件异常计数'
            }
        },
        // 用户
        USER: {
            INFO: {
                UID: '用户标识',
                NAME: '名称',
                LKSTAT: '用户锁定状态'
            }
        },
        // 用户状态
        USER_STATUS: {
            INFO: {
                UID: '用户标识',
                UPRNUM: '用户进程数',
                UZPRNUM: '用户僵尸进程个数',
                UUPRNUM: '用户不可中断进程个数',
                UTNUM: '用户线程数'
            }
        },
        // 插件
        PLUGIN: {
            INFO: {
                PLUG_ID: '插件ID，示例："NARI_123456789"',
                PSTAT: '正常=1，加载异常=2，运行异常=3',
                EREASON: '异常原因（空字符串表示无异常）'
            }
        },
        // 插件状态
        PLUGIN_STATUS: {
            INFO: {
                PLUG_ID: '插件ID',
                CPUR: 'CPU利用率',
                MEMR: '内存利用率',
                DISK: '使用磁盘大小(MB)',
                PRONUM: '进程或线程数量',
                FILENUM: '文件描述符数',
                PORTNUM: '文件描述符数'
            }
        },
        // 网络状态
        NETWORK_STATUS: {
            INFO: {
                NETID: '网络标识',
                NERR: '网络异常'
            }
        },
    }
}
module.exports = {
    ENUMS,
}
