const DicDefine_pb = require('../../../script/pb/DicDefine_pb.js');

const DCDParamDefine_pb = require('../../../script/pb/DCDParamDefine_pb.js')
const AssetModelDefine_pb = require('../../../script/pb/AssetModelDefine_pb.js');
const AssetModelCodeDefine_pb = require('../../../script/pb/AssetModelCodeDefine_pb.js');
const util = require('../../../lib/util.js');
const {cloneDeep} = require("lodash");
const tcpInstance = require("../../../lib/v2/tls_client");
const PlatformProxyServer_pb = require("../../../script/pb/PlatformProxyServer_pb");

const MAP2CHINA=  {
    COMMON: {
        ASSET: '资产信息',
        HARDWARE: '硬件',
        SOFTWARE: '软件',
        NETWORK: '网络',
    },
    // 资产
    ASSET: {
        INFO: {
            GID: '资产ID',
            ATAG: '资产标识',
            DSN: '设备序列号',
            DNAME: '设备名称',
            TYPE: '资产类型',
            CONAST: '已确认资产',
            ONLSTA: '在线状态',
            LABEL: '资产标签集',
            CREATET: '数据生成时间',
            UPDATET: '数据更新时间',
            SOURCE: '数据来源',
            ASSET_NODE: '资产节点',
            HARDWARE: '硬件',
            SOFTWARE: '软件',
            NETWORK: '网络',
            MANAGEMENT: '管理'
        },
        // 硬件
        HARDWARE: {
            INFO: {
                HID: '硬件标识',
                BRAND: '品牌',
                MODEL: '型号',
                VER: '版本',
                SN: '硬件序列号',
                CREATET: '数据生成时间',
                UPDATET: '数据更新时间',
                SOURCE: '数据来源',
                CPU: 'CPU',
                MEMORY: '内存',
                DISK: '磁盘',
                NETWORK_CARD: '有线网卡',
                WIRELESS: '无线网卡',
                PERIPHERAL_INTERFACE: '外设接口',
                POWER: '电源',
                GPU: '显卡',
                HBA: '主机总线适配器',
                RAID: '磁盘冗余阵列',
                BMC: '基板管理控制器',
                SENSOR: '传感器'
            },
            CPU: {
                INFO: {
                    CPUID: '处理器标识',
                    BRAND: '品牌',
                    MODEL: '型号',
                    VER: '版本',
                    WIDTH: '总线宽度',
                    ARCH: 'CPU架构',
                    CNUM: '物理核心数',
                    LOGICAL_CORE: '逻辑核心',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源'
                },
                LOGICAL_CORE: {
                    INFO: {
                        LCID: '逻辑核心标识',
                        CPUID: '处理器标识',
                        CREATET: '数据生成时间',
                        UPDATET: '数据更新时间',
                        SOURCE: '数据来源'
                    }
                },
            },
            MEMORY: {
                INFO: {
                    MEMID: '内存标识',
                    BRAND: '品牌',
                    MODEL: '型号',
                    VER: '版本',
                    CY: '容量',
                    MEMSNID: '内存序列号',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源'
                }
            },
            DISK: {
                INFO: {
                    WWID: '磁盘标识',
                    BRAND: '品牌',
                    MODEL: '型号',
                    VER: '版本',
                    CY: '容量',
                    DFILE: '设备文件',
                    MBR: '主引导记录哈希值',
                    RABLE: '移动存储设备',
                    DERR: '磁盘健康度',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源'
                }
            },
            NETWORK_CARD: {
                INFO: {
                    NCID: '网卡标识',
                    BRAND: '品牌',
                    MODEL: '型号',
                    VER: '版本',
                    BCMAC: '网卡MAC地址',
                    IFCNT: '接口数量',
                    WIDTH: '总线宽度',
                    ETHERNET: '物理网口',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源'
                },
                ETHERNET: {
                    INFO: {
                        ETHID: '网口标识',
                        NCID: '有线网卡标识',
                        NAME: '名称',
                        ETHTYP: '类型',
                        NTYPE: '网络位置类型',
                        QUERL: '队列规则',
                        MAC: 'MAC地址',
                        SPEED: '速率',
                        WKMD: '工作模式',
                        SLOTN: '插槽号',
                        CONCSTA: '连接状态',
                        CREATET: '数据生成时间',
                        UPDATET: '数据更新时间',
                        SOURCE: '数据来源'
                    }
                },
            },
            WIRELESS: {
                INFO: {
                    NCID: '网卡标识',
                    BRAND: '品牌',
                    MODEL: '型号',
                    VER: '版本',
                    MAC: 'MAC地址',
                    PROTO: '协议类型',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源'
                }
            },
            PERIPHERAL_INTERFACE: {
                INFO: {
                    PIID: '外设接口标识',
                    PINM: '接口名称',
                    TYPE: '类型',
                    ENABLE: '启用状态',
                    CONNUM: '接入数量',
                    AUTHS: '授权状态',
                    WIDTH: '总线宽度',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源'
                }
            },
            POWER: {
                INFO: {
                    PWRID: '电源标识',
                    BRAND: '品牌',
                    MODEL: '型号',
                    VER: '版本',
                    RP: '额定功率',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源'
                }
            },
            GPU: {
                INFO: {
                    GPUID: 'GPU卡标识',
                    BRAND: '品牌',
                    MODEL: '型号',
                    VER: '版本',
                    WIDTH: '总线宽度',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源'
                }
            },
            HBA: {
                INFO: {
                    HBAID: 'HBA卡标识',
                    BRAND: '品牌',
                    MODEL: '型号',
                    VER: '版本',
                    WIDTH: '总线宽度',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源'
                }
            },
            RAID: {
                INFO: {
                    RAIDID: 'RAID卡标识',
                    BRAND: '品牌',
                    MODEL: '型号',
                    VER: '版本',
                    WIDTH: '总线宽度',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源'
                }
            },
            BMC: {
                INFO: {
                    BRAND: '品牌',
                    MODEL: '型号',
                    BMCV: '版本',
                    BMCSUP: 'BMC支持设备',
                    BMCFIRV: 'BMC固件修订版本',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源'
                }
            },
            SENSOR: {
                INFO: {
                    NAME: '名称',
                    OTHERTMP: '数值',
                    UNIT: '单位',
                    TYPE: '类别',
                    STATE: '状态',
                    OTHERERRSTA: '异常状态',
                    MTIME: '时标',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源',
                }
            },
        },
        // 软件
        SOFTWARE: {
            INFO: {
                SOFTID: '软件标识',
                BRAND: '品牌',
                MOD: '型号',
                VER: '版本',
                KINFO: '内核信息',
                KPATH: '内核路径',
                KPARAM: '内核参数',
                HNAME: '主机名',
                STIME: '启动时间',
                SLEVEL: '启动级别',
                TZ: '时区',
                EVAR: '环境变量',
                EVHASH: '环境变量哈希值',
                RSTAT: '超级用户启用',
                USTAT: 'USB存储功能启用',
                MSTAT: '强制访问控制启用',
                LHOSTS: '本地域名',
                MAXTNUM: '系统最大线程数',
                MAXPNUM: '系统最大进程数',
                MAXFNUM: '系统最大句柄数',
                STERRTYPE: '对时方式',
                CREATET: '数据生成时间',
                UPDATET: '数据更新时间',
                SOURCE: '数据来源',
                USER: '用户',
                USER_GROUP: '用户组',
                SESSION: '会话',
                PARTITION: '分区',
                PROCESS: '进程',
                SERVICE: '服务',
                BOOT: '启动任务',
                DRIVER: '驱动',
                FILE: '文件',
                EXECUTABLE_FILE: '可执行文件',
                SOFTWARE_PACKAGE: '软件包',
                VULN_SCAN_RESULT: '漏洞扫描',
                CONFIG_CHECK_RESULT: '基线核查',
                APPLICATION: '应用实例',
                PLUGIN: '安全插件'
            },
            USER: {
                INFO: {
                    UID: '用户标识',
                    DUGRID: '默认用户组标识',
                    NAME: '名称',
                    USHELL: '默认SHELL',
                    UHOME: '用户主目录',
                    PCHGTIME: '密码变更时间',
                    LKSTAT: '用户锁定状态',
                    NPIP: '免密登录IP',
                    UMAXPRNUM: '用户最大进程数',
                    MAXFNUM: '用户最大句柄',
                    MAXTNUM: '用户最大线程',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源'
                }
            },
            USER_GROUP: {
                INFO: {
                    GRPID: '用户组标识',
                    NAME: '名称',
                    ULIST: '组内用户名清单',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源'
                }
            },
            SESSION: {
                INFO: {
                    SESSID: '会话标识',
                    FSESS: '上一级会话标识',
                    LUID: '登录用户标识',
                    LUNAME: '登录用户名',
                    SGID: '源资产标识',
                    SPID: '源端进程标识',
                    SPPATH: '源端进程全路径名',
                    DGID: '目的资产标识',
                    DPID: '目的端进程标识',
                    DPPATH: '目的端进程全路径名',
                    LMETHOD: '登录方式',
                    SIP: '源IP',
                    SPORT: '源端口',
                    DIP: '目的IP',
                    DPORT: '目的端口',
                    TNUM: '终端号',
                    SSTAT: '当前状态',
                    EVAR: '环境变量',
                    EVHASH: '环境变量哈希值',
                    LTIME: '登录时间戳',
                    ETIME: '退出时间戳',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源'
                }
            },
            PARTITION: {
                INFO: {
                    PARTID: '分区标识',
                    WWID: '磁盘ID',
                    DFILE: '磁盘设备文件',
                    NAME: '名称',
                    CAP: '容量',
                    TYPE: '类型',
                    FSTYPE: '文件格式',
                    MPOINT: '挂载点',
                    ITOT: '索引节点总数',
                    RWPERM: '读写权限',
                    CRTIME: '创建时间',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源'
                }
            },
            PROCESS: {
                INFO: {
                    PRID: '进程标识',
                    EID: '关联可执行文件标识',
                    PPID: '父进程标识',
                    PFNAME: '父进程进程文件名',
                    SESSID: '关联会话标识',
                    PID: '进程PID',
                    FNAME: '进程文件名',
                    NAME: '名称',
                    SCMD: '启动命令',
                    SPARAM: '启动参数',
                    WDIR: '工作目录',
                    STAT: '进程状态',
                    USER: '启动用户',
                    STIME: '启动时间',
                    ETIME: '退出时间',
                    QTYPE: '退出方式',
                    THNUM: '线程数量',
                    FDNUM: '进程使用句柄数',
                    DPID: '调试进程PID',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源'
                }
            },
            SERVICE: {
                INFO: {
                    SVID: '服务标识',
                    UID: '启动用户标识',
                    UNAME: '用户名',
                    EID: '关联可执行文件标识',
                    FNAME: '可执行文件名',
                    NAME: '名称',
                    DESC: '描述',
                    STYPE: '启动类型',
                    SELFSSTAT: '自启动状态',
                    STARTCMD: '启动命令',
                    STOPCMD: '终止命令',
                    RESCMD: '重启命令',
                    STAT: '运行状态',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源'
                }
            },
            BOOT: {
                INFO: {
                    BID: '启动任务标识',
                    TYPE: '任务类型',
                    UID: '任务类型标识',
                    UNAME: '用户名',
                    BFNAME: '用户标识',
                    BFHASH: '用户名',
                    MINUTE: '启动任务',
                    HOUR: '启动任务文件哈希',
                    DAY: '分钟',
                    MONTH: '小时',
                    WEEKDAY: '日',
                    CREATET: '月',
                    UPDATET: '星期',
                    SOURCE: '数据来源'
                }
            },
            DRIVER: {
                INFO: {
                    DID: '驱动标识',
                    FID: '文件标识',
                    FNAME: '驱动名称路径名',
                    NAME: '名称',
                    AUTHOR: '作者',
                    VER: '版本',
                    DESC: '描述',
                    SIG: '签名',
                    LDSTAT: '加载状态',
                    LDTIME: '加载时间',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源'
                }
            },
            FILE: {
                INFO: {
                    FID: '文件标识',
                    UID: '所属用户标识',
                    GRPID: '所属用户组标识',
                    SPID: '软件包标识',
                    NAME: '名称',
                    TYPE: '文件类型',
                    CLASS: '文件种类',
                    PATH: '文件目录',
                    PERM: '权限',
                    FHASH: '文件HASH',
                    FSIZE: '文件大小',
                    LTFILE: '链接目标文件',
                    CTIME: '创建时间',
                    LATIME: '最后访问时间',
                    LMTIME: '最后修改时间',
                    LCTIME: '最新属性变化时间',
                    INO: '索引节点号',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源'
                }
            },
            EXECUTABLE_FILE: {
                INFO: {
                    EID: '可执行文件标识',
                    CAPBIL: '权能',
                    SO_FILE_LIST: '关联动态库文件列表',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源'
                },
                SO_FILE_LIST: {
                    INFO: {
                        FID: '文件标识',
                        UID: '所属用户标识',
                        GRPID: '所属用户组标识',
                        SPID: '软件包标识',
                        NAME: '名称',
                        TYPE: '文件类型',
                        CLASS: '文件种类',
                        PATH: '文件目录',
                        PERM: '权限',
                        FHASH: '文件HASH',
                        FSIZE: '文件大小',
                        LTFILE: '链接目标文件',
                        CTIME: '创建时间',
                        LATIME: '最后访问时间',
                        LMTIME: '最后修改时间',
                        LCTIME: '最新属性变化时间',
                        INO: '索引节点号',
                        CREATET: '数据生成时间',
                        UPDATET: '数据更新时间',
                        SOURCE: '数据来源'
                    }
                }
            },
            SOFTWARE_PACKAGE: {
                INFO: {
                    SPID: '软件包标识',
                    NAME: '名称',
                    BRAND: '品牌',
                    VER: '版本',
                    PNAME: '安装包名',
                    PTYPE: '安装包类型',
                    ITIME: '安装时间',
                    SIG: '签名',
                    DESC: '描述',
                    IPATH: '安装路径',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源'
                }
            },
            VULN_SCAN_RESULT: {
                INFO: {
                    HOSTIPSTR: '扫描IP',
                    VULNID: '漏洞ID',
                    NODENAME: '漏洞名称',
                    SHORTDESC: '漏洞简短描述',
                    FULLDESC: '漏洞详细描述',
                    REPAIRADVICE: '修复建议',
                    RISKLEVEL: '危险级别',
                    PLATFORMS: '影响范围',
                    CVETAG: 'CVE编号',
                    CVSSSCORE: 'CVSS得分',
                    UPDATE: '修改时间',
                    CREATE: '发布时间',
                    SOFTWARE: '软件名称',
                    VERSION: '影响版本',
                    FINDTIME: '扫描时间',
                    REPAIR: '修复状态'
                }
            },
            CONFIG_CHECK_RESULT: {
                INFO: {
                    CIID: '核查项ID',
                    IP: '设备IP',
                    SUBTYPE: '设备子类型',
                    CHECKITEMTYPE: '核查项类型',
                    PARSE: '检查点结果',
                    ISCHECK: '检查结果',
                    CHECKITEMNAME: '核查项名称',
                    SYSCODE: '系统代码',
                    FIXEDPRO: '修复措施',
                    JUDGE: '判定标准',
                    ITEMCONTENT: '项内容说明',
                    CTIME: '创建时间'
                }
            },
            APPLICATION: {
                INFO: {
                    APPID: '应用软件标识',
                    NAME: '名称',
                    BRAND: '品牌',
                    VER: '版本',
                    TYPE: '应用类型',
                    STYPE: '应用子类型',
                    STIME: '启动时间',
                    RSTA: '运行状态',
                    APP_EXECUTABLE_FILE: '可执行程序列表',
                    MDIPA: '被管IP-A',
                    MDIPB: '被管IP-B',
                    DCDLIST: '监测装置列表',
                    APPDESC: '应用描述',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源',
                    ENSTAT: '监测代理软件启用状态',
                    ISCARD: '是否是加密卡',
                    FBIDAPP: '隔离应用软件标识'
                },
                APP_EXECUTABLE_FILE: {
                    INFO: {
                        EID: '可执行文件标识',
                        CAPBIL: '权能',
                        SO_FILE_LIST: '关联动态库文件列表',
                        CREATET: '数据生成时间',
                        UPDATET: '数据更新时间',
                        SOURCE: '数据来源'
                    },
                    SO_FILE_LIST: {
                        INFO: {
                            FID: '文件标识',
                            UID: '所属用户标识',
                            GRPID: '所属用户组标识',
                            SPID: '软件包标识',
                            NAME: '名称',
                            TYPE: '文件类型',
                            CLASS: '文件种类',
                            PATH: '文件目录',
                            PERM: '权限',
                            FHASH: '文件HASH',
                            FSIZE: '文件大小',
                            LTFILE: '链接目标文件',
                            CTIME: '创建时间',
                            LATIME: '最后访问时间',
                            LMTIME: '最后修改时间',
                            LCTIME: '最新属性变化时间',
                            INO: '索引节点号',
                            CREATET: '数据生成时间',
                            UPDATET: '数据更新时间',
                            SOURCE: '数据来源'
                        }
                    },
                }
            },
            PLUGIN: {
                INFO: {
                    PLUG_ID: '插件ID',
                    APPID: '应用软件标识',
                    TYPE: '插件类型',
                    NAME: '名称',
                    BRAND: '品牌',
                    VER: '版本',
                    STYPE: '业务类型',
                    FLIST: '插件功能码',
                    PSTAT: '插件状态',
                    EREASON: '异常原因',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源'
                }
            },
        },
        // 网络
        NETWORK: {
            INFO: {
                NETID: '网络标识',
                FWD: '网络转发功能启用',
                IPV6: 'IPV6启用',
                FINGER: '网络协议指纹',
                CTYPE: '空间类型',
                CREATET: '数据生成时间',
                UPDATET: '数据更新时间',
                SOURCE: '数据来源',
                NETWORK_INTERFACE: '网络接口',
                ROUTE: '网络路由',
                ADDRESS: '地址学习表',
                LISTENNING_PORT: '网络监听',
                NEIGHBOR: '网络邻居',
                VLAN: 'VLAN',
                ACCESS_CONTROL_LIST: '访问控制策略',
                VRRP: '网络设备虚拟路由协议组'
            },
            NETWORK_INTERFACE: {
                INFO: {
                    IFID: '接口标识',
                    ETHID: '网口标识',
                    NCID: '无线网卡标识',
                    IFNM: '接口名',
                    CLASS: '型式分类',
                    LINKSTA: '启用状态',
                    SPEED: '协商速率',
                    MTU: '最大传输单元',
                    IP: 'IP地址列表',
                    MAC: 'MAC地址',
                    DHCP: '地址分配模式',
                    DHCPS: 'DHCP服务器地址',
                    BONDM: '绑定模式',
                    BONDIF: '绑定接口名',
                    LIF: '逻辑接口名',
                    IFIDX: '接口索引',
                    AUTHS: '授权状态',
                    VLIST: '接口所属VLAN列表',
                    TYPE: '接口类型',
                    MACBIND: '绑定MAC地址列表',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源'
                }
            },
            ROUTE: {
                INFO: {
                    RID: '路由标识',
                    IFID: '接口标识',
                    DSTNET: '目标网络',
                    DSTMK: '目标掩码',
                    NHA: '下一跳地址',
                    TYPE: '类型',
                    METRIC: '度量值',
                    RPROT: '路由协议类型',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源'
                }
            },
            ADDRESS: {
                INFO: {
                    IFID: '接口标识',
                    VID: 'VLAN标识',
                    IP: 'IP地址',
                    MAC: 'MAC地址',
                    ARPTP: '类型',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源'
                }
            },
            LISTENNING_PORT: {
                INFO: {
                    LISPORTID: '网络监听ID',
                    IFID: '网络接口标识',
                    PRID: '进程标识',
                    PRNAME: '服务名',
                    IP: 'IP地址',
                    PORT: '监听端口',
                    PROTO: '最高层协议',
                    PROVER: '协议版本',
                    CLIN: '客户端连接数',
                    CWNUM: '关闭等待数',
                    TWNUM: '等待关闭数',
                    SRNUM: '同步接收数',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源'
                }
            },
            NEIGHBOR: {
                INFO: {
                    NBRID: '邻居信息标识',
                    IFID: '本机接口标识',
                    NBRIFID: '邻居接口标识',
                    NBRIDX: '邻居索引号',
                    UPTIME: '邻居信息更新时间',
                    CHATYPE: '邻居标识类型',
                    CHAID: '邻居标识',
                    NBRIP: '邻居设备IP',
                    PIDTYPE: '网口标识类型',
                    PORTID: '网口标识',
                    PORTDESC: '网口描述',
                    SYSNM: '邻居系统名',
                    SYSDESC: '邻居系统描述',
                    CREATET: '数据生成时间',
                    UPDATET: '数据更新时间',
                    SOURCE: '数据来源'
                }
            },
            VLAN: {
                INFO: {
                    VID: "VLAN标识",
                    VTYPE: "类型",
                    IP: "IP地址",
                    MASK: "掩码",
                    DESCRIPTION: "描述",
                    NIFLST: "接口列表",
                    CREATET: "数据生成时间",
                    UPDATET: "数据更新时间",
                    SOURCE: "数据来源"
                },
                NIFLST: {
                    INFO: {
                        IFID: "接口标识",
                        ETHID: "网口标识",
                        NCID: "无线网卡标识",
                        IFNM: "接口名",
                        CLASS: "型式分类",
                        LINKSTA: "启用状态",
                        SPEED: "协商速率",
                        MTU: "最大传输单元",
                        IP: "IP地址列表",
                        MAC: "MAC地址",
                        DHCP: "地址分配模式",
                        DHCPS: "DHCP服务器地址",
                        BONDM: "绑定模式",
                        BONDIF: "绑定接口名",
                        LIF: "逻辑接口名",
                        IFIDX: "接口索引",
                        AUTHS: "授权状态",
                        VLIST: "接口所属VLAN列表",
                        TYPE: "接口类型",
                        MACBIND: "绑定MAC地址列表",
                        CREATET: "数据生成时间",
                        UPDATET: "数据更新时间",
                        SOURCE: "数据来源"
                    }
                }
            },
            ACCESS_CONTROL_LIST: {
                INFO: {
                    ACLID: "访问控制策略标识",
                    IFID: "接口标识",
                    ACLNUM: "策略编号",
                    TYPE: "策略类型",
                    SRCMAC: "源MAC组",
                    DSTMAC: "目的MAC组",
                    FRTYPE: "帧类型",
                    SRCBIP: "源起始IP",
                    SRCEIP: "源终止IP",
                    DSTBIP: "目的起始IP",
                    DSTEIP: "目的终止IP",
                    PROTO: "协议",
                    SRCPORT: "源端口范围",
                    DSTPORT: "目的端口范围",
                    DIRECTION: "传输方向",
                    ACTION: "动作",
                    SRCNIP: "源转换IP地址",
                    DSTNIP: "目的转换IP地址",
                    SRCNP: "源转换端口",
                    DSTNP: "目的转换端口",
                    NATPRO: "转换协议类型",
                    DESC: "描述",
                    ENABLE: "启用状态",
                    EFCTIME: "生效时间段",
                    CREATET: "数据生成时间",
                    UPDATET: "数据更新时间",
                    SOURCE: "数据来源"
                }
            },
            VRRP: {
                INFO: {
                    "IFID": "网络接口标识",
                    "VRRPID": "VRRP组ID",
                    "VIP": "虚拟IP",
                    "VMASK": "掩码",
                    "MASTER": "主备状态",
                    "CREATET": "数据生成时间",
                    "UPDATET": "数据更新时间",
                    "SOURCE": "数据来源"
                }
            },
        },
    },

    // 资产节点
    ASSET_NODE: {
        ASSET_NODE: '资产节点',
        GID: '资产 ID',
        IP: 'IP',
        MAC: 'MAC',
        IFID: '网络接口 ID',
        SOURCE: '数据来源'
    },
    // SNMP 交互配置
    INTERCONF: {
        INTERCONF: 'SNMP 交互配置',
        GID: '资产 ID',
        RMTPORT: '远程登录端口',
        RMTTP: '远程方式',
        RMTACT: '远程账户',
        RMTPSD: '远程账户密码',
        SNMPPORT: 'SNMP监听端口',
        SNMPVER: 'snmp版本',
        SNMPV3USER: 'snmp v3 用户名',
        SNMPAUTH: 'snmp v3 认证算法',
        SNMPENC: 'snmp v3 加密算法',
        SNMPREAD: 'snmp read 团体名/认证密码',
        SNMPWRITE: 'snmp write 团体名/认证密码',
        ANAMETH: '解析方法'
    },
    // 管理
    MANAGEMENT: {
        MANAGEMENT: '管理',
        GID: '资产 ID',
        DCLOUDID: '调控云资产标识',
        DCCID: '所属调度标识',
        STAID: '所属区域标识',
        FNAME: '资产全称',
        SNAME: '资产简称',
        ATYPE: '资产类型',
        ABRAND: '资产厂商',
        AMODEL: '资产型号',
        STATUS: '运行状态',
        ISCII: '关基设施',
        CLASS: '资产类别',
        MTYPE: '管辖类型',
        NTTYPE: '网络类型',
        AREA: '安全区',
        NOTE: '备注',
        NOTES: '备注'
    },
    // 应用
    APPLICATION: {
        APPLICATION: '应用',
        GID: '资产 ID',
        APPID: '应用软件标识',
        NAME: '名称',
        BRAND: '品牌',
        VER: '版本',
        TYPE: '应用类型',
        STYPE: '应用子类型',
        MDIPA: '被管 IP-A',
        MDIPB: '被管 IP-B',
        APPDESC: '应用描述',
        SOURCE: '数据来源',
        ENSTAT: '监测代理软件启用状态',
        ISCARD: '是否是加密卡',
        FBIDAPP: '隔离应用软件标识',
    }
}
const DATA_TEMP = {
    ASSET: {
        INFO: ['GID', 'ATAG', 'DSN', 'DNAME', 'TYPE', 'CONAST', 'ONLSTA', 'LABEL', 'CREATET', 'UPDATET', 'SOURCE'],
    },
    HARDWARE: {
        INFO: ['HID', 'BRAND', 'MODEL', 'VER', 'SN', 'CREATET', 'UPDATET', 'SOURCE'], // 硬件基础信息
        // --硬件状态   字典: HERR
        HARDWARE_STATUS: ['HID', 'TCPUR', 'TMEMR', 'DISKR', 'HERR', 'MTIME'],
        // CPU  字典：ARCH SOURCE  数组：LOGICAL_CORE
        CPU: ['CPUID', 'BRAND', 'MODEL', 'VER', 'WIDTH', 'ARCH', 'CNUM', 'CREATET', 'UPDATET', 'SOURCE'],
        // --处理器状态
        CPU_STATUS: ['CPUID', 'CPUR', 'MTIME', 'LCID', 'CPUID', 'CREATET', 'UPDATET', 'SOURCE'],
        // CPU => 逻辑核心  字典：SOURCE
        LOGICAL_CORE: ['LCID', 'CPUID', 'CREATET', 'UPDATET', 'SOURCE'],
        // CPU => 逻辑核心状态  字典：SOURCE
        LCPU_STATUS: ['LCID', 'CPUID', 'CPUTT', 'CPUSFT', 'CPUUT', 'CPUHT', 'CPUIT', 'CPUNT', 'CPUSFT', 'CPULT', 'IOT', 'URATE', 'MTIME', 'CREATET', 'UPDATET', 'SOURCE'],
        // 内存  字典：SOURCE
        MEMORY: ['MEMID', 'BRAND', 'MODEL', 'VER', 'CY', 'MEMSNID', 'CREATET', 'UPDATET', 'SOURCE'],
        // 磁盘  字典：SOURCE
        DISK: ['WWID', 'BRAND', 'MODEL', 'VER', 'CY', 'DFILE', 'MBR', 'RABLE', 'DERR', 'CREATET', 'UPDATET', 'SOURCE'],
        // --磁盘状态
        DISK_STATUS: ['WWID', 'DUSED', 'MTIME'],
        // *有线网卡  字典：SOURCE 数组：ETHERNET
        NETWORK_CARD: ['NCID', 'BRAND', 'MODEL', 'VER', 'BCMAC', 'IFCNT', 'WIDTH', 'CREATET', 'UPDATET', 'SOURCE'],
        // --物理网口  字典：SOURCE
        ETHERNET: ['ETHID', 'NCID', 'NAME', 'ETHTYP', 'NTYPE', 'QUERL', 'MAC', 'SPEED', 'WKMD', 'SLOTN', 'CONCSTA', 'CREATET', 'UPDATET', 'SOURCE'],
        // 无线网卡  字典：SOURCE
        WIRELESS: ['NCID', 'BRAND', 'MODEL', 'VER', 'MAC', 'PROTO', 'CREATET', 'UPDATET', 'SOURCE'],
        // 外设接口  字典：TYPE  SOURCE
        PERIPHERAL_INTERFACE: ['PIID', 'PINM', 'TYPE', 'ENABLE', 'CONNUM', 'AUTHS', 'WIDTH', 'CREATET', 'UPDATET', 'SOURCE'],
        // --外设设备 字典：TYPE IFPCODE  SOURCE
        PERIPHERAL: ['PERID', 'PIID', 'NAME', 'BRAND', 'MODEL', 'TYPE', 'IFPCODE', 'CODE', 'VENDOR', 'CREATET', 'UPDATET', 'SOURCE'],
        // 电源  字典：SOURCE
        POWER: ['PWRID', 'BRAND', 'MODEL', 'VER', 'RP', 'CREATET', 'UPDATET', 'SOURCE'],
        // 显卡  字典：SOURCE
        GPU: ['GPUID', 'BRAND', 'MODEL', 'VER', 'WIDTH', 'CREATET', 'UPDATET', 'SOURCE'],
        // 主机总线适配器  字典：SOURCE
        HBA: ['HBAID', 'BRAND', 'MODEL', 'VER', 'WIDTH', 'CREATET', 'UPDATET', 'SOURCE'],
        // 磁盘冗余阵列  字典：SOURCE
        RAID: ['RAIDID', 'BRAND', 'MODEL', 'VER', 'WIDTH', 'CREATET', 'UPDATET', 'SOURCE'],
        // 基板管理控制器  字典：SOURCE
        BMC: ['BRAND', 'MODEL', 'BMCV', 'BMCSUP', 'BMCFIRV', 'CREATET', 'UPDATET', 'SOURCE'],
        // *传感器  字典：SOURCE OTHERERRSTA
        SENSOR: ['NAME', 'OTHERTMP', 'UNIT', 'TYPE', 'STATE', 'OTHERERRSTA', 'MTIME', 'CREATET', 'UPDATET', 'SOURCE']
    },
    // 软件
    SOFTWARE: {
        INFO: ['SOFTID', 'BRAND', 'MOD', 'VER', 'KINFO', 'KPATH', 'KPARAM', 'HNAME', 'STIME', 'SLEVEL', 'TZ', 'EVAR', 'EVHASH', 'RSTAT', 'USTAT', 'MSTAT', 'LHOSTS', 'MAXTNUM', 'MAXPNUM', 'MAXFNUM', 'STERRTYPE', 'CREATET', 'UPDATET', 'SOURCE'],
        // --*系统运行状态  字典：SERR
        SYSTEM_RUNTIME_STATUS: ['SOFTID', '15MLOAD', '5MLOAD', '1MLOAD', 'SUPRNUM', 'SZPRNUM', 'UNPRNUM', 'SUTNUM', 'SUHNUM', 'MUSED', 'MFREE', 'MSHARED', 'MCACHE', 'MAVAI', 'SUSED', 'SFREE', 'SIRATE', 'SORATE', 'SERR', 'MTIME'],
        // 用户 字典：SOURCE
        USER: ['UID', 'DUGRID', 'NAME', 'USHELL', 'UHOME', 'PCHGTIME', 'LKSTAT', 'NPIP', 'UMAXPRNUM', 'MAXFNUM', 'MAXTNUM', 'CREATET', 'UPDATET', 'SOURCE'],
        // --用户状态
        USER_STATUS: ['UID', 'UPRNUM', 'UZPRNUM', 'UUPRNUM', 'UTNUM', 'MTIME'],
        // 用户组 字典：SOURCE
        USER_GROUP: ['GRPID', 'NAME', 'ULIST', 'CREATET', 'UPDATET', 'SOURCE'],
        // *会话  字典：SOURCE LMETHOD
        SESSION: ['SESSID', 'FSESS', 'LUID', 'LUNAME', 'SGID', 'SPID', 'SPPATH', 'DGID', 'DPID', 'DPPATH', 'LMETHOD', 'SIP', 'SPORT', 'DIP', 'DPORT', 'TNUM', 'SSTAT', 'EVAR', 'EVHASH', 'LTIME', 'ETIME', 'CREATET', 'UPDATET', 'SOURCE'],
        // 分区  字典：SOURCE
        PARTITION: ['PARTID', 'WWID', 'DFILE', 'NAME', 'CAP', 'TYPE', 'FSTYPE', 'MPOINT', 'ITOT', 'RWPERM', 'CRTIME', 'CREATET', 'UPDATET', 'SOURCE'],
        // --分区状态  字典：SOURCE
        PARTITION_STATUS: ['PARTID', 'PURATE', 'PWRATE', 'PRRATE', 'PUSED', 'PUNUSED', 'PFRATE', 'PIWAIT', 'PUURATE', 'PIURATE', 'PIUSAGE', 'PIFREE', 'PIFRATE', 'IOERR', 'MTIME'],
        // 进程  字典：SOURCE
        PROCESS: ['PRID', 'EID', 'PPID', 'PFNAME', 'SESSID', 'PID', 'FNAME', 'NAME', 'SCMD', 'SPARAM', 'WDIR', 'STAT', 'USER', 'STIME', 'ETIME', 'QTYPE', 'THNUM', 'FDNUM', 'DPID', 'CREATET', 'UPDATET', 'SOURCE'],
        // --进程资源使用状态
        PROCESS_CPU_USAGE_STATUS: ['PRID', 'FNAME', 'CURATE', 'MURATE', 'MTIME'],
        // 服务  字典：SOURCE
        SERVICE: ['SVID', 'UID', 'UNAME', 'EID', 'FNAME', 'NAME', 'DESC', 'STYPE', 'SELFSSTAT', 'STARTCMD', 'STOPCMD', 'RESCMD', 'STAT', 'CREATET', 'UPDATET', 'SOURCE'],
        // 启动任务  字典：SOURCE
        BOOT: ['BID', 'TYPE', 'UID', 'UNAME', 'BFNAME', 'BFHASH', 'MINUTE', 'HOUR', 'DAY', 'MONTH', 'WEEKDAY', 'CREATET', 'UPDATET', 'SOURCE'],
        // 驱动  字典：SOURCE
        DRIVER: ['DID', 'FID', 'FNAME', 'NAME', 'AUTHOR', 'VER', 'DESC', 'SIG', 'LDSTAT', 'LDTIME', 'CREATET', 'UPDATET', 'SOURCE'],
        // 文件  字典：SOURCE
        FILE: ['FID', 'UID', 'GRPID', 'SPID', 'NAME', 'TYPE', 'CLASS', 'PATH', 'PERM', 'FHASH', 'FSIZE', 'LTFILE', 'CTIME', 'LATIME', 'LMTIME', 'LCTIME', 'INO', 'CREATET', 'UPDATET', 'SOURCE'],
        // 可执行文件  字典：SOURCE FILE
        EXECUTABLE_FILE: ['EID', 'CAPBIL', 'CREATET', 'UPDATET', 'SOURCE'],
        // 软件包  字典：SOURCE
        SOFTWARE_PACKAGE: ['SPID', 'NAME', 'BRAND', 'VER', 'PNAME', 'PTYPE', 'ITIME', 'SIG', 'DESC', 'IPATH', 'CREATET', 'UPDATET', 'SOURCE'],
        // 漏洞扫描
        VULN_SCAN_RESULT: ['HOSTIPSTR', 'VULNID', 'NODENAME', 'SHORTDESC', 'FULLDESC', 'REPAIRADVICE', 'RISKLEVEL', 'PLATFORMS', 'CVETAG', 'CVSSSCORE', 'UPDATE', 'CREATE', 'SOFTWARE', 'VERSION', 'FINDTIME', 'REPAIR'],
        // 基线核查
        CONFIG_CHECK_RESULT: ['CIID', 'IP', 'SUBTYPE', 'CHECKITEMTYPE', 'PARSE', 'ISCHECK', 'CHECKITEMNAME', 'SYSCODE', 'FIXEDPRO', 'JUDGE', 'ITEMCONTENT', 'CTIME'],
        // *应用实例  字典：SOURCE TYPE STYPE  数组：EXECUTABLE_FILE
        APPLICATION: ['APPID', 'NAME', 'BRAND', 'VER', 'TYPE', 'STYPE', 'STIME', 'RSTA', 'MDIPA', 'MDIPB', 'DCDLIST', 'APPDESC', 'CREATET', 'UPDATET', 'SOURCE'],
        // *安全插件  字典：SOURCE *STYPE
        PLUGIN: ['PLUG_ID', 'APPID', 'TYPE', 'NAME', 'BRAND', 'VER', 'STYPE', 'FLIST', 'PSTAT', 'EREASON', 'CREATET', 'UPDATET', 'SOURCE'],
        // --安全插件状态
        PLUGIN_STATUS: ['APPID', 'PLUG_ID', 'CPUR', 'MEMR', 'DISK', 'PRONUM', 'FILENUM', 'PORTNUM', ]
    },
    // 网络
    NETWORK: {
        //  字典： CTYPE  SOURCE
        INFO: ['NETID', 'FWD', 'IPV6', 'FINGER', 'CTYPE', 'CREATET', 'UPDATET', 'SOURCE'],
        // --网络状态  字典： *NERR
        NETWORK_STATUS: ['NETID', 'NERR'],
        // *网络接口  字典：BONDM SOURCE
        NETWORK_INTERFACE: ['IFID', 'ETHID', 'NCID', 'IFNM', 'CLASS', 'LINKSTA', 'SPEED', 'MTU', 'IP', 'MAC', 'DHCP', 'DHCPS', 'BONDM', 'BONDIF', 'LIF', 'IFIDX', 'AUTHS', 'VLIST', 'TYPE', 'MACBIND', 'CREATET', 'UPDATET', 'SOURCE'],
        // --网络接口运行状态  字典：SOURCE
        NETIF_RUNNING_STATUS: ['IFID', 'IN', 'OUT', 'INLOSE', 'OUTLOSE', 'INERR', 'OUTERR', 'RXPKGS', 'TXPKGS', 'RXERR', 'TXERR', 'FRAME', 'CARRIER', 'COLLIS', 'TXQLEN', 'LOST', 'ERR', 'MTIME', 'CREATET', 'UPDATET', 'SOURCE'],
        // 网络路由  字典：SOURCE
        ROUTE: ['RID', 'IFID', 'DSTNET', 'DSTMK', 'NHA', 'TYPE', 'METRIC', 'RPROT', 'CREATET', 'UPDATET', 'SOURCE'],
        // 地址学习表  字典：SOURCE
        ADDRESS: ['IFID', 'VID', 'IP', 'MAC', 'ARPTP', 'CREATET', 'UPDATET', 'SOURCE'],
        // 网络监听  字典：SOURCE
        LISTENNING_PORT: ['LISPORTID', 'IFID', 'PRID', 'PRNAME', 'IP', 'PORT', 'PROTO', 'PROVER', 'CLIN', 'CWNUM', 'TWNUM', 'SRNUM', 'CREATET', 'UPDATET', 'SOURCE'],
        // 网络邻居  字典：SOURCE
        NEIGHBOR: ['NBRID', 'IFID', 'NBRIFID', 'NBRIDX', 'UPTIME', 'CHATYPE', 'CHAID', 'NBRIP', 'PIDTYPE', 'PORTID', 'PORTDESC', 'SYSNM', 'SYSDESC', 'CREATET', 'UPDATET', 'SOURCE'],
        // *VLAN  字典：SOURCE
        VLAN: ['VID', 'VTYPE', 'IP', 'MASK', 'DESCRIPTION', 'CREATET', 'UPDATET', 'SOURCE'],
        // *访问控制策略  字典：SOURCE TYPE
        ACCESS_CONTROL_LIST: ['ACLID', 'IFID', 'ACLNUM', 'TYPE', 'SRCMAC', 'DSTMAC', 'FRTYPE', 'SRCBIP', 'SRCEIP', 'DSTBIP', 'DSTEIP', 'PROTO', 'SRCPORT', 'DSTPORT', 'DIRECTION', 'ACTION', 'SRCNIP', 'DSTNIP', 'SRCNP', 'DSTNP', 'NATPRO', 'DESC', 'ENABLE', 'EFCTIME', 'CREATET', 'UPDATET', 'SOURCE'],
        // 网络设备虚拟路由协议组  字典：SOURCE
        VRRP: ['IFID', 'VRRPID', 'VIP', 'VMASK', 'MASTER', 'CREATET', 'UPDATET', 'SOURCE'],
    },
    ASSET_NODE: ['GID', 'IP', 'MAC', 'IFID', 'SOURCE'],

    INTERCONF: ['GID', 'RMTPORT', 'RMTTP', 'RMTACT', 'RMTPSD', 'SNMPPORT', 'SNMPVER', 'SNMPV3USER', 'SNMPAUTH', 'SNMPENC', 'SNMPREAD', 'SNMPWRITE', 'ANAMETH'],
    MANAGEMENT: ['GID', 'DCLOUDID', 'DCCID', 'STAID', 'FNAME', 'SNAME', 'ATYPE', 'ABRAND', 'AMODEL', 'STATUS', 'ISCII', 'CLASS', 'MTYPE', 'NTTYPE', 'AREA', 'NOTES'],
    APPLICATION: ['GID', 'APPID', 'NAME', 'BRAND', 'VER', 'TYPE', 'STYPE', 'MDIPA', 'MDIPB', 'APPDESC', 'SOURCE', 'ENSTAT', 'ISCARD', 'FBIDAPP']
}
const ENUMS = {
    COMMON: {
        // 数据来源
        SOURCE: {
            [DicDefine_pb.DataSource.DATA_SOURCE_DUMMY]: '手动添加的虚拟值',
            [DicDefine_pb.DataSource.DATA_SOURCE_HOST_MONITORING]: '主机监测',
            [DicDefine_pb.DataSource.DATA_SOURCE_SWITCH]: '交换机',
            [DicDefine_pb.DataSource.DATA_SOURCE_ROUTER]: '路由器',
            [DicDefine_pb.DataSource.DATA_SOURCE_ISOLATION_DEVICE]: '隔离装置',
            [DicDefine_pb.DataSource.DATA_SOURCE_VERTICAL_ENCRYPTION]: '纵向加密',
            [DicDefine_pb.DataSource.DATA_SOURCE_FIREWALL]: '防火墙',
            [DicDefine_pb.DataSource.DATA_SOURCE_MONITORING_DEVICE]: '监测装置',
            [DicDefine_pb.DataSource.DATA_SOURCE_MONITORING_PLATFORM]: '监测平台',
            [DicDefine_pb.DataSource.DATA_SOURCE_OPERATION_GATEWAY]: '运维网关',
            [DicDefine_pb.DataSource.DATA_SOURCE_ASSET_DETECTION]: '资产探测',
            [DicDefine_pb.DataSource.DATA_SOURCE_TRAFFIC_COLLECTION]: '流量采集',
            [DicDefine_pb.DataSource.DATA_SOURCE_PLUGIN]: '插件',
        },
        // 资产类型
        ASSET_TYPE: {
            [DicDefine_pb.AssetType.ASSET_TYPE_DUMMY]: '手动添加的虚拟值',
            [DicDefine_pb.AssetType.ASSET_TYPE_UNKNOWN]: '未知',
            [DicDefine_pb.AssetType.ASSET_TYPE_SERVER]: '服务器',
            [DicDefine_pb.AssetType.ASSET_TYPE_WORKSTATION]: '工作站',
            [DicDefine_pb.AssetType.ASSET_TYPE_STORAGE_DEVICE]: '存储设备',
            [DicDefine_pb.AssetType.ASSET_TYPE_BLADE_CHASSIS]: '刀片服务器机箱',
            [DicDefine_pb.AssetType.ASSET_TYPE_BLADE_SERVER]: '刀片服务器',
            [DicDefine_pb.AssetType.ASSET_TYPE_ROUTER]: '路由器',
            [DicDefine_pb.AssetType.ASSET_TYPE_SWITCH]: '交换机',
            [DicDefine_pb.AssetType.ASSET_TYPE_INDUSTRIAL_SWITCH]: '工业交换机',
            [DicDefine_pb.AssetType.ASSET_TYPE_MEDIA_CONVERTER]: '光电转换器',
            [DicDefine_pb.AssetType.ASSET_TYPE_SERIAL_SERVER]: '串口服务器',
            [DicDefine_pb.AssetType.ASSET_TYPE_LATERAL_ISOLATION]: '横向隔离装置',
            [DicDefine_pb.AssetType.ASSET_TYPE_LONGITUDINAL_ENCRYPTION]: '纵向加密装置',
            [DicDefine_pb.AssetType.ASSET_TYPE_FIREWALL]: '防火墙',
            [DicDefine_pb.AssetType.ASSET_TYPE_IDS]: '入侵检测设备',
            [DicDefine_pb.AssetType.ASSET_TYPE_NET_SEC_MONITOR]: '网络安全监测装置',
            [DicDefine_pb.AssetType.ASSET_TYPE_ENCRYPTION_CARD]: '加密卡',
            [DicDefine_pb.AssetType.ASSET_TYPE_LARGE_SCREEN]: '大屏幕',
            [DicDefine_pb.AssetType.ASSET_TYPE_PRECISION_AC]: '精密空调',
            [DicDefine_pb.AssetType.ASSET_TYPE_KVM]: 'KVM',
            [DicDefine_pb.AssetType.ASSET_TYPE_TIME_SYNC]: '时间同步装置',
            [DicDefine_pb.AssetType.ASSET_TYPE_PRINTER]: '打印机',
            [DicDefine_pb.AssetType.ASSET_TYPE_NETWORK_CABLE]: '网络连接线',
            [DicDefine_pb.AssetType.ASSET_TYPE_MERGING_UNIT]: '合并单元',
            [DicDefine_pb.AssetType.ASSET_TYPE_INTELLIGENT_TERMINAL]: '智能终端',
            [DicDefine_pb.AssetType.ASSET_TYPE_SPECIAL_TELEMETRY_GATEWAY]: '专用远动网关机',
            [DicDefine_pb.AssetType.ASSET_TYPE_TELEMETRY_DEVICE]: '远动装置',
            [DicDefine_pb.AssetType.ASSET_TYPE_MEASUREMENT_CONTROL]: '测控装置',
            [DicDefine_pb.AssetType.ASSET_TYPE_PMU]: '相量测量装置',
            [DicDefine_pb.AssetType.ASSET_TYPE_ENERGY_ACQUISITION]: '电能量采集终端',
            [DicDefine_pb.AssetType.ASSET_TYPE_NETWORK_ANALYZER]: '网络分析仪',
            [DicDefine_pb.AssetType.ASSET_TYPE_SMART_PD_TERMINAL]: '智能配电终端',
            [DicDefine_pb.AssetType.ASSET_TYPE_SMART_PD_MONITORING]: '智能配电测控终端',
            [DicDefine_pb.AssetType.ASSET_TYPE_TRANSFORMER_MONITORING]: '配变监控终端',
            [DicDefine_pb.AssetType.ASSET_TYPE_SMART_PD_SYNC_MEAS]: '智能配电同步测量终端',
            [DicDefine_pb.AssetType.ASSET_TYPE_OS]: '操作系统',
            [DicDefine_pb.AssetType.ASSET_TYPE_DATABASE]: '数据库',
            [DicDefine_pb.AssetType.ASSET_TYPE_MIDDLEWARE]: '中间件',
            [DicDefine_pb.AssetType.ASSET_TYPE_APP_SOFTWARE_PKG]: '应用软件包',
            [DicDefine_pb.AssetType.ASSET_TYPE_APP_SOFTWARE]: '应用软件',
            [DicDefine_pb.AssetType.ASSET_TYPE_APPLICATION]: '应用程序',
            [DicDefine_pb.AssetType.ASSET_TYPE_MALWARE_MONITOR]: '恶意代码监测系统',
            [DicDefine_pb.AssetType.ASSET_TYPE_ANTIVIRUS]: '防病毒',
            [DicDefine_pb.AssetType.ASSET_TYPE_OPS_GATEWAY]: '运维网关',
            [DicDefine_pb.AssetType.ASSET_TYPE_OTHER]: '其他'
        }
    },
    HARDWARE: {
    // 硬件异常字典
    HERR: {
      [DicDefine_pb.HardwareExceptionStatus.HW_EX_DUMMY]: '手动添加的虚拟值',
      [DicDefine_pb.HardwareExceptionStatus.HW_EX_POWER_OVERLOAD]: '电源过载',
      [DicDefine_pb.HardwareExceptionStatus.HW_EX_POWER_CTRL_FAILURE]: '电源控制器异常',
      [DicDefine_pb.HardwareExceptionStatus.HW_EX_MAIN_POWER_FAILURE]: '主电源故障',
      [DicDefine_pb.HardwareExceptionStatus.HW_EX_DISK_ALERT]: '存在磁盘告警',
      [DicDefine_pb.HardwareExceptionStatus.HW_EX_DISK_ABNORMAL]: '硬盘状态异常',
      [DicDefine_pb.HardwareExceptionStatus.HW_EX_MEM_ECC_ERROR]: '内存ECC错误',
      [DicDefine_pb.HardwareExceptionStatus.HW_EX_FAN_FAILURE]: '风扇存在故障',
      [DicDefine_pb.HardwareExceptionStatus.HW_EX_POWER_REDUNDANCY_ALERT]: '电源冗余告警'
    },
    // CPU架构
    ARCH: {
      [DicDefine_pb.CpuArchitecture.CPU_ARCH_UNKNOWN]: '未知',
      [DicDefine_pb.CpuArchitecture.CPU_ARCH_X86]: 'X86',
      [DicDefine_pb.CpuArchitecture.CPU_ARCH_ARM]: 'ARM',
      [DicDefine_pb.CpuArchitecture.CPU_ARCH_POWERPC]: 'POWERPC',
      [DicDefine_pb.CpuArchitecture.CPU_ARCH_RISC_V]: 'RISC-V',
      [DicDefine_pb.CpuArchitecture.CPU_ARCH_MIPS]: 'MIPS',
      [DicDefine_pb.CpuArchitecture.CPU_ARCH_LOONGARCH]: 'LoongArch',
      [DicDefine_pb.CpuArchitecture.CPU_ARCH_OTHER]: '其他'
    },
    // 外设接口类型
    PERIPHERAL_INTERFACE_TYPE: {
      [DicDefine_pb.PeripheralInterfaceType.PERIPHERAL_INTERFACE_DUMMY]: '手动添加的虚拟值',
      [DicDefine_pb.PeripheralInterfaceType.PERIPHERAL_INTERFACE_USB]: 'USB接口',
      [DicDefine_pb.PeripheralInterfaceType.PERIPHERAL_INTERFACE_BLUETOOTH]: '蓝牙模块',
      [DicDefine_pb.PeripheralInterfaceType.PERIPHERAL_INTERFACE_SERIAL]: '串口',
      [DicDefine_pb.PeripheralInterfaceType.PERIPHERAL_INTERFACE_PARALLEL]: '并口',
      [DicDefine_pb.PeripheralInterfaceType.PERIPHERAL_INTERFACE_OPTICAL_DRIVE]: '光驱',
      [DicDefine_pb.PeripheralInterfaceType.PERIPHERAL_INTERFACE_OTHER]: '其他'
    },
    // 外设设备类型
    PERIPHERAL_TYPE: {
      [DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_UNKNOWN]: '未知',
      [DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_STORAGE]: '存储类',
      [DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_COMM]: '通信设备',
      [DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_INPUT]: '键鼠设备',
      [DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_OPTICAL_DISC]: '光盘',
      [DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_DISPLAY]: '显示器',
      [DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_PRINTER]: '打印机',
      [DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_WIRELESS_NIC]: '无线网卡',
      [DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_OTHER]: '其他'
    },
    // 外设接口协议编码
    IFPCODE: {
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_DUMMY]: '手动添加的虚拟值',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_AUDIO]: 'Audio',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_CDC_CTRL]: 'Communications and CDC Control',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_HID]: 'HID(Human Interface Device)',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_PHYSICAL]: 'Physical',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_IMAGE]: 'Image',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_PRINTER]: 'Printer',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_MASS_STORAGE]: 'Mass Storage',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_HUB]: 'Hub',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_CDC_DATA]: 'CDC-Data',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_SMART_CARD]: 'Smart Card',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_CONTENT_SECURITY]: 'Content Security',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_VIDEO]: 'Video',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_HEALTHCARE]: 'Personal Healthcare',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_DIAGNOSTIC]: 'Diagnostic Device',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_WIRELESS_CTRL]: 'Wireless Controller',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_MISCELLANEOUS]: 'Miscellaneous',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_APP_SPECIFIC]: 'Application Specific',
      [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_VENDOR_SPECIFIC]: 'Vendor Specific'
    },
    // 传感器异常
    OTHERERRSTA: {
      [DicDefine_pb.SensorExceptionStatus.SENSOR_EX_DUMMY]: '手动添加的虚拟值',
      [DicDefine_pb.SensorExceptionStatus.SENSOR_EX_FAN_SPEED]: '风扇转速异常',
      [DicDefine_pb.SensorExceptionStatus.SENSOR_EX_TEMPERATURE]: '温度异常'
    }
    },
    SOFTWARE: {
    // 登陆方式
    LMETHOD: {
      [DicDefine_pb.LoginMethod.LOGIN_METHOD_DUMMY]: '手动添加的虚拟值',
      [DicDefine_pb.LoginMethod.LOGIN_METHOD_FTP]: 'FTP',
      [DicDefine_pb.LoginMethod.LOGIN_METHOD_LOCAL]: 'LOCAL',
      [DicDefine_pb.LoginMethod.LOGIN_METHOD_RADIUS]: 'RADIUS',
      [DicDefine_pb.LoginMethod.LOGIN_METHOD_RDP]: 'RDP',
      [DicDefine_pb.LoginMethod.LOGIN_METHOD_SSH]: 'SSH',
      [DicDefine_pb.LoginMethod.LOGIN_METHOD_TELNET]: 'TELNET',
      [DicDefine_pb.LoginMethod.LOGIN_METHOD_TFTP]: 'TFTP',
      [DicDefine_pb.LoginMethod.LOGIN_METHOD_VNC]: 'VNC',
      [DicDefine_pb.LoginMethod.LOGIN_METHOD_X11]: 'X11',
      [DicDefine_pb.LoginMethod.LOGIN_METHOD_SERIAL]: '串口',
      [DicDefine_pb.LoginMethod.LOGIN_METHOD_OTHER]: '其他'
    },
    // 应用类型
    APPLICATION_TYPE: {
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_DUMMY]: '手动添加的虚拟值',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_VERTICAL_ENCRYPTION]: '纵向加密应用',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_HORIZONTAL_ISOLATION]: '横向隔离应用',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_FIREWALL]: '防火墙应用',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_SWITCH]: '交换机应用',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_ROUTER]: '路由器应用',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_HOST_MONITORING]: '主机监测应用',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_TRUSTED_VERIFICATION]: '可信验证应用',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_INTRUSION_DETECTION]: '入侵检测应用',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_OPERATION_GATEWAY]: '运维网关应用',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_MALICIOUS_CODE_MONITORING]: '恶意代码监测应用',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_MONITORING_DEVICE]: '监测装置应用',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_DATABASE]: '数据库应用',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_CRYPTOGRAPHIC_SERVER]: '服务器密码机应用',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_SECURITY_MONITORING_SYSTEM]: '安全监测系统应用',
      [DicDefine_pb.ApplicationType.APPLICATION_TYPE_OTHER]: '其他应用'
    },
    // 应用子类型
    APPLICATION_STYPE: {
      // 纵向加密
      1: {'VEAD': '纵向加密装置', 'CARD': '纵向加密卡', 'VEAD-MINI': '微型纵向加密装置'},
      // 横向隔离
      2: {'FID': '正向隔离装置', 'BID': '反向隔离装置', 'TS': '隔离传输软件'},
      // 防火墙
      3: {'FW': '防火墙', 'WAF': 'WEB防火墙'},
      // 交换机
      4: {'SW-MAIN': '局域网核心交换机', 'SW-FRONT': '调度数据网交换机', 'SW-PT': '局域网接入交换机'},
      // 路由器
      5: {'RT': '路由器'},
      // 主机监测
      6: {'SVR-PT': '服务器', 'SVR-PC': '工作站'},
      // 可信验证
      7: {'TVM': '主机可信验证'},
      // 入侵检测
      8: {'IDS': '入侵监测系统', 'IPS': '入侵防御系统'},
      // 运维网关
      9: {'SASH': '主站运维网关', 'PMG': '便携式运维网关'},
      // 恶意代码监测
      10: {'ACC': '防恶意代码客户端', 'MCMS': '恶意代码监测', 'AV': '防病毒', 'FMCD': '恶意代码网络流量监测采集'},
      // 监测装置
      11: {'DCD-Ⅰ': 'Ⅰ型监测装置', 'DCD-Ⅱ': 'Ⅱ型监测装置', 'DCD-H': '高性能监测装置', 'DCD-N': '普通版监测装置'},
      // 数据库
      12: {'DB': '数据库'},
      // 服务器密码机
      13: {'CS': '密码机', 'CS-CARD': '密码卡'},
      // 安全监测系统
      14: {'MP': '调度安全监测平台', 'DMP': '配电安全监测平台'},
      // 其他应用
      99: {'OTHER': '其他'}
    }
  },
    NETWORK: {
    // 资产用途
    CTYPE: {
      [DicDefine_pb.AssetPurpose.ASSET_PURPOSE_DUMMY]: '手动添加的虚拟值',
      [DicDefine_pb.AssetPurpose.ASSET_PURPOSE_GATEWAY]: '网关',
      [DicDefine_pb.AssetPurpose.ASSET_PURPOSE_ROUTING]: '路由',
      [DicDefine_pb.AssetPurpose.ASSET_PURPOSE_SWITCHING]: '交换',
      [DicDefine_pb.AssetPurpose.ASSET_PURPOSE_LONGITUDINAL]: '纵向',
      [DicDefine_pb.AssetPurpose.ASSET_PURPOSE_ISOLATION]: '隔离',
      [DicDefine_pb.AssetPurpose.ASSET_PURPOSE_FIREWALL]: '防火墙'
    },
    // 接口绑定模式
    BONDM: {
      [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_DUMMY]: '手动添加的虚拟值',
      [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_DISABLED]: '未启用',
      [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_0]: 'Mode 0',
      [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_1]: 'Mode 1',
      [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_2]: 'Mode 2',
      [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_3]: 'Mode 3',
      [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_4]: 'Mode 4',
      [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_5]: 'Mode 5',
      [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_6]: 'Mode 6',
      [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_BAGG]: 'BAGG',
      [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_LAGG]: 'LAGG'
    },
    // 策略类型字典
    ACCESS_CONTROL_LIST_TYPE: {
      [DicDefine_pb.PolicyType.POLICY_TYPE_DUMMY]: '手动添加的虚拟值',
      [DicDefine_pb.PolicyType.POLICY_TYPE_FIREWALL]: '防火墙策略',
      [DicDefine_pb.PolicyType.POLICY_TYPE_LONGITUDINAL_ENCRYPT]: '纵向加密策略',
      [DicDefine_pb.PolicyType.POLICY_TYPE_ISOLATION_DEVICE]: '隔离设备策略',
      [DicDefine_pb.PolicyType.POLICY_TYPE_SWITCH]: '交换机策略',
      [DicDefine_pb.PolicyType.POLICY_TYPE_ROUTER]: '路由器策略',
      [DicDefine_pb.PolicyType.POLICY_TYPE_OTHER]: '其他'
    }
  },
    MANAGEMENT: {
        // 资产投运字典
        STATUS: {
            [DicDefine_pb.AssetOperationStatus.ASSET_OPERATION_DUMMY]: '手动添加的虚拟值',
            [DicDefine_pb.AssetOperationStatus.ASSET_OPERATION_PRE_ACCESS]: '预接入',
            [DicDefine_pb.AssetOperationStatus.ASSET_OPERATION_IN_SERVICE]: '在运',
            [DicDefine_pb.AssetOperationStatus.ASSET_OPERATION_MAINTENANCE]: '检修',
            [DicDefine_pb.AssetOperationStatus.ASSET_OPERATION_DEACTIVATED]: '退运'
        },
        // 网络类型
        NTTYPE: {
            [DicDefine_pb.NetworkType.NETWORK_TYPE_DUMMY]: '手动添加的虚拟值',
            [DicDefine_pb.NetworkType.NETWORK_TYPE_BACKBONE_PLANE1]: '骨干网一平面',
            [DicDefine_pb.NetworkType.NETWORK_TYPE_BACKBONE_PLANE2]: '骨干网二平面',
            [DicDefine_pb.NetworkType.NETWORK_TYPE_NATIONAL_DISPATCH_ACCESS]: '国调接入网',
            [DicDefine_pb.NetworkType.NETWORK_TYPE_REGIONAL_DISPATCH_ACCESS]: '网调接入网',
            [DicDefine_pb.NetworkType.NETWORK_TYPE_PROVINCIAL_ACCESS]: '省级接入网',
            [DicDefine_pb.NetworkType.NETWORK_TYPE_PREFECTURAL_ACCESS]: '地级接入网',
            [DicDefine_pb.NetworkType.NETWORK_TYPE_DISPATCH_INTRANET]: '调度内网',
            [DicDefine_pb.NetworkType.NETWORK_TYPE_DISPATCH_INTRANET_B]: '调度内网B'
        },
        // 安全区
        AREA: {
            [DicDefine_pb.SecurityZone.SECURITY_ZONE_UNKNOWN]: '未知',
            [DicDefine_pb.SecurityZone.SECURITY_ZONE_I]: 'I区',
            [DicDefine_pb.SecurityZone.SECURITY_ZONE_II]: 'II区',
            [DicDefine_pb.SecurityZone.SECURITY_ZONE_III]: 'Ⅲ区',
            [DicDefine_pb.SecurityZone.SECURITY_ZONE_IV]: 'IV区',
            [DicDefine_pb.SecurityZone.SECURITY_ZONE_I_II]: 'I-II区',
            [DicDefine_pb.SecurityZone.SECURITY_ZONE_PROD_CTRL_MGMT]: '生产控制一管理信息大区',
            [DicDefine_pb.SecurityZone.SECURITY_ZONE_II_III]: 'II-III区',
            [DicDefine_pb.SecurityZone.SECURITY_ZONE_III_IV]: 'III-IV区',
            [DicDefine_pb.SecurityZone.SECURITY_ZONE_SEC_ACCESS]: '安全接入区',
            [DicDefine_pb.SecurityZone.SECURITY_ZONE_OTHER]: '其他'
        }
    },
    APPLICATION: {
        // 应用类型
        TYPE: {
            [DicDefine_pb.ApplicationType.APPLICATION_TYPE_DUMMY]: '手动添加的虚拟值',
            [DicDefine_pb.ApplicationType.APPLICATION_TYPE_VERTICAL_ENCRYPTION]: '纵向加密应用',
            [DicDefine_pb.ApplicationType.APPLICATION_TYPE_HORIZONTAL_ISOLATION]: '横向隔离应用',
            [DicDefine_pb.ApplicationType.APPLICATION_TYPE_FIREWALL]: '防火墙应用',
            [DicDefine_pb.ApplicationType.APPLICATION_TYPE_SWITCH]: '交换机应用',
            [DicDefine_pb.ApplicationType.APPLICATION_TYPE_ROUTER]: '路由器应用',
            [DicDefine_pb.ApplicationType.APPLICATION_TYPE_HOST_MONITORING]: '主机监测应用',
            [DicDefine_pb.ApplicationType.APPLICATION_TYPE_TRUSTED_VERIFICATION]: '可信验证应用',
            [DicDefine_pb.ApplicationType.APPLICATION_TYPE_INTRUSION_DETECTION]: '入侵检测应用',
            [DicDefine_pb.ApplicationType.APPLICATION_TYPE_OPERATION_GATEWAY]: '运维网关应用',
            [DicDefine_pb.ApplicationType.APPLICATION_TYPE_MALICIOUS_CODE_MONITORING]: '恶意代码监测应用',
            [DicDefine_pb.ApplicationType.APPLICATION_TYPE_MONITORING_DEVICE]: '监测装置应用',
            [DicDefine_pb.ApplicationType.APPLICATION_TYPE_DATABASE]: '数据库应用',
            [DicDefine_pb.ApplicationType.APPLICATION_TYPE_CRYPTOGRAPHIC_SERVER]: '服务器密码机应用',
            [DicDefine_pb.ApplicationType.APPLICATION_TYPE_SECURITY_MONITORING_SYSTEM]: '安全监测系统应用',
            [DicDefine_pb.ApplicationType.APPLICATION_TYPE_OTHER]: '其他应用'
        },
        // 应用子类型
        STYPE: {
            // 纵向加密
            1: {'VEAD': '纵向加密装置', 'CARD': '纵向加密卡', 'VEAD-MINI': '微型纵向加密装置'},
            // 横向隔离
            2: {'FID': '正向隔离装置', 'BID': '反向隔离装置', 'TS': '隔离传输软件'},
            // 防火墙
            3: {'FW': '防火墙', 'WAF': 'WEB防火墙'},
            // 交换机
            4: {'SW-MAIN': '局域网核心交换机', 'SW-FRONT': '调度数据网交换机', 'SW-PT': '局域网接入交换机'},
            // 路由器
            5: {'RT': '路由器'},
            // 主机监测
            6: {'SVR-PT': '服务器', 'SVR-PC': '工作站'},
            // 可信验证
            7: {'TVM': '主机可信验证'},
            // 入侵检测
            8: {'IDS': '入侵监测系统', 'IPS': '入侵防御系统'},
            // 运维网关
            9: {'SASH': '主站运维网关', 'PMG': '便携式运维网关'},
            // 恶意代码监测
            10: {'ACC': '防恶意代码客户端', 'MCMS': '恶意代码监测', 'AV': '防病毒', 'FMCD': '恶意代码网络流量监测采集'},
            // 监测装置
            11: {'DCD-Ⅰ': 'Ⅰ型监测装置', 'DCD-Ⅱ': 'Ⅱ型监测装置', 'DCD-H': '高性能监测装置', 'DCD-N': '普通版监测装置'},
            // 数据库
            12: {'DB': '数据库'},
            // 服务器密码机
            13: {'CS': '密码机', 'CS-CARD': '密码卡'},
            // 安全监测系统
            14: {'MP': '调度安全监测平台', 'DMP': '配电安全监测平台'},
            // 其他应用
            99: {'OTHER': '其他'}
        }
    },
}
function getShowNameByMapWithValue(Enums, value) {
    if ([undefined, null, ''].includes(value)) return '--'
    if (Enums && Object.keys(Enums).length) {
        if (value in Enums)
            return Enums[value]
    } else {
        return '--'
    }
}
// 解构数据 并且配置 字典
function formatDataFn(msg) {
    console.log('蛋蛋')
    const data = []
    const addInfoList = msg.getAddinfoList();
    let ASSET_PB = DCDParamDefine_pb.AssetConfig
    const getArrayDataByBufDataAndTemp = util.getArrayDataByBufDataAndTemp // 遍历模板取对应值
    addInfoList.forEach(anyItem => {
        try {
            const dataItem = { ASSET: {}, ASSET_NODE: [], INTERCONF: [], MANAGEMENT: [], APPLICATION: [] }
            const bytes = anyItem.getValue(); // 获取Any中的二进制数据
            const deAnyItem = ASSET_PB.deserializeBinary(bytes) // 反序列化
            console.log('addInfoList',anyItem)
            // 资产
            const ASSET_BUF = util.getProtobufFieldValue(deAnyItem, 'ASSET')
            if (ASSET_BUF) {
                dataItem['ASSET'] = util.getArrayDataByBufDataAndTemp(ASSET_BUF, DATA_TEMP.ASSET)
                dataItem['ASSET']['TYPE'] = getShowNameByMapWithValue(ENUMS.COMMON.ASSET_TYPE, dataItem['ASSET']['TYPE'])
                dataItem['ASSET']['SOURCE'] = getShowNameByMapWithValue(ENUMS.COMMON.SOURCE, dataItem['ASSET']['SOURCE'])
            }
            const ASSET_NODE_BUF = util.getProtobufFieldValue(deAnyItem, 'ASSET_NODE', true)
            ASSET_NODE_BUF.forEach(bufItem => {
                const itemData = util.getArrayDataByBufDataAndTemp(bufItem, DATA_TEMP.ASSET_NODE)
                itemData['SOURCE'] = getShowNameByMapWithValue(ENUMS.COMMON.SOURCE, itemData['SOURCE'])
                dataItem['ASSET_NODE'].push(itemData)

            }) // 资产节点
            const INTERCONF_BUF = util.getProtobufFieldValue(deAnyItem, 'INTERCONF', true)
            INTERCONF_BUF.forEach(bufItem => {
                const itemData = util.getArrayDataByBufDataAndTemp(bufItem, DATA_TEMP.INTERCONF)
                dataItem['INTERCONF'].push(itemData)
            }) // 交互配置
            const MANAGEMENT_BUF = util.getProtobufFieldValue(deAnyItem, 'MANAGEMENT', true)
            MANAGEMENT_BUF.forEach(bufItem => {
                const itemData = util.getArrayDataByBufDataAndTemp(bufItem, DATA_TEMP.MANAGEMENT)
                itemData['ATYPE'] = getShowNameByMapWithValue(ENUMS.COMMON.ASSET_TYPE, itemData['ATYPE'])
                itemData['STATUS'] = getShowNameByMapWithValue(ENUMS.MANAGEMENT.STATUS, itemData['STATUS'])
                itemData['NTTYPE'] = getShowNameByMapWithValue(ENUMS.MANAGEMENT.NTTYPE, itemData['NTTYPE'])
                itemData['AREA'] = getShowNameByMapWithValue(ENUMS.MANAGEMENT.AREA, itemData['AREA'])
                dataItem['MANAGEMENT'].push(itemData)
            }) // 管理
            const APPLICATION_BUF = util.getProtobufFieldValue(deAnyItem, 'APPLICATION', true)
            APPLICATION_BUF.forEach(bufItem => {
                const itemData = util.getArrayDataByBufDataAndTemp(bufItem, DATA_TEMP.APPLICATION)
                itemData['STYPE'] = itemData['TYPE'] && itemData['STYPE'] ? ENUMS.APPLICATION.STYPE[itemData['TYPE']][itemData['STYPE']] : itemData['STYPE']
                itemData['TYPE'] = getShowNameByMapWithValue(ENUMS.APPLICATION.TYPE, itemData['TYPE'])
                itemData['SOURCE'] = getShowNameByMapWithValue(ENUMS.COMMON.SOURCE, itemData['SOURCE'])
                dataItem['APPLICATION'].push(itemData)
            }) // 应用
            data.push(dataItem)
        } catch (e) {
            console.log('formatDataFn: ERR', e)
        }
    })
    return data
}
// 格式化树
function formatTreeDataFn(msg) {
    console.log('111222')
    const data = []
    const addInfoList = msg.getAddinfoList();
    let ASSET_PB = DCDParamDefine_pb.AssetConfig
    addInfoList.forEach(anyItem => {
        try {
            const bytes = anyItem.getValue(); // 获取Any中的二进制数据
            const deAnyItem = ASSET_PB.deserializeBinary(bytes) // 反序列化
            const ASSET_BUF = util.getProtobufFieldValue(deAnyItem, 'ASSET') // 取资产
            if (ASSET_BUF) {
                const items = util.getArrayDataByBufDataAndTemp(ASSET_BUF, DATA_TEMP.ASSET)
                data.push({
                    label: items.DNAME,
                    value: items.GID,
                    item: items,
                    children: [
                        { label: '硬件', value: Number(AssetModelCodeDefine_pb.HardwareAssetModelCode.HARDWARE), children: null },
                        { label: '软件', value: Number(AssetModelCodeDefine_pb.SoftwareAssetModelCode.SOFTWARE), children: null },
                        { label: '网络', value: Number(AssetModelCodeDefine_pb.NetworkAssetModelCode.NETWORK), children: null }
                    ]
                })
            }
        } catch (e) {

        }
    })
    return data
}
function handleNodeClick(clickData, assetData = []) {
    // console.log('点击事件', clickData.value, assetData, String(clickData.value).split('-'))
    // // 切割
    // const splitArr = String(clickData.value).split('-')
    // if (splitArr.length === 1) {
    //     let content = cloneDeep(assetData[Number(splitArr[0])])
    //     const header = cloneDeep(assetData[Number(splitArr[0])].ASSET)
    //     delete content.ASSET
    //     content = [content]
    //     return {
    //         headerData: header,
    //         contentData: content,
    //     }
    // }
    // if (splitArr.length === 2) {
    //     const header  = assetData[Number(splitArr[0])].ASSET
    //     const content = assetData[Number(splitArr[0])][splitArr[1]]
    //     return {
    //         headerData: header,
    //         contentData: [{  [splitArr[1]]: content }],
    //     }
    // }
    console.log('点击', clickData)
    tcpQueryNode(clickData.item)
}
tcpInstance.onMessage(util.genServerMsgKey(
    PlatformProxyServer_pb.FunctionType.FunctionType_DataQuery,
    PlatformProxyServer_pb.DataQueryPacketType.DATAQUERYPACKETTYPE_ASSET
), (pb) => {
    console.log('返回', pb)
})
function tcpQueryNode(data) {
    const msgPayload = {
        uuid: util.genTaskId(),
        CONAST: data.CONAST,
        assetId: data.GID,
        modelType: []
    }
    let sendMsg = JSON.stringify(msgPayload)
    console.log('send => ', sendMsg, 'func:', PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_DATAQUERY, PlatformProxyServer_pb.DataQueryPacketType.DATAQUERYPACKETTYPE_ASSET);
    // 配置管理,参数配置
    tcpInstance.sendJsonMsg(
        PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_DATAQUERY,
        PlatformProxyServer_pb.DataQueryPacketType.DATAQUERYPACKETTYPE_ASSET,
        sendMsg).then();
}
function ArrayWithLengthValidateFn(arr) {
    return Array.isArray(arr) && arr.length
}
module.exports = {
    MAP2CHINA,
    // 数据模板
    DATA_TEMP,
    // 字典
    ENUMS,
    // 解构返回数据
    formatDataFn,
    // 配置树
    formatTreeDataFn,
    // 点击事件
    handleNodeClick,
    ArrayWithLengthValidateFn
}
/**
 [{
 ASSET: {
 GID: '730232010000000001',
 ATAG: 'temp31222NRFCMD0001',
 DSN: 'unidxxxxxxxxxxxxx',
 DNAME: '装置名',
 TYPE: '7305',
 LABEL: '',
 SOURCE: 1
 },
 ASSET_NODE: [{
 IP: '************',
 MAC: '48:7b:6b:39:83:d3',
 IFID: 'eth1',
 SOURCE: 1
 }],
 INTERCONF: [{
 RMTPORT: '22',
 RMTTP: '0',
 RMTACT: 'user',
 RMTPSD: 'password',
 SNMPPORT: '161',
 SNMPVER: '1',
 SNMPV3USER: 'snmpv3',
 SNMPAUTH: '1',
 SNMPENC: '1',
 SNMPREAD: 'public',
 SNMPWRITE: 'private',
 ANAMETH: '0'
 }],
 MANAGEMENT: [{
 DCLOUDID: '070199010600000001',
 FNAME: '河北.调度主站.II 区_FES1',
 SNAME: 'II 区_FES1',
 ATYPE: '7101',
 ABRAND: '北京科东',
 AMODEL: 'PSNTA-5000-ZK',
 STATUS: '1001',
 ISCII: true,
 CLASS: 1,
 MTYPE: true,
 NTTYPE: 1,
 AREA: '1',
 NOTE: ''
 }],
 APPLICATION: [{
 APPID: 'P001',
 NAME: 'veadcard',
 BRAND: 'KEDONG',
 VER: '5.3.3',
 TYPE: 1,
 STYPE: 'VEAD',
 MDIPA: '************',
 MDIPB: '***********',
 APPDESC: 'vead',
 SOURCE: 1
 }]
 }]
 * */
