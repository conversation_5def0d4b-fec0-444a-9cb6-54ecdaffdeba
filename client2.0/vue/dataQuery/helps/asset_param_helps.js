const DicDefine_pb = require('../../../script/pb/DicDefine_pb.js');
const ASSET_ENUMS = require('../../ASSET_MAP/ASSET_ENUMS')
const ASSET_RULES = require('../../ASSET_MAP/ASSET_FORM_RULES')

const formItemRules = {
    GID: ASSET_RULES.InputSize18Rule('', true),
    ATAG: ASSET_RULES.InputSize18Rule('', true),
    SNMPREAD: ASSET_RULES.InputSize20Rule('', false),
    SNMPWRITE: ASSET_RULES.InputSize20Rule('', false),
    SNMPV3USER: ASSET_RULES.InputSize20Rule('', false),
    RMTPSD: ASSET_RULES.InputSize20Rule('', false),
    RMTACT: ASSET_RULES.InputSize20Rule('', false),
    SOURCE: ASSET_RULES.requiredSelectRule(),
    IP: ASSET_RULES.ipValidateRule('', false),
    MDIPA: ASSET_RULES.ipValidateRule('', false),
    MDIPB: ASSET_RULES.ipValidateRule('', false),
    MAC: ASSET_RULES.macValidateRule('', false),
    TYPE: ASSET_RULES.requiredSelectRule(),
    STYPE: ASSET_RULES.requiredSelectRule(),
    FNAME: ASSET_RULES.requiredInputRule(),
    SNAME: ASSET_RULES.requiredInputRule(),
    APPID: ASSET_RULES.requiredInputRule(),
    NAME: ASSET_RULES.requiredInputRule(),
    STATUS: ASSET_RULES.requiredSelectRule(),
    RMTPORT: ASSET_RULES.portValidateRule('', false),
    SNMPPORT: ASSET_RULES.portValidateRule('', false)
}
const formItemMap = {
    // 资产
    ASSET: {
        GID: { type: 'INPUT', label: '资产ID' },
        ATAG: { type: 'INPUT', label: '资产标识' },
        DNAME: { type: 'INPUT', label: '设备名称' },
        TYPE: { type: 'SELECT', label: '资产类型', options: ASSET_ENUMS.getEnumByMap(ASSET_ENUMS.COMMON.ASSET_TYPE, true) },
        LABEL: { type: 'INPUT', label: '资产标签集' },
        SOURCE: { type: 'SELECT', label: '数据来源', options: ASSET_ENUMS.getEnumByMap(ASSET_ENUMS.COMMON.SOURCE, true) }
    },
    // [{ code: '', name: '' }, { code: '', name: '' }, { code: '', name: '' }]
    // 资产节点
    ASSET_NODE: {
        // NDMTYPE: { type: 'SELECT', label: '管理类型', options: [{ code: 1, name: '添加' }, { code: 2, name: '修改' }, { code: 3, name: '删除' }] },
        IP: { type: 'INPUT', label: 'IP' },
        MAC: { type: 'INPUT', label: 'MAC' },
        IFID: { type: 'INPUT', label: '*网络接口ID' },
        SOURCE: { type: 'SELECT', label: '数据来源', options: ASSET_ENUMS.getEnumByMap(ASSET_ENUMS.COMMON.SOURCE, true) },
    },
    // 交互配置
    INTERCONF: {
        RMTPORT: { type: 'INPUT_NUMBER', label: 'SSH远程登录端口' },
        RMTTP: { type: 'SELECT', label: 'SSH远程方式', options: [{ code: 0, name: 'SSH' }] },
        RMTACT: { type: 'INPUT', label: '远程账户' },
        RMTPSD: { type: 'INPUT', label: '远程账户密码' },
        SNMPPORT: { type: 'INPUT_NUMBER', label: 'SNMP监听端口' },
        SNMPVER: { type: 'SELECT', label: 'SNMP版本', options: [{ code: 0, name: 'V2C' }, { code: 1, name: 'V3' }] },
        SNMPV3USER: { type: 'INPUT', label: 'SNMPv3用户名' },
        SNMPAUTH: { type: 'SELECT', label: 'SNMP认证算法', options: [{ code: 0, name: '不启用' }, { code: 1, name: 'MD5' }, { code: 2, name: 'SHA' }] },
        SNMPENC: { type: 'SELECT', label: 'SNMP加密算法', options: [{ code: 0, name: '不启用' }, { code: 1, name: 'DES' }, { code: 2, name: 'AES' }] },
        SNMPREAD: { type: 'INPUT', label: 'snmp read团体名/认证密码' },
        SNMPWRITE: { type: 'INPUT', label: 'snmp write团体名/加密密码' },
        ANAMETH: { type: 'SELECT', label: '解析方法', options: [{ code: 0, name: '正常解析' }, { code: 1, name: '正则解析' }] },
        STATUSUPFLAG: { type: 'SELECT', label: '资产软硬件状态', options: [{ code: 0, name: '关闭' }, { code: 1, name: '开启' }] },
    },
    // 管理
    MANAGEMENT: {
        DCLOUDID: { type: 'INPUT', label: '调控云资产标识' },
        DCCID: { type: 'INPUT', label: '所属调度标识' },
        STAID: { type: 'INPUT', label: '所属区域标识' },
        FNAME: { type: 'INPUT', label: '资产全称' },
        SNAME: { type: 'INPUT', label: '资产简称' },
        ATYPE: { type: 'SELECT', label: '资产类型', options: ASSET_ENUMS.getEnumByMap(ASSET_ENUMS.COMMON.ASSET_TYPE, true) },
        ABRAND: { type: 'INPUT', label: '资产厂商' },
        AMODEL: { type: 'INPUT', label: '资产型号' },
        STATUS: { type: 'SELECT', label: '运行状态', options: ASSET_ENUMS.getEnumByMap(ASSET_ENUMS.MANAGEMENT.MANAGEMENT_STATUS, true) },
        ISCII: { type: 'SWITCH', label: '关基设施' },
        CLASS: { type: 'SELECT', label: '资产类别', options: [{ code: 1, name: '长期运行资产' }, { code: 2, name: '临时接入资产' }, { code: 3, name: '允许临时关闭的资产' }] },
        MTYPE: { type: 'SWITCH', label: '管辖类型' },
        NTTYPE: { type: 'SELECT', label: '网络类型', options: ASSET_ENUMS.getEnumByMap(ASSET_ENUMS.MANAGEMENT.NET_TYPE, true) },
        AREA: { type: 'SELECT', label: '安全区', options: ASSET_ENUMS.getEnumByMap(ASSET_ENUMS.MANAGEMENT.SECURITY_ZONE, true) },
        NOTES: { type: 'INPUT', label: '备注' },
    },
    // 应用
    APPLICATION: {
        // APPMTYPE: { type: 'SELECT', label: '管理类型', options: [{ code: 1, name: '添加' }, { code: 2, name: '修改' }, { code: 3, name: '删除' }] },
        APPID: { type: 'INPUT', label: '*应用ID' },
        NAME: { type: 'INPUT', label: '*应用名称' },
        BRAND: { type: 'INPUT', label: '品牌' },
        VER: { type: 'INPUT', label: '版本号' },
        TYPE: { type: 'SELECT', label: '*应用类型', options: ASSET_ENUMS.getEnumByMap(ASSET_ENUMS.SOFTWARE.APPLICATION_TYPE, true) },
        STYPE: { type: 'SELECT', label: '*子类型', options: [] },
        MDIPA: { type: 'INPUT', label: '主IP地址A' },
        MDIPB: { type: 'INPUT', label: '主IP地址B' },
        APPDESC: { type: 'INPUT', label: '应用描述' },
        SOURCE: { type: 'SELECT', label: '*数据来源', options: ASSET_ENUMS.getEnumByMap(ASSET_ENUMS.COMMON.SOURCE, true) },
        ENSTAT: { type: 'SWITCH', label: '启用状态' }
    }
}

module.exports = {
    formItemMap,
    formItemRules
}