const BehaviorCodeDefine_pb = require('../../../script/pb/BehaviorCodeDefine_pb.js');
const AssetModelCodeDefine_pb = require('../../../script/pb/AssetModelCodeDefine_pb.js');
const AssetModelDefine_pb = require('../../../script/pb/AssetModelDefine_pb.js');
const ASSET_MAP_HELPS = require('../../ASSET_MAP/ASSET_MAP_HELPS');
const ASSET_MODEL = require("./asset_model_helps");
const HARDWARE = {
    // 磁盘增删 257
    [BehaviorCodeDefine_pb.HardwareBehaviorCode.DISK_ADD_DEL]: {
        name: '磁盘增删',
            ACTION_MAP: { 0: '增加', 1: '删除' }, // 动作类型
        AUTH: ['OBJ'], // SUB: 主体 OBJ:客体
            OBJ: {
            name: 'DISK',
                MAP: ASSET_MAP_HELPS.DISK,
                PROTO: AssetModelDefine_pb.MSG_DISK
        }
    },
    // 磁盘变更
    [BehaviorCodeDefine_pb.HardwareBehaviorCode.DISK_CHANGE]: {
        name: '磁盘变更',
            ACTION_MAP: { 0: 'MBR 变更' }, // 动作类型
        AUTH: ['OBJ'], // SUB: 主体 OBJ:客体
        OBJ: {
            name: 'DISK',
            MAP: ASSET_MAP_HELPS.DISK,
            PROTO: AssetModelDefine_pb.MSG_DISK,
        }
    },
    // 有线网卡增删
    [BehaviorCodeDefine_pb.HardwareBehaviorCode.NC_ADD_DEL]: {
        name: '有线网卡增删',
            ACTION_MAP: { 0: '增加', 1: '删除' }, // 动作类型
        AUTH: ['OBJ'], // SUB: 主体 OBJ:客体
            OBJ: {
            name: 'NETWORK_CARD',
                MAP: ASSET_MAP_HELPS.NETWORK_CARD,
                PROTO: AssetModelCodeDefine_pb.MSG_NETWORK_CARD,
        }
    },
    // 无线网卡增删
    [BehaviorCodeDefine_pb.HardwareBehaviorCode.WIRELESS_ADD_DEL]: {
        name: '无线网卡增删',
            ACTION_MAP: { 0: '增加', 1: '删除' }, // 动作类型
        AUTH: ['OBJ'], // SUB: 主体 OBJ:客体
            OBJ: {
            name: 'WIRELESS',
                MAP: ASSET_MAP_HELPS.WIRELESS,
                PROTO: AssetModelCodeDefine_pb.MSG_WIRELESS,
        }
    },
    // 外设接口增删
    [BehaviorCodeDefine_pb.HardwareBehaviorCode.PERI_INT_ADD_DEL]: {
        name: '外设接口增删',
            ACTION_MAP: { 0: '增加', 1: '删除' }, // 动作类型
        AUTH: ['OBJ'], // SUB: 主体 OBJ:客体
            OBJ: {
            name: 'PERIPHERAL_INTERFACE',
                MAP: ASSET_MAP_HELPS.PERIPHERAL_INTERFACE,
                PROTO: AssetModelCodeDefine_pb.MSG_PERIPHERAL_INTERFACE,
        }
    },
    // 外设接口启用禁用
    [BehaviorCodeDefine_pb.HardwareBehaviorCode.PERI_INT_DISABLE_ENABLE]: {
        name: '外设接口启用禁用',
            ACTION_MAP: { 0: '启用', 1: '禁用' }, // 动作类型
        AUTH: ['OBJ'], // SUB: 主体 OBJ:客体
            OBJ: {
            name: 'PERIPHERAL_INTERFACE',
                MAP: ASSET_MAP_HELPS.PERIPHERAL_INTERFACE,
                PROTO: AssetModelCodeDefine_pb.MSG_PERIPHERAL_INTERFACE,
        }
    },
    // 外设接入拔出
    [BehaviorCodeDefine_pb.HardwareBehaviorCode.PERI_CONNECT_UNPLUG]: {
        name: '外设接入拔出',
            ACTION_MAP: { 0: '接入', 1: '拔出' }, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
            OBJ: {
            name: 'PERIPHERAL',
                MAP: ASSET_MAP_HELPS.PERIPHERAL,
                PROTO: AssetModelCodeDefine_pb.MSG_PERIPHERAL,
        },
        SUB: {
            name: 'PERIPHERAL_INTERFACE',
                MAP: ASSET_MAP_HELPS.PERIPHERAL_INTERFACE,
                PROTO: AssetModelCodeDefine_pb.MSG_PERIPHERAL_INTERFACE,
        }
    },
    // 物理网口通断
    [BehaviorCodeDefine_pb.HardwareBehaviorCode.NET_INT_CONN_DISCONN]: {
        name: '物理网口通断',
            ACTION_MAP: { 0: 'LINK-UP', 1: 'LINK-DOWN' }, // 动作类型
        AUTH: ['SUB'], // SUB: 主体 OBJ:客体
            SUB: {
            name: 'ETHERNET',
                MAP: ASSET_MAP_HELPS.ETHERNET,
                PROTO: AssetModelCodeDefine_pb.MSG_ETHERNET,
        }
    },
}
const SOFTWARE = {
    // 系统变更
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.SOFTWARE_CHANGE]: {
        name: '系统变更',
        ACTION_MAP: {  1: '系统版本变更', 2: '内核信息变更', 4: '主机名变更', 8: '启动级别变更', 16: '系统环境变量变更', 32: '本地域名变更', 64: '系统最大线程数变更', 128: '时区变更', 256: '系统最大进程数变更', 1024: '系统最大句柄数变更' }, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'PROCESS',
            MAP: ASSET_MAP_HELPS.PROCESS,
            PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'SOFTWARE',
            MAP: ASSET_MAP_HELPS.SOFTWARE,
            PROTO: AssetModelCodeDefine_pb.MSG_SOFTWARE,
        }
    },
    // 系统配置开关
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.SOFTWARE_CONFIG_SWITCH]: {
        name: '系统配置开关',
        ACTION_MAP: { 0: 'USB存储功能启用', 1: 'USB存储功能禁用', 2: '强制访问控制启用', 3: '强制访问控制禁用', 4: '超级用户状态启用', 5: '超级用户状态禁用' }, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'PROCESS',
            MAP: ASSET_MAP_HELPS.PROCESS,
            PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'SOFTWARE',
            MAP: ASSET_MAP_HELPS.SOFTWARE,
            PROTO: AssetModelCodeDefine_pb.MSG_SOFTWARE,
        }
    },
    // 用户组增删
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.USER_GRP_ADD_DEL]: {
        name: '用户组增删',
        ACTION_MAP: { 0: '新增', 1: '删除' }, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'PROCESS',
            MAP: ASSET_MAP_HELPS.PROCESS,
            PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'USER_GROUP',
            MAP: ASSET_MAP_HELPS.USER_GROUP,
            PROTO: AssetModelCodeDefine_pb.MSG_USER_GROUP,
        }
    },
    // 用户组名变更
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.USER_GRP_NAME_CHANGE]: {
        name: '用户组名变更',
        ACTION_MAP: { 0: '组名变更' }, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'PROCESS',
            MAP: ASSET_MAP_HELPS.PROCESS,
            PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'USER_GROUP',
            MAP: ASSET_MAP_HELPS.USER_GROUP,
            PROTO: AssetModelCodeDefine_pb.MSG_USER_GROUP,
        }
    },
    // 用户增删
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.USER_ADD_DEL]: {
        name: '用户增删',
        ACTION_MAP: { 0: '新增', 1: '删除' }, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'PROCESS',
            MAP: ASSET_MAP_HELPS.PROCESS,
            PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'USER',
            MAP: ASSET_MAP_HELPS.USER,
            PROTO: AssetModelCodeDefine_pb.MSG_USER,
        }
    },
    // 用户属性变更
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.USER_ATTR_CHANGE]: {
        name: '用户属性变更',
        ACTION_MAP: { 1: '默认用户组标识变更', 2: '用户名变更', 4: '用户主目录变更', 32: '用户默认SHELL变更', 128: '用户密码变更', 256: '用户免密登录IP变更', 512: '用户最大进程数变更' }, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'PROCESS',
            MAP: ASSET_MAP_HELPS.PROCESS,
            PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'USER',
            MAP: ASSET_MAP_HELPS.USER,
            PROTO: AssetModelCodeDefine_pb.MSG_USER,
        }
    },
    // 用户锁定解锁
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.USER_LOCK_UNLOCK]: {
        name: '用户锁定解锁',
        ACTION_MAP: { 0: '锁定', 1: '解锁' }, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'PROCESS',
            MAP: ASSET_MAP_HELPS.PROCESS,
            PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'USER',
            MAP: ASSET_MAP_HELPS.USER,
            PROTO: AssetModelCodeDefine_pb.MSG_USER,
        }
    },
    // 分区增删
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.PART_ADD_DEL]: {
        name: '分区增删',
        ACTION_MAP: { 0: '新增', 1: '删除' }, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'PROCESS',
            MAP: ASSET_MAP_HELPS.PROCESS,
            PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'PARTITION',
            MAP: ASSET_MAP_HELPS.PARTITION,
            PROTO: AssetModelCodeDefine_pb.MSG_PARTITION,
        }
    },
    // 分区格式化
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.PART_FORMAT]: {
        name: '分区格式化',
        ACTION_MAP: { 0: '格式化' }, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'PROCESS',
            MAP: ASSET_MAP_HELPS.PROCESS,
            PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'PARTITION',
            MAP: ASSET_MAP_HELPS.PARTITION,
            PROTO: AssetModelCodeDefine_pb.MSG_PARTITION,
        }
    },
    // 分区挂载卸载
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.PART_MOUNT_UNM]: {
        name: '分区挂载卸载',
        ACTION_MAP: { 0: '挂载', 1: '卸载' }, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'PROCESS',
            MAP: ASSET_MAP_HELPS.PROCESS,
            PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'PARTITION',
            MAP: ASSET_MAP_HELPS.PARTITION,
            PROTO: AssetModelCodeDefine_pb.MSG_PARTITION,
        }
    },
    // 启动任务增删
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.BOOT_TASK_ADD_DEL]: {
        name: '启动任务增删',
        ACTION_MAP: { 0: '新增', 1: '删除'}, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'PROCESS',
            MAP: ASSET_MAP_HELPS.PROCESS,
            PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'BOOT',
            MAP: ASSET_MAP_HELPS.BOOT,
            PROTO: AssetModelCodeDefine_pb.MSG_BOOT_TASK,
        }
    },
    // 启动任务变更
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.BOOT_TASK_CHANGE]: {
        name: '启动任务变更',
        ACTION_MAP: {  0: '变更' }, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'PROCESS',
            MAP: ASSET_MAP_HELPS.PROCESS,
            PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'BOOT',
            MAP: ASSET_MAP_HELPS.BOOT,
            PROTO: AssetModelCodeDefine_pb.MSG_BOOT_TASK,
        }
    },
    // 驱动加载卸载
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.DRV_LOAD_UNL]: {
        name: '驱动加载卸载',
        ACTION_MAP: { 0: '加载', 1: '卸载'}, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'PROCESS',
            MAP: ASSET_MAP_HELPS.PROCESS,
            PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'DRIVER',
            MAP: ASSET_MAP_HELPS.DRIVER,
            PROTO: AssetModelCodeDefine_pb.MSG_DRIVER,
        }
    },
    // 软件升级
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.SOFTPAC_UPGRADE]: {
        name: '软件升级',
        ACTION_MAP: { 0: '软件升级'}, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'PROCESS',
            MAP: ASSET_MAP_HELPS.PROCESS,
            PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'SOFTWARE_PACKAGE',
            MAP: ASSET_MAP_HELPS.SOFTWARE_PACKAGE,
            PROTO: AssetModelCodeDefine_pb.MSG_SOFTWARE_PACKAGE,
        }
    },
    // 软件安装卸载
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.SOFTPAC_INSTALL_UNINST]: {
        name: '软件安装卸载',
        ACTION_MAP: { 0: '安装', 1: '卸载'}, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'PROCESS',
            MAP: ASSET_MAP_HELPS.PROCESS,
            PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'SERVICE',
            MAP: ASSET_MAP_HELPS.SERVICE,
            PROTO: AssetModelCodeDefine_pb.MSG_SERVICE,
        }
    },
    // 服务安装卸载
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.SERV_INSTALL_UNINST]: {
        name: '服务安装卸载',
        ACTION_MAP: { 0: '安装', 1: '卸载'}, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'PROCESS',
            MAP: ASSET_MAP_HELPS.PROCESS,
            PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'SERVICE',
            MAP: ASSET_MAP_HELPS.SERVICE,
            PROTO: AssetModelCodeDefine_pb.MSG_SERVICE,
        }
    },
    // 服务参数修改
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.SERV_PARAM_MOD]: {
        name: '服务参数修改',
        ACTION_MAP: {1: '启动类型变更', 2: '启动命令变更', 4: '重启命令变更', 8: '终止命令变更', 16: '自启动状态变更', 32: '运行状态变更'}, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'PROCESS',
            MAP: ASSET_MAP_HELPS.PROCESS,
            PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'SERVICE',
            MAP: ASSET_MAP_HELPS.SERVICE,
            PROTO: AssetModelCodeDefine_pb.MSG_SERVICE,
        }
    },
    // 服务启停
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.SERV_START_STOP]: {
        name: '服务启停',
        ACTION_MAP: {0: '启动', 1: '停止'}, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'PROCESS',
            MAP: ASSET_MAP_HELPS.PROCESS,
            PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'SERVICE',
            MAP: ASSET_MAP_HELPS.SERVICE,
            PROTO: AssetModelCodeDefine_pb.MSG_SERVICE,
        }
    },
    // 文件增删
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.SERV_START_STOP]: {
        name: '文件增删',
        ACTION_MAP: {0: '增加', 1: '删除', 2: '外设拷贝', 3: '非外设拷贝'}, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'PROCESS',
            MAP: ASSET_MAP_HELPS.PROCESS,
            PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'FILE',
            MAP: ASSET_MAP_HELPS.FILE,
            PROTO: AssetModelCodeDefine_pb.MSG_FILE,
        }
    },
    // 文件变更
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.FILE_CHANGE]: {
        name: '文件变更',
        ACTION_MAP: {1: '文件链接变更', 2: '文件权限变更', 4: '文件所属用户变更', 8: '文件所属用户组变更', 16: '文件hash变更'}, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'PROCESS',
            MAP: ASSET_MAP_HELPS.PROCESS,
            PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'FILE',
            MAP: ASSET_MAP_HELPS.FILE,
            PROTO: AssetModelCodeDefine_pb.MSG_FILE,
        }
    },
    // 核心文件内容变更
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.CORE_FILE_CHANGE]: {
        name: '核心文件内容变更',
        ACTION_MAP: {0: '变更'}, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'PROCESS',
            MAP: ASSET_MAP_HELPS.PROCESS,
            PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'FILE',
            MAP: ASSET_MAP_HELPS.FILE,
            PROTO: AssetModelCodeDefine_pb.MSG_FILE,
        }
    },
    // 文件访问
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.FILE_ACCESS]: {
        name: '文件访问',
        ACTION_MAP: {0: '文件访问'}, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'PROCESS',
            MAP: ASSET_MAP_HELPS.PROCESS,
            PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'FILE',
            MAP: ASSET_MAP_HELPS.FILE,
            PROTO: AssetModelCodeDefine_pb.MSG_FILE,
        }
    },
    // 可执行文件变更
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.EXEC_FILE_CHANGE]: {
        name: '可执行文件变更',
        ACTION_MAP: {0: '文件权能变更'}, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'PROCESS',
            MAP: ASSET_MAP_HELPS.PROCESS,
            PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'EXECUTABLE_FILE',
            MAP: ASSET_MAP_HELPS.EXECUTABLE_FILE,
            PROTO: AssetModelCodeDefine_pb.MSG_EXECUTABLE_FILE,
        }
    },
    // 进程启停
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.PROC_START_STOP]: {
        name: '进程启停',
        ACTION_MAP: {0: '启动', 1: '停止'}, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'PROCESS',
            MAP: ASSET_MAP_HELPS.PROCESS,
            PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'PROCESS',
            MAP: ASSET_MAP_HELPS.PROCESS,
            PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        }
    },
    // 进程系统调用
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.PROC_SYS_CALL]: {
        name: '进程系统调用',
        ACTION_MAP: {0: '系统调用'}, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        OBJ: {
            name: 'DISK', // 自定义
            MAP: ASSET_MAP_HELPS.DISK,
            PROTO: AssetModelCodeDefine_pb.MSG_DISK,
        }
    },
    // 会话登录
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.SESSION_LOGIN]: {
        name: '会话登录',
        ACTION_MAP: {0: '登录', 1: '登录中'}, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'SESSION',
            MAP: ASSET_MAP_HELPS.SESSION,
            PROTO: AssetModelCodeDefine_pb.MSG_SESSION,
        },
        OBJ: {
            name: 'SESSION',
            MAP: ASSET_MAP_HELPS.SESSION,
            PROTO: AssetModelCodeDefine_pb.MSG_SESSION,
        }
    },
    // 会话退出
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.SESSION_LOGOUT]: {
        name: '会话退出',
        ACTION_MAP: {0: '退出', 1: '阻断'}, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'SESSION',
            MAP: ASSET_MAP_HELPS.SESSION,
            PROTO: AssetModelCodeDefine_pb.MSG_SESSION,
        },
        OBJ: {
            name: 'SESSION',
            MAP: ASSET_MAP_HELPS.SESSION,
            PROTO: AssetModelCodeDefine_pb.MSG_SESSION,
        }
    },
    // 会话操作
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.SESSION_OPER]: {
        name: '会话操作',
        ACTION_MAP: {0: '会话操作'}, // 动作类型
        AUTH: ['SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'SESSION',
            MAP: ASSET_MAP_HELPS.SESSION,
            PROTO: AssetModelCodeDefine_pb.MSG_SESSION,
        }
    },
    // 应用启停
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.APP_START_STOP]: {
        name: '应用启停',
        ACTION_MAP: {0: '启动', 1: '停止'}, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'PROCESS',
            MAP: ASSET_MAP_HELPS.PROCESS,
            PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'APPLICATION',
            MAP: ASSET_MAP_HELPS.APPLICATION,
            PROTO: AssetModelCodeDefine_pb.MSG_APPLICATION,
        }
    },
    // 应用业务操作
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.APP_OPER]: {
        name: '应用业务操作',
        ACTION_MAP: {0: '业务操作'}, // 动作类型
        AUTH: ['SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'APPLICATION',
            MAP: ASSET_MAP_HELPS.APPLICATION,
            PROTO: AssetModelCodeDefine_pb.MSG_APPLICATION,
        }
    },
    // 安全插件变更
    [BehaviorCodeDefine_pb.SoftwareBehaviorCode.AGENT_SRCPLUG_CHANGE]: {
        name: '安全插件变更',
        ACTION_MAP: {0: '安装', 1: '卸载', 2: '升级', 3: '启动', 4: '停止'}, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'APPLICATION',
            MAP: ASSET_MAP_HELPS.APPLICATION,
            PROTO: AssetModelCodeDefine_pb.MSG_APPLICATION,
        },
        OBJ: {
            name: 'PLUGIN',
            MAP: ASSET_MAP_HELPS.PLUGIN,
            PROTO: AssetModelCodeDefine_pb.MSG_PLUGIN,
        }
    },
}
const NETWORK = {
    // 网络配置开关
    [BehaviorCodeDefine_pb.NetworkBehaviorCode.NET_CONFIG_SWITCH]: {
        name: '网络配置开关',
            ACTION_MAP: {0: '网络转发状态开启', 1: '网络转发状态关闭', 2: 'IPV6禁用', 3: 'IPV6启用'}, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
            SUB: {
            name: 'PROCESS',
                MAP: ASSET_MAP_HELPS.PROCESS,
                PROTO: AssetModelCodeDefine_pb.MSG_PROCESS
        },
        OBJ: {
            name: 'NETWORK',
                MAP: ASSET_MAP_HELPS.NETWORK,
                PROTO: AssetModelCodeDefine_pb.MSG_NETWORK,
        }
    },
    // 网络接口增删
    [BehaviorCodeDefine_pb.NetworkBehaviorCode.NET_INT_ADD_DEL]: {
        name: '网络接口增删',
            ACTION_MAP: {0: '增加', 1: '删除'}, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
            SUB: {
            name: 'PROCESS',
                MAP: ASSET_MAP_HELPS.PROCESS,
                PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'NETWORK_INTERFACE',
                MAP: ASSET_MAP_HELPS.NETWORK_INTERFACE,
                PROTO: AssetModelCodeDefine_pb.MSG_NETWORK_INTERFACE,
        }
    },
    // 网络接口变更
    [BehaviorCodeDefine_pb.NetworkBehaviorCode.NET_INT_CHANGE]: {
        name: '网络接口变更',
            ACTION_MAP: {1: 'IP修改', 2: 'IP增加', 4: 'IP删除', 8: 'MAC变更', 16: '地址分配模式变更', 32: 'BOND接口变更', 64: '掩码变更'}, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
            SUB: {
            name: 'PROCESS',
                MAP: ASSET_MAP_HELPS.PROCESS,
                PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'NETWORK_INTERFACE',
                MAP: ASSET_MAP_HELPS.NETWORK_INTERFACE,
                PROTO: AssetModelCodeDefine_pb.MSG_NETWORK_INTERFACE,
        }
    },
    // 网络接口启停
    [BehaviorCodeDefine_pb.NetworkBehaviorCode.NET_INT_START_STOP]: {
        name: '网络接口启停',
            ACTION_MAP: {0: '启动', 1: '停止'}, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
            SUB: {
            name: 'PROCESS',
                MAP: ASSET_MAP_HELPS.PROCESS,
                PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'NETWORK_INTERFACE',
                MAP: ASSET_MAP_HELPS.NETWORK_INTERFACE,
                PROTO: AssetModelCodeDefine_pb.MSG_NETWORK_INTERFACE,
        }
    },
    // 路由增删
    [BehaviorCodeDefine_pb.NetworkBehaviorCode.ROUTE_ADD_DEL]: {
        name: '路由增删',
            ACTION_MAP: {0: '新增', 1: '删除'}, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
            SUB: {
            name: 'PROCESS',
                MAP: ASSET_MAP_HELPS.PROCESS,
                PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'ROUTE',
                MAP: ASSET_MAP_HELPS.ROUTE,
                PROTO: AssetModelCodeDefine_pb.MSG_ROUTE,
        }
    },
    // 访问控制策略变更
    [BehaviorCodeDefine_pb.NetworkBehaviorCode.ACC_CTRL_POL_CHANGE]: {
        name: '访问控制策略变更',
            ACTION_MAP: {0: '增加', 1: '删除', 2: '修改'}, // 动作类型
        AUTH: ['SUB'], // SUB: 主体 OBJ:客体
            SUB: {
            name: 'APPLICATION',
                MAP: ASSET_MAP_HELPS.APPLICATION,
                PROTO: AssetModelCodeDefine_pb.MSG_APPLICATION,
        }
    },
    // 地址学习表变更
    [BehaviorCodeDefine_pb.NetworkBehaviorCode.ADDR_CHANGE]: {
        name: '地址学习表变更',
            ACTION_MAP: {0: '静态地址增加',1: '静态地址修改',2: '静态地址删除',3: '新增地址学习'}, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
            SUB: {
            name: 'PROCESS',
                MAP: ASSET_MAP_HELPS.PROCESS,
                PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'ADDRESS',
                MAP: ASSET_MAP_HELPS.ADDRESS,
                PROTO: AssetModelCodeDefine_pb.MSG_ADDRESS,
        }
    },
    // 监听端口开关
    [BehaviorCodeDefine_pb.NetworkBehaviorCode.LISTEN_PORT_SWITCH]: {
        name: '监听端口开关',
            ACTION_MAP: {0: '开启',1: '停止'}, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
            SUB: {
            name: 'PROCESS',
                MAP: ASSET_MAP_HELPS.PROCESS,
                PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'LISTENNING_PORT',
                MAP: ASSET_MAP_HELPS.LISTENNING_PORT,
                PROTO: AssetModelCodeDefine_pb.MSG_LISTENNING_PORT,
        }
    },
}

const CONNECT = {
    // 网络访问
    [BehaviorCodeDefine_pb.ConnectionBehaviorCode.NET_ACCESS]: {
        name: '网络访问',
        ACTION_MAP: {0: '开启',1: '停止'}, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'PROCESS',
            MAP: ASSET_MAP_HELPS.PROCESS,
            PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'LISTENNING_PORT',
            MAP: ASSET_MAP_HELPS.LISTENNING_PORT,
            PROTO: AssetModelCodeDefine_pb.MSG_LISTENNING_PORT,
        }
    },
    // 网络文件传输
    [BehaviorCodeDefine_pb.ConnectionBehaviorCode.NET_FILE_TRANSFER]: {
        name: '网络文件传输',
        ACTION_MAP: {0: '开启',1: '停止'}, // 动作类型
        AUTH: ['OBJ','SUB'], // SUB: 主体 OBJ:客体
        SUB: {
            name: 'PROCESS',
            MAP: ASSET_MAP_HELPS.PROCESS,
            PROTO: AssetModelCodeDefine_pb.MSG_PROCESS,
        },
        OBJ: {
            name: 'LISTENNING_PORT',
            MAP: ASSET_MAP_HELPS.LISTENNING_PORT,
            PROTO: AssetModelCodeDefine_pb.MSG_LISTENNING_PORT,
        }
    },
}
// 行为字典
const BEHAVIOR_MAP = {
    // 硬件类型
    ...HARDWARE,
    // 软件 SoftwareBehaviorCode
    ...SOFTWARE,
    // 网络 NetworkBehaviorCode
    ...NETWORK,
    // 连接
    ...CONNECT
}
module.exports = {
    BEHAVIOR_MAP,
    HARDWARE,
    SOFTWARE,
    NETWORK
}
