const RISK_LABEL_MAP = {
    "TA0001": {
        "name": "初始访问",
        "techniques": {
            "T1078": {
                "name": "有效账户",
                "sub_techniques": {"001": "默认账户", "002": "域账户", "003": "本地账户"}
            },
            "T1091": {
                "name": "通过可移动媒体进行复制",
                "sub_techniques": {"000": "通过可移动媒体进行复制"}
            },
            "T1133": {
                "name": "外部远程服务",
                "sub_techniques": {"000": "外部远程服务"}
            }
        }
    },
    "TA0002": {
        "name": "执行",
        "techniques": {
            "T1053": {
                "name": "计划任务/工作",
                "sub_techniques": {"002": "At程序", "003": "Cron程序", "006": "创建定时任务"}
            },
            "T1059": {
                "name": "命令和脚本解释器",
                "sub_techniques": {"004": "Unix Shell", "005": "Visual Basic", "006": "Python", "007": "JavaScript", "008": "网络设备 CLI", "901": "越基线指令执行", "902": "危险指令执行"}
            },
            "T1072": {
                "name": "软件部署工具",
                "sub_techniques": {"000": "软件部署工具"}
            },
            "T1106": {
                "name": "系统API",
                "sub_techniques": {"901": "越基线系统调用", "902": "启用非法端口"}
            },
            "T1129": {
                "name": "共享模块",
                "sub_techniques": {"000": "共享模块"}
            },
            "T1199": {
                "name": "信任关系",
                "sub_techniques": {"901": "免密文件变更"}
            },
            "T1200": {
                "name": "硬件添加",
                "sub_techniques": {
                    "901": "USB存储设备接入", "902": "通信设备接入", "903": "USB未知设备类型接入",
                    "904": "串口接入", "905": "WiFi接入", "906": "蓝牙接入", "907": "网口接入",
                    "908": "光驱接入", "909": "USB打印机设备接入", "910": "音箱接入", "911": "并口接入"
                }
            },
            "T1203": {
                "name": "利用客户端执行",
                "sub_techniques": {"000": "利用客户端执行"}
            },
            "T1204": {
                "name": "用户执行",
                "sub_techniques": {"001": "恶意链接", "002": "恶意文件", "901": "越基线进程启动"}
            },
            "T1569": {
                "name": "系统服务",
                "sub_techniques": {"901": "开放网络服务"}
            }
        }
    },
    "TA0003": {
        "name": "持久化",
        "techniques": {
            "T1037": {
                "name": "启动或登录初始化脚本",
                "sub_techniques": {
                    "004": "RC脚本变更"
                }
            },
            "T1053": {
                "name": "计划任务/工作",
                "sub_techniques": {"002": "At程序", "003": "Cron程序", "006": "创建定时任务"}
            },
            "T1078": {
                "name": "有效账户",
                "sub_techniques": {"001": "默认账户", "002": "域账户", "003": "本地账户"}
            },
            "T1098": {
                "name": "账户操纵",
                "sub_techniques": {"004": "SSH 授权密钥", "005": "设备注册"}
            },
            "T1133": {
                "name": "外部远程服务",
                "sub_techniques": {"000": "外部远程服务"}
            },
            "T1136": {
                "name": "创建账户",
                "sub_techniques": {"001": "创建本地用户", "002": "创建域账户"}
            },
            "T1205": {
                "name": "流量信号",
                "sub_techniques": {"001": "端口敲击", "002": "socket过滤器", "901": "越基线监听端口"}
            },
            "T1505": {
                "name": "服务器软件组件",
                "sub_techniques": {"001": "SQL 存储过程", "002": "传输代理", "003": "网页外壳"}
            },
            "T1542": {
                "name": "预操作系统启动",
                "sub_techniques": {"001": "系统固件", "002": "组件固件", "003": "引导区工具包", "004": "ROMMON工具包", "005": "TFTP 引导"}
            },
            "T1543": {
                "name": "创建或修改系统进程",
                "sub_techniques": {"002": "Systemd服务"}
            },
            "T1546": {
                "name": "事件触发执行",
                "sub_techniques": {"004": "Unix Shell 配置修改", "005": "陷阱", "016": "安装程序包"}
            },
            "T1547": {
                "name": "启动或登录自动启动执行",
                "sub_techniques": {"006": "内核模块和扩展", "013": "XDG 自动启动条目"}
            },
            "T1554": {
                "name": "泄露客户端软件二进制文件",
                "sub_techniques": {"000": "泄露客户端软件二进制文件"}
            },
            "T1556": {
                "name": "修改身份验证过程",
                "sub_techniques": {"003": "可插拔身份验证模块", "004": "网络设备身份验证","006": "多重身份验证"}
            },
            "T1574": {
                "name": "劫持执行流",
                "sub_techniques": {"007": "路径环境变量的路径拦截"}
            }
        }
    },
    "TA0004": {
        "name": "权限提升",
        "techniques": {
            "T1037": {
                "name": "启动或登录初始化脚本",
                "sub_techniques": {
                    "004": "RC脚本变更"
                }
            },
            "T1053": {
                "name": "计划任务/工作",
                "sub_techniques": {"002": "At程序", "003": "Cron程序"}
            },
            "T1055": {
                "name": "进程注入",
                "sub_techniques": {"008": "跟踪系统调用", "009": "处理器内存", "014": "VDSO劫持"}
            },
            "T1068": {
                "name": "利用特权升级",
                "sub_techniques": {"000": "利用特权升级"}
            },
            "T1078": {
                "name": "有效账户",
                "sub_techniques": {"001": "默认账户", "002": "域账户", "003": "本地账户"}
            },
            "T1543": {
                "name": "创建或修改系统进程",
                "sub_techniques": {"002": "Systemd服务"}
            },
            "T1546": {
                "name": "事件触发执行",
                "sub_techniques": {"016": "安装程序包"}
            },
            "T1547": {
                "name": "启动或登录自动启动执行",
                "sub_techniques": {"006": "内核模块和扩展"}
            },
            "T1548": {
                "name": "滥用提升控制机制",
                "sub_techniques": {"001": "用户权限变更（setuid与setgid）", "003": "sudo和sudo缓存"}
            },
            "T1574": {
                "name": "劫持执行流",
                "sub_techniques": {"006": "动态链接器劫持", "007": "路径环境变量的路径拦截"}
            }
        }
    },
    "TA0005": {
        "name": "防御规避",
        "techniques": {
            "T1027": {
                "name": "混淆的文件或信息",
                "sub_techniques": {
                    "001": "二进制填充",
                    "002": "软件打包",
                    "003": "隐写术",
                    "004": "交付后编译",
                    "005": "从工具中删除指示器",
                    "006": "网页走私",
                    "008": "剥离的有效载荷",
                    "009": "嵌入式有效负载"
                }
            },
            "T1036": {
                "name": "伪装",
                "sub_techniques": {
                    "002": "从右到左覆盖",
                    "003": "重命名系统实用程序",
                    "004": "伪装任务或服务",
                    "005": "匹配合法名称或位置",
                    "006": "文件名后的空格"
                }
            },
            "T1055": {
                "name": "进程注入",
                "sub_techniques": {"008": "跟踪系统调用", "009": "处理器内存", "014": "VDSO劫持"}
            },
            "T1070": {
                "name": "指标移除",
                "sub_techniques": {"002": "清除 Linux 或 Mac 系统日志", "003": "清除命令历史记录", "004": "文件删除", "006": "时间篡改", "007": "清除网络连接历史记录和配置", "008": "清除邮箱数据", "009": "清除持久性"}
            },
            "T1078": {
                "name": "有效账户",
                "sub_techniques": {"001": "默认账户", "002": "域账户", "003": "本地账户"}
            },
            "T1140": {
                "name": "对文件或信息进行解密/解码",
                "sub_techniques": {"000": "对文件或信息进行解密/解码"}
            },
            "T1205": {
                "name": "流量信号",
                "sub_techniques": {"001": "端口敲击", "002": "socket过滤器", "901": "越基线监听端口"}
            },
            "T1211": {
                "name": "用于防御规避的利用",
                "sub_techniques": {"000": "用于防御规避的利用"}
            },
            "T1222": {
                "name": "文件和目录权限修改",
                "sub_techniques": {"002": "Linux 和 Mac 文件和目录权限修改", "901": "关键目录下文件变更", "902": "文件访问越基线", "903": "关键文件变更"}
            },
            "T1480": {
                "name": "执行防护栏",
                "sub_techniques": {"001": "环境键控"}
            },
            "T1497": {
                "name": "虚拟化/沙盒规避",
                "sub_techniques": {"001": "系统检查", "002": "基于用户活动的检查", "003": "基于时间的规避"}
            },
            "T1542": {
                "name": "预操作系统启动",
                "sub_techniques": {"001": "系统固件", "002": "组件固件", "003": "引导区工具包", "004": "ROMMON工具包", "005": "TFTP 引导"}
            },
            "T1553": {
                "name": "颠覆信任控制",
                "sub_techniques": {"004": "安装根证书"}
            },
            "T1556": {
                "name": "修改身份验证过程",
                "sub_techniques": {"003": "可插拔身份验证模块","004": "网络设备身份验证", "006": "多重身份验证"}
            },
            "T1562": {
                "name": "损害防御",
                "sub_techniques": {"001": "禁用或修改工具", "003": "损害命令历史记录日志记录", "004": "禁用或修改系统防火墙", "006": "指标阻塞", "010": "降级攻击"}
            },
            "T1564": {
                "name": "隐藏工件",
                "sub_techniques": {"001": "隐藏文件和目录", "002": "隐藏用户", "003": "隐藏窗口", "005": "隐藏文件系统", "006": "运行虚拟实例", "007": "VBA 篡改", "008": "电子邮件隐藏规则"}
            },
            "T1574": {
                "name": "劫持执行流",
                "sub_techniques": {"006": "动态链接器劫持", "007": "路径环境变量的路径拦截"}
            },
            "T1599": {
                "name": "网络边界桥接",
                "sub_techniques": {"001": "网络地址转换遍历"}
            },
            "T1600": {
                "name": "削弱加密",
                "sub_techniques": {"001": "减少密钥空间", "002": "禁用加密硬件"}
            },
            "T1601": {
                "name": "修改系统映像",
                "sub_techniques": {"001": "修补系统映像", "002": "降级系统映像"}
            },
            "T1620": {
                "name": "反射代码加载",
                "sub_techniques": {"000": "反射代码加载"}
            },
            "T1622": {
                "name": "调试器规避",
                "sub_techniques": {"000": "调试器规避"}
            }
        }
    },
    "TA0006": {
        "name": "凭证访问",
        "techniques": {
            "T1040": {
                "name": "网络嗅探",
                "sub_techniques": {
                    "000": "网络嗅探"
                }
            },
            "T1056": {
                "name": "输入捕获",
                "sub_techniques": {"001": "键盘记录", "002": "GUI输入捕获", "003": "门户网站捕获"}
            },
            "T1110": {
                "name": "暴力破解",
                "sub_techniques": {"001": "密码猜测", "002": "密码破解", "003": "密码喷洒攻击", "004": "撞库"}
            },
            "T1111": {
                "name": "多重身份验证拦截",
                "sub_techniques": {"000": "多重身份验证拦截"}
            },
            "T1212": {
                "name": "利用凭据访问",
                "sub_techniques": {"000": "利用凭据访问"}
            },
            "T1552": {
                "name": "不安全的凭据",
                "sub_techniques": {"001": "文件中的凭据", "003": "Bash历史", "004": "私钥"}
            },
            "T1555": {
                "name": "密码存储中的凭据",
                "sub_techniques": {"002": "安全内存", "003": "来自 Web 浏览器的凭据", "005": "密码管理器"}
            },
            "T1556": {
                "name": "修改身份验证过程",
                "sub_techniques": {"003": "可插拔身份验证模块","004": "网络设备身份验证", "006": "多重身份验证"}
            },
            "T1557": {
                "name": "中间人攻击",
                "sub_techniques": {"002": "ARP 缓存中毒", "003": "DHCP 欺骗"}
            }
        }
    },
    "TA0007": {
        "name": "发现",
        "techniques": {
            "T1033": {
                "name": "系统所有者/用户发现",
                "sub_techniques": {
                    "000": "系统所有者/用户发现"
                }
            },
            "T1040": {
                "name": "网络嗅探",
                "sub_techniques": {
                    "000": "网络嗅探"
                }
            },
            "T1046": {
                "name": "网络服务发现",
                "sub_techniques": {
                    "000": "网络服务发现"
                }
            },
            "T1049": {
                "name": "系统网络连接发现",
                "sub_techniques": {
                    "000": "系统网络连接发现"
                }
            },
            "T1057": {
                "name": "进程发现",
                "sub_techniques": {"000": "进程发现"}
            },
            "T1069": {
                "name": "权限发现",
                "sub_techniques": {"001": "本地组", "002": "域组"}
            },
            "T1082": {
                "name": "系统信息发现",
                "sub_techniques": {"000": "系统信息发现"}
            },
            "T1083": {
                "name": "文件和目录发现",
                "sub_techniques": {"000": "文件和目录发现"}
            },
            "T1087": {
                "name": "帐户发现",
                "sub_techniques": {"001": "本地帐户", "002": "域帐户"}
            },
            "T1120": {
                "name": "外围设备发现",
                "sub_techniques": {"000": "外围设备发现"}
            },
            "T1124": {
                "name": "系统时间发现",
                "sub_techniques": {"000": "系统时间发现"}
            },
            "T1135": {
                "name": "网络共享发现",
                "sub_techniques": {"000": "网络共享发现"}
            },
            "T1201": {
                "name": "密码策略发现",
                "sub_techniques": {"000": "密码策略发现"}
            },
            "T1217": {
                "name": "浏览器书签发现",
                "sub_techniques": {"000": "浏览器书签发现"}
            },
            "T1497": {
                "name": "虚拟化/沙盒规避",
                "sub_techniques": {"001": "系统检查", "002": "基于用户活动的检查","003": "基于时间的规避"}
            },
            "T1518": {
                "name": "软件发现",
                "sub_techniques": {"001": "安全软件发现"}
            },
            "T1614": {
                "name": "系统位置发现",
                "sub_techniques": {"001": "系统语言发现"}
            },
            "T1622": {
                "name": "调试器规避",
                "sub_techniques": {"000": "调试器规避"}
            }
        }
    },
    "TA0008": {
        "name": "横向移动",
        "techniques": {
            "T1021": {
                "name": "远程服务",
                "sub_techniques": {
                    "906": "局域网串网",
                    "907": "运维网关多点登录"
                }
            },
            "T1072": {
                "name": "软件部署工具",
                "sub_techniques": {"000": "软件部署工具"}
            },
            "T1080": {
                "name": "污点共享内容",
                "sub_techniques": {"000": "污点共享内容"}
            },
            "T1091": {
                "name": "通过可移动媒体进行复制",
                "sub_techniques": {"000": "通过可移动媒体进行复制", "901": "挂载分区内进程启动"}
            },
            "T1210": {
                "name": "远程服务利用",
                "sub_techniques": {"901": "通用服务漏洞利用"}
            },
            "T1534": {
                "name": "内部鱼叉式网络钓鱼",
                "sub_techniques": {"000": "内部鱼叉式网络钓鱼"}
            },
            "T1563": {
                "name": "远程服务会话劫持",
                "sub_techniques": {"001": "SSH 劫持"}
            },
            "T1570": {
                "name": "横向工具转移",
                "sub_techniques": {"000": "横向工具转移"}
            }
        }
    },
    "TA0009": {
        "name": "收集",
        "techniques": {
            "T1025": {
                "name": "来自可移动媒介的数据",
                "sub_techniques": {
                    "000": "来自可移动媒介的数据",
                    "901": "USB存储功能开启"
                }
            },
            "T1039": {
                "name": "来自网络共享云端硬盘的数据",
                "sub_techniques": {
                    "000": "来自网络共享云端硬盘的数据"
                }
            },
            "T1056": {
                "name": "输入捕获",
                "sub_techniques": {"001": "键盘记录", "002": "GUI输入捕获", "003": "门户网站捕获"}
            },
            "T1074": {
                "name": "数据暂存",
                "sub_techniques": {"001": "本地数据暂存", "002": "远程数据暂存"}
            },
            "T1113": {
                "name": "屏幕截图",
                "sub_techniques": {"000": "屏幕截图"}
            },
            "T1114": {
                "name": "电子邮件收集",
                "sub_techniques": {"003": "电子邮件转发规则"}
            },
            "T1115": {
                "name": "剪贴板数据",
                "sub_techniques": {"000": "剪贴板数据"}
            },
            "T1119": {
                "name": "自动收集",
                "sub_techniques": {"000": "自动收集"}
            },
            "T1123": {
                "name": "音频捕获",
                "sub_techniques": {"901": "音箱输出"}
            },
            "T1125": {
                "name": "视频捕获",
                "sub_techniques": {"000": "视频捕获"}
            },
            "T1185": {
                "name": "浏览器会话劫持",
                "sub_techniques": {"000": "浏览器会话劫持"}
            },
            "T1557": {
                "name": "中间人攻击",
                "sub_techniques": {"002": "ARP 缓存中毒", "003": "DHCP 欺骗"}
            },
            "T1560": {
                "name": "存档收集的数据",
                "sub_techniques": {"001": "通过实用程序存档", "002": "通过库存档", "003": "通过自定义方法存档"}
            },
            "T1602": {
                "name": "来自配置存储库的数据",
                "sub_techniques": {"001": "SNMP (MIB 转储)", "002": "网络设备配置转储"}
            }
        }
    },
    "TA0010": {
        "name": "数据泄露",
        "techniques": {
            "T1029": {
                "name": "定时接送",
                "sub_techniques": {
                    "000": "定时接送"
                }
            },
            "T1030": {
                "name": "数据传输大小限制",
                "sub_techniques": {
                    "000": "数据传输大小限制"
                }
            },
            "T1041": {
                "name": "通过 C2 通道渗出",
                "sub_techniques": {
                    "000": "通过 C2 通道渗出"
                }
            },
            "T1048": {
                "name": "替代协议的渗透",
                "sub_techniques": {
                    "001": "通过对称加密非 C2 协议的渗透",
                    "002": "通过非对称加密非 C2 协议进行渗透",
                    "003": "通过未加密的非 C2 协议进行渗透"
                }
            },
            "T1052": {
                "name": "物理介质上的渗漏",
                "sub_techniques": {
                    "001": "通过 USB 进行渗透"
                }
            },
            "T1537": {
                "name": "将数据传输到云帐户",
                "sub_techniques": {"000": "将数据传输到云帐户"}
            },
            "T1567": {
                "name": "通过 Web 服务进行渗透",
                "sub_techniques": {"001": "渗透到代码存储库"}
            }
        }
    },
    "TA0011": {
        "name": "命令与控制",
        "techniques": {
            "T1071": {
                "name": "应用层协议",
                "sub_techniques": {"001": "网络协议", "002": "文件传输协议", "003": "邮件协议", "004": "域名解析"}
            },
            "T1080": {
                "name": "回退通道",
                "sub_techniques": {"000": "回退通道"}
            },
            "T1090": {
                "name": "代理",
                "sub_techniques": {"001": "内部代理", "002": "外部代理", "003": "多跳代理", "004": "域名前置"}
            },
            "T1092": {
                "name": "通过可移动媒体进行通信",
                "sub_techniques": {"000": "通过可移动媒体进行通信"}
            },
            "T1095": {
                "name": "非应用层协议",
                "sub_techniques": {"000": "非应用层协议"}
            },
            "T1102": {
                "name": "网络服务",
                "sub_techniques": {"001": "死信投递点解析器", "002": "双向通信", "003": "单向通信"}
            },
            "T1104": {
                "name": "多级通道",
                "sub_techniques": {"000": "多级通道"}
            },
            "T1105": {
                "name": "入口工具传输",
                "sub_techniques": {"000": "入口工具传输"}
            },
            "T1132": {
                "name": "数据编码",
                "sub_techniques": {"001": "标准编码", "002": "非标准编码"}
            },
            "T1205": {
                "name": "流量信号",
                "sub_techniques": {"001": "端口敲击", "002": "socket过滤器"}
            },
            "T1219": {
                "name": "远程访问软件",
                "sub_techniques": {"000": "远程访问软件"}
            },
            "T1568": {
                "name": "动态分辨率",
                "sub_techniques": {"001": "快速通量域名系统", "002": "域生成算法", "003": "域名解析计算"}
            },
            "T1571": {
                "name": "非标端口",
                "sub_techniques": {"000": "非标端口"}
            },
            "T1572": {
                "name": "协议隧道",
                "sub_techniques": {"901": "网络流量访问异常"}
            },
            "T1573": {
                "name": "加密通道",
                "sub_techniques": {"001": "对称密码学", "002": "非对称加密", "901": "设备验签错误"}
            }
        }
    },
    "TA0040": {
        "name": "影响",
        "techniques": {
            "T1485": {
                "name": "数据销毁",
                "sub_techniques": {"901": "大量关键文件篡改"}
            },
            "T1486": {
                "name": "为影响而加密的数据",
                "sub_techniques": {"000": "为影响而加密的数据"}
            },
            "T1489": {
                "name": "服务停止",
                "sub_techniques": {"000": "服务停止"}
            },
            "T1490": {
                "name": "抑制系统恢复",
                "sub_techniques": {"000": "抑制系统恢复"}
            },
            "T1491": {
                "name": "污损",
                "sub_techniques": {"001": "内部污损", "002": "外部污损"}
            },
            "T1496": {
                "name": "资源劫持",
                "sub_techniques": {"000": "资源劫持"}
            },
            "T1498": {
                "name": "网络拒绝服务",
                "sub_techniques": {"001": "直接网络泛洪", "002": "反射放大"}
            },
            "T1499": {
                "name": "终端拒绝服务",
                "sub_techniques": {"001": "操作系统耗尽泛滥", "002": "服务耗尽泛滥", "003": "应用程序耗尽洪水", "004": "应用程序或系统开发"}
            },
            "T1529": {
                "name": "系统关机/重启",
                "sub_techniques": {"000": "系统关机/重启"}
            },
            "T1531": {
                "name": "帐户访问权限删除",
                "sub_techniques": {"000": "帐户访问权限删除"}
            },
            "T1561": {
                "name": "磁盘擦除",
                "sub_techniques": {"001": "磁盘内容擦除", "002": "磁盘结构擦除"}
            },
            "T1565": {
                "name": "数据操作",
                "sub_techniques": {"000": "运行时数据操作", "001": "存储数据操作", "002": "传输数据操作"}
            }
        }
    },
    "TA0043": {
        "name": "侦察",
        "techniques": {
            "T1592": {
                "name": "收集受害者主机信息",
                "sub_techniques": {"001": "硬件", "002": "软件", "003": "固件"}
            },
            "T1595": {
                "name": "主动扫描",
                "sub_techniques": {"001": "IP扫描", "901": "端口扫描"}
            }
        }
    }

}
const LABEL_MAP = {
    1: '服务端网络访问基线',
    2: '进程网络访问基线',
    3: '网络端口监听基线',
    4: 'ARP信息基线',
    5: '主机进程基线',
    6: '关键文件访问基线',
    7: '关键文件变更基线',
    8: '进程系统调用基线',
    9: '主机服务启用基线',
    10: '主机驱动加载基线',
}
// 获取网络安全风险标签
function get_RISK_LABEL_MAP(label_code) {
    const codeArr = label_code.split('-') // 根据-切割成数组
    if (!codeArr.length) return '--'
    const nameArr = [null, null, null]
    const map = codeArr[0] in RISK_LABEL_MAP ? RISK_LABEL_MAP[codeArr[0]] : null
    if (!map) return '--'
    nameArr[0] = map.name
    nameArr[1] = codeArr[1] in map.techniques ? map.techniques[codeArr[1]].name : null
    nameArr[2] = codeArr[2] in map.techniques[codeArr[1]].sub_techniques ? map.techniques[codeArr[1]].sub_techniques[codeArr[2]].name : null
    return nameArr.filter(item => item).join('-')
}
function getNameByL(codeStr) {
    if(!codeStr) return '--'
    const codeArr = codeStr.split(',')
    const showName = []
    codeArr.forEach(item => {
        if (item.includes('-')) {
            showName.push(get_RISK_LABEL_MAP(item))
        } else {
            showName.push(item in LABEL_MAP ? LABEL_MAP[item] : '--')
        }
    })
    return showName.join(',')
}
module.exports = {
    // 网络安全风险标签
    RISK_LABEL_MAP,
    get_RISK_LABEL_MAP,
    // 行为结果
    ACTION_RESULT_MAP: {
        0: '成功',
        1: '失败',
        2: '未知',
    },
    LABEL_MAP,
    getNameByL
}

