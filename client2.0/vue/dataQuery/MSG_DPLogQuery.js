const util = require('../../lib/util.js');
const tcpInstance = require('../../lib/v2/tls_client.js');
const PublicDefine_pb = require('../../script/pb/PublicDefine_pb.js')
const { cloneDeep } = require('lodash')
const ProxyServer_pb = require("../../script/pb/ProxyServer_pb");
const PlatformProxyServer_pb = require('../../script/pb/PlatformProxyServer_pb.js');
const AssetModelDefine_pb = require('../../script/pb/AssetModelDefine_pb.js');
const ui_util = require("../../script/view/ui_util");

const {createApp, ref, onMounted, reactive, onBeforeMount, computed, onBeforeUnmount} = Vue;
const app = createApp({
    setup() {
        const box = ref(null)
        const mainTableHeight = ref(0)

        onMounted(() => {
            resizeHeight() // 重置高度
            window.addEventListener('resize', resizeHeight)
            initOnMessage() // 注册响应事件监听
            getData()
        })
        onBeforeUnmount(() => {
            window.removeEventListener('resize', resizeHeight)
        })

        const resizeHeight = () => {
            mainTableHeight.value = box.value.clientHeight ? box.value.clientHeight * 0.7 : 100
        }

        const logTypeEnum = ref([{ code: 1, name: '采集信息' }])
        // 设备类型
        const deviceTypeEnum = ref([
            { code: 'SVR', name: '主机设备' },
            { code: 'DCD', name: '监测装置' },
            { code: 'FIREWALL', name: '防火墙' },
            { code: 'VEAD', name: '纵向加密' },
            { code: 'FBID', name: '隔离装置' },
            { code: 'SW', name: '交换机' },
            { code: 'ROUTER', name: '路由器' },
            { code: 'SASH', name: '运维网关' },
            { code: 'IDS', name: '入侵检测' },
            { code: 'CS', name: '服务器密码机' },
        ])
        // 事件级别
        const eventLevelEnum = ref([
            { code: 1, name: '紧急' },
            { code: 2, name: '警报' },
            { code: 3, name: '严重' },
            { code: 4, name: '错误' },
            { code: 5, name: '注意' },
        ])

        const formData = ref({
            logType: 1,
            devType: [],
            eventLevel: [],
            startTime: '0',
            endTime: '0'
        })
        const startTime = ref(null)
        const endTime = ref(null)

        const initOnMessage = () => {
            tcpInstance.onMessage(util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_DATAQUERY, PlatformProxyServer_pb.DataQueryPacketType.DATAQUERYPACKETTYPE_LOG), (pb) => {
                console.log('获取', pb)
                try {
                    const msg = PlatformProxyServer_pb.MSG_DPLogQuery.deserializeBinary(pb);
                    const table = []
                    if (msg.getResult() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                        dataCnt.value = util.getProtobufFieldValue(msg, 'dataCnt')
                        console.log('总条目', dataCnt.value)
                        const logType = util.getProtobufFieldValue(msg, 'logType')
                        const logTypeName = '采集信息'
                        const addInfoList = msg.getAddinfoList();
                        addInfoList.forEach(bufItem => {
                            const tableItem = util.getArrayDataByBufDataAndTemp(bufItem, ['id', 'content'])
                            tableItem.logType = logType
                            tableItem.logTypeName = logTypeName
                            table.push(tableItem)
                        })
                    } else {
                        ui_util.getErrorTips(msg.getErrorCode(), '调阅日志信息')
                    }
                    console.log('send => 获取返回数据', table)
                    tableData.value = table
                } catch (e) {
                    console.log('解构err', e)
                    ui_util.showFadeTip('获取数据失败！')
                    tableData.value = []
                }
            })
        }

        const tableData = ref([])
        const pageNum = ref(1)
        const pageSize = ref(1000)
        const dataCnt = ref(0) // 总条目
        // 查询
        const searchFn = () => {
            formData.value.logType = 1
            startTime.value ? formData.value.startTime = String(moment(startTime.value).format('YYYY-MM-DD HH:mm:ss')) : formData.value.startTime = '0'
            endTime.value ? formData.value.endTime = String(moment(endTime.value).format('YYYY-MM-DD HH:mm:ss')) : formData.value.endTime = '0'
            pageNum.value = 1
            getData()
        }
        // 重置
        const resetFn = () => {
            startTime.value = null
            endTime.value = null
            formData.value.startTime = '0'
            formData.value.endTime = '0'
            formData.value.devType = []
            formData.value.eventLevel = []
            searchFn()
        }
        // 分页切换
        const currentChangeFn = () => {
            getData()
        }
        // 获取数据
        const getData = () => {
            const data = cloneDeep(formData.value)
            // const offset =
            const msgPayload = {
                uuid: util.genTaskId(),
                offset: (pageNum.value - 1) * 1000,
                logType: 1,
                devType: data.devType,
                eventLevel: data.eventLevel,
                startTime: data.startTime ? data.startTime : '0',
                endTime: data.endTime ? data.endTime : '0',
            }
            let sendMsg = JSON.stringify(msgPayload)
            console.log('send => ', sendMsg, msgPayload)
            tcpInstance.sendJsonMsg(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_DATAQUERY, PlatformProxyServer_pb.DataQueryPacketType.DATAQUERYPACKETTYPE_LOG, sendMsg).then();
        }
        return {
            box,
            mainTableHeight,

            logTypeEnum, deviceTypeEnum, eventLevelEnum,

            formData, startTime, endTime,
            tableData, pageNum, pageSize, dataCnt,

            initOnMessage,
            searchFn,
            resetFn,
            currentChangeFn,
        }
    }
})
app.use(ElementPlus, { locale: ElementPlusLocaleZhCn })
app.mount('#app');
