const util = require('../../lib/util.js');
const tcpInstance = require('../../lib/v2/tls_client.js');
const PublicDefine_pb = require('../../script/pb/PublicDefine_pb.js')
const PlatformProxyServer_pb = require('../../script/pb/PlatformProxyServer_pb.js');
const AssetModelDefine_pb = require('../../script/pb/AssetModelDefine_pb.js');
const { cloneDeep } = require('lodash')
const AssetModelCodeDefine_pb = require('../../script/pb/AssetModelCodeDefine_pb.js');
const ProxyServer_pb = require("../../script/pb/ProxyServer_pb");
const {createApp, ref, onMounted, reactive, onBeforeMount, computed, onBeforeUnmount} = Vue;
const BEHAVIOR_HELPS = require("./helps/behavior_model_helps");
const LABEL_MAP = require("./helps/LABEL_MAP");


const app = createApp({
    setup() {
        const box = ref(null)
        const queryModelHeight = ref(0) // 弹窗高度
        const queryModelInfoHeight = ref(0) // 弹窗高度
        onMounted(() => {
            resizeHeight() // 重置高度
            window.addEventListener('resize', resizeHeight)
            initOnMessage() // 注册响应事件监听
            getAssetList()
        })
        onBeforeUnmount(() => {
            window.removeEventListener('resize', resizeHeight)
        })
        let assetList = ref([])

        // 监听事件注册
        const initOnMessage = () => {
            // 调阅信息
            tcpInstance.onMessage(util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_DATAQUERY, PlatformProxyServer_pb.DataQueryPacketType.DATAQUERYPACKETTYPE_PLATFORMCOMMSTATUS), (pb) => {
                console.log('res0 =>', PlatformProxyServer_pb.MSG_DPPlatformCommStatusQuery.prototype);
                const msg = PlatformProxyServer_pb.MSG_DPPlatformCommStatusQuery.deserializeBinary(pb);
                console.log('res =>', msg.getResult(), PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS);
                if (msg.getResult() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                    const addInfoList = msg.getAddinfoList();
                    const data = []
                    addInfoList.forEach(anyItem => {
                        const dataItem = {
                            platIP: '',
                            status: '',
                            statusName: '--'
                        }
                        dataItem.platIP = util.getProtobufFieldValue(anyItem, 'platIP')
                        dataItem.status = util.getProtobufFieldValue(anyItem, 'status')
                        const statusEnum = {0: '认证协商', 1: '链路正常(主链路)', 2: '链路正常（备链路）', 3: '装置认证平台失败', 4: '平台认证装置失败', 5: '连接未建立'}
                        if (dataItem.status in statusEnum) {
                            dataItem.statusName = statusEnum[dataItem.status]
                        }
                        data.push(dataItem)
                    })
                    console.log('全量数据', data)
                    assetList.value = data
                }
            })
        }
        // 获取资产列表
        const getAssetList = () => {
            let sendMsg = JSON.stringify({uuid: util.genTaskId()})
            tcpInstance.sendJsonMsg(
                PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_DATAQUERY,
                PlatformProxyServer_pb.DataQueryPacketType.DATAQUERYPACKETTYPE_PLATFORMCOMMSTATUS,
                sendMsg).then();
        }
        const resizeHeight = () => {
            queryModelHeight.value = box.value.clientHeight ? box.value.clientHeight : 100
            queryModelInfoHeight.value = box.value.clientHeight ? box.value.clientHeight * 0.8 : 80
        }
        return {
            box,
            assetList,
            queryModelHeight,
        }
    }
})
app.use(ElementPlus, { locale: ElementPlusLocaleZhCn })
app.mount('#app');
