const util = require('../../lib/util.js');
const tcpInstance = require('../../lib/v2/tls_client.js');
const PublicDefine_pb = require('../../script/pb/PublicDefine_pb.js')
const PlatformProxyServer_pb = require('../../script/pb/PlatformProxyServer_pb.js');
const asset_map = require('./helps/connection_model_helps');
const AssetModelDefine_pb = require('../../script/pb/AssetModelDefine_pb.js');
const { cloneDeep } = require('lodash')
const AssetModelCodeDefine_pb = require('../../script/pb/AssetModelCodeDefine_pb.js');
const ProxyServer_pb = require("../../script/pb/ProxyServer_pb");
const ConnectionModelDefine_pb = require("../../script/pb/ConnectionModelDefine_pb");
// const
const {createApp, ref, onMounted, reactive, onBeforeMount, computed} = Vue;
const app = createApp({
    setup() {
        const connectA = ref({
            DNAME: '',
            GID: '',
            CONAST: ''
        })
        const connectB = ref({
            DNAME: '',
            GID: '',
            CONAST: ''
        })
        const MAP2CHINA = asset_map.MAP2CHINA
        let showContent = ref([])
        let assetList = ref([])
        const box = ref(null)
        const checkList = ref(Number(AssetModelCodeDefine_pb.SpaceConnectionModelCode.SPACECONNECTIONMODELCODE_PHYSICAL_NET_CONN))
        const checkEnum = ref([
            { name: '有线网络连接', code: Number(AssetModelCodeDefine_pb.SpaceConnectionModelCode.SPACECONNECTIONMODELCODE_PHYSICAL_NET_CONN) },
            { name: '资产访问', code: Number(AssetModelCodeDefine_pb.SpaceConnectionModelCode.SPACECONNECTIONMODELCODE_ASSET_ACCESS) },
        ])
        onMounted(() => {
            console.log('box', box.value.clientHeight)
            queryModelHeight.value = box.value.clientHeight ? box.value.clientHeight : 100
            queryModelInfoHeight.value = box.value.clientHeight ? box.value.clientHeight * 0.9 : 80
            window.addEventListener('resize', () => {
                console.log('resize => ', box.value.clientHeight)
                queryModelHeight.value = box.value.clientHeight ? box.value.clientHeight : 100
                queryModelInfoHeight.value = box.value.clientHeight ? box.value.clientHeight * 0.9 : 80
            })
            initOnMessage() // 注册响应事件监听
            getAssetList()
        })
        const queryModelVisible = ref(false) // 弹窗显示
        const queryModelHeight = ref(0) // 弹窗高度
        const queryModelInfoHeight = ref(0) // 弹窗高度
        // 获取资产列表
        const getAssetList = () => {
            let sendMsg = new ProxyServer_pb.MSG_CPLoadAssets();
            tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADASSETS, sendMsg).then()
        }
        let queryModelInfo = {}
        // 前置调阅按钮
        const getQueryInfoFront = (item) => {
            queryModelVisible.value = true
            checkList.value = Number(AssetModelCodeDefine_pb.SpaceConnectionModelCode.SPACECONNECTIONMODELCODE_PHYSICAL_NET_CONN)
            queryModelInfo = item
            getQueryInfo()
        }
        // 调阅信息
        const getQueryInfo = () => {
            const msgPayload = {
                uuid: util.genTaskId(),
                assetIdA: connectA.value.GID,
                CONASTA: connectA.value.CONAST ? 1 : 2,
                assetIdB: connectB.value.GID,
                CONASTB: connectB.value.CONAST ? 1 : 2,
                modelType: checkList.value
            }
            let sendMsg = JSON.stringify(msgPayload)
            console.log('send => ', sendMsg)
            tcpInstance.sendJsonMsg(
                PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_DATAQUERY,
                PlatformProxyServer_pb.DataQueryPacketType.DATAQUERYPACKETTYPE_CONNECTION,
                sendMsg).then();
        }
        // 监听事件注册
        const initOnMessage = () => {
            // 资产列表
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCLOADASSETS, (pb) => {
                const data = []
                const msg = ProxyServer_pb.MSG_PCLoadAssets.deserializeBinary(pb);
                const assetsBufList = msg.getAssetsList()
                assetsBufList.forEach(bufItem => {
                    try {
                        data.push(util.getArrayDataByBufDataAndTemp(bufItem, ['GID', 'DNAME', 'CONAST']))
                    } catch (e) {
                        console.log('解构err', e)
                    }
                })
                assetList.value = data
                // }
            })
            // 调阅信息
            tcpInstance.onMessage(util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_DATAQUERY, PlatformProxyServer_pb.DataQueryPacketType.DATAQUERYPACKETTYPE_CONNECTION), (pb) => {
                console.log('11111')
                const msg = PlatformProxyServer_pb.MSG_DPConnectionModelQuery.deserializeBinary(pb);
                console.log('12121', msg);
                console.log('12121', msg.getResult(), PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS);
                if (msg.getResult() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                    formatDataFn(msg)
                }
            })
        }
        // 格式化展示数据
        const formatDataFn = (msg) => {
            const addInfoList = msg.getAddinfoList();
            console.log('获取资产', addInfoList, addInfoList.length)
            const getArrayDataByBufDataAndTemp = util.getArrayDataByBufDataAndTemp // 遍历模板取对应值
            const data = []
            const TEMP = asset_map.DATA_TEMP
            const Enum = asset_map.ENUMS
            if(!checkList.value) { // 未选值
                showContent.value = []
                return
            }
            let Connect = ConnectionModelDefine_pb.MSG_Connection
            addInfoList.forEach((anyItem, anyItemIndex) => {
                try {
                    const bytes = anyItem.getValue()
                    console.log('getValue =>',Connect.prototype)
                    console.log('anyurl', anyItem.getTypeUrl())
                    console.log('deserializeBinary0 => ', 'deserializeBinary' in Connect)
                    const deAnyItem = Connect.deserializeBinary(bytes)
                    console.log('deserializeBinary => ', deAnyItem, util.getProtobufFieldValue(deAnyItem, 'physical_connectivity', true))
                    data.push({})
                    if (checkList.value === Number(AssetModelCodeDefine_pb.SpaceConnectionModelCode.SPACECONNECTIONMODELCODE_PHYSICAL_NET_CONN)) {
                        data[anyItemIndex]['physical_connectivity'] = []
                        // 硬件
                        const physical_connectivity_BUF = util.getProtobufFieldValue(deAnyItem, 'physical_connectivity', true)
                        console.log('xxx', physical_connectivity_BUF)
                        physical_connectivity_BUF.forEach(HARDWARE_ITEM_BUF => {
                            console.log('xxx1', HARDWARE_ITEM_BUF)

                            const HARDWARE_ITEM = util.getArrayDataByBufDataAndTemp(HARDWARE_ITEM_BUF, asset_map.DATA_TEMP.physical_connectivity)
                            HARDWARE_ITEM['SOURCE'] = util.setEnumNameByFieldAndCode('SOURCE', HARDWARE_ITEM, asset_map.ENUMS.COMMON.SOURCE)
                            data[anyItemIndex]['physical_connectivity'].push(HARDWARE_ITEM)
                        })
                    }
                    if (checkList.value === Number(AssetModelCodeDefine_pb.SpaceConnectionModelCode.SPACECONNECTIONMODELCODE_ASSET_ACCESS)) {
                        data[anyItemIndex]['network_access'] = []
                        // 硬件
                        const network_access_BUF = util.getProtobufFieldValue(deAnyItem, 'network_access', true)
                        network_access_BUF.forEach(HARDWARE_ITEM_BUF => {
                            const HARDWARE_ITEM = util.getArrayDataByBufDataAndTemp(HARDWARE_ITEM_BUF, asset_map.DATA_TEMP.network_access)
                            HARDWARE_ITEM['SOURCE'] = util.setEnumNameByFieldAndCode('SOURCE', HARDWARE_ITEM, asset_map.ENUMS.COMMON.SOURCE)
                            data[anyItemIndex]['network_access'].push(HARDWARE_ITEM)
                        })
                    }
                    console.log('解析数据', data);
                } catch (e) {
                    console.log('**********解构报错 => ', e);
                }
            })
            showContent.value = data
        }
        const removeConnect = (Type) => {
            if (Type === 'A') {
                connectA.value = {
                   GID: '',
                   DNAME: '',
                   CONAST: 1,
                }
            } else {
                connectB.value = {
                    GID: '',
                    DNAME: '',
                    CONAST: 1,
                }
            }
            console.log('移除比对', Type, connectA.value, connectB.value)
        }
        const addConnect = (Type, row) => {
            const data = {}
            ;['GID','DNAME', 'CONAST'].forEach(item => {
                data[item] = row[item]
            })
            if (Type === 'A') {
                connectA.value = data
            } else {
                connectB.value = data
            }
            console.log('加入比对', Type, connectA.value, connectB.value)
        }

        // 查询表单
        const searchForm = ref({
            GID: '',
            DNAME: ''
        })

        // 模糊过滤后的数据
        const filteredAssetList = computed(() => {
            return assetList.value.filter(item => {
                const gidMatch = item.GID?.toLowerCase().includes(searchForm.value.GID.toLowerCase())
                const nameMatch = item.DNAME?.toLowerCase().includes(searchForm.value.DNAME.toLowerCase())
                return gidMatch && nameMatch
            })
        })
        return {
            searchForm,
            filteredAssetList,
            removeConnect,
            addConnect,
            MAP2CHINA,
            box,
            checkList,
            checkEnum,
            queryModelVisible,
            queryModelHeight,
            queryModelInfoHeight,
            getQueryInfoFront,
            getQueryInfo,
            assetList,
            showContent,
            connectA,
            connectB
        }
    }
})
app.use(ElementPlus, { locale: ElementPlusLocaleZhCn })
app.mount('#app');
