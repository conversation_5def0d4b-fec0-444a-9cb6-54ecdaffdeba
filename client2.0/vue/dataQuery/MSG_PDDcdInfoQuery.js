const util = require('../../lib/util.js');
const tcpInstance = require('../../lib/v2/tls_client.js');
const PublicDefine_pb = require('../../script/pb/PublicDefine_pb.js')
const PlatformProxyServer_pb = require('../../script/pb/PlatformProxyServer_pb.js');
const asset_map = require('./helps/connection_model_helps');
const AssetModelDefine_pb = require('../../script/pb/AssetModelDefine_pb.js');
const { cloneDeep } = require('lodash')
const AssetModelCodeDefine_pb = require('../../script/pb/AssetModelCodeDefine_pb.js');
const ProxyServer_pb = require("../../script/pb/ProxyServer_pb");
const ConnectionModelDefine_pb = require("../../script/pb/ConnectionModelDefine_pb");
// const
const {createApp, ref, onMounted, reactive, onBeforeMount, computed} = Vue;
const app = createApp({
    setup() {
        const box = ref(null)
        onMounted(() => {
            initOnMessage() // 注册响应事件监听
            getQueryInfo()
        })
        // 调阅信息
        const getQueryInfo = () => {
            const msgPayload = {
                uuid: util.genTaskId()
            }
            let sendMsg = JSON.stringify(msgPayload)
            console.log('send => ', sendMsg)
            tcpInstance.sendJsonMsg(
                PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_DATAQUERY,
                PlatformProxyServer_pb.DataQueryPacketType.DATAQUERYPACKETTYPE_DCDINFO,
                sendMsg).then();
        }
        // 监听事件注册
        const initOnMessage = () => {
            // 调阅信息
            tcpInstance.onMessage(util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_DATAQUERY, PlatformProxyServer_pb.DataQueryPacketType.DATAQUERYPACKETTYPE_DCDINFO), (pb) => {
                console.log('self => ', pb)
                const msg = PlatformProxyServer_pb.MSG_DPDcdInfoQuery.deserializeBinary(pb);
                if (msg.getResult() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                    formatDataFn(msg)
                }
            })
        }
        const showData = ref({
            GID: '--', assModVer: '--', actModVer: '--', conModVer: '--'
        })
        const fieldLabels = {
            GID: '装置自身ID',
            assModVer: '资产模型版本',
            actModVer: '行为模型版本',
            conModVer: '连接模型版本',
        }
        // 格式化展示数据
        const formatDataFn = (msg) => {
            const data = {}
            showData.value['GID'] = util.getProtobufFieldValue(msg, 'GID')
            showData.value['assModVer'] = util.getProtobufFieldValue(msg, 'assModVer')
            showData.value['actModVer'] = util.getProtobufFieldValue(msg, 'actModVer')
            showData.value['conModVer'] = util.getProtobufFieldValue(msg, 'conModVer')
            console.log('获取展示数据', data)
        }
        // 过滤掉 undefined/null 的字段（可选）
        const mappedFields = Object.fromEntries(
            Object.entries(showData).filter(([_, v]) => v !== undefined)
        )
        return {
            box,
            mappedFields,
            fieldLabels,
            getQueryInfo,
            showData
        }
    }
})
app.use(ElementPlus, { locale: ElementPlusLocaleZhCn })
app.mount('#app');
