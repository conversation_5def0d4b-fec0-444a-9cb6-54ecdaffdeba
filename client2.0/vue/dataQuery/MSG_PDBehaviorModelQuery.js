const util = require('../../lib/util.js');
const tcpInstance = require('../../lib/v2/tls_client.js');
const PublicDefine_pb = require('../../script/pb/PublicDefine_pb.js')
const PlatformProxyServer_pb = require('../../script/pb/PlatformProxyServer_pb.js');
const AssetModelDefine_pb = require('../../script/pb/AssetModelDefine_pb.js');
const { cloneDeep } = require('lodash')
const AssetModelCodeDefine_pb = require('../../script/pb/AssetModelCodeDefine_pb.js');
const ProxyServer_pb = require("../../script/pb/ProxyServer_pb");
const {createApp, ref, onMounted, reactive, onBeforeMount, computed, onBeforeUnmount} = Vue;
const BEHAVIOR_HELPS = require("./helps/behavior_model_helps"); // 根据行为类型获取：主客体
const LABEL_MAP = require("./helps/LABEL_MAP"); // 标签转义
const ASSET_MAP_HELPS = require("../ASSET_MAP/ASSET_MAP_HELPS");

const app = createApp({
    setup() {
        const baseFlagEnum = ref([
            { code: 'BL', name: '基线分析' },
            { code: 'RL', name: '规则分析' },
            { code: 'AI', name: '智能监测' },
        ])
        const box = ref(null)
        const queryModelVisible = ref(false) // 弹窗显示
        const queryModelCount = ref(0) // 查询条件下总条数
        const queryModelHeight = ref(0) // 弹窗高度
        const queryModelInfoHeight = ref(0) // 弹窗高度
        onMounted(() => {
            resizeHeight() // 重置高度
            window.addEventListener('resize', resizeHeight)
            initOnMessage() // 注册响应事件监听
            getAssetList()
        })
        onBeforeUnmount(() => {
            window.removeEventListener('resize', resizeHeight)
        })
        let assetList = ref([])
        let bigTime = 0
        // 监听事件注册
        const initOnMessage = () => {
            // 资产列表
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCLOADASSETS, (pb) => {
                const data = []
                const msg = ProxyServer_pb.MSG_PCLoadAssets.deserializeBinary(pb);
                const assetsBufList = msg.getAssetsList()
                assetsBufList.forEach(bufItem => {
                    try {
                        console.log('push')
                        data.push(util.getArrayDataByBufDataAndTemp(bufItem, ['GID', 'DNAME', 'CONAST']))
                    } catch (e) {
                        console.log('解构err', e)
                    }
                })
                assetList.value = data
            })
            // 调阅信息
            tcpInstance.onMessage(util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_DATAQUERY, PlatformProxyServer_pb.DataQueryPacketType.DATAQUERYPACKETTYPE_BEHAVIOR), (pb) => {
                console.log('res0 =>');
                const msg = PlatformProxyServer_pb.MSG_DPBehaviorModelQuery.deserializeBinary(pb);
                console.log('res =>', msg.getResult(), PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS);
                if (msg.getResult() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                    queryModelCount.value = msg.getDatacnt() // 总条数
                    // console.log('全量数据1', queryModelCount.value)
                    // formatDataFn(msg)
                    const addInfoList = msg.getAddinfoList();
                    const data = []
                    addInfoList.forEach(anyItem => {
                        const wdwd = anyItem.getActionsList()
                        const ACTIONS_BUF = util.getProtobufFieldValue(anyItem, 'actions', true)
                        console.log('数据量',wdwd.length)
                        ACTIONS_BUF.forEach((ACTIONS_BUF_ITEM, index) => {
                            const dataItem = {
                                CODE: util.getProtobufFieldValue(ACTIONS_BUF_ITEM, 'CODE'), // 行为编码
                                CODENAME: '', // 中文转义
                                TIME: util.getProtobufFieldValue(ACTIONS_BUF_ITEM, 'TIME'), // 发生时间
                                TYPE: util.getProtobufFieldValue(ACTIONS_BUF_ITEM, 'TYPE'), // 动作类型
                                TYPE_NAME: '', // 中文转义
                                RET: JSON.parse(util.getProtobufFieldValue(ACTIONS_BUF_ITEM, 'RET')), // 动作结果
                            }
                            // console.time('耗时测试');
                            // const dataItem = {
                            //     CODE: ACTIONS_BUF_ITEM.getCode(), // 行为编码
                            //     CODENAME: '', // 中文转义
                            //     TIME: ACTIONS_BUF_ITEM.getTime(), // 发生时间
                            //     TYPE: ACTIONS_BUF_ITEM.getType(), // 动作类型
                            //     TYPE_NAME: '', // 中文转义
                            //     RET: JSON.parse(ACTIONS_BUF_ITEM.getRet()), // 动作结果
                            // }
                            // console.timeEnd('耗时测试'); // 结束计时并输出耗时
                            const BEHAVIOR_MAP = BEHAVIOR_HELPS.BEHAVIOR_MAP[util.getProtobufFieldValue(ACTIONS_BUF_ITEM, 'CODE')]
                            if (BEHAVIOR_MAP) {
                                dataItem.CODENAME = BEHAVIOR_MAP.name
                                dataItem.TYPE_NAME = BEHAVIOR_MAP.ACTION_MAP[dataItem.TYPE]
                                try {
                                    if (BEHAVIOR_MAP.AUTH.includes('SUB')) {
                                        dataItem.SUB = util.getProtobufFieldValue(ACTIONS_BUF_ITEM, 'SUB')
                                    }
                                    if (BEHAVIOR_MAP.AUTH.includes('OBJ')) {
                                        dataItem.OBJ = util.getProtobufFieldValue(ACTIONS_BUF_ITEM, 'OBJ')
                                    }
                                } catch (e) {
                                    console.log('解构报错 =>', e)
                                }

                            }
                            data.push(dataItem)
                        })
                    })
                    console.log('全量数据', data, queryModelCount.value)
                    queryData.value = data

                }
                console.timeEnd(`耗时测试`); // 结束计时并输出耗时
            })
        }
        const pageNum = ref(1)
        let searchData = reactive({
            assetId: '',
            CONAST: 1,
            modelType: 0,
            offset: 0,
            baseFlag: [],
            startTime: '0',
            endTime: '0'
        })
        const resetSearchData = () => {
            type.value = 0
            dateRange.value = null
            pageNum.value = 0
            baseFlag.value = []
            searchData.baseFlag = []
            searchData.modelType = 0
            searchData.startTime = '0'
            searchData.endTime = '0'
        }
        // 获取资产列表
        const getAssetList = () => {
            let sendMsg = new ProxyServer_pb.MSG_CPLoadAssets();
            tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADASSETS, sendMsg).then()
        }
        const resizeHeight = () => {
            queryModelHeight.value = box.value.clientHeight ? box.value.clientHeight : 100
            queryModelInfoHeight.value = box.value.clientHeight ? box.value.clientHeight * 0.8 : 80
        }
        // 根据资产信息前置调阅
        const getQueryBehaviorByAssetId = (row) => {
            Object.assign(searchData, {
                assetId: row.GID,
                CONAST: row.CONAST ? 1 : 2,
                baseFlag: []
            })
            resetSearchData()
            pageNum.value = 1
            queryModelVisible.value = true
            queryBehaviorBySearchData(true)
        }
        // 调阅数据
        const queryData = ref([])
        // 重置
        const resetQueryBehaviorBySearchData = () => {
            Object.assign(searchData,  {
                modelType: 0,
                offset: 0,
                baseFlag: [],
                startTime: '0',
                endTime: '0'
            })
            baseFlag.value = []
            type.value = 0
            dateRange.value = null
            pageNum.value = 1
            queryBehaviorBySearchData()
        }
        const changeBaseFlag = (val) => {
            searchData.baseFlag = val
        }
        let lastData = 0
        // 调阅资产下的行为模型数据
        const queryBehaviorBySearchData = (isInit = false) => {
            console.log('调阅模型', cloneDeep(searchData))
            const data = cloneDeep(searchData)
            data.uuid = util.genTaskId();
            let sendMsg = JSON.stringify(cloneDeep(data))
            console.log('send =>', sendMsg)
            tcpInstance.sendJsonMsg(
                PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_DATAQUERY,
                PlatformProxyServer_pb.DataQueryPacketType.DATAQUERYPACKETTYPE_BEHAVIOR,
                sendMsg).then();
        }
        // 动作结果
        const actionResultVisible = ref(false)
        const actionResult = ref(null)
        const actionResultData = ref([])
        // 动作结果
        const actionResultFn = (row) => {
            console.log('行为结果', row)
            if ('RET' in row) {
                actionResult.value = row.RET
                actionResultData.value = row.RET.LBL.map(item => {
                    return {
                        ...item,
                        NAME: { BL: '越线标签', SUS: '风险标签' }[item.N],
                        LABEL: LABEL_MAP.getNameByL(item.L),
                    }
                })
            }
            actionResultVisible.value = true
        }
        const SUBOrOBJVisible = ref(false)
        const SUBOrOBJShowData = ref([])
        const SUBOrOBJTitle = ref('主体')
        const SUBOrOBJMap = ref({})
        const SUBOrOBJShowName = ref('')
        const getSUBOrSUBByRowFn = (TYPE, row) => {
            SUBOrOBJTitle.value = TYPE === 'SUB' ? '主体详情' : '客体详情'
            const BEHAVIOR_MAP = BEHAVIOR_HELPS.BEHAVIOR_MAP[row.CODE] // 获取map实体
            if (BEHAVIOR_MAP) {
                try {
                    const bytes = row[TYPE].getValue()
                    const msg = BEHAVIOR_MAP[TYPE].PROTO.deserializeBinary(bytes)
                    SUBOrOBJMap.value = BEHAVIOR_MAP[TYPE].MAP
                    SUBOrOBJShowName.value = ASSET_MAP_HELPS.COMMON_MAP[BEHAVIOR_MAP[TYPE].name]
                    SUBOrOBJShowData.value = [{INFO: [util.buildObjectFromProto(BEHAVIOR_MAP[TYPE].MAP, msg, true)]}]
                    console.log('获取主客体展示数据', SUBOrOBJShowData.value)
                } catch (e) {
                    console.log('解构主客体报错 =>', e)
                    SUBOrOBJShowData.value = []
                }
            } else {
                SUBOrOBJShowData.value = []
            }
            SUBOrOBJVisible.value = true
        }
        const getDataType = (data) => {
            return util.getDataType(data)
        }
        // 查询表单
        const searchForm = ref({
            GID: '',
            DNAME: ''
        })
        // 模糊过滤后的数据
        const filteredAssetList = computed(() => {
            return assetList.value.filter(item => {
                const gidMatch = item.GID?.toLowerCase().includes(searchForm.value.GID.toLowerCase())
                const nameMatch = item.DNAME?.toLowerCase().includes(searchForm.value.DNAME.toLowerCase())
                return gidMatch && nameMatch
            })
        })
        // 调阅界面筛选用
        const props = ref({ multiple: false, emitPath: false })
        let modelTypeEnums = ref([])
        const type = ref(0)
        const baseFlag = ref([])
        const dateRange = ref(null)
        const typeChangeFn = (val) => {
            if (val === 0) {
                modelTypeEnums.value = []
                searchData.modelType = 0
            } else {
                const mapper = { 1: BEHAVIOR_HELPS.HARDWARE, 2: BEHAVIOR_HELPS.SOFTWARE, 3: BEHAVIOR_HELPS.NETWORK }[val]
                modelTypeEnums.value = Object.keys(mapper).map(key => {
                    return {label: mapper[key].name, value: Number(key) }
                })
                searchData.modelType = modelTypeEnums.value[0].value
            }
        }
        const changeTimeRange = (val) => {
            console.log('切换时间', val)
            if (!val) {
                searchData.startTime = '0'
                searchData.endTime = '0'
            } else {
                searchData.startTime = val[0]
                searchData.endTime = val[1]
            }
            console.log('查询', searchData)
        }
        const queryCurrentChange = (val) => {
            pageNum.value = val
            queryBehaviorBySearchData()
        }
        const closeQueryModel = () => {
            queryModelVisible.value = false
            resetSearchData()
        }
        return {
            closeQueryModel,
            changeTimeRange,
            dateRange,
            filteredAssetList,
            searchForm,
            box,
            assetList,
            // 调阅用
            searchData,
            queryModelHeight,
            queryModelInfoHeight,
            queryModelVisible,
            getQueryBehaviorByAssetId,
            pageNum,
            queryModelCount,
            queryData,
            // 行为结果
            actionResultVisible,
            actionResultFn,
            actionResult,
            actionResultData,
            // 主体或客体展示
            SUBOrOBJVisible,
            SUBOrOBJShowData,
            SUBOrOBJTitle,
            SUBOrOBJMap,
            SUBOrOBJShowName,
            getSUBOrSUBByRowFn,
            getDataType,
            baseFlagEnum,
            // 调阅筛选用数据
            queryCurrentChange,
            queryBehaviorBySearchData,
            resetQueryBehaviorBySearchData,
            type,
            baseFlag,
            changeBaseFlag,
            props,
            modelTypeEnums,
            typeChangeFn
        }
    }
})
app.use(ElementPlus, { locale: ElementPlusLocaleZhCn })
app.mount('#app');
