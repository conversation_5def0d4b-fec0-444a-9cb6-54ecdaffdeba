const util = require('../../lib/util.js');
const tcpInstance = require('../../lib/v2/tls_client.js');
const PublicDefine_pb = require('../../script/pb/PublicDefine_pb.js')
const PlatformProxyServer_pb = require('../../script/pb/PlatformProxyServer_pb.js');
const AssetModelDefine_pb = require('../../script/pb/AssetModelDefine_pb.js');
const { cloneDeep } = require('lodash')
const AssetModelCodeDefine_pb = require('../../script/pb/AssetModelCodeDefine_pb.js');
const ProxyServer_pb = require("../../script/pb/ProxyServer_pb");
const {createApp, ref, onMounted, reactive, onBeforeMount, computed, onBeforeUnmount} = Vue;
const BEHAVIOR_HELPS = require("./helps/behavior_model_helps");
const LABEL_MAP = require("./helps/LABEL_MAP");
const ASSET_STATE_MAP_HELPS = require("./helps/ASSET_STATE_MODEL_HELPS");
const ui_util = require('../../script/view/ui_util.js');


const app = createApp({
    setup() {
        const box = ref(null)
        const queryModelVisible = ref(false) // 弹窗显示
        const queryModelCount = ref(0) // 查询条件下总条数
        const queryModelHeight = ref(0) // 弹窗高度
        const queryModelInfoHeight = ref(0) // 弹窗高度
        const assetListRef = ref(null) // 资产列表
        onMounted(() => {
            resizeHeight() // 重置高度
            window.addEventListener('resize', resizeHeight)
            initOnMessage() // 注册响应事件监听
            getAssetList()
        })
        onBeforeUnmount(() => {
            window.removeEventListener('resize', resizeHeight)
        })
        let assetList = ref([]) // 资产列表
        const queryData = ref([]) // 调阅信息
        const selectTableData = ref([]) // 表格选中数据
        const showContent = ref([])
        const treeData = ref([])
        const MAP2CHINA = ref({})
        const treeRef = ref(null)
        // 监听事件注册
        const initOnMessage = () => {
            // 资产列表
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCLOADASSETS, (pb) => {
                console.log('获取资产列表', pb)
                const data = []
                const msg = ProxyServer_pb.MSG_PCLoadAssets.deserializeBinary(pb);
                const assetsBufList = msg.getAssetsList()
                assetsBufList.forEach(bufItem => {
                    try {
                        data.push(util.getArrayDataByBufDataAndTemp(bufItem, ['GID', 'DNAME', 'CONAST', 'ATAG']))
                    } catch (e) {
                        console.log('解构err', e)
                    }
                })
                console.log('资产列表assetList.value =>', data)
                assetList.value = data
            })

            // 调阅信息
            tcpInstance.onMessage(util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_DATAQUERY, PlatformProxyServer_pb.DataQueryPacketType.DATAQUERYPACKETTYPE_ASSETSTATUS), (pb) => {
                console.log('res0 =>');
                const msg = PlatformProxyServer_pb.MSG_DPAssetStatusQuery.deserializeBinary(pb);
                console.log('res =>', msg.getResult(), PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS);
                if (msg.getResult() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                    const addInfoList = msg.getAddinfoList();
                    console.log('数据', addInfoList)
                    const data = []
                    MAP2CHINA.value = ASSET_STATE_MAP_HELPS.ENUMS.ASSET
                    addInfoList.forEach(anyItem => {
                        console.log('模版 =>', ASSET_STATE_MAP_HELPS.ENUMS.ASSET, anyItem)
                        const dataItem = util.buildObjectFromProto(ASSET_STATE_MAP_HELPS.ENUMS.ASSET, anyItem)
                        console.log('获取数据', dataItem)
                        data.push(dataItem)
                    })
                    const tree = []
                    selectTableData.value.forEach(item => {
                        if (item.CONAST) {
                            data.forEach((itm, idx) => {
                                if (itm.GID === item.GID) tree.push({ label: item.DNAME, value: itm.GID, dataIndex: idx })
                            })
                        } else {
                            data.forEach((itm, idx) => {
                                if (itm.ATAG === item.ATAG) tree.push({ label: item.DNAME, value: itm.ATAG, dataIndex: idx })
                            })
                        }
                        treeData.value = tree
                        queryData.value = data
                        setTimeout(() => {
                            if (treeData.value.length && queryData.value.length) {
                                treeRef.value.setCurrentKey(treeData.value[0].value)
                                showContent.value = [{INFO: [queryData.value[treeData.value[0].dataIndex]]}]
                            }
                        }, 500)
                        console.log('treeData', tree)
                    })
                    queryModelVisible.value = true
                    console.log('queryData =>', queryData.value, 'treeData =>', treeData.value)
                } else {
                    ui_util.getErrorTips(msg.getResult(), '验证用户身份')
                }
            })
        }
        // 获取资产列表
        const getAssetList = () => {
            let sendMsg = new ProxyServer_pb.MSG_CPLoadAssets();
            console.log('send asset_list => ', sendMsg)
            tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADASSETS, sendMsg).then()
        }
        const resizeHeight = () => {
            queryModelHeight.value = box.value.clientHeight ? box.value.clientHeight : 100
            queryModelInfoHeight.value = box.value.clientHeight ? box.value.clientHeight * 0.8 : 80
        }
        // 调阅资产下的行为模型数据
        const queryBehaviorBySearchData = () => {
            const selectData = assetListRef.value.getSelectionRows()
            if (!selectData.length) return ui_util.showFadeTip('请勾选调阅数据！')
            const data = { uuid: util.genTaskId() }
            data.GID = []
            data.ATAG = []
            selectData.forEach(item => {
                if (!item.CONAST) {
                    data.ATAG.push(item.ATAG)
                } else {
                    data.GID.push(item.GID)
                }
            })
            selectTableData.value = assetListRef.value.getSelectionRows()
            let sendMsg = JSON.stringify(cloneDeep(data))
            console.log('send =>', sendMsg)
            tcpInstance.sendJsonMsg(
                PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_DATAQUERY,
                PlatformProxyServer_pb.DataQueryPacketType.DATAQUERYPACKETTYPE_ASSETSTATUS,
                sendMsg).then();
        }
        const getSUBOrSUBByRowFn = (TYPE, row) => {
            /**
             SUBOrOBJTitle.value = TYPE === 'SUB' ? '主体详情' : '客体详情'
             const BEHAVIOR_MAP = BEHAVIOR_HELPS.BEHAVIOR_MAP[row.CODE] // 获取map实体
             if (BEHAVIOR_MAP) {
             try {
             const bytes = row[TYPE].getValue()
             const msg = BEHAVIOR_MAP[TYPE].PROTO.deserializeBinary(bytes)
             SUBOrOBJMap.value = BEHAVIOR_MAP[TYPE].MAP
             SUBOrOBJShowName.value = ASSET_MAP_HELPS.COMMON_MAP[BEHAVIOR_MAP[TYPE].name]
             SUBOrOBJShowData.value = [{INFO: [util.buildObjectFromProto(BEHAVIOR_MAP[TYPE].MAP, msg, true)]}]
             console.log('获取主客体展示数据', SUBOrOBJShowData.value)
             } catch (e) {
             console.log('解构主客体报错 =>', e)
             SUBOrOBJShowData.value = []
             }
             } else {
             SUBOrOBJShowData.value = []
             }
             SUBOrOBJVisible.value = true
             * */
        }
        const getDataType = (data) => {
            return util.getDataType(data)
        }
        // 查询表单
        const searchForm = ref({GID: '', DNAME: ''})
        // 模糊过滤后的数据
        const filteredAssetList = computed(() => {
            return assetList.value.filter(item => {
                const gidMatch = item.GID?.toLowerCase().includes(searchForm.value.GID.toLowerCase())
                const nameMatch = item.DNAME?.toLowerCase().includes(searchForm.value.DNAME.toLowerCase())
                return gidMatch && nameMatch
            })
        })
        // 点击树节点
        const handleNodeClick = (data, node) => {
            console.log('当前点击节点:', data, data.dataIndex)
            if (queryData.value.length) {
                showContent.value = [{INFO: [queryData.value[data.dataIndex]]}]
            }
        }
        return {

            handleNodeClick,
            assetListRef,
            filteredAssetList,
            searchForm,
            box,
            assetList,
            // 调阅用
            treeData,
            queryData,
            queryModelHeight,
            queryModelInfoHeight,
            queryModelVisible,
            queryModelCount,
            getSUBOrSUBByRowFn,
            getDataType,
            // 调阅筛选用数据
            queryBehaviorBySearchData,
            showContent,
            MAP2CHINA,
            treeRef
        }
    }
})
app.use(ElementPlus, { locale: ElementPlusLocaleZhCn })
app.mount('#app');
