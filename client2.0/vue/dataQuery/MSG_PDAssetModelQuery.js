const util = require('../../lib/util.js');
const tcpInstance = require('../../lib/v2/tls_client.js');
const PublicDefine_pb = require('../../script/pb/PublicDefine_pb.js')
const PlatformProxyServer_pb = require('../../script/pb/PlatformProxyServer_pb.js');
const asset_map = require('./helps/asset_model_helps');
const AssetModelDefine_pb = require('../../script/pb/AssetModelDefine_pb.js');
const { cloneDeep } = require('lodash')
const AssetModelCodeDefine_pb = require('../../script/pb/AssetModelCodeDefine_pb.js');
const ProxyServer_pb = require("../../script/pb/ProxyServer_pb");
const ASSET_MAP_HELPS = require('../ASSET_MAP/ASSET_MAP_HELPS')
const {createApp, ref, onMounted, reactive, onBeforeMount} = Vue;
const app = createApp({
    setup() {
        const MAP2CHINA = ASSET_MAP_HELPS.ASSET_MODEL_QUERY.ASSET_MAP
        let showContent = ref([])
        let assetList = ref([])
        const box = ref(null)
        const checkList = ref([
            Number(AssetModelCodeDefine_pb.SpaceAssetModelCode.SPACE_ASSET),
            Number(AssetModelCodeDefine_pb.HardwareAssetModelCode.HARDWARE),
            Number(AssetModelCodeDefine_pb.SoftwareAssetModelCode.SOFTWARE),
            Number(AssetModelCodeDefine_pb.NetworkAssetModelCode.NETWORK),
        ])
        const checkEnum = ref([
            { name: '资产节点', code: Number(AssetModelCodeDefine_pb.SpaceAssetModelCode.SPACE_ASSET) },
            { name: '硬件', code: Number(AssetModelCodeDefine_pb.HardwareAssetModelCode.HARDWARE) },
            { name: '软件', code: Number(AssetModelCodeDefine_pb.SoftwareAssetModelCode.SOFTWARE) },
            { name: '网络', code: Number(AssetModelCodeDefine_pb.NetworkAssetModelCode.NETWORK) },
        ])
        onMounted(() => {
            console.log('box', box.value.clientHeight)
            queryModelHeight.value = box.value.clientHeight ? box.value.clientHeight : 100
            queryModelInfoHeight.value = box.value.clientHeight ? box.value.clientHeight * 0.9 : 80
            window.addEventListener('resize', () => {
                console.log('resize => ', box.value.clientHeight)
                queryModelHeight.value = box.value.clientHeight ? box.value.clientHeight : 100
                queryModelInfoHeight.value = box.value.clientHeight ? box.value.clientHeight * 0.9 : 80
            })
            initOnMessage() // 注册响应事件监听
            getAssetList()
        })
        const queryModelVisible = ref(false) // 弹窗显示
        const queryModelHeight = ref(0) // 弹窗高度
        const queryModelInfoHeight = ref(0) // 弹窗高度
        // 获取资产列表
        const getAssetList = () => {
            let sendMsg = new ProxyServer_pb.MSG_CPLoadAssets();
            tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADASSETS, sendMsg).then()
        }
        let queryModelInfo = {}
        // 前置调阅按钮
        const getQueryInfoFront = (item) => {
            queryModelVisible.value = true
            checkList.value = [
                // 资产节点
                Number(AssetModelCodeDefine_pb.SpaceAssetModelCode.SPACE_ASSET),
                // 硬件
                Number(AssetModelCodeDefine_pb.HardwareAssetModelCode.HARDWARE),
                // 软件
                Number(AssetModelCodeDefine_pb.SoftwareAssetModelCode.SOFTWARE),
                // 网络
                Number(AssetModelCodeDefine_pb.NetworkAssetModelCode.NETWORK),
            ]
            queryModelInfo = item
            getQueryInfo()
        }
        // 调阅信息
        const getQueryInfo = () => {
            const msgPayload = cloneDeep({ uuid: util.genTaskId(), CONAST: queryModelInfo.CONAST ? 1 : 2, assetId: queryModelInfo.GID, modelType: checkList.value })
            msgPayload.modelType = !checkList.value.length || checkList.value.length === 4 ? [268435456] : msgPayload.modelType

            let sendMsg = JSON.stringify(msgPayload)
            console.log('send => ', sendMsg)
            tcpInstance.sendJsonMsg(
                PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_DATAQUERY,
                PlatformProxyServer_pb.DataQueryPacketType.DATAQUERYPACKETTYPE_ASSET,
                sendMsg).then();
        }
        // 监听事件注册
        const initOnMessage = () => {
            // 资产列表
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCLOADASSETS, (pb) => {
                const data = []
                const msg = ProxyServer_pb.MSG_PCLoadAssets.deserializeBinary(pb);
                    const assetsBufList = msg.getAssetsList()
                    assetsBufList.forEach(bufItem => {
                        try {
                            data.push(util.getArrayDataByBufDataAndTemp(bufItem, ['GID', 'DNAME', 'CONAST']))
                        } catch (e) {
                            console.log('解构err', e)
                        }
                    })
                    assetList.value = data
                // }
            })
            // 调阅信息
            tcpInstance.onMessage(util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_DATAQUERY, PlatformProxyServer_pb.DataQueryPacketType.DATAQUERYPACKETTYPE_ASSET), (pb) => {
                console.log('11111')
                const msg = PlatformProxyServer_pb.MSG_DPAssetModelQuery.deserializeBinary(pb);
                console.log('12121', msg.getResult(), PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS);
                if (msg.getResult() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                    formatDataFn(msg)
                }
            })
        }
        // 格式化展示数据
        const formatDataFn = (msg) => {
            const addInfoList = msg.getAddinfoList();
            console.log('获取资产 =>', addInfoList, addInfoList.length)
            let ASSET_PB = AssetModelDefine_pb.MSG_ASSET
            const getArrayDataByBufDataAndTemp = util.getArrayDataByBufDataAndTemp // 遍历模板取对应值
            const data = {
                ASSET: {}, // 资产基本信息
                ASSET_NODE: [], // 资产节点
                HARDWARE: [], // 硬件
                SOFTWARE: [], // 软件
                NETWORK: [], // 网络
            }
            const TEMP = asset_map.DATA_TEMP
            const Enum = asset_map.ENUMS
            addInfoList.forEach(anyItem => {
                try {
                    // 资产
                    data.ASSET = util.buildObjectFromProto(ASSET_MAP_HELPS.ASSET_MODEL_QUERY.ASSET, anyItem, true)
                    // 资产节点
                    if (checkList.value.includes(Number(AssetModelCodeDefine_pb.SpaceAssetModelCode.SPACE_ASSET)) || !checkList.value.length) {
                        // 硬件
                        const ASSET_NODE_BUF = util.getProtobufFieldValue(anyItem, 'ASSET_NODE', true)
                        ASSET_NODE_BUF.forEach(ASSET_NODE_BUF_ITEM => {
                            data.ASSET_NODE.push(util.buildObjectFromProto(ASSET_MAP_HELPS.ASSET_MODEL_QUERY.ASSET_NODE, ASSET_NODE_BUF_ITEM, true))
                        })
                    } else {
                        delete data.ASSET_NODE
                    }
                    // 数据来源
                    if (checkList.value.includes(Number(AssetModelCodeDefine_pb.HardwareAssetModelCode.HARDWARE)) || !checkList.value.length) {
                        // 硬件
                        const HARDWARE_BUF = util.getProtobufFieldValue(anyItem, 'HARDWARE', true)
                        HARDWARE_BUF.forEach(HARDWARE_ITEM_BUF => {
                            data.HARDWARE.push(util.buildObjectFromProto(ASSET_MAP_HELPS.ASSET_MODEL_QUERY.HARDWARE, HARDWARE_ITEM_BUF, true))
                        })
                    } else {
                        delete data.HARDWARE
                    }
                    if (checkList.value.includes(Number(AssetModelCodeDefine_pb.SoftwareAssetModelCode.SOFTWARE)) || !checkList.value.length) {
                        // // 软件
                        const SOFTWARE_BUF = util.getProtobufFieldValue(anyItem, 'SOFTWARE', true)
                        SOFTWARE_BUF.forEach(SOFTWARE_ITEM_BUF => {
                            data.SOFTWARE.push(util.buildObjectFromProto(ASSET_MAP_HELPS.ASSET_MODEL_QUERY.SOFTWARE, SOFTWARE_ITEM_BUF, true))
                        })
                    } else {
                        delete data.SOFTWARE
                    }
                    if (checkList.value.includes(Number(AssetModelCodeDefine_pb.NetworkAssetModelCode.NETWORK)) || !checkList.value.length) {
                        // 网络
                        const NETWORK_BUF = util.getProtobufFieldValue(anyItem, 'NETWORK', true)
                        NETWORK_BUF.forEach(NETWORK_ITEM_BUF => {
                            data.NETWORK.push(util.buildObjectFromProto(ASSET_MAP_HELPS.ASSET_MODEL_QUERY.NETWORK, NETWORK_ITEM_BUF, true))
                        })
                    } else {
                        delete data.NETWORK
                    }


                    console.log('解析数据', data);
                    showContent.value = [data]
                } catch (e) {
                    console.log('**********解构报错 => ', e);
                }
            })
        }
        return {
            MAP2CHINA,
            box,
            checkList,
            checkEnum,
            queryModelVisible,
            queryModelHeight,
            queryModelInfoHeight,
            getQueryInfoFront,
            getQueryInfo,
            assetList,
            showContent,

        }
    }
})
app.use(ElementPlus, { locale: ElementPlusLocaleZhCn })
app.mount('#app');
