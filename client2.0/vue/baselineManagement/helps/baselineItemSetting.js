const BaselineDefine_pb = require('../../../script/pb/BaselineDefine_pb');
const RULES_SETTING = require('../../ASSET_MAP/ASSET_FORM_RULES')
const baseLineItem = {
    // 服务端网络访问基线
    [BaselineDefine_pb.BaselineLabel.BASELINE_SERVER_NET_ACCESS]: {
        name: '服务端网络访问基线',
        Item: [
            { prop: 'PROTO', label: '可解最高层协议', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'CLIENTIP', label: '访问客户端IP（主体IP）', type: 'INPUT', rules: RULES_SETTING.ipValidateRule('', true) },
            { prop: 'GID', label: 'GID(客体)', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'SERVERIP', label: '服务端IP（客体IP）', type: 'INPUT', rules: RULES_SETTING.ipValidateRule('', true) },
            { prop: 'SERVERPORT', label: '服务端端口（客体PORT）', type: 'INPUT', rules: RULES_SETTING.portValidateRule('', true) },
            { prop: 'GENMETHOD',
                label: '基线生成方式',
                type: 'SELECT',
                options: [{ code: 1, name: '自统计生成' }, { code: 2, name: '人工设置' }],
                rules: RULES_SETTING.requiredSelectRule()
            },
        ],
        protobuf: BaselineDefine_pb.MSG_ServerAccessBL
    },
    // 进程网络访问基线
    [BaselineDefine_pb.BaselineLabel.BASELINE_PROCESS_NET_ACCESS]: {
        name: '进程网络访问基线',
        Item: [
            { prop: 'EXENM', label: '进程可执行文件名', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'EXEDIR', label: '可执行文件全路径', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'GID', label: '资产ID', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'CLIENTIP', label: '客户端IP', type: 'INPUT', rules: RULES_SETTING.ipValidateRule('', true) },
            { prop: 'SERVERIP', label: '服务端IP', type: 'INPUT', rules: RULES_SETTING.ipValidateRule('', true) },
            { prop: 'SERVERPORT', label: '服务端端口', type: 'INPUT', rules: RULES_SETTING.portValidateRule() },
            { prop: 'PROTO', label: '最高层协议', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'GENMETHOD', label: '基线生成方式', type: 'SELECT', options: [{ code: 1, name: '自学习' }, { code: 2, name: '人工设置' }], rules: RULES_SETTING.requiredSelectRule() }
        ],
        protobuf: BaselineDefine_pb.MSG_ProcessNetworkAccessBL
    },
    // 网络端口监听基线
    [BaselineDefine_pb.BaselineLabel.BASELINE_NET_PORT_LISTEN]: {
        name: '网络端口监听基线',
        Item: [
            { prop: 'FNAME', label: '进程文件名', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'GID', label: '资产ID', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'SRCIP', label: '绑定IP', type: 'INPUT', rules: RULES_SETTING.ipValidateRule('', true) },
            { prop: 'PORT', label: '监听端口', type: 'INPUT', rules: RULES_SETTING.portValidateRule('', true) },
            { prop: 'PROTO', label: '最高层协议', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'GENMETHOD', label: '基线生成方式', type: 'SELECT', options: [{ code: 1, name: '自学习' }, { code: 2, name: '人工设置' }], rules: RULES_SETTING.requiredSelectRule() }
        ],
        protobuf: BaselineDefine_pb.MSG_PortListeningBL
    },
    // ARP信息基线
    [BaselineDefine_pb.BaselineLabel.BASELINE_ARP_INFO]: {
        name: 'ARP信息基线',
        Item: [
            { prop: 'GID', label: 'GID', type: 'INPUT' },
            { prop: 'IP', label: '对端IP', type: 'INPUT', rules: RULES_SETTING.ipValidateRule('', true) },
            { prop: 'MAC', label: '对端MAC', type: 'INPUT', rules: RULES_SETTING.macValidateRule('', true) },
            { prop: 'IFNM', label: '网络接口名', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'GENMETHOD', label: '基线生成方式', type: 'SELECT', options: [{ code: 1, name: '自学习' }, { code: 2, name: '人工设置' }], rules: RULES_SETTING.requiredSelectRule() }
        ],
        protobuf: BaselineDefine_pb.MSG_ArpBL
    },
    // 主机进程基线
    [BaselineDefine_pb.BaselineLabel.BASELINE_HOST_PROCESS]: {
        name: '主机进程基线',
        Item: [
            { prop: 'GID', label: 'GID', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'FNAME', label: '进程文件名', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'SCMD', label: 'BASE64编码的启动命令', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'USER', label: '启动用户', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'GENMETHOD', label: '基线生成方式', type: 'SELECT', options: [{ code: 1, name: '自学习' }, { code: 2, name: '人工设置' }], rules: RULES_SETTING.requiredSelectRule() }
        ],
        protobuf: BaselineDefine_pb.MSG_ProcessBL

    },
    // 关键文件访问基线
    [BaselineDefine_pb.BaselineLabel.BASELINE_KEY_FILE_ACCESS]: {
        name: '关键文件访问基线',
        Item: [
            { prop: 'SCMD', label: 'BASE64编码的启动命令', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'GID', label: 'GID', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'NAME', label: '文件名', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'PATH', label: '文件路径', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'GENMETHOD', label: '基线生成方式', type: 'SELECT', options: [{ code: 1, name: '自学习生成' }, { code: 2, name: '人工设置' }], rules: RULES_SETTING.requiredSelectRule() }
        ],
        protobuf: BaselineDefine_pb.MSG_CriticalFileAccessBL
    },
    // 关键文件变更基线
    [BaselineDefine_pb.BaselineLabel.BASELINE_KEY_FILE_CHANGE]: {
        name: '关键文件变更基线',
        Item: [
            { prop: 'SCMD', label: 'BASE64编码的启动命令', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'GID', label: 'GID', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'NAME', label: '文件名', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'PATH', label: '文件路径', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'TIME_GROUP', label: '时间段编号', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'COUNT', label: '变更次数阈值', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'GENMETHOD', label: '基线生成方式', type: 'SELECT', options: [{ code: 1, name: '自学习' }, { code: 2, name: '人工设置' }], rules: RULES_SETTING.requiredSelectRule() }
        ],
        protobuf: BaselineDefine_pb.MSG_CriticalFileChangeBL
    },
    // 进程系统调用基线
    [BaselineDefine_pb.BaselineLabel.BASELINE_PROC_SYSCALL]: {
        name: '进程系统调用基线',
        Item: [
            { prop: 'SCMD', label: 'BASE64编码的启动命令', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'GID', label: 'GID', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'SYSCALL', label: '系统调用名', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'GENMETHOD', label: '基线生成方式', type: 'SELECT', options: [{ code: 1, name: '自学习' }, { code: 2, name: '人工设置' }], rules: RULES_SETTING.requiredSelectRule() }
        ],
        protobuf: BaselineDefine_pb.MSG_ProcessSyscallBL
    },
    // 主机服务启用基线
    [BaselineDefine_pb.BaselineLabel.BASELINE_HOST_SERVICE]: {
        name: '主机服务启用基线',
        Item: [
            { prop: 'GID', label: 'GID', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'NAME', label: '服务名称', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'GENMETHOD', label: '基线生成方式', type: 'SELECT', options: [{ code: 1, name: '自学习生成' }, { code: 2, name: '人工设置' }], rules: RULES_SETTING.requiredSelectRule() }
        ],
        protobuf: BaselineDefine_pb.MSG_ServiceBL
    },
    // 主机驱动加载基线
    [BaselineDefine_pb.BaselineLabel.BASELINE_HOST_DRIVER_LOAD]: {
        name: '主机驱动加载基线',
        Item: [
            { prop: 'GID', label: 'GID', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'NAME', label: '驱动名称', type: 'INPUT', rules: RULES_SETTING.requiredInputRule() },
            { prop: 'GENMETHOD', label: '基线生成方式', type: 'SELECT', options: [{ code: 1, name: '自学习生成' }, { code: 2, name: '人工设置' }], rules: RULES_SETTING.requiredSelectRule() }
        ],
        protobuf: BaselineDefine_pb.MSG_DriverBL
    }
}
// 返回基线 code name
function getEnumByBaseLineItem() {
    const arr = []
    Object.keys(baseLineItem).forEach(key => arr.push({ code: key, name: baseLineItem[key]['name'] }))
    return arr
}
function getRulesByBaseLineCode(code) {
    const obj = {}
    if (code in baseLineItem) {
        const Item = baseLineItem[code]['Item']
        Item.forEach(item => obj[item.prop] = item.rules)
        console.log('返回rules', code, baseLineItem[code]['Item'], obj)
        return obj
    } else {
        return {}
    }
}
function getFieldArrByCode(code) {
    const data = []
    if (code && code in baseLineItem) {
        const Item = baseLineItem[code]['Item']
        Item.forEach(item => {
            data.push(item.prop)
        })
    }
    return data
}
const studyTimeRules = {
    studyTime: RULES_SETTING.positiveIntegerInputRule('统计周期', true)
}
module.exports = {
    baseLineItem,
    getEnumByBaseLineItem,
    getRulesByBaseLineCode,
    studyTimeRules,
    getFieldArrByCode
}