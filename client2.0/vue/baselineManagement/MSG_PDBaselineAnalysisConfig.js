// proto
const ProxyServer_pb = require("../../script/pb/ProxyServer_pb");
const PlatformProxyServer_pb = require('../../script/pb/PlatformProxyServer_pb.js');
const PublicDefine_pb = require('../../script/pb/PublicDefine_pb.js')
const AssetModelDefine_pb = require('../../script/pb/AssetModelDefine_pb.js');
const BaselineDefine_pb = require('../../script/pb/BaselineDefine_pb');

const HELPS = require('./helps/baselineItemSetting')
// js
const util = require('../../lib/util.js');
const tcpInstance = require('../../lib/v2/tls_client.js');
const { cloneDeep } = require('lodash')
const ui_util = require("../../script/view/ui_util");
const {createApp, ref, onMounted, reactive, onBeforeMount, computed, onBeforeUnmount} = Vue;
const { ElMessage, ElMessageBox } = ElementPlus;
const app = createApp({
    setup() {
        const box = ref(null)
        const queryModelVisible = ref(false) // 弹窗显示
        const queryModelHeight = ref(0) // 弹窗高度
        const updateModelHeight = ref(0) // 弹窗高度
        const queryModelInfoHeight = ref(0) // 弹窗高度

        let baseLineOptions = ref(HELPS.getEnumByBaseLineItem()) // 基线列表code,name
        console.log(HELPS.getEnumByBaseLineItem(), util.getDataType(String(BaselineDefine_pb.BaselineLabel.BASELINE_SERVER_NET_ACCESS)))
        let baseLineTypeValue = ref(String(BaselineDefine_pb.BaselineLabel.BASELINE_SERVER_NET_ACCESS)) // 选中的基线类型
        let baseLineOptionsItem = ref(HELPS.baseLineItem[String(BaselineDefine_pb.BaselineLabel.BASELINE_SERVER_NET_ACCESS)].Item)
        let baseLineRules = ref(HELPS.getRulesByBaseLineCode(String(BaselineDefine_pb.BaselineLabel.BASELINE_SERVER_NET_ACCESS)))

        let assetList = ref([]) // 资产列表
        onMounted(() => {
            console.log('box', box.value.clientHeight)
            resizeHeight()
            window.addEventListener('resize', resizeHeight)
            initOnMessage() // 注册响应事件监听
            getAssetList()
        })
        onBeforeUnmount(() => {
            window.removeEventListener('resize', resizeHeight)
        })
        const studyTimeModelHeight = ref(0)
        const resizeHeight = () => {
            console.log('resize => ', box.value.clientHeight)
            queryModelHeight.value = box.value.clientHeight ? box.value.clientHeight : 100
            queryModelInfoHeight.value = box.value.clientHeight ? box.value.clientHeight * 0.7 : 80 // 管理
            updateModelHeight.value = box.value.clientHeight ? box.value.clientHeight * 0.5 : 80 // 增改
            studyTimeModelHeight.value = box.value.clientHeight ? box.value.clientHeight * 0.3 : 80 // 统计周期
        }
        // 获取资产列表
        const getAssetList = () => {
            let sendMsg = new ProxyServer_pb.MSG_CPLoadAssets();
            tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADASSETS, sendMsg).then()
        }
        const changeBaselineTypeFn = (VAL) => {
            baseLineOptionsItem.value = []
            setTimeout(() => {
                baseLineOptionsItem.value = HELPS.baseLineItem[baseLineTypeValue.value].Item
                baseLineRules.value = HELPS.getRulesByBaseLineCode(baseLineTypeValue.value)
                pageNum.value = 1
                baselineSummaryFn('4') // 再次调用分页
            })
        }
        const studyTimeRules = ref(HELPS.studyTimeRules)
        const studyTimeModelVisible = ref(false) // 学习周期
        const formData = ref({})
        // 基线管理key
        const requestMsgId = util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_BASELINEMANAGEMENT)

        const updateRowData = ref({})
        const updateIndex = ref(0)
        const onClickUserValidate = (type, row, index) => {
            if (type !== 'ADD' && type !== 'STUDY_TIME') {
                updateRowData.value = row
                updateIndex.value = index
            }
            $('#validateUserDlg').modal('show'); // 打开用户密码校验
            $('#validateUserForm').get(0).reset() // 置空密码校验数据
            $('#opraType').val(type) // 存入保存标识
            // 弹出密码校验框
            $('#validateuser_btn').on('click',function (e) {
                if ($("#validateuser_btn").hasClass("disabled")) return
                $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
                e.preventDefault(); //阻止bootstrap submit自动刷新页面默认行为。
                let username=$("body").attr("user_name_info")
                let psd=$('#validateUserDlg #password').val()
                const user_sign = $("body").attr("user_sign_info");
                // 发送消息
                let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
                sendMsg.setUserName(username)
                sendMsg.setPassword(psd)
                sendMsg.setRequestMsgId(requestMsgId)
                console.log('send userValidate =>', sendMsg)
                tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg).then();
            })
        }
        const baselineTableData = ref([]) // 基线数据
        // 监听事件注册
        const initOnMessage = () => {
            // 用户校验监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCVALIDATEUSER, (validatePb) => {
                const validateMsg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(validatePb);
                console.log('validateMsg.getErrorCode()', validateMsg.getErrorCode())
                if (validateMsg.getErrorCode() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                    if (validateMsg.getRequestMsgId() === requestMsgId) {
                        operationFn($('#opraType').val())
                    } else ui_util.getErrorTips(validateMsg.getErrorCode(), '操作')
                } else ui_util.getErrorTips(validateMsg.getErrorCode(), '验证用户身份')
            })
            // 资产列表
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCLOADASSETS, (pb) => {
                const data = []
                const msg = ProxyServer_pb.MSG_PCLoadAssets.deserializeBinary(pb);
                const assetsBufList = msg.getAssetsList()
                assetsBufList.forEach(bufItem => {
                    try {
                        data.push(util.getArrayDataByBufDataAndTemp(bufItem, ['GID', 'DNAME', 'CONAST']))
                    } catch (e) {
                        console.log('解构err', e)
                    }
                })
                assetList.value = data
                // }
            })
            // 调阅信息
            tcpInstance.onMessage(util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_BASELINEMANAGEMENT), (pb) => {
                $('#validateuser_btn').off('click'); // 用完一定要手动移除该事件，否则会积累触发点击事件
                // FIX 临时方案 因为不知道后端返回的数据用哪个message来解构所以暂时取基线分析的消息定义
                const msg = PlatformProxyServer_pb.MSG_DPBaselineLearningConfig.deserializeBinary(pb)
                const type = msg.getType()
                console.log('类型', msg.getType())
                // 基线统计
                if (type == 1) {
                    console.log(PlatformProxyServer_pb.MSG_DPBaselineLearningConfig.prototype)
                    const msg1 = PlatformProxyServer_pb.MSG_DPBaselineLearningConfig.deserializeBinary(pb)
                    const data = util.getArrayDataByBufDataAndTemp(msg1, ['type', 'baseType', 'dataCnt', 'param', 'state'])
                    if (data.state == 0) {
                        // 设置统计周期
                        if (data.param == 5) {
                            closeStudyTimeModel()
                            ui_util.showFadeTip('操作成功！')
                            baselineSummaryFn('4') // 再次调用分页
                            return
                        }
                        let arr = []
                        try {
                            dataCnt.value = data.dataCnt
                            const addInfo = util.getProtobufFieldValue(msg1, 'addInfo', true)
                            console.log('addInfo', addInfo)
                            addInfo.forEach((item, index) => {
                                const baselineinfo = item.getBaselineinfo()
                                const bytes = baselineinfo.getValue()
                                const proto = HELPS.baseLineItem[String(data.baseType)].protobuf
                                const anyData = proto.deserializeBinary(bytes)
                                const keyArr = baseLineOptionsItem.value.map(item => item.prop)
                                const baseData = util.getArrayDataByBufDataAndTemp(proto.deserializeBinary(bytes), keyArr)
                                arr.push({ id: item.getId(), ...baseData })
                            })
                        } catch (e) {
                            console.log('基线统计 err =>', e)
                        }
                        baselineTableData.value = arr
                    } else {
                        ui_util.getErrorTips(data.state, '操作')
                    }

                    // 基线分析
                } else if (type == 2) {
                    const msg1 = PlatformProxyServer_pb.MSG_DPBaselineAnalysisConfig.deserializeBinary(pb)
                    const data = util.getArrayDataByBufDataAndTemp(msg1, ['type', 'baseType', 'param', 'state'])
                    console.log('返回', data)
                    if (data.state == 0) {
                        ui_util.showFadeTip('操作成功！')
                        closeUpdateModel()
                        baselineSummaryFn('4') // 再次调用分页
                    } else {
                        ui_util.getErrorTips(data.state, '操作')
                    }

                }
            })
        }
        const queryModelGID = ref('')
        // 前置调阅按钮
        const getQueryInfoFront = (row) => {
            queryModelGID.value = row.GID
            baselineSummaryFn('4')
            queryModelVisible.value = true
        }
        const translateOption = (val, options) => {
            const opt = options?.find(o => o.code === val)
            return opt ? opt.name : val
        }
        const queryCurrentChange = (val) => {
            pageNum.value = val
            baselineSummaryFn('4')
        }
        const updateModelVisible = ref(false)
        const updateModelType = ref('ADD')
        const operationFn = (type) => {
            updateModelType.value = type // 类型
            switch (type) {
                // 新增
                case 'ADD': {
                    formData.value.GID = queryModelGID.value
                    formData.value.GENMETHOD = 2
                    updateModelVisible.value = true
                    break
                }
                // 编辑
                case 'EDIT': {
                    console.log('打开弹窗', type, updateRowData.value, updateIndex.value, updateModelType.value)
                    const data = cloneDeep(updateRowData.value)
                    if ('id' in data) delete data.id
                    formData.value = data
                    formData.value.GENMETHOD = 2
                    updateModelVisible.value = true
                    break
                }
                // 删除
                case 'DELETE': {
                    updateModelType.value = 'DELETE'
                    baselineAnalysisFn('8')
                    break
                }
                // 统计周期
                case 'STUDY_TIME': {
                    studyTimeModelVisible.value = true
                    break
                }
            }
        }
        const updateModelSaveFn = () => {
            formDataRef.value.validate((valid) => {
                if (valid) {
                    console.log('保存', cloneDeep(formData.value))
                    baselineAnalysisFn(updateModelType.value === 'ADD' ? '6' : '7')
                } else {
                    // ui_util.showFadeTip('请完善数据！')
                }
            })
        }
        // 统计周期保存
        const studyTimeModelSaveFn = () => {
            studyTimeFormRef.value.validate((valid) => {
                if (valid) {
                    console.log('保存', cloneDeep(studyTimeForm.value))
                    baselineSummaryFn('5')
                } else {
                    // ui_util.showFadeTip('请完善数据！')
                }
            })
        }


        const studyTimeForm = ref({ studyTime: null })
        const formDataRef = ref(null)
        const studyTimeFormRef = ref(null)

        const genmethodValue = ref('1') // 基线生成方式
        const pageNum = ref(1) // 分页
        const dataCnt = ref(0)
        // 基线统计请求用  分页查询/统计周期设置
        const baselineSummaryFn = (param) => {
            const data = {
                uuid: util.genTaskId(),
                type: '1',
                baseType: baseLineTypeValue.value,
                GID: queryModelGID.value,
                offset: String(pageNum.value === 1 ? 0 : (pageNum.value - 1) * 1000),
                // offset: '0',
                GENMETHOD: param === '4' ? genmethodValue.value : '1', // 只有4才可以自选其他不行
                param, // 1,2,3 前端不需要使用 4为分页查询，5为设置周期
                addInfo: []
            }
            if(param === '5') {
                const formData = cloneDeep(studyTimeForm.value)
                const studyValue = String(formData.studyTime)
                data.addInfo = [{ studyTime: studyValue }]
            } // 设置统计周期
            let sendMsg = JSON.stringify(data)
            console.log('send 统计=> ', `param: ${param}`, sendMsg, data);
            tcpInstance.sendJsonMsg(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_BASELINEMANAGEMENT, sendMsg).then();
        }
        // 基线分析请求用  新增/编辑/删除
        const baselineAnalysisFn = (param) => {
            const data = {
                uuid: util.genTaskId(),
                type: '2',
                baseType: baseLineTypeValue.value,
                param, // 6 增加/7 修改/8 删除
                addInfo: []
            }
            data.addInfo = [{
                id: updateModelType.value === 'ADD' ? '0' : updateRowData.value['id'],
                GENMETHOD: updateModelType.value !== 'DELETE' ? '2' : '',
                baseLineInfo: updateModelType.value !== 'DELETE' ? cloneDeep(formData.value) : {}
            }] // 设置统计周期
            let sendMsg = JSON.stringify(data)
            console.log('send 分析=> ', `param: ${param}`, sendMsg, data);
            tcpInstance.sendJsonMsg(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_BASELINEMANAGEMENT, sendMsg).then();
        }

        const closeQueryModelFn = () => {
            baseLineTypeValue.value = String(BaselineDefine_pb.BaselineLabel.BASELINE_SERVER_NET_ACCESS) // 选中的基线类型
            baseLineOptionsItem.value = HELPS.baseLineItem[String(BaselineDefine_pb.BaselineLabel.BASELINE_SERVER_NET_ACCESS)].Item
            baseLineRules.value = HELPS.getRulesByBaseLineCode(String(BaselineDefine_pb.BaselineLabel.BASELINE_SERVER_NET_ACCESS))
            pageNum.value = 1
            formData.value = {}
            studyTimeForm.value = { studyTime: null }
            queryModelVisible.value = false
        }
        const closeUpdateModel = () => {
            updateModelVisible.value = false
            formData.value = {}
        }
        const closeStudyTimeModel = () => {
            studyTimeForm.value = { studyTime: null }
            studyTimeModelVisible.value = false
        }
        return {
            baselineTableData,
            box,
            assetList, // 资产列表
            getQueryInfoFront, // 资产列表管理按钮事件
            // 管理弹窗用
            queryModelVisible,
            queryModelHeight,
            queryModelInfoHeight,

            // 基线查询用

            // 统计周期
            studyTimeForm,
            studyTimeModelVisible,
            studyTimeFormRef,
            studyTimeModelHeight,
            studyTimeModelSaveFn,
            studyTimeRules,
            // 基线类型管理
            baseLineOptions,
            baseLineTypeValue,
            baseLineOptionsItem,
            baseLineRules,
            changeBaselineTypeFn, // 切换基线类型，调用查询
            translateOption, // 翻译下拉值

            // 新增/编辑/删除 弹窗用
            formDataRef,
            formData,
            updateModelHeight,
            updateModelVisible,
            updateModelType,
            updateModelSaveFn, // 保存

            onClickUserValidate, // 执行操作前置权限判断

            // 关闭用
            closeQueryModelFn,
            closeUpdateModel,
            closeStudyTimeModel,
            genmethodValue,
            dataCnt,
            pageNum,
            queryCurrentChange,
        }
    }
})
app.use(ElementPlus, { locale: ElementPlusLocaleZhCn })
app.mount('#app');