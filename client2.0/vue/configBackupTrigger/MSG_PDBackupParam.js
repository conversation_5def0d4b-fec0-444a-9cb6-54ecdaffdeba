const DCDParamDefine_pb = require('../../script/pb/DCDParamDefine_pb.js')
const PublicDefine_pb = require('../../script/pb/PublicDefine_pb.js');
const BehaviorCodeDefine_pb = require('../../script/pb/BehaviorCodeDefine_pb.js');
// 平台
const PlatformProxyServer_pb = require('../../script/pb/PlatformProxyServer_pb.js');
const ProxyServer_pb = require('../../script/pb/ProxyServer_pb.js');
const util = require('../../lib/util.js');
const ui_util = require('../../script/view/ui_util.js');
const tcpInstance = require('../../lib/v2/tls_client.js');
const {createApp, ref, onMounted, reactive, onBeforeMount} = Vue;
const {ipcRenderer} = require('electron');
const { ElMessage, ElMessageBox } = ElementPlus;
const { cloneDeep } = require('lodash')

const fs = require('fs')
const crypto = require('crypto')

const app = createApp({
    setup() {
        const user_sign = $("body").attr("user_sign_info");
        const user_name = $("body").attr("user_name_info");
        console.log("user_sign:", user_sign + user_name);
        // 备份用
        const backup_requestMsgId = util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_BACKUPPARAM)
        // 恢复用
        const restore_requestMsgId = util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_RESTOREPARAM)

        onBeforeMount(() => {
            initOnMessage()
        })
        const initOnMessage = () => {
            // 用户校验监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCVALIDATEUSER, (validatePb) => {
                const validateMsg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(validatePb);
                if (validateMsg.getErrorCode() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                    $('#validateuser_btn').off('click'); // 用完一定要手动移除该事件，否则会积累触发点击事件
                    // 备份
                    if (validateMsg.getRequestMsgId() === backup_requestMsgId) {
                        backupModalVisible.value = true
                        backupModalVisible.value = true
                        backupModalPath.value = ''
                        console.log('backupModalPathButtonRef.value', backupModalPathButtonRef.value)
                        // 恢复
                    } else if(validateMsg.getRequestMsgId() === restore_requestMsgId) {
                        tcpConfigPDSetting('RESTORE')
                    }
                } else {
                    ui_util.getErrorTips(validateMsg.getErrorCode(), '验证用户身份')
                }
            })
            // 数据备份
            tcpInstance.onMessage(backup_requestMsgId, (buffer) => {
                console.log('buffer', buffer.length)
                const jsonLength = buffer.readInt16BE(); // 数据长度
                console.log('jsonLength', jsonLength);
                const jsonEnd = 2 + jsonLength;
                const resData = JSON.parse(buffer.slice(2, jsonEnd).toString()) // 从2字节开始到结束
                const fileData = buffer.slice(jsonEnd) // 文件部分
                console.log('resData', resData)
                if (resData.result == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                    const addInfo = resData.addInfo.length ? resData.addInfo[0]: { filemd5: '', filesize: 0 }
                    console.log('addInfo => ', addInfo, crypto.createHash('md5').update(fileData).digest('hex'), addInfo.filemd5)
                    const isValid = crypto.createHash('md5').update(fileData).digest('hex') === addInfo.filemd5;
                    console.log('fileData => ', fileData, isValid)
                    if (isValid) {
                        const timeStamp = String(moment(new Date()).format('YYYY-MM-DD-HH-mm-ss'));
                        const filePath = backupModalPath.value+'/'+`data_backup-${{1:'装置配置备份',2:'基线备份',3:'证书备份'}[backupParam.value]}-${timeStamp}.bak`
                        util.saveToFile(Buffer.from(fileData), filePath, false);
                        console.log('111')
                        backupModalVisible.value = false
                        backupModalPath.value = ''
                        ui_util.showFadeTip('数据备份成功')
                    } else {
                        return ui_util.showFadeTip('文件校验失败！')
                    }
                } else {
                    ui_util.getErrorTips(resData.result, '验证用户身份')
                }
            })
            // 数据恢复
            tcpInstance.onMessage(restore_requestMsgId, (buffer) => {
                console.log('返回数据', buffer)
                const msg = PlatformProxyServer_pb.MSG_DPRestoreParam.deserializeBinary(buffer)
                if (msg.getResult() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                    ui_util.showFadeTip('数据恢复成功')
                }
            })

        }
        const backupParam = ref(null) // 备份参数
        const reStoreParam = ref(null) // 恢复参数
        const fileInput = ref(null)
        // 数据恢复文件用
        const reStoreFileName = ref('')
        const reStoreFileSize = ref(0)
        const reStoreFileBufferMD5 = ref('')
        const reStoreFilePath = ref('')
        const reStoreFileBuffer = ref(null)
        // 点击数据恢复框
        const reStoreUploadHandleClick = () => {
            if (!reStoreFileName.value) {
                fileInput.value?.click()
            }
        }
        // 数据恢复文件上传
        const onFileChange = (event) => {
            const files = event.target.files
            if (!files || files.length === 0) return

            const file = files[0]
            const filePath = file.path

            try {
                const buffer = fs.readFileSync(filePath)
                const md5 = crypto.createHash('md5').update(buffer).digest('hex')
                reStoreFileName.value = file.name
                reStoreFileSize.value = file.size
                reStoreFileBufferMD5.value = md5
                reStoreFilePath.value = filePath
                reStoreFileBuffer.value = buffer

                console.log('自己校验自己', crypto.createHash('md5').update(buffer).digest('hex') === md5)
                console.log('文件路径 =>', filePath, '文件 Buffer =>', buffer, 'MD5 =>', md5, 'fileSize =>', file.size)
            } catch (err) {
                console.error('读取文件失败:', err)
            }

            event.target.value = ''
        }
        // 删除数据恢复已经上传的文件
        const removeFile = (event) => {
            event.stopPropagation() // 阻止冒泡避免触发 restore-box 的 click
            reStoreFileName.value = ''
            reStoreFileSize.value = 0
            reStoreFileBufferMD5.value = ''
            reStoreFilePath.value = ''
            reStoreFileBuffer.value = null
        }
        // 工具函数，格式化文件大小
        const formatFileSize = (size) => {
            if (size < 1024) return size + ' B'
            if (size < 1024 * 1024) return (size / 1024).toFixed(2) + ' KB'
            return (size / (1024 * 1024)).toFixed(2) + ' MB'
        }
        // 点击提交按钮
        const save2ValidateFn = (TYPE) => {
            if (TYPE === 'BACKUP') {
                if (!backupParam.value) return ui_util.showFadeTip('请完善数据备份信息！')
            } else {
                if (!reStoreParam.value || !reStoreFileName.value) return ui_util.showFadeTip('请完善数据恢复信息！')
            }
            $('#validateUserDlg').modal('show'); // 打开用户密码校验
            $('#validateUserForm').get(0).reset() // 置空密码校验数据
            // 弹出密码校验框
            $('#validateuser_btn').on('click',function (e) {
                if ($("#validateuser_btn").hasClass("disabled")) return;
                $('#validateUserDlg').modal('hide');//注意关闭modal用法 请勿直接.hide()!
                e.preventDefault();//阻止bootstrap submit自动刷新页面默认行为。
                let username=user_name
                let psd=$('#validateUserDlg #password').val()
                // 发送消息
                let sendMsg = new ProxyServer_pb.MSG_CPValidateUser();
                sendMsg.setUserName(username)
                sendMsg.setPassword(psd)
                sendMsg.setRequestMsgId(TYPE === 'BACKUP' ? backup_requestMsgId : restore_requestMsgId)
                tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPVALIDATEUSER, sendMsg).then();
            })
        }
        // 数据备份弹窗
        const backupModalVisible = ref(false)
        // 数据备份弹窗文件存储path
        const backupModalPath = ref('')
        const backupFileInputRef = ref(null)
        const backupModalPathButtonRef = ref(null)
        const backupCancelFn = () => {
            backupModalPath.value = ''
            backupModalVisible.value = false
        }
        const backupDoneFn = () => {
            if (!backupModalPath.value) return ui_util.showFadeTip('请选择数据备份存储路径！')
            tcpConfigPDSetting('BACKUP')
        }
        const tcpConfigPDSetting = (TYPE) => {
            // 备份
            if (TYPE === 'BACKUP') {
                const sendMsg = JSON.stringify({
                    uuid: util.genTaskId(),
                    param: backupParam.value
                })
                console.log('send BACK_UP => ', sendMsg)

                tcpInstance.sendJsonMsg(
                    PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE,
                    PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_BACKUPPARAM,
                    sendMsg
                ).then();
                // 恢复
            } else if (TYPE === 'RESTORE') {
                const sendMsg = JSON.stringify({
                    uuid: util.genTaskId(),
                    param: String(reStoreParam.value),
                    addInfo: [{
                        filemd5: reStoreFileBufferMD5.value,
                        filesize: reStoreFileSize.value
                    }]
                })
                const jsonLength = Buffer.from(sendMsg).length;
                let buffer1 = Buffer.alloc(2)
                buffer1.writeInt16BE(jsonLength); // 存入数据长度
                const bufferPart2 = Buffer.from(sendMsg, 'utf8');
                const bufferPart3 = reStoreFileBuffer.value.length ?  reStoreFileBuffer.value  : Buffer.alloc(0)
                let combinedBuffer = Buffer.concat([buffer1, bufferPart2, bufferPart3]);
                console.log('send RESTORE => ', combinedBuffer, 'json长度 =>', combinedBuffer.length, 'jsonLength', jsonLength)
                tcpInstance.sendJsonMsg(
                    PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE,
                    PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_RESTOREPARAM,
                    combinedBuffer
                ).then();
            }
        }
        const openSelectDict = async() => {
            const folderPath = await ipcRenderer.invoke('select-folder');
            if (folderPath) {
                console.log('选中的文件夹路径:', folderPath);
                backupModalPath.value = folderPath
            } else {
                console.log('用户取消选择文件夹');
            }
        }
        return {
            openSelectDict,
            save2ValidateFn,
            backupParam,
            reStoreParam,
            reStoreUploadHandleClick,
            fileInput,
            removeFile,
            onFileChange,
            reStoreFileName,
            reStoreFileSize,
            reStoreFileBufferMD5,
            reStoreFilePath,
            reStoreFileBuffer,
            formatFileSize,
            backupModalVisible,
            backupModalPath,
            backupFileInputRef,
            backupModalPathButtonRef,
            backupCancelFn,
            backupDoneFn
        }
    }
})
app.use(ElementPlus, { locale: ElementPlusLocaleZhCn })
app.mount('#app');
