const DCDParamDefine_pb = require('../../script/pb/DCDParamDefine_pb.js')
const PublicDefine_pb = require('../../script/pb/PublicDefine_pb.js');
const BehaviorCodeDefine_pb = require('../../script/pb/BehaviorCodeDefine_pb.js');
// 平台
const PlatformProxyServer_pb = require('../../script/pb/PlatformProxyServer_pb.js');
const ProxyServer_pb = require('../../script/pb/ProxyServer_pb.js');
const util = require('../../lib/util.js');
const ui_util = require('../../script/view/ui_util.js');
const tcpInstance = require('../../lib/v2/tls_client.js');
const {createApp, ref, onMounted, reactive, onBeforeMount, onBeforeUnmount} = Vue;
const {ipc<PERSON><PERSON>er} = require('electron');
const { ElMessage, ElMessageBox } = ElementPlus;
const { cloneDeep } = require('lodash')

const app = createApp({
    setup() {
        const box = ref(null)
        const tableHeight = ref(0)
        const queryModelHeight = ref(0)
        const resizeHeight = () => {
            tableHeight.value = box.value.clientHeight ? box.value.clientHeight * 0.8 : 100
            queryModelHeight.value = tableHeight.value * 0.8
        }
        const selectData = ref({})
        const tableData = ref([])
        onMounted(() => {
            resizeHeight() // 重置高度
            window.addEventListener('resize', resizeHeight)
            initOnMessage() // 注册响应事件监听
            getPluginListFn() // 获取插件列表
        })
        // 先校验 => 执行后续功能
        const validateToNext = (Type, row) => {
            $('#validateUserDlg').modal('show'); // 打开用户密码校验
            $('#validateUserForm').get(0).reset() // 置空密码校验数据
            $('#opraType').val(Type)
            if (row) {
                selectData.value = row.id
                console.log('用户前置校验 =>', 'id => ', selectData.value, row)
            }
        }
        const uploadRequestMsgId = util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PLUGINMANAGEMENT)

        const initOnMessage = () => {
            // 用户校验监听
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCVALIDATEUSER, (validatePb) => {
                const validateMsg = ProxyServer_pb.MSG_PCValidateUser.deserializeBinary(validatePb);
                if (validateMsg.getErrorCode() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                    // 文件上传
                    if (validateMsg.getRequestMsgId() === uploadRequestMsgId) sendPluginTcpFn($('#opraType').val()) // 调用相应方法
                } else {
                    ui_util.getErrorTips(validateMsg.getErrorCode(), '验证用户身份')
                }
            })
            // 插件相关接口
            tcpInstance.onMessage(util.genServerMsgKey(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_CERTSET), dealPluginTcpFn)
            // 插件列表
            tcpInstance.onMessage(ProxyServer_pb.PSMessageId.PSMESSAGEID_PCLOADASSETS, (pb) => {
                const msg = ProxyServer_pb.MSG_PCLoadAssets.deserializeBinary(pb);
                getPluginListDealFn(msg)
            })
        }
        const viewVisible = ref(true)
        const openViewVisibleFn = () => {
            viewVisible.value = true
        }
        const closeViewVisibleFn = () => {
            viewVisible.value = false
        }
        // 插件相关接口
        const sendPluginTcpFn = (TYPE) => {
            if (!TYPE || !selectData.value) return
            const titleType = { 'ENABLE': '启动' ,'DISABLE': '停止运行' ,'UN_INSTALL': '卸载' }[TYPE]
            ElMessageBox.confirm(`是否要${titleType}该插件？`, '提示', {confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'})
                .then(() => {
                    const sendMsg = JSON.stringify({
                        uuid: util.genTaskId(),
                        param: { 'ENABLE': '2' ,'DISABLE': '3' ,'UN_INSTALL': '4' }[TYPE],
                        plugId: selectData.value.plugId
                    })
                    tcpInstance.sendJsonMsg(PlatformProxyServer_pb.FunctionType.FUNCTIONTYPE_CONFIGMANAGE, PlatformProxyServer_pb.ConfigManagePacketType.CONFIGMANAGEPACKETTYPE_PLUGINMANAGEMENT, sendMsg).then();
                })
                .catch(() => {
                    ui_util.showFadeTip('已取消')
                })

        }
        const dealPluginTcpFn = (buffer) => {
            const msg = PlatformProxyServer_pb.MSG_DPStartPlugin.deserializeBinary(buffer);
            if (msg.getResult() == PublicDefine_pb.ErrorCode.ERRORCODE_SUCCESS) {
                const param = util.getProtobufFieldValue(msg, 'param')
                const titleType = { '2': '启动' ,'3': '停运' ,'4': '卸载' }[param]
                getPluginListFn()
                ui_util.showFadeTip(`${titleType}成功！`)
            } else {
                ui_util.getErrorTips(msg.getErrorCode(), '插件操作')
            }
        }
        const getPluginListFn = () => {
            let sendMsg = new ProxyServer_pb.MSG_CPLoadAssets();
            tcpInstance.sendProtoMsg(ProxyServer_pb.PSMessageId.PSMESSAGEID_CPLOADASSETS, sendMsg).then()
        }
        const getPluginListDealFn = (msg) => {
            const table = []
            try {
                const pluginsBufList = msg.getPluginsList()
                pluginsBufList.forEach(plugin => {
                    const tableItem = util.getArrayDataByBufDataAndTemp(plugin, ['PLUG_ID', 'APPID', 'TYPE', 'NAME', 'BRAND', 'VER', 'ARCH', 'SYSVER', 'STYPE', 'FLIST', 'PSTAT', 'EREASON', 'CREATET', 'UPDATET'])
                    table.push(tableItem)
                })
                tableData.value = table
            } catch (e) {
                console.log('获取插件列表解构 err =>', e)
                tableData.value = table
            }
        }
        return {
            box,
            tableHeight,
            queryModelHeight,
            tableData,
            validateToNext,
            // 查看
            viewVisible,
            openViewVisibleFn,
            closeViewVisibleFn
        }
    }
})
app.use(ElementPlus, { locale: ElementPlusLocaleZhCn })
app.mount('#app');