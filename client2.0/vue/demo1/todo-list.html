<div class="container mt-4">
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h3 class="panel-title">Vue 3 Todo List</h3>
        </div>
        <div class="panel-body">
            <div class="input-group mb-3">
                <input
                        type="text"
                        class="form-control"
                        placeholder="Add a new todo..."
                        v-model="newTodoText"
                        @keyup.enter="addTodo"
                />
                <span class="input-group-btn">
          <button class="btn btn-primary" @click="addTodo">Add Todo</button>
        </span>
            </div>

            <ul class="list-group mt-3">
                <li
                        class="list-group-item d-flex justify-content-between align-items-center"
                        v-for="todo in filteredTodos"
                        :key="todo.id"
                        :class="{ 'list-group-item-success': todo.completed }"
                >
                    <div @click="toggleTodoCompletion(todo.id)" style="cursor: pointer;">
            <span :style="{ 'text-decoration': todo.completed ? 'line-through' : 'none' }">
              {{ todo.text }}
            </span>
                    </div>
                    <button class="btn btn-danger btn-xs pull-right" @click="removeTodo(todo.id)">
                        <span class="glyphicon glyphicon-remove" aria-hidden="true"></span>
                    </button>
                </li>
            </ul>

            <div class="btn-group mt-3" role="group">
                <button
                        class="btn btn-default"
                        :class="{ 'btn-primary': filter === 'all' }"
                        @click="filter = 'all'"
                >
                    All ({{ todos.length }})
                </button>
                <button
                        class="btn btn-default"
                        :class="{ 'btn-primary': filter === 'active' }"
                        @click="filter = 'active'"
                >
                    Active ({{ activeTodosCount }})
                </button>
                <button
                        class="btn btn-default"
                        :class="{ 'btn-primary': filter === 'completed' }"
                        @click="filter = 'completed'"
                >
                    Completed ({{ completedTodosCount }})
                </button>
            </div>
            <hr>
            <p class="text-muted">
                调用老项目接口示例：
                <button class="btn btn-info btn-xs" @click="sendTodoCountToBackend">
                    发送Todo数量到后端
                </button>
            </p>
            <p v-if="backendResponse">后端响应: {{ backendResponse }}</p>
        </div>
    </div>
</div>