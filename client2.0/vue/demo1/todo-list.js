// new_vue_module/todo-list.js
// 确保 Vue 已经通过 CDN 加载并可用
const { ref, computed, onMounted } = Vue;

// 1. 定义一个函数来异步加载 HTML 模板
async function loadTemplate(url) {
    const response = await fetch(url);
    if (!response.ok) {
        throw new Error(`Failed to load template from ${url}: ${response.statusText}`);
    }
    return await response.text();
}

// 2. 定义 Vue 组件的配置对象
// 注意：template 属性会稍后动态填充
const TodoListComponent = {
    // template: '加载中...', // 初始可以显示一个加载提示
    setup() {
        const newTodoText = ref('');
        const todos = ref([]);
        const nextTodoId = ref(1); // 用于生成唯一的 ID
        const filter = ref('all'); // 'all', 'active', 'completed'
        const backendResponse = ref(''); // 用于显示后端响应

        // 计算属性
        const filteredTodos = computed(() => {
            if (filter.value === 'active') {
                return todos.value.filter(todo => !todo.completed);
            } else if (filter.value === 'completed') {
                return todos.value.filter(todo => todo.completed);
            }
            return todos.value;
        });

        const activeTodosCount = computed(() => {
            return todos.value.filter(todo => !todo.completed).length;
        });

        const completedTodosCount = computed(() => {
            return todos.value.filter(todo => todo.completed).length;
        });

        // 方法
        const addTodo = () => {
            if (newTodoText.value.trim() === '') {
                return;
            }
            todos.value.push({
                id: nextTodoId.value++,
                text: newTodoText.value.trim(),
                completed: false,
            });
            newTodoText.value = '';
        };

        const toggleTodoCompletion = (id) => {
            const todo = todos.value.find(t => t.id === id);
            if (todo) {
                todo.completed = !todo.completed;
            }
        };

        const removeTodo = (id) => {
            todos.value = todos.value.filter(todo => todo.id !== id);
        };

        // 调用老项目接口的方法
        const sendTodoCountToBackend = () => {
            const msgId = 2001; // 为 Todo 数量定义一个新的消息ID
            const sendMsg = {
                totalTodos: todos.value.length,
                activeTodos: activeTodosCount.value,
                completedTodos: completedTodosCount.value,
                timestamp: Date.now()
            };

            console.log(`[Vue Todo] 发送 Todo 数量到后端: ${msgId}, ${JSON.stringify(sendMsg)}`);

            // 确保 sendGuiMsg2 函数存在
            if (typeof sendGuiMsg2 === 'function') {
                sendGuiMsg2(msgId, sendMsg);
                backendResponse.value = '消息已发送，等待后端响应...';
            } else {
                console.error("[Vue Todo] sendGuiMsg2 函数未定义或不可用！");
                backendResponse.value = '错误：sendGuiMsg2 函数不可用。';
            }
        };

        // 暴露给外部调用，以便 onMsg 可以更新 Vue 组件
        // 注意：这里我们返回 backendResponse 的 ref，以便外部可以直接更新它的 .value
        // 但更好的做法是暴露一个方法让外部调用，因为它是一个响应式引用
        window.handleTodoBackendResponse = (msgId, pb) => {
            console.log(`[Vue Todo] 收到后端响应 (onMsg): ${msgId}, ${JSON.stringify(pb)}`);
            if (msgId === 2001) {
                // 尝试解析 pb.data，如果它是一个 JSON 字符串
                let responseData = pb;
                try {
                    if (pb && pb.data && typeof pb.data === 'string') {
                        responseData = JSON.parse(pb.data);
                    }
                } catch (e) {
                    console.warn("Failed to parse backend response data as JSON:", e);
                }
                backendResponse.value = `收到响应 (MsgID: ${msgId}): ${JSON.stringify(responseData)}`;
            } else {
                backendResponse.value = `收到未知响应 (MsgID: ${msgId}): ${JSON.stringify(pb)}`;
            }
        };

        return {
            newTodoText,
            todos,
            filter,
            filteredTodos,
            activeTodosCount,
            completedTodosCount,
            backendResponse,
            addTodo,
            toggleTodoCompletion,
            removeTodo,
            sendTodoCountToBackend
        };
    },
};

// 3. 将组件配置导出，以便在 index.html 中使用
// 这是一个 Promise，它在模板加载完成后解析为组件配置
window.getTodoListComponent = async () => {
    try {
        TodoListComponent.template = await loadTemplate(`file://${__dirname}/vue/demo1/todo-list.html`);
        return TodoListComponent;
    } catch (error) {
        console.error("Error loading Todo List component template:", error);
        // 返回一个包含错误信息的简单组件
        return {
            template: `<div>错误：无法加载 Todo List 模块。请检查文件路径。</div>`
        };
    }
};