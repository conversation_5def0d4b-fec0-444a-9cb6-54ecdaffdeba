const asset_model = require('../dataQuery/helps/asset_model_helps')
const ASSET_ENUMS = require('./ASSET_ENUMS')
const {ASSET_PARAM} = require("./ASSET_ENUMS");
const ETHERNET = {
    INFO: {
        ETHID: '网口标识',
        NCID: '有线网卡标识',
        NAME: '名称',
        ETHTYP: '类型',
        NTYPE: '网络位置类型',
        QUERL: '队列规则',
        MAC: 'MAC地址',
        SPEED: '速率',
        WKMD: '工作模式',
        SLOTN: '插槽号',
        CONCSTA: '连接状态',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        ETHTYP: ASSET_ENUMS.HARDWARE.ETHERNET_TYPE_ETHTYP,
        NTYPE: ASSET_ENUMS.HARDWARE.NET_TYPE_NTYPE,
        CONCSTA: ASSET_ENUMS.HARDWARE.CONNECTION_STATUS,
        SOURCE: ASSET_ENUMS.COMMON.SOURCE,
    }
}
const LOGICAL_CORE = {
    INFO: {
        LCID: '逻辑核心标识',
        CPUID: '处理器标识',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        SOURCE: ASSET_ENUMS.COMMON.SOURCE
    }
}

// 资产节点
const ASSET_NODE = {
    INFO: {
        GID: '资产ID',
        IP: 'IP地址',
        MAC: 'MAC地址',
        IFID: '网络接口ID',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源',
    },
    MAP2CHINA: {
        SOURCE: ASSET_ENUMS.COMMON.SOURCE
    }
}

// 硬件
const CPU = {
    INFO: {
        CPUID: '处理器标识',
        BRAND: '品牌',
        MODEL: '型号',
        VER: '版本',
        WIDTH: '总线宽度',
        ARCH: 'CPU架构',
        CNUM: '物理核心数',
        LOGICAL_CORE: '逻辑核心',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    LOGICAL_CORE,
    MAP2CHINA: {
        ARCH: ASSET_ENUMS.HARDWARE.CPU_ARCH,
        SOURCE: ASSET_ENUMS.COMMON.SOURCE
    }
}
const MEMORY = {
    INFO: {
        MEMID: '内存标识',
        BRAND: '品牌',
        MODEL: '型号',
        VER: '版本',
        CY: '容量',
        MEMSNID: '内存序列号',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        SOURCE: ASSET_ENUMS.COMMON.SOURCE
    }
}
const DISK = {
    INFO: {
        WWID: '磁盘标识',
        BRAND: '品牌',
        MODEL: '型号',
        VER: '版本',
        CY: '容量',
        DFILE: '设备文件',
        MBR: '主引导记录哈希值',
        RABLE: '移动存储设备',
        DERR: '磁盘健康度',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        SOURCE: ASSET_ENUMS.COMMON.SOURCE
    }
}
const NETWORK_CARD = {
    INFO: {
        NCID: '网卡标识',
        BRAND: '品牌',
        MODEL: '型号',
        VER: '版本',
        BCMAC: '网卡MAC地址',
        IFCNT: '接口数量',
        WIDTH: '总线宽度',
        ETHERNET: '物理网口',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        SOURCE: ASSET_ENUMS.COMMON.SOURCE
    },
    ETHERNET // 物理网口
}
const WIRELESS = {
    INFO: {
        NCID: '网卡标识',
        BRAND: '品牌',
        MODEL: '型号',
        VER: '版本',
        MAC: 'MAC地址',
        PROTO: '协议类型',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        SOURCE: ASSET_ENUMS.COMMON.SOURCE
    }
}
const PERIPHERAL_INTERFACE = {
    INFO: {
        PIID: '外设接口标识',
        PINM: '接口名称',
        TYPE: '类型',
        ENABLE: '启用状态',
        CONNUM: '接入数量',
        AUTHS: '授权状态',
        WIDTH: '总线宽度',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        SOURCE: ASSET_ENUMS.COMMON.SOURCE,
        TYPE: ASSET_ENUMS.HARDWARE.PERIPHERAL_INTERFACE_TYPE
    }
} // 外设接口
const PERIPHERAL = {
    INFO: {
        PERID: '外设设备标识', PIID: '外设接口标识', NAME: '名称', BRAND: '品牌', MODEL: '型号', TYPE: '类型',
        IFPCODE: '协议编码', CODE: '设备编号', VENDOR: '厂商编号', CREATET: '数据生成时间', UPDATET: '数据更新时间', SOURCE: '数据来源',
    },
    MAP2CHINA: {
        IFPCODE: ASSET_ENUMS.HARDWARE.IF_PROTO_CODE,
        TYPE: ASSET_ENUMS.HARDWARE.PERIPHERAL_TYPE,
    }
} // 外设设备
const POWER = {
    INFO: {
        PWRID: '电源标识',
        BRAND: '品牌',
        MODEL: '型号',
        VER: '版本',
        RP: '额定功率',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        SOURCE: ASSET_ENUMS.COMMON.SOURCE
    }
}
const GPU = {
    INFO: {
        GPUID: 'GPU卡标识',
        BRAND: '品牌',
        MODEL: '型号',
        VER: '版本',
        WIDTH: '总线宽度',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        SOURCE: ASSET_ENUMS.COMMON.SOURCE
    }
}
const HBA = {
    INFO: {
        HBAID: 'HBA卡标识',
        BRAND: '品牌',
        MODEL: '型号',
        VER: '版本',
        WIDTH: '总线宽度',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        SOURCE: ASSET_ENUMS.COMMON.SOURCE
    }
}
const RAID = {
    INFO: {
        RAIDID: 'RAID卡标识',
        BRAND: '品牌',
        MODEL: '型号',
        VER: '版本',
        WIDTH: '总线宽度',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        SOURCE: ASSET_ENUMS.COMMON.SOURCE
    }
}
const BMC = {
    INFO: {
        BRAND: '品牌',
        MODEL: '型号',
        BMCV: '版本',
        BMCSUP: 'BMC支持设备',
        BMCFIRV: 'BMC固件修订版本',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        SOURCE: ASSET_ENUMS.COMMON.SOURCE
    }
}
const SENSOR = {
    INFO: {
        NAME: '名称',
        OTHERTMP: '数值',
        UNIT: '单位',
        TYPE: '类别',
        STATE: '状态',
        OTHERERRSTA: '异常状态',
        MTIME: '时标',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源',
    },
    MAP2CHINA: {
        SOURCE: ASSET_ENUMS.COMMON.SOURCE
    }
}



// 软件
const USER = {
    INFO: {
        UID: '用户标识',
        DUGRID: '默认用户组标识',
        NAME: '名称',
        USHELL: '默认SHELL',
        UHOME: '用户主目录',
        PCHGTIME: '密码变更时间',
        LKSTAT: '用户锁定状态',
        NPIP: '免密登录IP',
        UMAXPRNUM: '用户最大进程数',
        MAXFNUM: '用户最大句柄',
        MAXTNUM: '用户最大线程',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        SOURCE: ASSET_ENUMS.COMMON.SOURCE,
    }
}
const USER_GROUP = {
    INFO: {
        GRPID: '用户组标识',
        NAME: '名称',
        ULIST: '组内用户名清单',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        SOURCE: ASSET_ENUMS.COMMON.SOURCE,
    }
}
const SESSION = {
    INFO: {
        SESSID: '会话标识',
        FSESS: '上一级会话标识',
        LUID: '登录用户标识',
        LUNAME: '登录用户名',
        SGID: '源资产标识',
        SPID: '源端进程标识',
        SPPATH: '源端进程全路径名',
        DGID: '目的资产标识',
        DPID: '目的端进程标识',
        DPPATH: '目的端进程全路径名',
        LMETHOD: '登录方式',
        SIP: '源IP',
        SPORT: '源端口',
        DIP: '目的IP',
        DPORT: '目的端口',
        TNUM: '终端号',
        SSTAT: '当前状态',
        EVAR: '环境变量',
        EVHASH: '环境变量哈希值',
        LTIME: '登录时间戳',
        ETIME: '退出时间戳',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        LMETHOD: ASSET_ENUMS.SOFTWARE.LOGIN_METHOD,
        SSTAT: ASSET_ENUMS.SOFTWARE.SESSION_STATUS,
        SOURCE: ASSET_ENUMS.COMMON.SOURCE,
    }
}
const PARTITION = {
    INFO: {
        PARTID: '分区标识',
        WWID: '磁盘ID',
        DFILE: '磁盘设备文件',
        NAME: '名称',
        CAP: '容量',
        TYPE: '类型',
        FSTYPE: '文件格式',
        MPOINT: '挂载点',
        ITOT: '索引节点总数',
        RWPERM: '读写权限',
        CRTIME: '创建时间',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        SOURCE: ASSET_ENUMS.COMMON.SOURCE,
    }
}
const PROCESS = {
    INFO: {
        PRID: '进程标识',
        EID: '关联可执行文件标识',
        PPID: '父进程标识',
        PFNAME: '父进程进程文件名',
        SESSID: '关联会话标识',
        PID: '进程PID',
        FNAME: '进程文件名',
        NAME: '名称',
        SCMD: '启动命令',
        SPARAM: '启动参数',
        WDIR: '工作目录',
        STAT: '进程状态',
        USER: '启动用户',
        STIME: '启动时间',
        ETIME: '退出时间',
        QTYPE: '退出方式',
        THNUM: '线程数量',
        FDNUM: '进程使用句柄数',
        DPID: '调试进程PID',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        QTYPE: ASSET_ENUMS.SOFTWARE.QUIT_TYPE,
        SOURCE: ASSET_ENUMS.COMMON.SOURCE,
    }
}
const SERVICE = {
    INFO: {
        SVID: '服务标识',
        UID: '启动用户标识',
        UNAME: '用户名',
        EID: '关联可执行文件标识',
        FNAME: '可执行文件名',
        NAME: '名称',
        DESC: '描述',
        STYPE: '启动类型',
        SELFSSTAT: '自启动状态',
        STARTCMD: '启动命令',
        STOPCMD: '终止命令',
        RESCMD: '重启命令',
        STAT: '运行状态',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        STYPE: ASSET_ENUMS.SOFTWARE.START_TYPE,
        STAT: ASSET_ENUMS.SOFTWARE.SERVICE_STATUS,
        SOURCE: ASSET_ENUMS.COMMON.SOURCE,
    }
}
const BOOT = {
    INFO: {
        BID: '启动任务标识',
        TYPE: '任务类型',
        UID: '任务类型标识',
        UNAME: '用户名',
        BFNAME: '用户标识',
        BFHASH: '用户名',
        MINUTE: '启动任务',
        HOUR: '启动任务文件哈希',
        DAY: '分钟',
        MONTH: '小时',
        WEEKDAY: '日',
        CREATET: '月',
        UPDATET: '星期',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        TYPE: ASSET_ENUMS.SOFTWARE.BOOT_TASK_TYPE,
        SOURCE: ASSET_ENUMS.COMMON.SOURCE,
    }
}
const DRIVER = {
    INFO: {
        DID: '驱动标识',
        FID: '文件标识',
        FNAME: '驱动名称路径名',
        NAME: '名称',
        AUTHOR: '作者',
        VER: '版本',
        DESC: '描述',
        SIG: '签名',
        LDSTAT: '加载状态',
        LDTIME: '加载时间',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        SOURCE: ASSET_ENUMS.COMMON.SOURCE,
    }
}
const FILE = {
    INFO: {
        FID: '文件标识',
        UID: '所属用户标识',
        GRPID: '所属用户组标识',
        SPID: '软件包标识',
        NAME: '名称',
        TYPE: '文件类型',
        CLASS: '文件种类',
        PATH: '文件目录',
        PERM: '权限',
        FHASH: '文件HASH',
        FSIZE: '文件大小',
        LTFILE: '链接目标文件',
        CTIME: '创建时间',
        LATIME: '最后访问时间',
        LMTIME: '最后修改时间',
        LCTIME: '最新属性变化时间',
        INO: '索引节点号',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        TYPE: ASSET_ENUMS.SOFTWARE.FILE_TYPE,
        CLASS: ASSET_ENUMS.SOFTWARE.FILE_CLASS,
        SOURCE: ASSET_ENUMS.COMMON.SOURCE,
    }
}
const EXECUTABLE_FILE = {
    INFO: {
        EID: '可执行文件标识',
        CAPBIL: '权能',
        SO_FILE_LIST: '关联动态库文件列表',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        SOURCE: ASSET_ENUMS.COMMON.SOURCE,
    },
    SO_FILE_LIST: FILE
}
const SOFTWARE_PACKAGE = {
    INFO: {
        SPID: '软件包标识',
        NAME: '名称',
        BRAND: '品牌',
        VER: '版本',
        PNAME: '安装包名',
        PTYPE: '安装包类型',
        ITIME: '安装时间',
        SIG: '签名',
        DESC: '描述',
        IPATH: '安装路径',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        PTYPE: ASSET_ENUMS.SOFTWARE.INSTALL_PACKAGE_TYPE,
        SOURCE: ASSET_ENUMS.COMMON.SOURCE,
    }
}
const VULN_SCAN_RESULT = {
    INFO: {
        HOSTIPSTR: '扫描IP',
        VULNID: '漏洞ID',
        NODENAME: '漏洞名称',
        SHORTDESC: '漏洞简短描述',
        FULLDESC: '漏洞详细描述',
        REPAIRADVICE: '修复建议',
        RISKLEVEL: '危险级别',
        PLATFORMS: '影响范围',
        CVETAG: 'CVE编号',
        CVSSSCORE: 'CVSS得分',
        UPDATE: '修改时间',
        CREATE: '发布时间',
        SOFTWARE: '软件名称',
        VERSION: '影响版本',
        FINDTIME: '扫描时间',
        REPAIR: '修复状态'
    },
    MAP2CHINA: {
        RISKLEVEL: ASSET_ENUMS.SOFTWARE.RISK_LEVEL,
        REPAIR: ASSET_ENUMS.SOFTWARE.REPAIR_STATUS,
        SOURCE: ASSET_ENUMS.COMMON.SOURCE,
    }
}
const CONFIG_CHECK_RESULT = {
    INFO: {
        CIID: '核查项ID',
        IP: '设备IP',
        SUBTYPE: '设备子类型',
        CHECKITEMTYPE: '核查项类型',
        PARSE: '检查点结果',
        ISCHECK: '检查结果',
        CHECKITEMNAME: '核查项名称',
        SYSCODE: '系统代码',
        FIXEDPRO: '修复措施',
        JUDGE: '判定标准',
        ITEMCONTENT: '项内容说明',
        CTIME: '创建时间'
    },
    MAP2CHINA: {
        SUBTYPE: ASSET_ENUMS.SOFTWARE.CONFIG_CHECK_RESULT_SUBTYPE,
        ISCHECK: ASSET_ENUMS.SOFTWARE.ISCHECK,
    }
}
const APPLICATION = {
    INFO: {
        APPID: '应用软件标识',
        NAME: '名称',
        BRAND: '品牌',
        VER: '版本',
        TYPE: '应用类型',
        STYPE: '应用子类型',
        STIME: '启动时间',
        RSTA: '运行状态',
        APP_EXECUTABLE_FILE: '可执行程序列表',
        MDIPA: '被管IP-A',
        MDIPB: '被管IP-B',
        DCDLIST: '监测装置列表',
        APPDESC: '应用描述',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源',
        ENSTAT: '监测代理软件启用状态',
        ISCARD: '是否是加密卡',
        FBIDAPP: '隔离应用软件标识'
    },
    MAP2CHINA: {
        RSTA: ASSET_ENUMS.SOFTWARE.RUNNING_STATUS,
        AUTHN: ASSET_ENUMS.SOFTWARE.AUTHENTICATION,
        TYPE: ASSET_ENUMS.SOFTWARE.APPLICATION_TYPE,
        STYPE: ASSET_ENUMS.SOFTWARE.APPLICATION_STYPE,
        SOURCE: ASSET_ENUMS.COMMON.SOURCE,
    },
    APP_EXECUTABLE_FILE: FILE
}
const PLUGIN = {
    INFO: {
        PLUG_ID: '插件ID',
        APPID: '应用软件标识',
        TYPE: '插件类型',
        NAME: '名称',
        BRAND: '品牌',
        VER: '版本',
        STYPE: '业务类型',
        FLIST: '插件功能码',
        PSTAT: '插件状态',
        EREASON: '异常原因',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        TYPE: ASSET_ENUMS.SOFTWARE.PLUGIN_TYPE,
        PSTAT: ASSET_ENUMS.SOFTWARE.PLUGIN_PSTAT,
        STYPE: ASSET_ENUMS.SOFTWARE.PLUGIN_STYPE,
        SOURCE: ASSET_ENUMS.COMMON.SOURCE,
    }
}

// 网络
const NETWORK_INTERFACE = {
    INFO: {
        IFID: '接口标识',
        ETHID: '网口标识',
        NCID: '无线网卡标识',
        IFNM: '接口名',
        CLASS: '型式分类',
        LINKSTA: '启用状态',
        SPEED: '协商速率',
        MTU: '最大传输单元',
        IP: 'IP地址列表',
        MAC: 'MAC地址',
        DHCP: '地址分配模式',
        DHCPS: 'DHCP服务器地址',
        BONDM: '绑定模式',
        BONDIF: '绑定接口名',
        LIF: '逻辑接口名',
        IFIDX: '接口索引',
        AUTHS: '授权状态',
        VLIST: '接口所属VLAN列表',
        TYPE: '接口类型',
        MACBIND: '绑定MAC地址列表',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        CLASS: ASSET_ENUMS.NETWORK.NETWORK_INTERFACE_CLASS,
        LINKSTA: ASSET_ENUMS.NETWORK.LINK_STATUS,
        BONDM: ASSET_ENUMS.NETWORK.BOND_MODE,
        SPEED: ASSET_ENUMS.NETWORK.NETWORK_INTERFACE_SPEED,
        TYPE: ASSET_ENUMS.NETWORK.NETWORK_INTERFACE_TYPE,
        SOURCE: ASSET_ENUMS.COMMON.SOURCE,
    }
}
const ROUTE = {
    INFO: {
        RID: '路由标识',
        IFID: '接口标识',
        DSTNET: '目标网络',
        DSTMK: '目标掩码',
        NHA: '下一跳地址',
        TYPE: '类型',
        METRIC: '度量值',
        RPROT: '路由协议类型',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        TYPE: ASSET_ENUMS.NETWORK.ROUTE_TYPE,
        SOURCE: ASSET_ENUMS.COMMON.SOURCE,
    }
}
const ADDRESS = {
    INFO: {
        IFID: '接口标识',
        VID: 'VLAN标识',
        IP: 'IP地址',
        MAC: 'MAC地址',
        ARPTP: '类型',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        TYPE: ASSET_ENUMS.NETWORK.ARP_TYPE,
        SOURCE: ASSET_ENUMS.COMMON.SOURCE,
    }
}
const LISTENNING_PORT = {
    INFO: {
        LISPORTID: '网络监听ID',
        IFID: '网络接口标识',
        PRID: '进程标识',
        PRNAME: '服务名',
        IP: 'IP地址',
        PORT: '监听端口',
        PROTO: '最高层协议',
        PROVER: '协议版本',
        CLIN: '客户端连接数',
        CWNUM: '关闭等待数',
        TWNUM: '等待关闭数',
        SRNUM: '同步接收数',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        SOURCE: ASSET_ENUMS.COMMON.SOURCE,
    }
}
const NEIGHBOR = {
    INFO: {
        NBRID: '邻居信息标识',
        IFID: '本机接口标识',
        NBRIFID: '邻居接口标识',
        NBRIDX: '邻居索引号',
        UPTIME: '邻居信息更新时间',
        CHATYPE: '邻居标识类型',
        CHAID: '邻居标识',
        NBRIP: '邻居设备IP',
        PIDTYPE: '网口标识类型',
        PORTID: '网口标识',
        PORTDESC: '网口描述',
        SYSNM: '邻居系统名',
        SYSDESC: '邻居系统描述',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源'
    },
    MAP2CHINA: {
        SOURCE: ASSET_ENUMS.COMMON.SOURCE,
    }
}
const VLAN = {
    INFO: {
        VID: "VLAN标识",
        VTYPE: "类型",
        IP: "IP地址",
        MASK: "掩码",
        DESCRIPTION: "描述",
        NIFLST: "接口列表",
        CREATET: "数据生成时间",
        UPDATET: "数据更新时间",
        SOURCE: "数据来源"
    },
    NIFLST: NETWORK_INTERFACE,
    MAP2CHINA: {
        SOURCE: ASSET_ENUMS.COMMON.SOURCE,
    }
}
const ACCESS_CONTROL_LIST = {
    INFO: {
        ACLID: "访问控制策略标识",
        IFID: "接口标识",
        ACLNUM: "策略编号",
        TYPE: "策略类型",
        SRCMAC: "源MAC组",
        DSTMAC: "目的MAC组",
        FRTYPE: "帧类型",
        SRCBIP: "源起始IP",
        SRCEIP: "源终止IP",
        DSTBIP: "目的起始IP",
        DSTEIP: "目的终止IP",
        PROTO: "协议",
        SRCPORT: "源端口范围",
        DSTPORT: "目的端口范围",
        DIRECTION: "传输方向",
        ACTION: "动作",
        SRCNIP: "源转换IP地址",
        DSTNIP: "目的转换IP地址",
        SRCNP: "源转换端口",
        DSTNP: "目的转换端口",
        NATPRO: "转换协议类型",
        DESC: "描述",
        ENABLE: "启用状态",
        EFCTIME: "生效时间段",
        CREATET: "数据生成时间",
        UPDATET: "数据更新时间",
        SOURCE: "数据来源"
    },
    MAP2CHINA: {
        SOURCE: ASSET_ENUMS.COMMON.SOURCE,
        TYPE: ASSET_ENUMS.NETWORK.ACCESS_CONTROL_LIST_TYPE,
        DIRECTION: ASSET_ENUMS.NETWORK.DIRECTION,
        ACTION: ASSET_ENUMS.NETWORK.ACTION,
    }
}
const VRRP = {
    INFO: {
        "IFID": "网络接口标识",
        "VRRPID": "VRRP组ID",
        "VIP": "虚拟IP",
        "VMASK": "掩码",
        "MASTER": "主备状态",
        "CREATET": "数据生成时间",
        "UPDATET": "数据更新时间",
        "SOURCE": "数据来源"
    },
    MAP2CHINA: {
        SOURCE: ASSET_ENUMS.COMMON.SOURCE,
        MASTER: ASSET_ENUMS.NETWORK.MASTER_STATUS
    }
}

const HARDWARE = {
    INFO: {
        HID: '硬件标识',
        BRAND: '品牌',
        MODEL: '型号',
        VER: '版本',
        SN: '硬件序列号',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源',
        CPU: 'CPU',
        MEMORY: '内存',
        DISK: '磁盘',
        NETWORK_CARD: '有线网卡',
        WIRELESS: '无线网卡',
        PERIPHERAL_INTERFACE: '外设接口',
        POWER: '电源',
        GPU: '显卡',
        HBA: '主机总线适配器',
        RAID: '磁盘冗余阵列',
        BMC: '基板管理控制器',
        SENSOR: '传感器'
    },
    MAP2CHINA: {
        SOURCE: ASSET_ENUMS.COMMON.SOURCE,
    },
    MEMORY,
    CPU, // 处理器
    DISK, // 磁盘
    NETWORK_CARD, // 有线网卡
    WIRELESS, // 无线网卡
    PERIPHERAL_INTERFACE, // 外设接口
    POWER, // 电源
    GPU, // 显卡
    HBA, // 主机总线适配器
    RAID, // 磁盘冗余阵列
    BMC, // 基板管理控制器
    SENSOR, // 传感器
} // 硬件
const SOFTWARE = {
    INFO: {
        SOFTID: '软件标识',
        BRAND: '品牌',
        MOD: '型号',
        VER: '版本',
        KINFO: '内核信息',
        KPATH: '内核路径',
        KPARAM: '内核参数',
        HNAME: '主机名',
        STIME: '启动时间',
        SLEVEL: '启动级别',
        TZ: '时区',
        EVAR: '环境变量',
        EVHASH: '环境变量哈希值',
        RSTAT: '超级用户启用',
        USTAT: 'USB存储功能启用',
        MSTAT: '强制访问控制启用',
        LHOSTS: '本地域名',
        MAXTNUM: '系统最大线程数',
        MAXPNUM: '系统最大进程数',
        MAXFNUM: '系统最大句柄数',
        STERRTYPE: '对时方式',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源',
        USER: '用户',
        USER_GROUP: '用户组',
        SESSION: '会话',
        PARTITION: '分区',
        PROCESS: '进程',
        SERVICE: '服务',
        BOOT: '启动任务',
        DRIVER: '驱动',
        FILE: '文件',
        EXECUTABLE_FILE: '可执行文件',
        SOFTWARE_PACKAGE: '软件包',
        VULN_SCAN_RESULT: '漏洞扫描',
        CONFIG_CHECK_RESULT: '基线核查',
        APPLICATION: '应用实例',
        PLUGIN: '安全插件',
    },
    MAP2CHINA: {
        SOURCE: ASSET_ENUMS.COMMON.SOURCE,
        STERRTYPE: ASSET_ENUMS.SOFTWARE.STERRTYPE,
    },
    USER, // 用户
    USER_GROUP, // 用户组
    SESSION, // 会话
    PARTITION, // 分区
    PROCESS, // 进程
    SERVICE, // 服务
    BOOT, // 启动任务
    DRIVER, // 驱动
    FILE, // 文件
    EXECUTABLE_FILE, // 可执行文件
    SOFTWARE_PACKAGE, // 软件包
    VULN_SCAN_RESULT, // 漏洞扫描
    CONFIG_CHECK_RESULT, // 基线核查
    APPLICATION, // 应用实例
    PLUGIN // 安全插件
} // 软件
const NETWORK = {
    INFO: {
        NETID: '网络标识',
        FWD: '网络转发功能启用',
        IPV6: 'IPV6启用',
        FINGER: '网络协议指纹',
        CTYPE: '空间类型',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间',
        SOURCE: '数据来源',
        NETWORK_INTERFACE: '网络接口',
        ROUTE: '网络路由',
        ADDRESS: '地址学习表',
        LISTENNING_PORT: '网络监听',
        NEIGHBOR: '网络邻居',
        VLAN: 'VLAN',
        ACCESS_CONTROL_LIST: '访问控制策略',
        VRRP: '网络设备虚拟路由协议组'
    },
    NETWORK_INTERFACE, // 网络接口
    ROUTE, // 网络路由
    ADDRESS, // 地址学习表
    LISTENNING_PORT, // 网络监听
    NEIGHBOR, // 网络邻居
    VLAN, // VLAN
    ACCESS_CONTROL_LIST, // 访问控制策略
    VRRP, // 网络设备虚拟路由协议组
    MAP2CHINA: {
        SOURCE: ASSET_ENUMS.COMMON.SOURCE,
        ROLE: ASSET_ENUMS.NETWORK.ASSET_ROLE
    }
} // 网络
// 连接
// ASSET2CHINA 中文对照

// 管理
const MANAGEMENT = {
    INFO: {
        GID: '资产 ID',
        DCLOUDID: '调控云资产标识',
        DCCID: '所属调度标识',
        STAID: '所属区域标识',
        FNAME: '资产全称',
        SNAME: '资产简称',
        ATYPE: '资产类型',
        ABRAND: '资产厂商',
        AMODEL: '资产型号',
        STATUS: '运行状态',
        ISCII: '关基设施',
        CLASS: '资产类别',
        MTYPE: '管辖类型',
        NTTYPE: '网络类型',
        AREA: '安全区',
        NOTES: '备注',
        CREATET: '数据生成时间',
        UPDATET: '数据更新时间'
    },
    MAP2CHINA: {
        ATYPE: ASSET_ENUMS.COMMON.ASSET_TYPE,
        STATUS: ASSET_ENUMS.MANAGEMENT.MANAGEMENT_STATUS,
        CLASS: ASSET_ENUMS.MANAGEMENT.MANAGEMENT_CLASS,
        NTTYPE: ASSET_ENUMS.MANAGEMENT.NET_TYPE,
        AREA: ASSET_ENUMS.MANAGEMENT.SECURITY_ZONE
    }
}
const QUERY2CHINA = {
    // 资产模型
    ASSET: {
        // 资产基本信息
        INFO: {
            GID: '资产ID',
            ATAG: '资产标识',
            DSN: '设备序列号',
            DNAME: '设备名称',
            TYPE: '资产类型',
            CONAST: '已确认资产',
            ONLSTA: '在线状态',
            LABEL: '资产标签集',
            CREATET: '数据生成时间',
            UPDATET: '数据更新时间',
            SOURCE: '数据来源',
            ASSET_NODE: '资产节点',
            HARDWARE: '硬件',
            SOFTWARE: '软件',
            NETWORK: '网络',
            MANAGEMENT: '管理'
        },
        // 硬件
        HARDWARE,
        // 软件
        SOFTWARE,
        // 网络
        NETWORK
    },
    // 连接模型
    CONNECT: {}
}

const COMMON_MAP = {
    HARDWARE: '硬件',
    CPU: '处理器',
    DISK: '磁盘',
    NETWORK_CARD: '有线网卡',
    WIRELESS: '无线网卡',
    PERIPHERAL_INTERFACE: '外设接口',
    PERIPHERAL: '外设设备',
    POWER: '电源',
    GPU: '显卡',
    HBA: '主机总线适配器',
    BMC: '基板管理控制器',
    SENSOR: '传感器',
    ETHERNET: '物理网口',

    SOFTWARE: '软件',
    USER: '用户',
    USER_GROUP: '用户组',
    SESSION: '会话',
    PARTITION: '分区',
    PROCESS: '进程',
    SERVICE: '服务',
    BOOT: '启动任务',
    DRIVER: '驱动',
    FILE: '文件',
    EXECUTABLE_FILE: '可执行文件',
    SOFTWARE_PACKAGE: '软件包',
    VULN_SCAN_RESULT: '漏洞扫描',
    CONFIG_CHECK_RESULT: '基线核查',
    APPLICATION: '应用实例',
    PLUGIN: '安全插件',

    NETWORK: '网络',
    NETWORK_INTERFACE: '网络接口',
    ROUTE: '网络路由',
    ADDRESS: '地址学习表',
    LISTENNING_PORT: '网络监听',
    NEIGHBOR: '网络邻居',
    VLAN: 'VLAN',
    ACCESS_CONTROL_LIST: '访问控制策略',
    VRRP: '网络设备虚拟路由协议组',
}
const ASSET_MODEL_QUERY = {
    ASSET_MAP: {
        COMMON: {
            ASSET: '资产信息',
            ASSET_NODE: '资产节点',
            HARDWARE: '硬件',
            SOFTWARE: '软件',
            NETWORK: '网络',
        },
        ASSET: {
            INFO: {
                GID: '资产ID',
                ATAG: '资产标识',
                DSN: '设备序列号',
                DNAME: '设备名称',
                TYPE: '资产类型',
                CONAST: '已确认资产',
                ONLSTA: '在线状态',
                LABEL: '资产标签集',
                CREATET: '数据生成时间',
                UPDATET: '数据更新时间',
                SOURCE: '数据来源',
                ASSET_NODE: '资产节点',
                HARDWARE: '硬件',
                SOFTWARE: '软件',
                NETWORK: '网络',
                MANAGEMENT: '管理'
            },
            ASSET_NODE,
            // 硬件
            HARDWARE,
            // 软件
            SOFTWARE,
            // 网络
            NETWORK,
        },
    },
    // 资产基本信息
    ASSET: {
        INFO: {
            GID: '资产ID',
            ATAG: '资产标识',
            DSN: '设备序列号',
            DNAME: '设备名称',
            TYPE: '资产类型',
            CONAST: '已确认资产',
            ONLSTA: '在线状态',
            LABEL: '资产标签集',
            CREATET: '数据生成时间',
            UPDATET: '数据更新时间',
            SOURCE: '数据来源'
        },
        MAP2CHINA: {
            SOURCE: ASSET_ENUMS.COMMON.SOURCE,
        }
    },
    ASSET_NODE,
    HARDWARE,
    SOFTWARE,
    NETWORK,
    MANAGEMENT
}
const ASSET_PARAM_MAP = {
    COMMON: {
        ASSET: '资产基本信息',
        ASSET_NODE: '资产节点',
        INTERCONF: '交互配置',
        MANAGEMENT: '管理',
        APPLICATION: '应用',
    },
    ASSET: {
        INFO: {
            GID: '资产全局ID',
            ATAG: '资产标识',
            DNAME: '设备名称',
            TYPE: '资产类型',
            LABEL: '资产标签集',
            SOURCE: '数据来源'
        },
        MAP2CHINA: {
            SOURCE: ASSET_ENUMS.COMMON.SOURCE,
            TYPE: ASSET_ENUMS.COMMON.ASSET_TYPE
        }
    },
    ASSET_NODE: {
        INFO: {
            NDMTYPE: '管理类型',
            GID: '资产ID',
            IP: 'IP',
            MAC: 'MAC',
            IFID: '网络接口ID',
            SOURCE: '数据来源'
        },
        MAP2CHINA: {
            NDMTYPE: ASSET_ENUMS.ASSET_PARAM.ASSET_PARAM_NDMTYPE,
            SOURCE: ASSET_ENUMS.COMMON.SOURCE,
        }
    },
    INTERCONF: {
        INFO: {
            RMTPORT: 'SSH远程登录端口',
            RMTTP: 'SSH远程方式',
            RMTACT: '远程账户',
            RMTPSD: '远程账户密码',
            SNMPPORT: 'SNMP监听端口',
            SNMPVER: 'snmp版本',
            SNMPV3USER: 'snmp v3用户名',
            SNMPAUTH: 'snmp v3认证算法',
            SNMPENC: 'snmp v3加密算法',
            SNMPREAD: 'snmp read团体名/认证密码',
            SNMPWRITE: 'snmp write团体名/加密密码',
            ANAMETH: '解析方法',
            STATUSUPFLAG: '资产软硬件状态上送开关'
        },
        MAP2CHINA: {
            RMTTP: ASSET_ENUMS.ASSET_PARAM.ASSET_PARAM_RMTTP,
            SNMPVER: ASSET_ENUMS.ASSET_PARAM.ASSET_PARAM_SNMPVER,
            SNMPAUTH: ASSET_ENUMS.ASSET_PARAM.ASSET_PARAM_SNMPAUTH,
            SNMPENC: ASSET_ENUMS.ASSET_PARAM.ASSET_PARAM_SNMPENC,
            ANAMETH: ASSET_ENUMS.ASSET_PARAM.ASSET_PARAM_ANAMETH,
            STATUSUPFLAG: ASSET_ENUMS.ASSET_PARAM.ASSET_PARAM_STATUSUPFLAG
        }
    },
    MANAGEMENT: {
        INFO: {
            DCLOUDID: '调控云资产标识',
            DCCID: '所属调度标识',
            STAID: '所属区域标识',
            FNAME: '资产全称',
            SNAME: '资产简称',
            ATYPE: '资产类型',
            ABRAND: '资产厂商',
            AMODEL: '资产型号',
            STATUS: '运行状态',
            ISCII: '关基设施',
            CLASS: '资产类别',
            MTYPE: '管辖类型',
            NTTYPE: '网络类型',
            AREA: '安全区',
            NOTES: '备注'
        },
        MAP2CHINA: {
            ATYPE: ASSET_ENUMS.COMMON.ASSET_TYPE,
            STATUS: ASSET_ENUMS.MANAGEMENT.MANAGEMENT_STATUS,
            CLASS: ASSET_ENUMS.ASSET_PARAM.ASSET_PARAM_CLASS,
            NTTYPE: ASSET_ENUMS.MANAGEMENT.NET_TYPE,
            AREA: ASSET_ENUMS.MANAGEMENT.SECURITY_ZONE
        }
    },
    APPLICATION: {
        INFO: {
            APPMTYPE: '管理类型',
            APPID: '应用软件标识',
            NAME: '名称',
            BRAND: '品牌',
            VER: '版本',
            TYPE: '应用类型',
            STYPE: '应用子类型',
            MDIPA: '被管IP-A',
            MDIPB: '被管IP-B',
            APPDESC: '应用描述',
            SOURCE: '数据来源',
            ENSTAT: '监测代理软件启用状态'
        },
        MAP2CHINA: {
            APPMTYPE: ASSET_ENUMS.ASSET_PARAM.ASSET_PARAM_APPMTYPE,
            TYPE: ASSET_ENUMS.SOFTWARE.APPLICATION_TYPE,
            SOURCE: ASSET_ENUMS.COMMON.SOURCE
        }
    }
}
module.exports = {
    // 资产节点
    ASSET_NODE,
    // 硬件
    HARDWARE,
    CPU,
    MEMORY,
    DISK,
    NETWORK_CARD,
    WIRELESS,
    PERIPHERAL_INTERFACE,
    POWER,
    GPU,
    HBA,
    RAID,
    BMC,
    SENSOR,
    PERIPHERAL,
    ETHERNET,
    // 软件
    SOFTWARE,
    USER,
    USER_GROUP,
    SESSION,
    PARTITION,
    PROCESS,
    SERVICE,
    BOOT,
    DRIVER,
    FILE,
    EXECUTABLE_FILE,
    SOFTWARE_PACKAGE,
    VULN_SCAN_RESULT,
    CONFIG_CHECK_RESULT,
    APPLICATION,
    PLUGIN,
    // 网络
    NETWORK,
    NETWORK_INTERFACE,
    ROUTE,
    ADDRESS,
    LISTENNING_PORT,
    NEIGHBOR,
    VLAN,
    ACCESS_CONTROL_LIST,
    VRRP,
    // 管理
    MANAGEMENT,
    // 全量对照
    QUERY2CHINA,
    COMMON_MAP,
    // 资产模型调阅专用
    ASSET_MODEL_QUERY,
    ASSET_PARAM_MAP,
};
