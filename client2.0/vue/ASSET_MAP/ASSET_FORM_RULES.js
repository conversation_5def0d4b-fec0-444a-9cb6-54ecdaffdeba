const validateIp = (rule, value, callback) => {
    const reg = /^((\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.){3}(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])(-((\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.){3}(\d{1,2}|1\d\d|2[0-4]\d|25[0-5]))?$/
    if (value && reg.test(value) === false) {
        return callback(new Error('请确保IP格式正确'))
    } else {
        return callback()
    }
}
const validateMac = (rule, value, callback) => {
    // 允许为空
    if (!value) {
        return callback()
    }
    // MAC 地址正则：支持 00:1A:2B:3C:4D:5E 或 00-1A-2B-3C-4D-5E
    const reg = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/

    if (!reg.test(value)) {
        return callback(new Error('请确保MAC地址正确'))
    } else {
        return callback()
    }
}
// 端口号
const validatePort = (rule, value, callback) => {
    if (!value && value !== 0) return callback()
    if (isNaN(value)) return callback(new Error('请在1 - 65535之间取值证书'))
    const reg = /^(0|[1-9]\d*)$/
    if (!reg.test(value)) {
        return callback(new Error('请输入非负整数'))
    }
    if (value < 1 || value > 65535) {
        return callback(new Error('请在0 - 65535之间取值'))
    }
    return callback()
}
// 非负整数
const validateInteger = (rule, value, callback) => {
    if (!value) return callback() // 允许为空
    if (isNaN(value)) return callback(new Error('请输入非负整数')) // 数字
    // 必须为非负整数（0 或正整数）
    const reg = /^(0|[1-9]\d*)$/
    if (!reg.test(value)) {
        return callback(new Error('请输入非负整数'))
    }
    // 检查是否超出 int32 最大值
    const num = Number(value)
    if (num > 2147483647) {
        return callback(new Error('数值过大'))
    }
    return callback()
}
// 正整数
const validatePositiveInteger = (rule, value, callback) => {
    if (!value) return callback(new Error('请填写正整数')) // 允许为空
    if (isNaN(value)) return callback(new Error('请填写正整数')) // 数字
    // 必须为非负整数（0 或正整数）
    const reg = /^(0|[1-9]\d*)$/
    if (!reg.test(value)) {
        return callback(new Error('请填写正整数'))
    }
    // 检查是否超出 int32 最大值
    const num = Number(value)
    if (num > 2147483647) {
        return callback(new Error('数值过大'))
    }
    return callback()
}
const validate18 = (rule, value, callback) => {
    // 允许为空
    if (!value) return callback()
    if (value.length > 18) return callback(new Error('输入位数过长'))
    return callback()
}
const validate20 = (rule, value, callback) => {
    // 允许为空
    if (!value) return callback()
    if (value.length > 18) return callback(new Error('输入位数过长'))
    return callback()
}
module.exports = {
    InputSize18Rule: (title='', required=false) => {
        return [
            { required, message: `请输入${title}`, trigger: 'blur' },
            { required, message: validate18.Error, validator: validate18, trigger: ['blur', 'change'] },
        ]
    },
    InputSize20Rule: (title='', required=false) => {
        return [
            { required, message: `请输入${title}`, trigger: 'blur' },
            { required, message: validate20.Error, validator: validate20, trigger: ['blur', 'change'] },
        ]
    },
    // 必填框
    requiredInputRule: (title='') => {
        return [{ required: true, message: `请输入${title}`, trigger: ['blur'] }]
    },
    requiredSelectRule: (title='') => {
        return [{ required: true, message: `请选择${title}`, trigger: ['blur', 'change'] }]
    },
    // 整数数字输入框校验
    IntegerInputRule: (title='', required=false) => {
        return [
            { required, message: `请输入${title}`, trigger: 'blur' },
            { required, message: validateInteger.Error, validator: validateInteger, trigger: ['blur', 'change'] },
        ]
    },
    // 整数数字输入框校验
    positiveIntegerInputRule: (title='', required=false) => {
        return [
            { required, message: `请输入${title}`, trigger: 'blur' },
            { required, message: validatePositiveInteger.Error, validator: validatePositiveInteger, trigger: ['blur', 'change'] },
        ]
    },
    // IP检验
    ipValidateRule: (title='', required=true) => {
        return [
            { required, message: `请输入${title}`, trigger: 'blur' },
            { required, message: validateIp.Error, validator: validateIp, trigger: ['blur', 'change'] },
        ]
    },
    // IP检验
    macValidateRule: (title='', required=true) => {
        return [
            { required, message: `请输入${title}`, trigger: 'blur' },
            { required, message: validateMac.Error, validator: validateMac, trigger: ['blur', 'change'] },
        ]
    },
    // 端口
    portValidateRule: (title='', required=true) => {
        return [
            { required, message: `请输入${title}`, trigger: 'blur' },
            { required, message: validatePort.Error, validator: validatePort, trigger: ['blur', 'change'] },
        ]
    }
}