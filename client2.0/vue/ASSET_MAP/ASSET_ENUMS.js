const DicDefine_pb = require("../../script/pb/DicDefine_pb");
const SOURCE = {
    [DicDefine_pb.DataSource.DATA_SOURCE_DUMMY]: '未知',
    [DicDefine_pb.DataSource.DATA_SOURCE_HOST_MONITORING]: '主机监测',
    [DicDefine_pb.DataSource.DATA_SOURCE_SWITCH]: '交换机',
    [DicDefine_pb.DataSource.DATA_SOURCE_ROUTER]: '路由器',
    [DicDefine_pb.DataSource.DATA_SOURCE_ISOLATION_DEVICE]: '隔离装置',
    [DicDefine_pb.DataSource.DATA_SOURCE_VERTICAL_ENCRYPTION]: '纵向加密',
    [DicDefine_pb.DataSource.DATA_SOURCE_FIREWALL]: '防火墙',
    [DicDefine_pb.DataSource.DATA_SOURCE_MONITORING_DEVICE]: '监测装置',
    [DicDefine_pb.DataSource.DATA_SOURCE_MONITORING_PLATFORM]: '监测平台',
    [DicDefine_pb.DataSource.DATA_SOURCE_OPERATION_GATEWAY]: '运维网关',
    [DicDefine_pb.DataSource.DATA_SOURCE_ASSET_DETECTION]: '资产探测',
    [DicDefine_pb.DataSource.DATA_SOURCE_TRAFFIC_COLLECTION]: '流量采集',
    [DicDefine_pb.DataSource.DATA_SOURCE_PLUGIN]: '插件',
}
const ASSET_TYPE = {
    [DicDefine_pb.AssetType.ASSET_TYPE_UNKNOWN]: '未知',
    [DicDefine_pb.AssetType.ASSET_TYPE_SERVER]: '服务器',
    [DicDefine_pb.AssetType.ASSET_TYPE_WORKSTATION]: '工作站',
    [DicDefine_pb.AssetType.ASSET_TYPE_STORAGE_DEVICE]: '存储设备',
    [DicDefine_pb.AssetType.ASSET_TYPE_BLADE_CHASSIS]: '刀片服务器机箱',
    [DicDefine_pb.AssetType.ASSET_TYPE_BLADE_SERVER]: '刀片服务器',
    [DicDefine_pb.AssetType.ASSET_TYPE_ROUTER]: '路由器',
    [DicDefine_pb.AssetType.ASSET_TYPE_SWITCH]: '交换机',
    [DicDefine_pb.AssetType.ASSET_TYPE_INDUSTRIAL_SWITCH]: '工业交换机',
    [DicDefine_pb.AssetType.ASSET_TYPE_MEDIA_CONVERTER]: '光电转换器',
    [DicDefine_pb.AssetType.ASSET_TYPE_SERIAL_SERVER]: '串口服务器',
    [DicDefine_pb.AssetType.ASSET_TYPE_LATERAL_ISOLATION]: '横向隔离装置',
    [DicDefine_pb.AssetType.ASSET_TYPE_LONGITUDINAL_ENCRYPTION]: '纵向加密装置',
    [DicDefine_pb.AssetType.ASSET_TYPE_FIREWALL]: '防火墙',
    [DicDefine_pb.AssetType.ASSET_TYPE_IDS]: '入侵检测设备',
    [DicDefine_pb.AssetType.ASSET_TYPE_NET_SEC_MONITOR]: '网络安全监测装置',
    [DicDefine_pb.AssetType.ASSET_TYPE_ENCRYPTION_CARD]: '加密卡',
    [DicDefine_pb.AssetType.ASSET_TYPE_LARGE_SCREEN]: '大屏幕',
    [DicDefine_pb.AssetType.ASSET_TYPE_PRECISION_AC]: '精密空调',
    [DicDefine_pb.AssetType.ASSET_TYPE_KVM]: 'KVM',
    [DicDefine_pb.AssetType.ASSET_TYPE_TIME_SYNC]: '时间同步装置',
    [DicDefine_pb.AssetType.ASSET_TYPE_PRINTER]: '打印机',
    [DicDefine_pb.AssetType.ASSET_TYPE_NETWORK_CABLE]: '网络连接线',
    [DicDefine_pb.AssetType.ASSET_TYPE_MERGING_UNIT]: '合并单元',
    [DicDefine_pb.AssetType.ASSET_TYPE_INTELLIGENT_TERMINAL]: '智能终端',
    [DicDefine_pb.AssetType.ASSET_TYPE_SPECIAL_TELEMETRY_GATEWAY]: '专用远动网关机',
    [DicDefine_pb.AssetType.ASSET_TYPE_TELEMETRY_DEVICE]: '远动装置',
    [DicDefine_pb.AssetType.ASSET_TYPE_MEASUREMENT_CONTROL]: '测控装置',
    [DicDefine_pb.AssetType.ASSET_TYPE_PMU]: '相量测量装置',
    [DicDefine_pb.AssetType.ASSET_TYPE_ENERGY_ACQUISITION]: '电能量采集终端',
    [DicDefine_pb.AssetType.ASSET_TYPE_NETWORK_ANALYZER]: '网络分析仪',
    [DicDefine_pb.AssetType.ASSET_TYPE_SMART_PD_TERMINAL]: '智能配电终端',
    [DicDefine_pb.AssetType.ASSET_TYPE_SMART_PD_MONITORING]: '智能配电测控终端',
    [DicDefine_pb.AssetType.ASSET_TYPE_TRANSFORMER_MONITORING]: '配变监控终端',
    [DicDefine_pb.AssetType.ASSET_TYPE_SMART_PD_SYNC_MEAS]: '智能配电同步测量终端',
    [DicDefine_pb.AssetType.ASSET_TYPE_OS]: '操作系统',
    [DicDefine_pb.AssetType.ASSET_TYPE_DATABASE]: '数据库',
    [DicDefine_pb.AssetType.ASSET_TYPE_MIDDLEWARE]: '中间件',
    [DicDefine_pb.AssetType.ASSET_TYPE_APP_SOFTWARE_PKG]: '应用软件包',
    [DicDefine_pb.AssetType.ASSET_TYPE_APP_SOFTWARE]: '应用软件',
    [DicDefine_pb.AssetType.ASSET_TYPE_APPLICATION]: '应用程序',
    [DicDefine_pb.AssetType.ASSET_TYPE_MALWARE_MONITOR]: '恶意代码监测系统',
    [DicDefine_pb.AssetType.ASSET_TYPE_ANTIVIRUS]: '防病毒',
    [DicDefine_pb.AssetType.ASSET_TYPE_OPS_GATEWAY]: '运维网关',
    [DicDefine_pb.AssetType.ASSET_TYPE_OTHER]: '其他'
}
const HARDWARE_ERR_STATUS = {
    [DicDefine_pb.HardwareExceptionStatus.HW_EX_DUMMY]: '未知',
    [DicDefine_pb.HardwareExceptionStatus.HW_EX_POWER_OVERLOAD]: '电源过载',
    [DicDefine_pb.HardwareExceptionStatus.HW_EX_POWER_CTRL_FAILURE]: '电源控制器异常',
    [DicDefine_pb.HardwareExceptionStatus.HW_EX_MAIN_POWER_FAILURE]: '主电源故障',
    [DicDefine_pb.HardwareExceptionStatus.HW_EX_DISK_ALERT]: '存在磁盘告警',
    [DicDefine_pb.HardwareExceptionStatus.HW_EX_MEM_ECC_ERROR]: '内存ECC错误',
    [DicDefine_pb.HardwareExceptionStatus.HW_EX_FAN_FAILURE]: '风扇存在故障',
    [DicDefine_pb.HardwareExceptionStatus.HW_EX_POWER_REDUNDANCY_ALERT]: '电源冗余告警'
}
const ETHERNET_TYPE_ETHTYP = { 1: '电口', 2: '光口', 99: '其他'}
const NET_TYPE_NTYPE = { 1: '内网', 2: '外网', 3: '网桥'}
const CONNECTION_STATUS = { 1: '连接', 2: '断开' }
const CPU_ARCH = {
    [DicDefine_pb.CpuArchitecture.CPU_ARCH_UNKNOWN]: '未知',
    [DicDefine_pb.CpuArchitecture.CPU_ARCH_X86]: 'X86',
    [DicDefine_pb.CpuArchitecture.CPU_ARCH_ARM]: 'ARM',
    [DicDefine_pb.CpuArchitecture.CPU_ARCH_POWERPC]: 'POWERPC',
    [DicDefine_pb.CpuArchitecture.CPU_ARCH_RISC_V]: 'RISC-V',
    [DicDefine_pb.CpuArchitecture.CPU_ARCH_MIPS]: 'MIPS',
    [DicDefine_pb.CpuArchitecture.CPU_ARCH_LOONGARCH]: 'LoongArch',
    [DicDefine_pb.CpuArchitecture.CPU_ARCH_OTHER]: '其他'
}
const PERIPHERAL_INTERFACE_TYPE = {
    [DicDefine_pb.PeripheralInterfaceType.PERIPHERAL_INTERFACE_DUMMY]: '未知',
    [DicDefine_pb.PeripheralInterfaceType.PERIPHERAL_INTERFACE_USB]: 'USB接口',
    [DicDefine_pb.PeripheralInterfaceType.PERIPHERAL_INTERFACE_BLUETOOTH]: '蓝牙模块',
    [DicDefine_pb.PeripheralInterfaceType.PERIPHERAL_INTERFACE_SERIAL]: '串口',
    [DicDefine_pb.PeripheralInterfaceType.PERIPHERAL_INTERFACE_PARALLEL]: '并口',
    [DicDefine_pb.PeripheralInterfaceType.PERIPHERAL_INTERFACE_OPTICAL_DRIVE]: '光驱',
    [DicDefine_pb.PeripheralInterfaceType.PERIPHERAL_INTERFACE_OTHER]: '其他'
}
const PERIPHERAL_TYPE = {
    [DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_UNKNOWN]: '未知',
    [DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_STORAGE]: '存储类',
    [DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_COMM]: '通信设备',
    [DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_INPUT]: '键鼠设备',
    [DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_OPTICAL_DISC]: '光盘',
    [DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_DISPLAY]: '显示器',
    [DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_PRINTER]: '打印机',
    [DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_WIRELESS_NIC]: '无线网卡',
    [DicDefine_pb.PeripheralDeviceType.PERIPHERAL_DEVICE_OTHER]: '其他'
}
const IF_PROTO_CODE = {
    [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_DUMMY]: '未知',
    [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_AUDIO]: 'Audio',
    [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_CDC_CTRL]: 'Communications and CDC Control',
    [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_HID]: 'HID(Human Interface Device)',
    [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_PHYSICAL]: 'Physical',
    [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_IMAGE]: 'Image',
    [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_PRINTER]: 'Printer',
    [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_MASS_STORAGE]: 'Mass Storage',
    [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_HUB]: 'Hub',
    [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_CDC_DATA]: 'CDC-Data',
    [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_SMART_CARD]: 'Smart Card',
    [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_CONTENT_SECURITY]: 'Content Security',
    [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_VIDEO]: 'Video',
    [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_HEALTHCARE]: 'Personal Healthcare',
    [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_DIAGNOSTIC]: 'Diagnostic Device',
    [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_WIRELESS_CTRL]: 'Wireless Controller',
    [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_MISCELLANEOUS]: 'Miscellaneous',
    [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_APP_SPECIFIC]: 'Application Specific',
    [DicDefine_pb.PeripheralProtocol.PERIPHERAL_PROTOCOL_VENDOR_SPECIFIC]: 'Vendor Specific'
}

const STERRTYPE = { 1: 'NTP', 2: 'B 码' }
const SOFTWARE_ERR_SERR = {
    [DicDefine_pb.SoftwareException.SOFTWARE_EXCEPTION_DUMMY]: '未知',
    [DicDefine_pb.SoftwareException.SOFTWARE_EXCEPTION_SYSTEM_LOAD]: '系统负载异常',
    [DicDefine_pb.SoftwareException.SOFTWARE_EXCEPTION_ZOMBIE_PROCESS]: '存在僵尸进程',
    [DicDefine_pb.SoftwareException.SOFTWARE_EXCEPTION_HIGH_RISK_PORT]: '启用高危端口',
    [DicDefine_pb.SoftwareException.SOFTWARE_EXCEPTION_TIME_SYNC]: '对时异常',
    [DicDefine_pb.SoftwareException.SOFTWARE_EXCEPTION_KERNEL_LOCK]: '内核软死锁状态',
}
const LOGIN_METHOD = {
    [DicDefine_pb.LoginMethod.LOGIN_METHOD_DUMMY]: '未知',
    [DicDefine_pb.LoginMethod.LOGIN_METHOD_FTP]: 'FTP',
    [DicDefine_pb.LoginMethod.LOGIN_METHOD_LOCAL]: 'LOCAL',
    [DicDefine_pb.LoginMethod.LOGIN_METHOD_RADIUS]: 'RADIUS',
    [DicDefine_pb.LoginMethod.LOGIN_METHOD_RDP]: 'RDP',
    [DicDefine_pb.LoginMethod.LOGIN_METHOD_SSH]: 'SSH',
    [DicDefine_pb.LoginMethod.LOGIN_METHOD_TELNET]: 'TELNET',
    [DicDefine_pb.LoginMethod.LOGIN_METHOD_TFTP]: 'TFTP',
    [DicDefine_pb.LoginMethod.LOGIN_METHOD_VNC]: 'VNC',
    [DicDefine_pb.LoginMethod.LOGIN_METHOD_X11]: 'X11',
    [DicDefine_pb.LoginMethod.LOGIN_METHOD_SERIAL]: '串口',
    [DicDefine_pb.LoginMethod.LOGIN_METHOD_OTHER]: '其他'
}
const SESSION_STATUS = { 1: '登录', 2: '退出', 3: '阻断', }
const QUIT_TYPE = { 0: '未知', 1: '主动', 2: '被动' }
const START_TYPE = { 0: '未知', 1: 'simple', 2: 'forking', 3: 'oneshot', 4: 'dbus', 5: 'notify', 6: 'idle' }
const SERVICE_STATUS = { 1: '启动', 2: '停止', 3: '暂停' }
const BOOT_TASK_TYPE = { 1: '系统启动任务', 2: '用户启动任务' }
const FILE_TYPE = { 1: '文件', 2: '目录', 3: '链接', }
const FILE_CLASS = { 1: '普通文件', 2: '动态库', 3: '二进制可执行文件', 4: '脚本文件', 5: '其他' }
const INSTALL_PACKAGE_TYPE = { 1: 'rpm', 2: 'deb', 3: 'exe', 4: 'msi', 99: '其他' }
const RISK_LEVEL = { 1: '低', 2: '中', 3: '高', 4: '信息' }
const REPAIR_STATUS = { 1: '已修复', 2: '未修复' }
const CONFIG_CHECK_RESULT_SUBTYPE = { 0: '防火墙', 1: '交换机', 2: '主机', 3: '数据库' }
const ISCHECK = { 1: '合规', 2: '不合规', 3: '需人工确认' }
// 应用类型
const APPLICATION_TYPE = {
    [DicDefine_pb.ApplicationType.APPLICATION_TYPE_DUMMY]: '未知',
        [DicDefine_pb.ApplicationType.APPLICATION_TYPE_VERTICAL_ENCRYPTION]: '纵向加密应用',
        [DicDefine_pb.ApplicationType.APPLICATION_TYPE_HORIZONTAL_ISOLATION]: '横向隔离应用',
        [DicDefine_pb.ApplicationType.APPLICATION_TYPE_FIREWALL]: '防火墙应用',
        [DicDefine_pb.ApplicationType.APPLICATION_TYPE_SWITCH]: '交换机应用',
        [DicDefine_pb.ApplicationType.APPLICATION_TYPE_ROUTER]: '路由器应用',
        [DicDefine_pb.ApplicationType.APPLICATION_TYPE_HOST_MONITORING]: '主机监测应用',
        [DicDefine_pb.ApplicationType.APPLICATION_TYPE_TRUSTED_VERIFICATION]: '可信验证应用',
        [DicDefine_pb.ApplicationType.APPLICATION_TYPE_INTRUSION_DETECTION]: '入侵检测应用',
        [DicDefine_pb.ApplicationType.APPLICATION_TYPE_OPERATION_GATEWAY]: '运维网关应用',
        [DicDefine_pb.ApplicationType.APPLICATION_TYPE_MALICIOUS_CODE_MONITORING]: '恶意代码监测应用',
        [DicDefine_pb.ApplicationType.APPLICATION_TYPE_MONITORING_DEVICE]: '监测装置应用',
        [DicDefine_pb.ApplicationType.APPLICATION_TYPE_DATABASE]: '数据库应用',
        [DicDefine_pb.ApplicationType.APPLICATION_TYPE_CRYPTOGRAPHIC_SERVER]: '服务器密码机应用',
        [DicDefine_pb.ApplicationType.APPLICATION_TYPE_SECURITY_MONITORING_SYSTEM]: '安全监测系统应用',
        [DicDefine_pb.ApplicationType.APPLICATION_TYPE_OTHER]: '其他应用'
}
// 应用子类型
const APPLICATION_STYPE = {
    // 纵向加密
    1: {'VEAD': '纵向加密装置', 'CARD': '纵向加密卡', 'VEAD-MINI': '微型纵向加密装置'},
    // 横向隔离
    2: {'FID': '正向隔离装置', 'BID': '反向隔离装置', 'TS': '隔离传输软件'},
    // 防火墙
    3: {'FW': '防火墙', 'WAF': 'WEB防火墙'},
    // 交换机
    4: {'SW-MAIN': '局域网核心交换机', 'SW-FRONT': '调度数据网交换机', 'SW-PT': '局域网接入交换机'},
    // 路由器
    5: {'RT': '路由器'},
    // 主机监测
    6: {'SVR-PT': '服务器', 'SVR-PC': '工作站'},
    // 可信验证
    7: {'TVM': '主机可信验证'},
    // 入侵检测
    8: {'IDS': '入侵监测系统', 'IPS': '入侵防御系统'},
    // 运维网关
    9: {'SASH': '主站运维网关', 'PMG': '便携式运维网关'},
    // 恶意代码监测
    10: {'ACC': '防恶意代码客户端', 'MCMS': '恶意代码监测', 'AV': '防病毒', 'FMCD': '恶意代码网络流量监测采集'},
    // 监测装置
    11: {'DCD-Ⅰ': 'Ⅰ型监测装置', 'DCD-Ⅱ': 'Ⅱ型监测装置', 'DCD-H': '高性能监测装置', 'DCD-N': '普通版监测装置'},
    // 数据库
    12: {'DB': '数据库'},
    // 服务器密码机
    13: {'CS': '密码机', 'CS-CARD': '密码卡'},
    // 安全监测系统
    14: {'MP': '调度安全监测平台', 'DMP': '配电安全监测平台'},
    // 其他应用
    99: {'OTHER': '其他'}
}
const RUNNING_STATUS = { 1: '正常', 2: '停止', 3: '异常' }
const AUTHENTICATION = { 0: '未知', 1: '单因子', 2: '双因子' }
const PLUGIN_TYPE = { P: '平台', D: '装置', A: '探针' }
const PLUGIN_PSTAT = { 1: '正常', 2: '加载异常', 3: '运行异常', 4: '停止' }
const PLUGIN_STYPE = {
    [DicDefine_pb.PluginBusinessType.PLUGIN_BUSINESS_DUMMY]: '保留0值',
    [DicDefine_pb.PluginBusinessType.PLUGIN_BUSINESS_PERIPHERAL_CONTROL]: '外设管控插件',
    [DicDefine_pb.PluginBusinessType.PLUGIN_BUSINESS_D60_MONITORING]: '0运行监测插件',
    [DicDefine_pb.PluginBusinessType.PLUGIN_BUSINESS_HOST_CONFIG_CHECK]: '主机配置核查插件',
    [DicDefine_pb.PluginBusinessType.PLUGIN_BUSINESS_HOST_CONNECTION_DETECTION]: '主机外联检测插件',
    [DicDefine_pb.PluginBusinessType.PLUGIN_BUSINESS_VULNERABILITY_SCAN]: '漏洞扫描',
    [DicDefine_pb.PluginBusinessType.PLUGIN_BUSINESS_BASIC_RULE_DETECTION]: '基础规则检测',
    [DicDefine_pb.PluginBusinessType.PLUGIN_BUSINESS_INTELLIGENT_DETECTION]: '智能检测',
    [DicDefine_pb.PluginBusinessType.PLUGIN_BUSINESS_ASSET_IDENTIFICATION]: '资产识别',
    [DicDefine_pb.PluginBusinessType.PLUGIN_BUSINESS_SWITCH_COLLECTION]: '交换机采集（对交换机的采集与管控）',
    [DicDefine_pb.PluginBusinessType.PLUGIN_BUSINESS_FIREWALL_COLLECTION]: '防火墙采集（对防火墙的采集与管控）',
    [DicDefine_pb.PluginBusinessType.PLUGIN_BUSINESS_SYSLOG_PARSING]: 'SYSLOG解析',
    [DicDefine_pb.PluginBusinessType.PLUGIN_BUSINESS_HOST_TRUST_VERIFICATION]: '主机可信验证监测',
    [DicDefine_pb.PluginBusinessType.PLUGIN_BUSINESS_MALICIOUS_CODE_MONITORING]: '恶意代码监测监测',
    [DicDefine_pb.PluginBusinessType.PLUGIN_BUSINESS_MAINTENANCE_GATEWAY_MONITORING]: '主站运维网关监测',
}

const ASSET_ROLE = { 1: '网关', 2: '路由', 4: '交换', 8: '纵向', 15: '隔离', 20: '防火墙'  }
const NETWORK_ERR_STATUS = {
    [DicDefine_pb.NetworkException.NETWORK_EXCEPTION_DUMMY]: '保留值',
    [DicDefine_pb.NetworkException.NETWORK_EXCEPTION_DEVICE_IP_CONFLICT]: '网络设备IP地址冲突（超过额定功率）',
    [DicDefine_pb.NetworkException.NETWORK_EXCEPTION_ACCESS_IP_CONFLICT]: '接入设备IP地址冲突',
    [DicDefine_pb.NetworkException.NETWORK_EXCEPTION_DEVICE_MAC_CONFLICT]: '网络设备MAC地址冲突',
    [DicDefine_pb.NetworkException.NETWORK_EXCEPTION_DEVICE_IP_DUPLICATE]: '网络设备IP地址冲突',
    [DicDefine_pb.NetworkException.NETWORK_EXCEPTION_ILLEGAL_CONNECTION]: '设备违规外联',
    [DicDefine_pb.NetworkException.NETWORK_EXCEPTION_TRAFFIC_OVERFLOW]: '网络流量超阈值',
}
const NETWORK_INTERFACE_CLASS = {0: '未知', 1: '物理接口', 2: '逻辑接口'}
const LINK_STATUS = {0: '未知', 2: 'UP', 3: 'DOWN'}
const NETWORK_INTERFACE_SPEED = {1: '10m', 2: '100m', 3: '1000m', 4: '10000m'}
const BOND_MODE = {
    [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_DUMMY]: '未知',
    [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_DISABLED]: '未启用',
    [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_0]: '轮询模式 (Balance-RR)',
    [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_1]: '主备模式 (Active-Backup)',
    [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_2]: '平衡异或模式 (Balance-XOR)',
    [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_3]: '广播模式 (Broadcast)',
    [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_4]: 'LACP动态聚合 (802.3ad)',
    [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_5]: '自适应传输负载均衡 (Balance-TLB)',
    [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_6]: '自适应负载均衡 (Balance-ALB)',
    [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_BAGG]: 'BAGG',
    [DicDefine_pb.InterfaceBindingMode.BINDING_MODE_LAGG]: 'LAGG',
}
const NETWORK_INTERFACE_TYPE = {1: '内网口', 2: '外网口', 3: '网桥口', 4: 'TRUNK口', 5: 'BOND口'}
const ROUTE_TYPE = {1: '静态', 2: '动态'}
const ARP_TYPE = {1: '静态', 2: '动态'}
const ACCESS_CONTROL_LIST_TYPE = {
    [DicDefine_pb.PolicyType.POLICY_TYPE_DUMMY]: '手动添加的虚拟值',
    [DicDefine_pb.PolicyType.POLICY_TYPE_FIREWALL]: '防火墙策略',
    [DicDefine_pb.PolicyType.POLICY_TYPE_LONGITUDINAL_ENCRYPT]: '纵向加密策略',
    [DicDefine_pb.PolicyType.POLICY_TYPE_ISOLATION_DEVICE]: '隔离设备策略',
    [DicDefine_pb.PolicyType.POLICY_TYPE_SWITCH]: '交换机策略',
    [DicDefine_pb.PolicyType.POLICY_TYPE_ROUTER]: '路由器策略',
    [DicDefine_pb.PolicyType.POLICY_TYPE_OTHER]: '其他'
}
const DIRECTION = {1: '双向', 2: 'in', 3: 'out'}
const ACTION = {1: '丢弃', 2: '拒绝', 3: '接受', 4: 'NAT', 5: '隧道'}
const MANAGEMENT_STATUS = {
    [DicDefine_pb.AssetOperationStatus.ASSET_OPERATION_DUMMY]: '未知',
    [DicDefine_pb.AssetOperationStatus.ASSET_OPERATION_PRE_ACCESS]: '预接入',
    [DicDefine_pb.AssetOperationStatus.ASSET_OPERATION_IN_SERVICE]: '在运',
    [DicDefine_pb.AssetOperationStatus.ASSET_OPERATION_MAINTENANCE]: '检修',
    [DicDefine_pb.AssetOperationStatus.ASSET_OPERATION_DEACTIVATED]: '退运'
}
const MANAGEMENT_CLASS = {1: '长期运行资产', 2: '临时接入资产', 3: '允许临时关闭的资产'}
const NET_TYPE = {
    [DicDefine_pb.NetworkType.NETWORK_TYPE_DUMMY]: '手动添加的虚拟值',
    [DicDefine_pb.NetworkType.NETWORK_TYPE_BACKBONE_PLANE1]: '骨干网一平面',
    [DicDefine_pb.NetworkType.NETWORK_TYPE_BACKBONE_PLANE2]: '骨干网二平面',
    [DicDefine_pb.NetworkType.NETWORK_TYPE_NATIONAL_DISPATCH_ACCESS]: '国调接入网',
    [DicDefine_pb.NetworkType.NETWORK_TYPE_REGIONAL_DISPATCH_ACCESS]: '网调接入网',
    [DicDefine_pb.NetworkType.NETWORK_TYPE_PROVINCIAL_ACCESS]: '省级接入网',
    [DicDefine_pb.NetworkType.NETWORK_TYPE_PREFECTURAL_ACCESS]: '地级接入网',
    [DicDefine_pb.NetworkType.NETWORK_TYPE_DISPATCH_INTRANET]: '调度内网',
    [DicDefine_pb.NetworkType.NETWORK_TYPE_DISPATCH_INTRANET_B]: '调度内网B'
}
const SECURITY_ZONE = {
    [DicDefine_pb.SecurityZone.SECURITY_ZONE_UNKNOWN]: '未知',
    [DicDefine_pb.SecurityZone.SECURITY_ZONE_I]: 'I区',
    [DicDefine_pb.SecurityZone.SECURITY_ZONE_II]: 'II区',
    [DicDefine_pb.SecurityZone.SECURITY_ZONE_III]: 'Ⅲ区',
    [DicDefine_pb.SecurityZone.SECURITY_ZONE_IV]: 'IV区',
    [DicDefine_pb.SecurityZone.SECURITY_ZONE_I_II]: 'I-II区',
    [DicDefine_pb.SecurityZone.SECURITY_ZONE_PROD_CTRL_MGMT]: '生产控制一管理信息大区',
    [DicDefine_pb.SecurityZone.SECURITY_ZONE_II_III]: 'II-III区',
    [DicDefine_pb.SecurityZone.SECURITY_ZONE_III_IV]: 'III-IV区',
    [DicDefine_pb.SecurityZone.SECURITY_ZONE_SEC_ACCESS]: '安全接入区',
    [DicDefine_pb.SecurityZone.SECURITY_ZONE_OTHER]: '其他'
}
const MASTER_STATUS = { 1: 'Master', 2: 'Backup' }

// 资产参数用
const ASSET_PARAM_NDMTYPE = {1: '添加', 2: '修改', 3: '删除',}
const ASSET_PARAM_RMTTP = { 0: 'SSH' }
const ASSET_PARAM_SNMPVER = { 0: 'V2C', 1: 'V3' }
const ASSET_PARAM_SNMPAUTH = { 0: '不启用', 1: 'MD5', 2: 'SHA' }
const ASSET_PARAM_SNMPENC = { 0: '不启用', 1: 'DES', 2: 'AES' }
const ASSET_PARAM_ANAMETH = { 0: '正常解析', 1: '正则解析' }
const ASSET_PARAM_STATUSUPFLAG = { 0: '关闭', 1: '开启' }
const ASSET_PARAM_CLASS = { 1: '长期运行资产', 2: '临时接入资产', 3: '允许临时关闭的资产' }
const ASSET_PARAM_APPMTYPE = { 1: '添加', 2: '修改', 3: '删除' }
function getEnumByMap(Maps, isNumber=false) {
    if (!Object.keys(Maps).length) return []
    const arr = []
    Object.keys(Maps).forEach((key) => {
        let code = key
        if (isNumber) {
            if (!isNaN(code)) {
                code = Number(key)
            }
        }
        arr.push({ code, name: Maps[key]  })
    })
    return arr
}
module.exports = {
    ASSET_PARAM: {
        ASSET_PARAM_NDMTYPE,
        ASSET_PARAM_RMTTP,
        ASSET_PARAM_SNMPVER,
        ASSET_PARAM_SNMPAUTH,
        ASSET_PARAM_SNMPENC,
        ASSET_PARAM_ANAMETH,
        ASSET_PARAM_STATUSUPFLAG,
        ASSET_PARAM_CLASS,
        ASSET_PARAM_APPMTYPE
    },
    // 通用
    COMMON: {
        // 数据来源
        SOURCE,
        // 资产类型
        ASSET_TYPE,
    },
    // 硬件
    HARDWARE: {
        // 硬件异常状态
        HARDWARE_ERR_STATUS,
        // CPU架构
        CPU_ARCH,
        // 物理网口类型
        ETHERNET_TYPE_ETHTYP,
        // 物理网口  类型
        NET_TYPE_NTYPE,
        // 连接状态
        CONNECTION_STATUS,
        // 外设接口 类型
        PERIPHERAL_INTERFACE_TYPE,
        // 外设设备 类型
        PERIPHERAL_TYPE,
        // 外设协议编码 字典
        IF_PROTO_CODE,
    },
    // 软件
    SOFTWARE: {
        // 对时方式
        STERRTYPE,
        // 软件异常状态 字典
        SOFTWARE_ERR_SERR,
        // 登录方式 字典
        LOGIN_METHOD,
        // 会话——当前状态
        SESSION_STATUS,
        // 退出方式
        QUIT_TYPE,
        // 启动类型
        START_TYPE,
        // 运行状态
        SERVICE_STATUS,
        // 启动任务  任务类型 字典
        BOOT_TASK_TYPE,
        // 文件类型
        FILE_TYPE,
        // 文件种类
        FILE_CLASS,
        // 安装包类型
        INSTALL_PACKAGE_TYPE,
        // 危险级别
        RISK_LEVEL,
        // 修复状态
        REPAIR_STATUS,
        // 核查对象类型
        CONFIG_CHECK_RESULT_SUBTYPE,
        // 检查结果
        ISCHECK,
        // 应用类型
        APPLICATION_TYPE,
        // 应用类型子类型
        APPLICATION_STYPE,
        // 运行状态
        RUNNING_STATUS,
        // 身份鉴别模式
        AUTHENTICATION,
        // 插件类型
        PLUGIN_TYPE,
        // 插件状态
        PLUGIN_PSTAT,
        // 插件 业务类型
        PLUGIN_STYPE
    },
    // 网络
    NETWORK: {
        // 资产角色 字典
        ASSET_ROLE,
        // 网络异常状态 字典
        NETWORK_ERR_STATUS,
        // 型式分类
        NETWORK_INTERFACE_CLASS,
        // 启动状态
        LINK_STATUS,
        // 协商速率
        NETWORK_INTERFACE_SPEED,
        // 绑定模式
        BOND_MODE,
        // 接口类型 字典
        NETWORK_INTERFACE_TYPE,
        // 路由类型 字典
        ROUTE_TYPE,
        // 地址学习表 类型
        ARP_TYPE,
        // 策略类型
        ACCESS_CONTROL_LIST_TYPE,
        // 传输方向
        DIRECTION,
        // 动作
        ACTION,
        // 主备状态
        MASTER_STATUS
    },
    // 管理
    MANAGEMENT: {
        // 资产投运字典
        MANAGEMENT_STATUS,
        // 资产类别
        MANAGEMENT_CLASS,
        // 网络类型
        NET_TYPE,
        // 安全区 字典
        SECURITY_ZONE,
    },
    // TODO 方法
    getEnumByMap
}
