<!--&lt;!&ndash; element-plus &ndash;&gt;-->

<!--<script src="vue/admin/controller/port_param.js"></script>-->

<div class="main-content">
    <div class="main-content-inner">
        <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs" style="height: calc(100% - 100px);">
            <div class="page-content">
                <div class="page-header">
                    <h1>配置交互
                        <small>
                            <i class="ace-icon fa fa-angle-double-right"></i>
                            参数备份与恢复
                        </small>
                    </h1>
                </div>
                <!-- /.page-header -->
                <div class="row">
                    <div id="app">
                        <div ref="box" class="backup-container">
                            <div class="backup-panel">
                                <!-- 左侧 -->
                                <div class="panel-left">
                                    <h2 class="panel-title">数据备份</h2>
                                    <div class="custom-select-wrapper">
                                        <el-select v-model="backupParam" placeholder="请选择备份类型" class="custom-select" :suffix-icon="null">
                                            <el-option label="装置配置备份" :value="1"></el-option>
                                            <el-option label="基线备份" :value="2"></el-option>
                                            <el-option label="证书备份" :value="3"></el-option>
                                        </el-select>
                                        <span class="fake-arrow"><svg width="14" height="14" viewBox="0 0 24 24" fill="#2699d4"><path d="M7 10l5 5 5-5z" /></svg></span>
                                    </div>
                                    <p v-if="backupParam === 3" class="warning-text">注：证书备份前请确保已备份好装置配置!</p>
                                    <el-button type="primary" class="panel-button" @click="() => save2ValidateFn('BACKUP')" >备份</el-button>
                                </div>

                                <!-- 右侧 -->
                                <div class="panel-right">
                                    <h2 class="panel-title">数据恢复</h2>
                                    <div class="custom-select-wrapper">
                                        <el-select v-model="reStoreParam" placeholder="请选择恢复类型" class="custom-select" :suffix-icon="null">
                                            <el-option label="装置配置备份" :value="1"></el-option>
                                            <el-option label="基线备份" :value="2"></el-option>
                                            <el-option label="证书备份" :value="3"></el-option>
                                        </el-select>
                                        <span class="fake-arrow"><svg width="14" height="14" viewBox="0 0 24 24" fill="#2699d4"><path d="M7 10l5 5 5-5z" /></svg></span>
                                    </div>
                                    <div class="restore-box" @click="reStoreUploadHandleClick" v-if="!reStoreFileName">
                                        <p class="restore-tip">请选择 .bak 后缀文件</p>
                                        <svg class="restore-icon" viewBox="0 0 24 24" fill="#2699d4"><path d="M5 20h14v-2H5v2zM12 2l-5.5 5.5h4v7h3v-7h4L12 2z" /></svg>
                                        <input ref="fileInput" type="file" accept=".bak" class="file-input" @change="onFileChange" tabindex="-1" />
                                    </div>
                                    <div class="file-info" v-else>
                                        <span class="file-name" :title="reStoreFileName">{{ reStoreFileName }}</span>
                                        <span class="file-size">{{ formatFileSize(reStoreFileSize) }}</span>
                                        <button class="delete-btn" @click.stop="removeFile">删除</button>
                                    </div>
                                    <p class="warning-text">注：数据恢复成功后,需重启装置生效!</p>
                                    <el-button type="success" class="panel-button" @click="() => save2ValidateFn('RESTORE')">恢复</el-button>
                                </div>
                            </div>
                            <el-dialog title="数据备份" v-model="backupModalVisible" width="30%">
                                <el-row>
                                    <el-col :span="6">请选择：</el-col>
                                    <el-col :span="10">
                                        <el-input v-model="backupModalPath" readonly ref="backupFileInputRef" style="width: 100%;"></el-input>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-button @click="openSelectDict">选择文件夹</el-button>
                                    </el-col>
                                </el-row>
                            <span slot="footer" class="dialog-footer">
                                <el-button @click="backupCancelFn">取消</el-button>
                                <el-button type="primary" @click="backupDoneFn">保存</el-button>
                            </span>
                                </el-dialog>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /.main-content -->
    <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
        <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
    </a>
    <!-- 验证用户身份-->
    <div class="modal" style="z-index: 4000;" id="validateUserDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         aria-hidden="true">
        <form class="form-horizontal" id="validateUserForm" method="post" action="" data-toggle="validator" role="form">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header" style="color: white;background: deepskyblue">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                        <h4 class="modal-title">验证用户身份</h4>
                    </div>
                    <div class="modal-body" style="padding: 10px 50px;">
                        <p>请输入登录密码</p>
                        <input type="hidden" id="opraType"/>
                        <input type="password" id="password" class="form-control" placeholder="密码" required/>
                        <div class="help-block with-errors"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary" id="validateuser_btn">确定
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <!-- /.modal -->
    <!-- 导入导出选择对话框 -->
<!--    <div class="modal fade" id="confirmPortDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"-->
<!--         aria-hidden="true">-->
<!--        <form class="form-horizontal" id="confirmPortForm" method="post" action="" data-toggle="validator" role="form">-->
<!--            <div class="modal-dialog" role="document">-->
<!--                <div class="modal-content">-->
<!--                    <div class="modal-header">-->
<!--                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">-->
<!--                            <span aria-hidden="true">&times;</span>-->
<!--                            <span class="sr-only">Close</span>-->
<!--                        </button>-->
<!--                        <h4 class="modal-title" wa-name="import_tt">请选择</h4>-->
<!--                    </div>-->
<!--                    <div class="modal-body">-->
<!--                        <div class="form-group">-->
<!--                            <label class="col-sm-2 control-label text-right" for="select_file">请选择:</label>-->
<!--                            <div class="col-sm-9">-->
<!--                                <label class="ace-file-input">-->
<!--                                    <input type="file" id="select_file" required/>-->
<!--                                </label>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                        <div class="form-group hidden">-->
<!--                            <label class="col-sm-2 control-label text-right" for="select_folder">请选择:</label>-->
<!--                            <div class="col-sm-9">-->
<!--                                <label class="ace-file-input">-->
<!--                                    <input type="file" id="select_folder" webkitdirectory directory required/>-->
<!--                                </label>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                    <div class="modal-footer">-->
<!--                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>-->
<!--                        <button type="button" class="btn btn-primary" id="port_btn">确定</button>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!--        </form>-->
<!--    </div>-->
    <!-- /.modal -->

</div>
<style scoped>
    html, body, #app {
        height: 100%;
        margin: 0;
    }

    .backup-container {
        height: 100%;
        padding: 20px;
        box-sizing: border-box;
        background-color: #f9f9f9;
    }

    .backup-panel {
        display: flex;
        height: 100%;
        border: 2px solid #2699d4;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 0 12px rgba(38, 153, 212, 0.2);
    }

    .panel-left,
    .panel-right {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding: 20px;
        box-sizing: border-box;
        background-color: white;
    }

    .panel-left {
        border-right: 1px solid #ccc;
    }

    .panel-title {
        font-size: 20px;
        color: #2699d4;
        margin-bottom: 20px;
    }

    .custom-select-wrapper {
        position: relative;
        width: 100%;
        margin-bottom: 16px;
    }

    .custom-select :deep(.el-input__inner) {
        padding-right: 32px !important;
    }

    .fake-arrow {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
    }

    .warning-text {
        color: red;
        font-size: 14px;
        margin-bottom: 16px;
    }

    .spacer {
        flex-grow: 1;
    }

    .panel-button {
        width: 100%;
        font-size: 14px;
    }


</style>
<style scoped>
    .restore-box {
        position: relative;
        height: 200px;
        background-color: #eef7fb;
        border: 1px dashed #2699d4;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition: background-color 0.3s;
        user-select: none;
        margin-bottom: 10px;
    }

    .restore-box:hover {
        background-color: #e0f1f9;
    }

    .restore-tip {
        color: #2699d4;
        font-size: 14px;
        margin-bottom: 12px;
        pointer-events: none;
    }

    .restore-icon {
        width: 48px;
        height: 48px;
        transition: transform 0.2s;
        pointer-events: none;
    }

    .restore-box:hover .restore-icon {
        transform: scale(1.1);
    }

    .file-input {
        position: absolute !important;
        width: 1px !important;
        height: 1px !important;
        opacity: 0 !important;
        pointer-events: none !important;
        overflow: hidden !important;
        top: 0;
        left: 0;
        z-index: -1;
    }

    .file-info {
        margin-bottom: 10px;
        height: 200px;
        background-color: #eef7fb;
        border: 1px dashed #2699d4;
        border-radius: 8px;

        display: flex;
        justify-content: center;
        align-items: center;
        gap: 16px;
        padding: 0 20px;
        box-sizing: border-box;
        user-select: none;
    }

    .file-name {
        max-width: 40%;
        white-space: nowrap;
        margin-right: 5px;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: 600;
        color: #2699d4;
        font-size: 16px;
    }

    .file-size {
        color: #666;
        font-size: 14px;
        white-space: nowrap;
    }

    .delete-btn {
        background-color: #ff4d4f;
        border: none;
        color: white;
        font-weight: 600;
        border-radius: 4px;
        padding: 6px 12px;
        cursor: pointer;
        user-select: none;
        transition: background-color 0.3s;
        flex-shrink: 0;
    }

    .delete-btn:hover {
        background-color: #d9363e;
    }
</style>

<style>
    .info_box {
        width: 100%;
        height: 100%;
    }
    /* ------------------------------------------------------------------------------------------------- */
    /* 让 #app 占满剩余空间 */
    .main-content {
        height: 100%;

    }
    .main-content-inner, .page-content, .row {
        height: 100%;
    }

    html, body {
        height: 100%;
        margin: 0;
        padding: 0;
    }

    #app {
        display: flex;
        flex-direction: column;
        height: 100%;
        background-color: #f9f9f9;
    }
</style>

<link rel="stylesheet" href="lib/elementPlus/index.css"/>
<script src="lib/third/vue3/vue.global.js"></script>
<script src="lib/elementPlus/index.full.min.js"></script>
<script src="lib/elementPlus/zh-cn.js"></script>

<script>
    console.log(require.cache)
    delete require.cache[require.resolve('./vue/configBackupTrigger/MSG_PDBackupParam.js')];
    require('./vue/configBackupTrigger/MSG_PDBackupParam.js');
    // $('#handlerForm').validator();
</script>
