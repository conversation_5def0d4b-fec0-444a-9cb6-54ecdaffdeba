<script type="text/javascript">
  try {
    ace.settings.loadState('sidebar')
  } catch (e) {
  }
</script>
<ul class="l-b-nav">
  <li class data-id data-code="personal_mgr" data-tt="账号管理" data-nav-id="personal_mgr" data-nav-name="人员管理">
    <div class="l-b-navbox-1">
      <img src="assets/images/l-b-images/selected_bar.png">
    </div>
    <div class="list l-b-navbox-2" align="center">
      <img src="assets/images/l-b-images/user_manage.png"><br>账号管理
      <input name="menuId" type="hidden" value>
      <input name="menuUrl" type="hidden" value>
    </div>
  </li>
  <li class data-id data-code="event_processing" data-tt="设备参数"
      data-nav-id="event_processing,route_configuration,NTP_timing,communication_param,network_config"
      data-nav-name="事件处理,路由配置,NTP对时,通信参数,网卡配置">
    <div class="l-b-navbox-1">
      <img src="assets/images/l-b-images/selected_bar.png">
    </div>
    <div class="list l-b-navbox-2" align="center">
      <img src="assets/images/l-b-images/parameter.png"><br>设备参数
      <input name="menuId" type="hidden" value>
      <input name="menuUrl" type="hidden" value>
    </div>
  </li>
  <li class data-id data-code="policy_config" data-tt="系统设置"
      data-nav-id="policy_config,certificate_mgr,soft_upgrade,system_backup"
      data-nav-name="策略配置,证书管理,软件升级,系统备份">
    <div class="l-b-navbox-1">
      <img src="assets/images/l-b-images/selected_bar.png">
    </div>
    <div class="list l-b-navbox-2" align="center">
      <img src="assets/images/l-b-images/sys_manage.png"><br>系统设置
      <input name="menuId" type="hidden" value>
      <input name="menuUrl" type="hidden" value>
    </div>
  </li>
    <li class data-id data-code="system_msg" data-tt="系统调试"
        data-nav-id="system_msg,screen_evt,system_log"
        data-nav-name="系统信息,屏蔽事件,系统日志">
        <div class="l-b-navbox-1">
            <img src="assets/images/l-b-images/selected_bar.png">
        </div>
        <div class="list l-b-navbox-2" align="center">
            <img src="assets/images/l-b-images/sys_manage.png"><br>系统调试
            <input name="menuId" type="hidden" value>
            <input name="menuUrl" type="hidden" value>
        </div>
    </li>
    <li class data-id data-code="port_param" data-tt="参数配置"
        data-nav-id="port_param,route_param,sntp_param,link_param,self_param,device_param,transfer_param,merge_param,asset_model,asset_param"
        data-nav-name="网口参数,路由参数,SNTP参数,通信参数,自身运行参数,设备运行参数,数据传输参数,行为归并参数,资产模型,资产参数">
        <div class="l-b-navbox-1">
            <img src="assets/images/l-b-images/selected_bar.png">
        </div>
        <div class="list l-b-navbox-2" align="center">
            <img src="assets/images/l-b-images/sys_manage.png"><br>参数配置
            <input name="menuId" type="hidden" value>
            <input name="menuUrl" type="hidden" value>
        </div>
    </li>
    <li class data-id data-code="cert_conf" data-tt="证书配置"
        data-nav-id="cert_conf,param_BackupAndRestore"
        data-nav-name="证书配置,参数备份与恢复">
        <div class="l-b-navbox-1">
            <img src="assets/images/l-b-images/selected_bar.png">
        </div>
        <div class="list l-b-navbox-2" align="center">
            <img src="assets/images/l-b-images/sys_manage.png"><br>配置交互
            <input name="menuId" type="hidden" value>
            <input name="menuUrl" type="hidden" value>
        </div>
    </li>
    <li class data-id data-code="asset_model_query" data-tt="数据调阅"
        data-nav-id="asset_model_query,connection_model_query,self_model_query,behavior_model_query,platform_model_query,asset_state_model_query,log_model_query"
        data-nav-name="资产模型调阅,连接模型调阅,自身信息调阅,行为模型调阅,平台通信状态调阅,资产状态调阅,日志信息调阅">
        <div class="l-b-navbox-1">
            <img src="assets/images/l-b-images/selected_bar.png">
        </div>
        <div class="list l-b-navbox-2" align="center">
            <img src="assets/images/l-b-images/sys_manage.png"><br>数据调阅
            <input name="menuId" type="hidden" value>
            <input name="menuUrl" type="hidden" value>
        </div>
    </li>
    <li class data-id data-code="baselineManagement" data-tt="基线管理"
        data-nav-id="baselineManagement"
        data-nav-name="基线管理">
        <div class="l-b-navbox-1">
            <img src="assets/images/l-b-images/selected_bar.png">
        </div>
        <div class="list l-b-navbox-2" align="center">
            <img src="assets/images/l-b-images/sys_manage.png"><br>基线管理
            <input name="menuId" type="hidden" value>
            <input name="menuUrl" type="hidden" value>
        </div>
    </li>
</ul><!-- /.nav-list -->
