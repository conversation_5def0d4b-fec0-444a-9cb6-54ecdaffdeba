<div class="main-content">
  <div class="main-content-inner">
    <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs">
      <div class="page-content">
        <div class="page-header">
          <h1>系统设置
            <small>
              <i class="ace-icon fa fa-angle-double-right"></i>
              数据库备份
            </small>
          </h1>
        </div><!-- /.page-header -->
        <div class="row">
          <div class="col-xs-12">
            <div class="widget-box transparent ui-sortable-handle">
              <div class="widget-header">
                <div class="widget-toolbar no-border wa-floatL">
                  <ul class="nav nav-tabs" id="data-tabs">
                    <li class="active">
                      <a href="#backup_policy" data-toggle="tab">备份策略</a>
                    </li>
                    <li class="">
                      <a href="#backup_recover" data-toggle="tab">备份恢复</a>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="widget-body">
                <div class="widget-main padding-12 no-padding-left no-padding-right">
                  <div class="tab-content padding-4" id="data-tab-cont">
                    <div id="backup_policy" class="tab-pane active">
                      <form class="form-horizontal" id="backupPolicyForm" role="form">
                        <input type="hidden" id="app_backup_policy_id"/>
                        <div class="form-group">
                          <label class="col-sm-3 control-label text-right">自动备份时间:</label>
                          <div class="col-sm-9">
                            每
                            <select name="month" style="width: 60px;">
                              <option value="0">*</option>
                              <option value="2">2</option>
                              <option value="3">3</option>
                              <option value="4">4</option>
                              <option value="5">5</option>
                              <option value="6">6</option>
                              <option value="8">8</option>
                              <option value="12">12</option>
                            </select>
                            月 &nbsp;
                            <select name="day" style="width: 60px;">
                              <option value="1">1</option>
                              <option value="3">3</option>
                              <option value="5">5</option>
                              <option value="8">8</option>
                              <option value="12">12</option>
                              <option value="15">15</option>
                              <option value="20">20</option>
                              <option value="25">25</option>
                              <option value="28">28</option>
                            </select>
                            日 &nbsp;
                            <input type="text" id="hour" name="hour" placeholder="小时 0-23"
                                   style="width: 70px;"
                                   pattern="^(\d)|(1\d)|(2[0-3])$" required/>
                            时 &nbsp;
                            <input type="text" id="month" name="month" placeholder="分钟 0-59"
                                   style="width: 70px;"
                                   pattern="^[1-5]?[0-9]$" required/>
                            分 &nbsp;
                            <div class="help-block with-errors"></div>
                          </div>
                        </div>
                        <div class="form-group">
                          <label class="col-sm-3 control-label text-right">
                            是否压缩:
                          </label>
                          <div class="col-sm-9">
                            <label class="radio-inline">
                              <input type="radio" name="compress" value="0">是
                            </label>
                            <label class="radio-inline">
                              <input type="radio" name="compress" value="1">否
                            </label>
                          </div>
                        </div>
                        <div class="form-group">
                          <label class="col-sm-3 control-label text-right">
                            是否加密:
                          </label>
                          <div class="col-sm-9">
                            <label class="radio-inline">
                              <input type="radio" name="encryption" value="0">是
                            </label>
                            <label class="radio-inline">
                              <input type="radio" name="encryption" value="1">否
                            </label>
                          </div>
                        </div>
                        <div class="col-sm-offset-3 col-sm-9">
                          <button type="submit" class="btn btn-info" id="btn_policy_save">
                            <i class="ace-icon fa fa-check bigger-110"></i>保存
                          </button>
                        </div>
                      </form>
                    </div>
                    <div id="backup_recover" class="tab-pane">
                      <div class="row wa-mb10">
                        <div class="col-xs-12 text-right">
                          <button class="btn btn-sm btn-primary" id="recover_database">备份数据库</button>
                        </div>
                      </div>
                      <div class="row" style="margin-bottom: 25px">
                        <div class="col-xs-12">
                          <table id="database-tb" class="table table-bordered table-hover">
                          </table>
                          <!--<div id="platform-pager"></div>-->
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div><!-- /.col -->
        </div><!-- /.row -->
      </div><!-- /.page-content -->
    </div>
  </div><!-- /.main-content -->
  <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
    <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
  </a>
  <!-- 确认删除对话框 -->
  <script>
    // console.log(require.cache)
    // delete require.cache[require.resolve('./script/view/device_mgr/communication_param.js')];
    // require('./script/view/device_mgr/communication_param.js');
    $('#backupPolicyForm').validator();
  </script>
  <script>
    $('#data-tabs a[href="#backup_recover"]').on('shown.bs.tab', function (e) {
      // const parent_column = $("#database-tb").closest('[class*="col-"]');
      // $("#database-tb").jqGrid('setGridWidth', parent_column.width());
      loadDatabaseTb();
    });

    /**
     * 初始化平台页tb
     */
    let platData=[
      {id:1,backup_file:"20181125123000_jydb.dump"},
      {id:2,backup_file:"20181025123000_jydb.dump"}
    ]
    function loadDatabaseTb() {
      $("#database-tb").jqGrid(
        {
          datatype: "local",
          height: "90%",
          autowidth: true,
          shrinkToFit: true,//按比例初始化列宽
          // pager: '#platform-pager',
          // pginput: false, //不显示输入页码跳转框
          viewrecords: true,
          // rowNum: 5,//每页显示数据个数
          // rowList: [5, 10, 20, 50, 100],
          emptyrecords: "无数据",
          loadtext: "Loading...",
          rownumbers: true,
          loadComplete: function () {
            let table = this;
            setTimeout(function () {
              updatePagerIcons(table)
            }, 0)
          },
          colNames: ['id', '备份文件', '操作'],
          colModel: [
            {name: 'id', index: 'id', hidden: true},
            {name: 'backup_file', index: 'backup_file',},
            {name: 'operation', index: 'operation', sortable: false, formatter: displayButtons}]
        });

      // 清空和填充表格数据
      $("#database-tb").jqGrid('clearGridData');
      for (let i = 0; i <= platData.length; i++) {
        $("#database-tb").jqGrid('addRowData', i + 1, platData[i]);
      }
      // 自动生成序号
      function updatePagerIcons() {
        $("#database-tb").jqGrid('setLabel', 'rn', '序号');
      }
      /**\
       * 创造行末尾按钮并修改删除点击方法.
       * @param cellvalue
       * @param options
       * @param rowObject
       * @returns {string}
       */
      function displayButtons(cellvalue, options, rowObject) {
        return "<div class='hidden-sm hidden-xs btn-group'>" +
          "<button class='btn btn-minier btn-inverse wa-mlr5' " +
          "row-obj-str='" + JSON.stringify(rowObject) + "'" +
          "onclick=\"console.log($(this).parent().parent().parent().children('.jqgrid-rownum').text())\">" +
          "<i class='ace-icon fa fa-repeat bigger-80'>&nbsp;恢复</i>" +
          "</button>" +
          "<button class='btn btn-minier btn-danger'" +
          "row-id='" + rowObject.id + "'" +
          "onclick=\"$('#confirmDlg').modal('show')\n" +
          "$('#remove_plat_id').val($(this).parent().parent().parent().children('.jqgrid-rownum').text())\">" +
          "<i class='ace-icon fa fa-trash-o bigger-80'>&nbsp;删除</i>" +
          "</button>" +
          "</div>";
      }

      // 控制表格自适应窗口.
      $(window).on('resize.jqGrid', function () {
        $("#database-tb").jqGrid('setGridWidth', $(".page-content").width());
      });
      const parent_column = $("#database-tb").closest('[class*="col-"]');
      $(document).on('settings.ace.jqGrid', function (ev, event_name, collapsed) {
        if (event_name == 'sidebar_collapsed' || event_name == 'main_container_fixed') {
          setTimeout(function () {
            $("#database-tb").jqGrid('setGridWidth', parent_column.width());
          }, 0);
        }
      })
    }
    // loadDatabaseTb();
  </script>
</div>
