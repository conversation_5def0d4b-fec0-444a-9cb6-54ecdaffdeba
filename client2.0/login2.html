<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta charset="utf-8"/>
    <title>登录2</title>

    <meta name="description" content="User login page"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0"/>
    <!-- bootstrap & fontawesome -->
    <link rel="stylesheet" href="assets/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="assets/font-awesome/4.5.0/css/font-awesome.min.css"/>
    <!-- text fonts -->
    <link rel="stylesheet" href="assets/css/fonts.googleapis.com.css"/>
    <!-- ace styles -->
    <link rel="stylesheet" href="assets/css/ace.min.css"/>
    <link rel="stylesheet" href="assets/css/ace-skins.min.css"/>
    <link rel="stylesheet" href="assets/css/ace-rtl.min.css"/>
    <!-- jqgrid styles-->
    <link rel="stylesheet" href="assets/css/ui.jqgrid.min.css"/>
    <link rel="stylesheet" href="./assets/css/daterangepicker.min.css"/>
    <link rel="stylesheet" href="./lib/third/css/busy-load.css"/>
    <!--<link href="https://cdn.jsdelivr.net/npm/busy-load/dist/app.min.css" rel="stylesheet">-->
    <link rel="stylesheet" href="assets/css/spop.min.css"/>
    <link rel="stylesheet" href="style/light_blue_style.css"/>
    <script src="assets/js/jquery-2.1.4.min.js"></script>
    <script src="assets/js/ace-extra.min.js"></script>
</head>
<body class="login-layout blur-login l-b-body" id="bd_container">
<div id="app">
    vue div
</div>
<!-- basic scripts -->
<script src="assets/js/jquery-2.1.4.min.js"></script>
<script src="lib/third/validator.js"></script>
<script src="assets/js/spop.min.js"></script>
<script src="lib/third/busy-load.js"></script>
<script src="lib/third/vue3/vue.global.js"></script>
<script src="vue/demo1/todo-list.js"></script>
<script type="text/javascript">
    if ('ontouchstart' in document.documentElement) document.write("<script src='assets/js/jquery.mobile.custom.min.js'>" + "<" + "/script>");
</script>

<script>
    const { createApp } = Vue;

    // 挂载 Vue 3 应用程序到 DOM
    // getTodoListComponent() 返回一个 Promise，因为它需要异步加载模板
    window.onload = async () => { // 确保所有脚本和DOM都加载完毕
        const TodoListComponent = await window.getTodoListComponent();
        createApp(TodoListComponent).mount('#app');
    };

    // 确保老项目的 onMsg 可以调用到 Vue 组件的方法来更新 UI
    // 你需要修改 old_project_scripts.js 中的 onMsg 函数
    // 示例：
    // function onMsg(msgId, pb) {
    //     // ... 老项目的处理逻辑 ...
    //     if (msgId === 2001) { // 假设 2001 是 Vue Todo 模块发送/接收的消息ID
    //         if (typeof window.handleTodoBackendResponse === 'function') {
    //             window.handleTodoBackendResponse(msgId, pb);
    //         }
    //     }
    // }
</script>
</body>
</html>
