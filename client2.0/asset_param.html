<!--&lt;!&ndash; element-plus &ndash;&gt;-->

<!--<script src="vue/admin/controller/port_param.js"></script>-->

<div class="main-content">
    <div class="main-content-inner">
        <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs" style="height: calc(100% - 100px);">
            <div class="page-content">
                <div class="page-header">
                    <h1>参数配置
                        <small>
                            <i class="ace-icon fa fa-angle-double-right"></i>
                            资产参数
                        </small>
                    </h1>
                </div>
                <!-- /.page-header -->
                <div class="row">
                    <div id="app">
                        <el-row justify="end"><el-button type="primary" style="margin-right: 10px" @click="() => { onClickUserValidate('ADD') }">新增</el-button> </el-row>
                        <div ref="box" class="app-bottom">
                            <div class="app-bottom-left" style="overflow-y: auto;">
                                <el-tree
                                    ref="treeRef"
                                    :data="treeData"
                                    :props="defaultProps"
                                    default-expand-all
                                    :expand-on-click-node="false"
                                    node-key="value"
                                    highlight-current
                                    @node-click="handleNodeClick"
                                >
                                    <!-- 自定义节点内容 -->
                                    <template #default="{ node, data }">
                                        <span class="custom-tree-node">
                                        <span>{{ node.label }}</span>
                                        </span>
                                    </template>
                                </el-tree>
                            </div>
                            <div class="app-bottom-right" style="overflow-y: auto;">
                                <div style="display: flex;flex-wrap: nowrap;justify-content: flex-end">
                                    <el-button v-if="showEditButton" @click="() => { onClickUserValidate('EDIT') }">编辑</el-button>
                                    <el-button v-if="showDeleteButton" type="danger" @click="() => { onClickUserValidate('DELETE') }">删除</el-button>
                                </div>
                                <el-row :gutter="10" class="" style="margin-top: 10px;margin-bottom: 10px;border: 1px solid black;padding: 10px 0 10px 0;">
                                    <el-col :span="24" style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;" >
                                        <div class="field-label">资产基本信息:</div>

                                    </el-col>
                                    <el-col v-for="(field, index) in Object.keys(showHeader)" class="next_row" :span="8" >
                                        <span class="field-label">{{  code2ChineseMap['ASSET']['INFO'][field] }}:</span>{{ showHeader[field]  }}
                                    </el-col>
                                </el-row>
<!--                                <div v-if="showContent.length" class="arrow-down"></div>-->
                                <template v-if="showContent.length">
                                    <!-- 每项: 硬件 -->
                                    <el-row :gutter="10" v-for="(item, key) in showContent" :key="'showContent' + key" style="margin-top: 10px;margin-bottom: 10px;border: 1px solid black;padding: 10px 0 10px 0;">
                                        <!-- 第一层：硬件标识    ASSET_NODE MANAGEMENT -->
                                        <template v-for="(field, itemKey) of Object.keys(item)">
                                            <!-- item[field]对应这一项的值 -->
                                            <el-col v-if="!(Array.isArray(item[field]))" :key="'itemKey' + key" :span="8" class="next_row" >
                                                <span class="field-label">{{  code2ChineseMap['COMMON'][field] }}:</span>{{ item[field]  }}
                                            </el-col>
                                            <!-- 数组 -->
                                            <template v-else>
                                                <el-col :key="'itemKey' + key" :span="24" >
                                                    <el-row class="field-arr-label"><el-col :span="24" class="next_row">{{  code2ChineseMap['COMMON'][field] }}:</el-col></el-row>
                                                    <el-row :gutter="10" style="padding: 10px 0 10px 0;border-bottom: 1px solid #ebeef5;border-top: 1px solid #ebeef5;" >
                                                        <template v-if="item[field].length">
<!--                                                            <span class="field-label" style="font-weight: lighter;">{{ code2ChineseMap[field]['INFO'][field2] }}:</span>-->
                                                            <el-col :span="24" class="next_row">
                                                                <el-row v-for="(item2, index2) in item[field]" style="padding-bottom: 5px;padding-left: 5px;border-bottom: 1px solid #ebeef5;">
                                                                    <template v-for="(field2, itemKey2) in Object.keys(item2)"  :key="key + itemKey + index2">
                                                                        <el-col :span="8" v-if="!(Array.isArray(item2[field2]))" class="next_row">
                                                                            {{ code2ChineseMap[field]['INFO'][field2] }}:
                                                                            <el-tooltip :content="String(item2[field2])" effect="light">
                                                                                <!-- item[field][index2][field2] -->
                                                                                <span class="ellipsis-span">{{ item2[field2] }}</span>
                                                                            </el-tooltip>
                                                                        </el-col>
                                                                        <template v-else>
                                                                            <el-col :span="24" class="next_row">
                                                                                <el-row class="field-arr-label-next"><el-col :span="24">*{{ code2ChineseMap[field]['INFO'][field2] }}:</el-col></el-row>
                                                                                <el-row :gutter="10" style="padding: 0 10px 10px 10px;">
                                                                                    <template v-if="item[field][index2][field2].length">
                                                                                        <template v-for="(item3, index3) in item[field][index2][field2]">
                                                                                            <template v-for="(field3, itemKey3) in Object.keys(item3)" :key="itemKey3+itemKey2+11">
                                                                                                <el-col :span="8" v-if="!(Array.isArray(item3[field3]))" class="next_row">
                                                                                                    <span class="field-label">{{ code2ChineseMap[field][field2]['INFO'][field3] }}:</span>
                                                                                                    <el-tooltip :content="String(item3[field3])" effect="light">
                                                                                                        <!-- item[field][index2][field3] -->
                                                                                                        <span class="ellipsis-span">{{ item3[field3] }}</span>
                                                                                                    </el-tooltip>
                                                                                                </el-col>
                                                                                            </template>
                                                                                        </template>
                                                                                    </template>
                                                                                    <template v-else><el-col style="padding-left: 30px;">暂无数据</el-col></template>
                                                                                </el-row>
                                                                            </el-col>
                                                                        </template>
                                                                    </template>
                                                                </el-row>
                                                            </el-col>

                                                        </template>
                                                        <template v-else><el-col style="text-align: center;">暂无数据</el-col></template>
                                                    </el-row>
                                                </el-col>
                                            </template>
                                        </template>

                                    </el-row>
                            </div>
                            </template>
                            <template v-else>
                                <div  style="height: 100%;width: 100%;display: flex;align-items: center;justify-content: center;">
                                    <span>暂无数据</span>
                                </div>
                            </template>
                        </div>
                        <el-dialog v-model="updateModalVisible" :title="updateModalType === 'ADD' ? '新增' : '编辑'"  width="80%" @close="() => { updateModalSaveFn('CLOSE') }">
                            <div :style="{ height: `${dialogHeight}px` }">
                                <div class="info_box" ref="infoBoxRef">
                                    <div class="form_title">资产基本信息</div>
                                    <el-form ref="ASSET_REF" :model="formData.ASSET" :rules="formItemRules" label-width="110px">
                                        <el-row><el-col v-for="key in Object.keys(formItem.ASSET)" :span="8">
                                            <el-form-item :label="formItem.ASSET[key]['label']" :prop="key">
                                                <el-input v-if="formItem.ASSET[key]['type'] === 'INPUT'" :disabled="['GID', 'ATAG'].includes(key) && updateModalType === 'EDIT'" v-model="formData.ASSET[key]" class="formItemWidth"></el-input>
                                                <el-select v-else ref="selectRef" v-model="formData.ASSET[key]" :disabled="['GID', 'SOURCE', 'ATAG'].includes(key) && updateModalType === 'EDIT'" >
                                                    <el-option v-for="optionItem in formItem.ASSET[key]['options']" :label="optionItem.name" :value="optionItem.code"></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col></el-row>
                                    </el-form>
                                    <div class="form_title">资产节点</div>
                                    <el-row justify="end"><el-button type="primary" style="margin-right: 10px" @click="() => { operationTableDataByIndex('ASSET_NODE', 'ADD') }">新增</el-button></el-row>
                                    <el-form ref="ASSET_NODE_REF" :model="formData" :rules="formItemRules">
                                        <el-row style="padding: 5px">
                                            <el-table stripe  height="300" :data="formData.ASSET_NODE">
                                                <template v-for="key in Object.keys(formItem.ASSET_NODE)">
                                                    <el-table-column :key="key" :label="formItem.ASSET_NODE[key]['label']" :prop="key">
                                                        <template #default="scope">
                                                            <el-form-item style="width: 100%" :prop="'ASSET_NODE' + `.${scope.$index}` + `.${key}`" :rules="formItemRules[key]">
<!--                                                                {{ 'ASSET_NODE' + `[${scope.$index}]` + `.${key}` }}-->
<!--                                                                ***{{ key }}***-->
<!--                                                                {{ formItemRules[key] }}-->
                                                                <el-input v-if="key === 'IFID'" v-model="scope.row[key]" :disabled="scope.row['NDMTYPE'] == 2" class="formItemWidth"></el-input>
                                                                <el-input v-else-if="formItem.ASSET_NODE[key]['type'] === 'INPUT'" v-model="scope.row[key]" class="formItemWidth"></el-input>
                                                                <el-select v-else-if="formItem.ASSET_NODE[key]['type'] === 'SELECT'" ref="selectRef" v-model="scope.row[key]">
                                                                    <el-option v-for="optionItem in formItem.ASSET_NODE[key]['options']" :label="optionItem.name" :value="optionItem.code"></el-option>
                                                                </el-select>
                                                                <el-switch v-else-if="formItem.ASSET_NODE[key]['type'] === 'SWITCH'" v-model="scope.row[key]"></el-switch>
                                                            </el-form-item>
                                                        </template>
                                                    </el-table-column>
                                                </template>
                                                <el-table-column label="操作" width="200">
                                                    <template #default="scope">
                                                        <el-button type="primary" size="small" @click="() => { operationTableDataByIndex('ASSET_NODE', 'DELETE', scope.$index) }">删除</el-button>
                                                    </template>
                                                </el-table-column>
                                            </el-table>
                                        </el-row>
                                    </el-form>
                                    <div class="form_title">交互配置</div>
                                    <el-form ref="INTERCONF_REF" :model="formData.INTERCONF" :rules="formItemRules" label-width="110px">
                                        <el-row><el-col v-for="key in Object.keys(formItem.INTERCONF)" :span="8">
                                            <el-form-item :label="formItem.INTERCONF[key]['label']" :prop="key">
                                                <el-input v-if="formItem.INTERCONF[key]['type'] === 'INPUT'" v-model="formData.INTERCONF[key]" class="formItemWidth"></el-input>
                                                <el-input v-else-if="formItem.INTERCONF[key]['type'] === 'INPUT_NUMBER'" v-model="formData.INTERCONF[key]" type="number" class="formItemWidth"></el-input>
                                                <el-select v-else v-model="formData.INTERCONF[key]" ref="selectRef">
                                                    <el-option v-for="optionItem in formItem.INTERCONF[key]['options']" :label="optionItem.name" :value="optionItem.code"></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col></el-row>
                                    </el-form>
                                    <div class="form_title">管理</div>
                                    <el-form ref="MANAGEMENT_REF" :model="formData.MANAGEMENT" :rules="formItemRules" label-width="110px">
                                        <el-row><el-col v-for="key in Object.keys(formItem.MANAGEMENT)" :span="8">
                                            <el-form-item :label="formItem.MANAGEMENT[key]['label']" :prop="key">
                                                <el-input v-if="formItem.MANAGEMENT[key]['type'] === 'INPUT'" v-model="formData.MANAGEMENT[key]" class="formItemWidth"></el-input>
                                                <el-select v-else-if="formItem.MANAGEMENT[key]['type'] === 'SELECT'" v-model="formData.MANAGEMENT[key]" ref="selectRef">
                                                    <el-option v-for="optionItem in formItem.MANAGEMENT[key]['options']" :label="optionItem.name" :value="optionItem.code"></el-option>
                                                </el-select>
                                                <el-switch v-else-if="formItem.MANAGEMENT[key]['type'] === 'SWITCH'" v-model="formData.MANAGEMENT[key]"></el-switch>
                                            </el-form-item>
                                        </el-col></el-row>
                                    </el-form>
                                    <div class="form_title">应用</div>
                                    <el-row justify="end"><el-button type="primary" style="margin-right: 10px" @click="() => { operationTableDataByIndex('APPLICATION', 'ADD') }">新增</el-button></el-row>
                                    <el-form ref="APPLICATION_REF" :model="formData" :rules="formItemRules">
                                        <el-row style="padding: 5px">
                                            <el-table stripe  height="300" :data="formData.APPLICATION">
                                                <template v-for="key in Object.keys(formItem.APPLICATION)">
                                                    <el-table-column width="150" :label="formItem.APPLICATION[key]['label']" :prop="key">
                                                        <template #default="scope">
                                                            <el-form-item style="width: 100%" :prop="`APPLICATION.${scope.$index}.${key}`" :rules="formItemRules[key]">
                                                                <!-- 子类型 -->
                                                                <el-select v-if="key === 'STYPE'" :disabled="!scope.row['TYPE']" v-model="scope.row[key]"  ref="selectRef">
                                                                    <el-option v-for="optionItem in getSTYPE(scope.row['TYPE'])" :label="optionItem.name" :value="optionItem.code"></el-option>
                                                                </el-select>
                                                                <el-input v-else-if="formItem.APPLICATION[key]['type'] === 'INPUT'" v-model="scope.row[key]" class="formItemWidth"></el-input>
                                                                <el-select v-else-if="formItem.APPLICATION[key]['type'] === 'SELECT'" v-model="scope.row[key]" @change="($event) => { changeSelect('APPLICATION', scope.$index, key, $event ) }" ref="selectRef">
                                                                    <el-option v-for="optionItem in formItem.APPLICATION[key]['options']" :label="optionItem.name" :value="optionItem.code"></el-option>
                                                                </el-select>
                                                                <el-switch v-else-if="formItem.APPLICATION[key]['type'] === 'SWITCH'" v-model="scope.row[key]"></el-switch>
                                                            </el-form-item>
                                                        </template>
                                                    </el-table-column>
                                                </template>
                                                <el-table-column label="操作" width="200">
                                                    <template #default="scope">
                                                        <el-button type="primary" size="small" @click="() => { operationTableDataByIndex('APPLICATION', 'DELETE', scope.$index) }">删除</el-button>
                                                    </template>
                                                </el-table-column>
                                            </el-table>
                                        </el-row>
                                    </el-form>
                                </div>
                            </div>
                            <div slot="footer" class="dialog-footer">
                                <el-button type="primary" @click="() => { updateModalSaveFn('SAVE') }">提交</el-button>
                                <el-button @click="() => { updateModalSaveFn('CLOSE') }">关闭</el-button>
                            </div>
                        </el-dialog>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /.main-content -->
    <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
        <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
    </a>
    <!-- 验证用户身份-->
    <div class="modal" style="z-index: 4000;" id="validateUserDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         aria-hidden="true">
        <form class="form-horizontal" id="validateUserForm" method="post" action="" data-toggle="validator" role="form">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header" style="color: white;background: deepskyblue">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                        <h4 class="modal-title">验证用户身份</h4>
                    </div>
                    <div class="modal-body" style="padding: 10px 50px;">
                        <p>请输入登录密码</p>
                        <input type="hidden" id="opraType"/>
                        <input type="password" id="password" class="form-control" placeholder="密码" required/>
                        <div class="help-block with-errors"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary" id="validateuser_btn">确定
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <!-- /.modal -->
    <!-- 导入导出选择对话框 -->
    <div class="modal fade" id="confirmPortDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         aria-hidden="true">
        <form class="form-horizontal" id="confirmPortForm" method="post" action="" data-toggle="validator" role="form">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                            <span class="sr-only">Close</span>
                        </button>
                        <h4 class="modal-title" wa-name="import_tt">请选择</h4>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label class="col-sm-2 control-label text-right" for="select_file">请选择:</label>
                            <div class="col-sm-9">
                                <label class="ace-file-input">
                                    <input type="file" id="select_file" required/>
                                </label>
                            </div>
                        </div>
                        <div class="form-group hidden">
                            <label class="col-sm-2 control-label text-right" for="select_folder">请选择:</label>
                            <div class="col-sm-9">
                                <label class="ace-file-input">
                                    <input type="file" id="select_folder" webkitdirectory directory required/>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" id="port_btn">确定</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <!-- /.modal -->

</div>
<style>
    .dialog_content {
        padding: 20px !important;
    }
    .dialog-footer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 20px !important;
        background-color: #eff3f8;
    }
    /* 去掉el-input内置的边框 */
    .el-input__inner {
        border: none !important;
    }
    /*.formItemWidth {*/
    /*    width: 250px !important;*/
    /*}*/
    .el-table__header th {
        border-bottom: 3px solid #e1e1e1 !important;
    }
    /* 表格最底部的线（蓝色，加粗） */
    .el-table::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        border-bottom: 2px solid #e1e1e1 !important;
    }
    .cell-input {
        height: 26px;
        margin-left: -10px;
    }

    .cell-select .el-select__wrapper {
        height: 26px;
        min-height: 26px;
        margin-left: -11px;
    }

    .el-table .el-table__row {
        height: 50px;
    }
</style>
<style>
    .form_title {
        display: flex;
        align-items: center;
        font-size: 20px;
        font-weight: bold;
    }
    .form_title::before {
        content: "";
        display: inline-block;
        width: 6px;         /* 色块宽度 */
        height: 20px;       /* 色块高度 */
        background-color: #2699d4; /* 色块颜色 */
        margin-right: 8px;  /* 色块与文字间距 */
        border-radius: 2px; /* 圆角，可选 */
    }
    .info_box {
        width: 100%;
        height: 100%;
        overflow-y: auto;
    }
    /* ------------------------------------------------------------------------------------------------- */
    /* 让 #app 占满剩余空间 */
    .main-content {
        height: 100%;

    }
    .main-content-inner, .page-content, .row {
        height: 100%;
    }

    html, body {
        height: 100%;
        margin: 0;
        padding: 0;
    }

    #app {
        display: flex;
        flex-direction: column;
        height: 100%;
        background-color: #f9f9f9;
    }


    .app-bottom {
        flex: 1;
        display: flex;
        overflow: hidden;
    }

    .app-top {
        height: 100px;
        border: 1px solid #e1e1e1;
        border-radius: 4px;
        margin: 10px;
        padding: 10px;
        color: #333;
        font-weight: 500;
        background: transparent;
    }

    .app-bottom-left {
        width: 200px;
        height: calc(100% - 100px);
        border: 1px solid #e1e1e1;
        border-radius: 4px;
        margin: 10px;
        padding: 10px;
        background: transparent;
        color: #444;
    }

    .app-bottom-right {
        flex: 1;
        height: calc(100% - 100px);
        border: 1px solid #e1e1e1;
        border-radius: 4px;
        margin: 10px;
        padding: 16px;
        background: transparent;
        color: #444;
    }



</style>

<style>
    .next_row {
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    }
    .arrow-down {
        display: block;
        position: relative;
        width: 60px;
        height: 55px;
        margin: 20px auto;
        filter: drop-shadow(0 0 6px rgba(52, 152, 219, 0.3)); /* 蓝色外发光 */
    }
    /* 箭杆 - 蓝色渐变 */
    .arrow-down::before {
        content: "";
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 6px;
        height: calc(100% - 25px);
        background: linear-gradient(
                to bottom,
                rgba(52, 152, 219, 1) 0%,       /* 顶部实心蓝 */
                rgba(52, 152, 219, 0.6) 80%,    /* 中部半透明 */
                rgba(52, 152, 219, 0.2) 100%    /* 底部几乎透明 */
        );
        border-radius: 3px; /* 圆角使边缘更柔和 */
    }
    /* 箭头 - 蓝色渐变三角 */
    .arrow-down::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 25px;
        height: 25px;
        background: linear-gradient(
                to bottom,
                rgba(52, 152, 219, 0.8) 0%,
                rgba(52, 152, 219, 0.3) 100%
        );
        clip-path: polygon(0% 0%, 100% 0%, 50% 100%);
    }
    .ellipsis-span {
        display: inline-block;
        max-width: 120px; /* 可根据 el-col 宽度灵活调整 */
        overflow: hidden;
        padding-bottom: 3px;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;
    }
    .field-arr-label {
        color: #9999a5;
        margin-top: 10px;
        margin-bottom: 10px;
        font-weight: bold;
    }
    .field-arr-label-next {
        color: black;
        margin-top: 10px;
        margin-bottom: 10px;
        font-weight: bold;
    }
    .field-label {
        font-weight: bold;
    }
    /* 隐藏跳转框前后的文字 */
    .custom-pagination .el-pagination__jump {
        margin-left: 0;
        font-size: 0;
    }
    .custom-pagination .el-pagination__jump:before,
    .custom-pagination .el-pagination__jump:after {
        content: none;
    }
    .el-table__header th {
        border-bottom: 3px solid #e1e1e1 !important;
    }
    /* 表格最底部的线（蓝色，加粗） */
    .el-table::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        border-bottom: 2px solid #e1e1e1 !important;
    }
    .cell-input {
        height: 26px;
        margin-left: -10px;
    }

    .cell-select .el-select__wrapper {
        height: 26px;
        min-height: 26px;
        margin-left: -11px;
    }

    .el-table .el-table__row {
        height: 50px;
    }
    .btn-prev {
        border-radius: 50% !important;
    }
    .btn-next {
        border-radius: 50% !important;
    }

</style>
<link rel="stylesheet" href="lib/elementPlus/index.css"/>
<script src="lib/third/vue3/vue.global.js"></script>
<script src="lib/elementPlus/index.full.min.js"></script>
<script src="lib/elementPlus/zh-cn.js"></script>

<script>
    console.log(require.cache)
    delete require.cache[require.resolve('./vue/paramConf/AssetConfig.js')];
    require('./vue/paramConf/AssetConfig.js');
    // $('#handlerForm').validator();
</script>
