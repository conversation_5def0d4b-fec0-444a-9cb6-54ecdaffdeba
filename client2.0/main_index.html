<div class="row" style="height: 100%;background-color:#eef7fe!important">
  <div class="col-sm-10 col-sm-offset-1" style="height: 100%">
    <div class="login-container l-b-logincont" style="height: 100%">
      <div id="navbar" class="navbar navbar-default ace-save-state l-b-loginhd" style="margin-bottom: 10px;padding-bottom: 5px;">
        <div class="navbar-container ace-save-state" id="navbar-container">
          <div class="navbar-header pull-left col-sm-8">
            <img src="assets/images/logo.png">
            <p class="white l-b-loghd-span" id="id-text2">电力监控系统网络安全监测装置(基础型)</p>
          </div>
          <div class="navbar-buttons navbar-header pull-right col-sm-4" role="navigation" style="margin-top: 33px">
            <div style="text-align: right;color: #a0a0a0;font-size: 14px;margin-top:20px">
              <input type="hidden" name="tag" value="invalid">
              <span>您好,<b id="userName">管理员xxx</b></span>
              <a href="javascript:;" id="settingBtn" title="修改信息" style="color: #222;text-decoration: underline">修改信息</a>
              <a href="javascript:;" id="logoutBtn" class="ace-icon glyphicon glyphicon-share" title="退出" style="color: #222;"></a>
              <a href="javascript:;" id="resetBtn" class="ace-icon glyphicon glyphicon-repeat hidden" title="重启装置" style="color: #222;"></a>
              <p class="wa-mb0">装置当前时间: <span id="deciveTime"></span></p>
            </div>
          </div>
        </div><!-- /.navbar-container -->
      </div>
      <div class="main-container ace-save-state" id="main-container">
        <script type="text/javascript">
          try {
            ace.settings.loadState('main-container')
          } catch (e) {
          }
        </script>
        <div id="sidebar" class="sidebar responsive ace-save-state l-b-sidebar"></div>
        <nav class="navbar navbar-default navbar-static-top l-b-navbar" role="navigation"></nav>
        <div class="parent_content l-b-parent_content"></div>
        <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
          <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
        </a>
      </div><!-- /.main-container -->
    </div>
  </div>
</div>
<!-- 验证用户身份-->
<div class="modal" id="psd_validateUserDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
     aria-hidden="true" data-backdrop="static">
  <form class="form-horizontal" id="psd_validateUserForm" method="post" action="" data-toggle="validator" role="form">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header" style="color: white;background: deepskyblue">
          <!--<button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>-->
          <h4 class="modal-title">验证用户身份</h4>
        </div>
        <div class="modal-body" style="padding: 10px 50px;">
          <p>请输入登录密码</p>
          <input type="password" id="psd_password" class="form-control" placeholder="密码" required/>
          <div class="help-block with-errors"></div>
        </div>
        <div class="modal-footer">
          <!--<button type="button" class="btn btn-default" data-dismiss="modal">取消</button>-->
          <button type="submit" class="btn btn-primary" id="psd_validateuser_btn">确定
          </button>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
  </form>
</div><!-- /.modal -->
<!-- 确认退出对话框 -->
<div class="modal fade" id="confirmLogoutDlg">
  <div class="modal-dialog" role="document" style="width: 39%;margin: 10% auto;">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
          <span class="sr-only">Close</span>
        </button>
        <h4 class="modal-title" id wa-name="">确认退出</h4>
      </div>
      <div class="modal-body"><p>确定要退出控制面板吗？</p></div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" data-dismiss="modal" id="logout_btn">确定
        </button>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<!-- 确认重启装置对话框 -->
<div class="modal fade" id="confirmResetDlg">
  <div class="modal-dialog" role="document" style="width: 39%;margin: 10% auto;">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
          <span class="sr-only">Close</span>
        </button>
        <h4 class="modal-title" id wa-name="">确认重启装置</h4>
      </div>
      <div class="modal-body"><p>确定要重启装置吗？</p></div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" data-dismiss="modal" id="reset_btn">确定
        </button>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<!-- basic scripts -->
<script src="assets/js/jquery-2.1.4.min.js"></script>
<script type="text/javascript">
  if ('ontouchstart' in document.documentElement) document.write("<script src='assets/js/jquery.mobile.custom.min.js'>" + "<" + "/script>");
</script>
<script src="assets/js/bootstrap.min.js"></script>
<script src="assets/js/bootstrap-multiselect.min.js"></script>
<script src="assets/js/moment.min.js"></script>
<script src="assets/js/daterangepicker.min.js"></script>
<script src="lib/third/validator.js"></script>
<script src="lib/third/echarts/echarts.min.js"></script>
<script src="lib/third/echarts/shine.js"></script>
<script src="lib/third/busy-load.js"></script>
<!--<script src="https://cdn.jsdelivr.net/npm/busy-load/dist/app.min.js"></script>-->

<script src="assets/js/jquery-ui.custom.min.js"></script>
<script src="assets/js/jquery.ui.touch-punch.min.js"></script>
<script src="assets/js/jquery.easypiechart.min.js"></script>
<script src="assets/js/jquery.sparkline.index.min.js"></script>
<script src="assets/js/jquery.flot.min.js"></script>
<script src="assets/js/jquery.flot.pie.min.js"></script>
<script src="assets/js/jquery.flot.resize.min.js"></script>

<script src="assets/js/ace-extra.min.js"></script>
<script src="assets/js/jquery.nestable.min.js"></script>
<!-- jqGrid scripts -->
<script src="assets/js/jquery.jqGrid.min.js"></script>
<script src="assets/js/grid.locale-en.js"></script>
<!-- ace scripts -->
<script src="assets/js/ace-elements.min.js"></script>
<script src="assets/js/ace.min.js"></script>
<script>
  delete require.cache[require.resolve('./script/view/login.js')];
  require('./script/view/login.js');
</script>
