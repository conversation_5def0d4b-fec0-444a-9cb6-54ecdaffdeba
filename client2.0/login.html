<!DOCTYPE html>
<html lang="en">
<head>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
  <meta charset="utf-8"/>
  <title>登录</title>

  <meta name="description" content="User login page"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0"/>
  <!-- bootstrap & fontawesome -->
  <link rel="stylesheet" href="assets/css/bootstrap.min.css"/>
  <link rel="stylesheet" href="assets/font-awesome/4.5.0/css/font-awesome.min.css"/>
  <!-- text fonts -->
  <link rel="stylesheet" href="assets/css/fonts.googleapis.com.css"/>
  <!-- ace styles -->
  <link rel="stylesheet" href="assets/css/ace.min.css"/>
  <link rel="stylesheet" href="assets/css/ace-skins.min.css"/>
  <link rel="stylesheet" href="assets/css/ace-rtl.min.css"/>
  <!-- jqgrid styles-->
  <link rel="stylesheet" href="assets/css/ui.jqgrid.min.css"/>
  <link rel="stylesheet" href="./assets/css/daterangepicker.min.css"/>
  <link rel="stylesheet" href="./lib/third/css/busy-load.css"/>
  <!--<link href="https://cdn.jsdelivr.net/npm/busy-load/dist/app.min.css" rel="stylesheet">-->
  <link rel="stylesheet" href="assets/css/spop.min.css"/>
  <link rel="stylesheet" href="style/light_blue_style.css"/>
  <script src="assets/js/jquery-2.1.4.min.js"></script>
  <script src="assets/js/ace-extra.min.js"></script>
</head>
<body class="login-layout blur-login l-b-body" id="bd_container">
<div class="main-container">
  <div class="main-content">
    <div class="row">
      <div class="col-sm-10 col-sm-offset-1">
        <div class="login-container l-b-logincont">
          <div class="l-b-loginhd">
            <img src="assets/images/logo.png">
            <p class="white l-b-loghd-span" id="id-text2">电力监控系统网络安全监测装置(基础型)</p>
            <p id="date-time" style="text-align: right;">2019年05月23日 星期四</p>
          </div>
          <!--<div class="space-6"></div>-->
          <div class="position-relative l-b-loginbox">
            <div id="login-box" class="login-box visible widget-box no-border l-b-log-box">
              <div class="widget-body">
                <div class="widget-main l-b-wtmain">
                  <h4 class="header blue lighter bigger">
                    <!--<i class="ace-icon fa fa-coffee green"></i>-->
                    请输入账户信息
                  </h4>
                  <div class="space-6"></div>
                  <form class="form-horizontal" id="loginForm" role="form">
                    <fieldset>
                      <label class="block clearfix">
                        <span class="block input-icon input-icon-right">
                          <input type="text" id="username" class="form-control" placeholder="用户名" wa-name="loginput"/>
                          <i class="ace-icon fa fa-user"></i>
                        </span>
                      </label>
                      <label class="block clearfix">
                        <span class="block input-icon input-icon-right">
                          <input type="password" id="password" class="form-control" placeholder="密码" wa-name="loginput"
                                 onpaste="return false" oncontextmenu="return false" oncopy="return false"
                                 oncut="return false"/>
                          <i class="ace-icon fa fa-lock"></i>
                        </span>
                      </label>
                      <label class="block clearfix">
                        <span class="block input-icon input-icon-right">
                          <input type="text" id="ip" class="form-control" wa-name="loginput"
                                 placeholder="请输入ip,格式为0.0.0.0" maxlength="15"/>
                        </span>
                      </label>
                      <label class="block clearfix">
                        <span class="block input-icon input-icon-right">
                          <input type="text" id="port" class="form-control" wa-name="loginput"
                                 placeholder="请输入端口号,默认8801" maxlength="15"/>
                        </span>
                      </label>
                      <div class="space"></div>
                      <div class="clearfix">
                        <button type="button" id="loginBtn" class="width-35 pull-right btn btn-sm btn-primary">
                          <i class="ace-icon fa fa-key"></i>
                          <span class="bigger-110">登录</span>
                        </button>
                      </div>
                      <div class="space-4"></div>
                    </fieldset>
                  </form>
                </div><!-- /.widget-main -->
              </div><!-- /.widget-body -->
            </div><!-- /.login-box -->
          </div><!-- /.position-relative -->
        </div>
      </div><!-- /.col -->
    </div><!-- /.row -->
  </div><!-- /.main-content -->
  <div class="footer">
    <div class="footer-inner">
      <div class="footer-content l-b-footer">
        <span class="bigger-120">
          <!--<span class="blue bolder">山东山大电力技术股份有限公司</span>-->
          <!--网络安全监测装置 &copy;-->
          <span id="new_year">2018</span>
          <span> © All Rights Reserved.</span>
          <!--<span id="version">MONI-SAFE-V1.0.0 20190322 12:22:22 17D3</span>-->
        </span>
      </div>
    </div>
  </div>
</div><!-- /.main-container -->
<!-- basic scripts -->
<script src="assets/js/jquery-2.1.4.min.js"></script>
<script src="lib/third/validator.js"></script>
<script src="assets/js/spop.min.js"></script>
<script src="lib/third/busy-load.js"></script>
<script src="lib/third/vue3/vue.global.js"></script>
<script type="text/javascript">
  if ('ontouchstart' in document.documentElement) document.write("<script src='assets/js/jquery.mobile.custom.min.js'>" + "<" + "/script>");
</script>
<script>
  // You can also require other files to run in this process
  require('./script/view/login.js');
  $('#loginForm').validator();
</script>


<!--<script>-->
<!--  const { createApp } = Vue;-->
<!--  // 挂载 Vue 3 应用程序到 DOM-->
<!--  // getTodoListComponent() 返回一个 Promise，因为它需要异步加载模板-->
<!--  // window.onload = async () => { // 确保所有脚本和DOM都加载完毕-->
<!--    const TodoListComponent = await window.getTodoListComponent();-->
<!--    createApp(TodoListComponent).mount('#app');-->
<!--    console.log('createApp window.onload')-->
<!--  // };-->

<!--  console.log('createApp')-->
<!--</script>-->

<script src="vue/demo1/todo-list.js"></script>

</body>
</html>
