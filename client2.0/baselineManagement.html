<!--&lt;!&ndash; element-plus &ndash;&gt;-->

<!--<script src="vue/admin/controller/port_param.js"></script>-->

<div class="main-content">
    <div class="main-content-inner">
        <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs" style="height: calc(100% - 100px);">
            <div class="page-content">
                <div class="page-header">
                    <h1>基线管理
                        <small>
                            <i class="ace-icon fa fa-angle-double-right"></i>
                            基线管理
                        </small>
                    </h1>
                </div>
                <!-- /.page-header -->
                <div class="row">
                    <div id="app">
                        <div ref="box" style="height: 100%">
                            <el-row>
                                <el-table stripe :data="assetList" :height="queryModelHeight" style="width: 100%">
                                    <el-table-column type="index" label="序号" width="80"></el-table-column>
                                    <el-table-column label="GID" prop="GID"></el-table-column>
                                    <el-table-column label="设备名称" prop="DNAME"></el-table-column>
                                    <el-table-column label="操作" width="200">
                                        <template #default="scope">
                                            <el-button type="primary" size="small" @click="() => { getQueryInfoFront(scope.row) }">管理</el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>

                            </el-row>
                        </div>
                        <!-- 基线管理列表 -->
                        <el-dialog v-model="queryModelVisible" title="管理"  width="80%" @close="closeQueryModelFn">
                            <div :style="{ height: `${queryModelHeight}px` }">
                                <div class="info_box" style="padding: 10px;display: flex;flex-direction: column">
                                    <el-row type="flex" align="middle" justify="space-between" style="width: 100%;">
                                        <el-col :span="9">
                                            <div style="display: flex; align-items: center;">
                                                <span style="margin-right: 10px; font-weight: bold;">基线类型</span>
                                                <el-select v-model="baseLineTypeValue" placeholder="请选择基线类型" style="width: 200px;" @change="changeBaselineTypeFn">
                                                    <el-option v-for="item in baseLineOptions" :key="item.code" :label="item.name" :value="item.code" />
                                                </el-select>
                                            </div>
                                        </el-col>
                                        <el-col :span="9">
                                            <div style="display: flex; align-items: center;">
                                                <span style="margin-right: 10px; font-weight: bold;">基线生成方式</span>
                                                <el-select v-model="genmethodValue" placeholder="请选择基线类型" style="width: 200px;" @change="changeBaselineTypeFn">
                                                    <el-option label="统计产生" value="1"></el-option>
                                                    <el-option label="手动设置" value="2"></el-option>
                                                </el-select>
                                            </div>
                                        </el-col>
                                        <el-col :span="6" style="text-align: right;">
                                            <el-button type="primary" @click="() => { onClickUserValidate('ADD') }">新增</el-button>
                                            <el-button type="primary" @click="() => { onClickUserValidate('STUDY_TIME') }">统计周期</el-button>
                                        </el-col>
                                    </el-row>
                                    <div v-if="baseLineOptionsItem.length">
                                        <el-table :height="queryModelInfoHeight" :data="baselineTableData" style="width: 100%">
                                            <!-- 动态列 -->
                                            <el-table-column v-for="col in baseLineOptionsItem" :key="col.prop" :prop="col.prop" :label="col.label">
                                                <!-- 插槽控制渲染 -->
                                                <template #default="scope">
                                                    <!-- 如果是SELECT类型，需要翻译 -->
                                                    <span v-if="col.type === 'SELECT'">{{ scope.row[col.prop] ? translateOption(scope.row[col.prop], col.options) : '--' }}</span>
                                                    <!-- 普通文本展示 -->
                                                    <span v-else>{{ scope.row[col.prop] }}</span>
                                                </template>
                                            </el-table-column>
                                            <el-table-column label="操作" width="200">
                                                <template #default="scope">
                                                    <el-button type="primary" size="small" @click="() => { onClickUserValidate('EDIT', scope.row, scope.$index) }">修改</el-button>
                                                    <el-button type="danger" size="small" @click="() => { onClickUserValidate('DELETE', scope.row) }">删除</el-button>
                                                </template>
                                            </el-table-column>
                                        </el-table>
                                    </div>
                                    <div style="display: flex;align-items: center;justify-content: center">
                                        <el-pagination
                                                :current-page="pageNum"
                                                background
                                                :page-size="1000"
                                                layout="prev, jumper, next"
                                                :total="dataCnt"
                                                @current-change="queryCurrentChange"
                                        ></el-pagination>
                                    </div>
                                </div>
                            </div>
                        </el-dialog>
                        <!-- 新增/编辑/删除 -->
                        <el-dialog v-if="updateModelVisible" v-model="updateModelVisible" :title="updateModelType === 'ADD' ? '新增' : '编辑'"  width="50%" @close="closeUpdateModel">
                            <div :style="{ height: `${updateModelHeight}px` }">
                                <div class="info_box" style="padding: 15px;overflow-y: auto">
                                    <el-form ref="formDataRef" :model="formData" :rules="baseLineRules" label-width="150px">
                                        <el-row v-for="item in baseLineOptionsItem">
                                            <el-col :span="24">
                                                <el-form-item :label="item.label" :prop="item.prop">
                                                    <el-input v-if="item.prop === 'GID'" :disabled="true" v-model="formData[item.prop]" class="formItemWidth"></el-input>
                                                    <el-input v-else-if="item.type === 'INPUT'" v-model="formData[item.prop]" class="formItemWidth"></el-input>
                                                    <el-select v-else-if="item.prop === 'GENMETHOD'" v-model="formData[item.prop]" :disabled="true">
                                                        <el-option v-for="optionItem in [item['options'][1]]" :label="optionItem.name" :value="optionItem.code"></el-option>
                                                    </el-select>
                                                    <el-select v-else v-model="formData[item.prop]">
                                                        <el-option v-for="optionItem in item['options']" :label="optionItem.name" :value="optionItem.code"></el-option>
                                                    </el-select>
                                                </el-form-item>
                                            </el-col>
                                        </el-row>
                                    </el-form>
                                </div>
                            </div>
                            <span slot="footer" class="dialog-footer">
                                <el-button @click="closeUpdateModel">取消</el-button>
                                <el-button type="primary" @click="updateModelSaveFn">保存</el-button>
                            </span>
                        </el-dialog>
                        <!-- 统计周期 -->
                        <el-dialog v-if="studyTimeModelVisible" v-model="studyTimeModelVisible" title="统计周期"  width="30%" @close="closeStudyTimeModel">
                            <div :style="{ height: `${studyTimeModelHeight}px` }">
                                <div class="info_box" style="padding: 15px;display: flex;align-items: center;justify-content: center;padding: 20px">
                                    <el-form ref="studyTimeFormRef" :model="studyTimeForm" :rules="studyTimeRules" label-width="110px">
                                        <el-row>
                                            <el-col :span="24">
                                                <el-form-item label="统计周期(秒)" prop="studyTime">
                                                    <el-input v-model="studyTimeForm['studyTime']" class="formItemWidth"></el-input>
                                                </el-form-item>
                                            </el-col>
                                        </el-row>
                                    </el-form>
                                </div>
                            </div>
                            <span slot="footer" class="dialog-footer">
                                <el-button @click="closeStudyTimeModel">取消</el-button>
                                <el-button type="primary" @click="studyTimeModelSaveFn">保存</el-button>
                            </span>
                        </el-dialog>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /.main-content -->
    <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
        <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
    </a>
    <!-- 验证用户身份-->
    <div class="modal" style="z-index: 4000;" id="validateUserDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         aria-hidden="true">
        <form class="form-horizontal" id="validateUserForm" method="post" action="" data-toggle="validator" role="form">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header" style="color: white;background: deepskyblue">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                        <h4 class="modal-title">验证用户身份</h4>
                    </div>
                    <div class="modal-body" style="padding: 10px 50px;">
                        <p>请输入登录密码</p>
                        <input type="hidden" id="opraType"/>
                        <input type="password" id="password" class="form-control" placeholder="密码" required/>
                        <div class="help-block with-errors"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary" id="validateuser_btn">确定
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <!-- /.modal -->
    <!-- 导入导出选择对话框 -->
    <div class="modal fade" id="confirmPortDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         aria-hidden="true">
        <form class="form-horizontal" id="confirmPortForm" method="post" action="" data-toggle="validator" role="form">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                            <span class="sr-only">Close</span>
                        </button>
                        <h4 class="modal-title" wa-name="import_tt">请选择</h4>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label class="col-sm-2 control-label text-right" for="select_file">请选择:</label>
                            <div class="col-sm-9">
                                <label class="ace-file-input">
                                    <input type="file" id="select_file" required/>
                                </label>
                            </div>
                        </div>
                        <div class="form-group hidden">
                            <label class="col-sm-2 control-label text-right" for="select_folder">请选择:</label>
                            <div class="col-sm-9">
                                <label class="ace-file-input">
                                    <input type="file" id="select_folder" webkitdirectory directory required/>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" id="port_btn">确定</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <!-- /.modal -->

</div>
<style>

    .info_box {
        width: 100%;
        height: 100%;
    }
    /* 去掉el-input内置的边框 */
    .el-input__inner {
        border: none !important;
    }
    .el-dialog {
        padding: 0 !important;
    }
    .el-dialog__header {
        background-color: #00bfff !important;
        color: white !important;
        padding-top: 10px !important;
        padding-left: 10px !important;
    }
    .el-dialog__body {
    }
    .el-dialog__title {
        color: white !important;
    }
    .dialog_content {
        height: 100px;
        padding: 20px !important;
    }
    .dialog-footer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 20px !important;
        background-color: #eff3f8;
    }
    /* ------------------------------------------------------------------------------------------------- */
    /* 让 #app 占满剩余空间 */
    .main-content {
        height: 100%;

    }
    .main-content-inner, .page-content, .row {
        height: 100%;
    }

    html, body {
        height: 100%;
        margin: 0;
        padding: 0;
    }

    #app {
        display: flex;
        flex-direction: column;
        height: 100%;
        background-color: #f9f9f9;
    }


    .app-bottom {
        flex: 1;
        display: flex;
        overflow: hidden;
    }

    .app-top {
        height: 100px;
        border: 1px solid #e1e1e1;
        border-radius: 4px;
        margin: 10px;
        padding: 10px;
        color: #333;
        font-weight: 500;
        background: transparent;
    }

    .app-bottom-left {
        width: 200px;
        height: calc(100% - 100px);
        border: 1px solid #e1e1e1;
        border-radius: 4px;
        margin: 10px;
        padding: 10px;
        background: transparent;
        color: #444;
    }

    .app-bottom-right {
        flex: 1;
        height: calc(100% - 100px);
        border: 1px solid #e1e1e1;
        border-radius: 4px;
        margin: 10px;
        padding: 16px;
        background: transparent;
        color: #444;
    }



</style>

<style>
    .next_row {
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    }
    .arrow-down {
        display: block;
        position: relative;
        width: 60px;
        height: 55px;
        margin: 20px auto;
        filter: drop-shadow(0 0 6px rgba(52, 152, 219, 0.3)); /* 蓝色外发光 */
    }

    /* 箭杆 - 蓝色渐变 */
    .arrow-down::before {
        content: "";
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 6px;
        height: calc(100% - 25px);
        background: linear-gradient(
                to bottom,
                rgba(52, 152, 219, 1) 0%,       /* 顶部实心蓝 */
                rgba(52, 152, 219, 0.6) 80%,    /* 中部半透明 */
                rgba(52, 152, 219, 0.2) 100%    /* 底部几乎透明 */
        );
        border-radius: 3px; /* 圆角使边缘更柔和 */
    }

    /* 箭头 - 蓝色渐变三角 */
    .arrow-down::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 25px;
        height: 25px;
        background: linear-gradient(
                to bottom,
                rgba(52, 152, 219, 0.8) 0%,
                rgba(52, 152, 219, 0.3) 100%
        );
        clip-path: polygon(0% 0%, 100% 0%, 50% 100%);
    }
    .ellipsis-span {
        display: inline-block;
        max-width: 120px; /* 可根据 el-col 宽度灵活调整 */
        overflow: hidden;
        padding-bottom: 3px;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;
    }
    .field-arr-label {
        color: #333;
        background: #fafafa; /* 外层深色渐变 */
        margin-left: 2px;
        margin-top: 10px;
        margin-bottom: 10px;
        font-size: 1.2em;
        font-weight: bold;
        padding-left: 5px;
        border-left: 4px solid #409eff; /* 蓝色左侧边框 */
        border-radius: 4px;
    }
    .field-arr-label-next {
        color: black;
        margin-top: 10px;
        margin-left: 10px;
        font-size: 1.1em;
        padding: 8px 12px;
        margin-bottom: 10px;
        font-weight: bold;
        border-left: 4px solid #36A1D1; /* 蓝色左侧边框 */
        border-radius: 4px;
    }
    .field-arr-label-next-next {
        color: #2b2d30; /* 更醒目的蓝色 */
        margin: 12px 0;
        font-weight: 700;
        font-size: 1em;
        padding: 8px 15px;
        /*background-color: #a9e8e6; !* 浅蓝色背景 *!*/
        border-left: 4px solid #2E86AB; /* 左侧装饰条 */
        border-radius: 4px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05); /* 轻微阴影 */
    }
    .field-arr-label-next-next-next {
        color: #2b2d30; /* 更醒目的蓝色 */
        margin: 12px 0;
        font-weight: 700;
        font-size: 0.9em;
        padding: 8px 18px;
        /*background-color: #a9e8e6; !* 浅蓝色背景 *!*/
        border-left: 4px solid #256D89; /* 左侧装饰条 */
        border-radius: 4px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05); /* 轻微阴影 */
    }
    .active {
        transition: all 0.3s ease; /* 悬停动画 */
    }
    .active:hover {
        background-color: #e1f0ff;
        transform: translateY(-1px);
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
    }
    .field-label {
        font-size: 1.1em;
        font-weight: bold;
    }
    .field-label1 {
        font-size: 1em;
        font-weight: bold;
    }
    .field-label2 {
        font-size: 0.9em;
        font-weight: bold;
    }
    .field-label3 {
        font-size: 0.8em;
        font-weight: bold;
    }
    .el-table__header th {
        border-bottom: 3px solid #e1e1e1 !important;
    }
    /* 表格最底部的线（蓝色，加粗） */
    .el-table::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        border-bottom: 2px solid #e1e1e1 !important;
    }
    .cell-input {
        height: 26px;
        margin-left: -10px;
    }

    .cell-select .el-select__wrapper {
        height: 26px;
        min-height: 26px;
        margin-left: -11px;
    }

    .el-table .el-table__row {
        height: 50px;
    }
    .btn-prev {
        border-radius: 50% !important;
    }
    .btn-next {
        border-radius: 50% !important;
    }

    .item2Index {
        width: 18px;               /* 圆圈宽度 */
        height: 18px;              /* 圆圈高度 */
        background-color: #409eff;
        margin: 5px 0 5px 2px;
        color: white;
    }
    /* CPU */
    .item3Index {
        background-color: #36A1D1;
        width: 18px;               /* 圆圈宽度 */
        height: 18px;              /* 圆圈高度 */
        margin: 5px 0 5px 2px;
        color: #F0F9FF;
    }
    /* 漏记核心 */
    .item4Index {
        background-color: #2E86AB;
        width: 18px;               /* 圆圈宽度 */
        height: 18px;              /* 圆圈高度 */
        margin: 5px 0 5px 2px;
        color: #E0F2FF;
    }
    /*  */
    .item5Index {
        background-color: #256D89;
        font-size: 12px !important;          /* 文字大小 */
        width: 14px;               /* 圆圈宽度 */
        height: 14px;              /* 圆圈高度 */
        margin: 5px 0 5px 2px;
        color: #D0EBFF;
    }
    .itemIndex_class {
        font-weight: bold;        /* 加粗文字 */
        font-size: 14px;          /* 文字大小 */
        line-height: 1;           /* 行高设为1避免偏移 */
        border-radius: 50%;        /* 圆形 */
        display: inline-flex;       /* 使用 flex 布局方便居中 */
        align-items: center;       /* 垂直居中 */
        justify-content: center;   /* 水平居中 */
    }

</style>
<link rel="stylesheet" href="lib/elementPlus/index.css"/>
<script src="lib/third/vue3/vue.global.js"></script>
<script src="lib/elementPlus/index.full.min.js"></script>
<script src="lib/elementPlus/zh-cn.js"></script>

<script>
    console.log(require.cache)
    delete require.cache[require.resolve('./vue/baselineManagement/MSG_PDBaselineAnalysisConfig.js')];
    require('./vue/baselineManagement/MSG_PDBaselineAnalysisConfig.js');
    // $('#handlerForm').validator();
</script>
