<div class="main-content">
  <div class="main-content-inner">
    <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs">
      <div class="page-content">
        <div class="page-header">
          <h1>设备参数
            <small>
              <i class="ace-icon fa fa-angle-double-right"></i>
              NTP对时参数配置
            </small>
          </h1>
        </div><!-- /.page-header -->
        <div class="row">
          <div class="col-xs-12">
            <form class="form-horizontal" id="ntpForm" role="form">
              <input type="hidden" id="app_ntp_id"/>
              <div class="form-group">
                <label class="col-sm-3 control-label no-padding-right" for="mmip">
                  主时钟主网Ip地址<span style="color:red">*</span>:
                </label>
                <div class="col-sm-9">
                  <input type="text" id="mmip" name="mmip"
                         placeholder="请输入正确Ip,格式0.0.0.0~***************" class="col-xs-10 col-sm-6"
                         maxlength="15"
                         pattern="(?=(\b|\D))(((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))\.){3}((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))(?=(\b|\D))"
                         required>
                  <div class="help-block with-errors"></div>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-3 control-label no-padding-right" for="msip">
                  主时钟备网Ip地址:<span style="color:red">*</span></label>
                <div class="col-sm-9">
                  <input type="text" id="msip" name="msip"
                         placeholder="请输入正确Ip,格式0.0.0.0~***************" class="col-xs-10 col-sm-6"
                         maxlength="15"
                         pattern="(?=(\b|\D))(((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))\.){3}((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))(?=(\b|\D))"
                         required>
                  <div class="help-block with-errors"></div>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-3 control-label no-padding-right" for="smip">
                  备时钟主网Ip地址:<span style="color:red">*</span></label>
                <div class="col-sm-9">
                  <input type="text" id="smip" name="smip"
                         placeholder="请输入正确Ip,格式0.0.0.0~***************" class="col-xs-10 col-sm-6"
                         maxlength="15"
                         pattern="(?=(\b|\D))(((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))\.){3}((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))(?=(\b|\D))"
                         required>
                  <div class="help-block with-errors"></div>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-3 control-label no-padding-right" for="ssip">
                  备时钟备网Ip地址:<span style="color:red">*</span></label>
                <div class="col-sm-9">
                  <input type="text" id="ssip" name="ssip"
                         placeholder="请输入正确Ip,格式0.0.0.0~***************" class="col-xs-10 col-sm-6"
                         maxlength="15"
                         pattern="(?=(\b|\D))(((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))\.){3}((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))(?=(\b|\D))"
                         required>
                  <div class="help-block with-errors"></div>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-3 control-label no-padding-right" for="port">
                  端口号<span style="color:red">*</span>:
                </label>
                <div class="col-sm-9">
                  <input type="text" id="port" name="port"
                         placeholder="请输入正确端口号,端口号范围为1-65535,NTP默认端口号123" class="col-xs-10 col-sm-6"
                         maxlength="5"
                         pattern="(^[6][0-4]\d{3}$)|(^[6][5][0-4]\d{2}$)|(^[6][5][5][0-2][0-9]$)|(^[6][5][5][3][0-5]$)|(^[1-5]\d{0,4}$)|10000|(^[1-9]\d{0,3}$)"
                         required>
                  <div class="help-block with-errors"></div>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-3 control-label no-padding-right" for="cycle">
                  对时周期<span style="color:red">*</span>:
                </label>
                <div class="col-sm-9">
                  <input type="text" id="cycle" name="cycle" style="margin-bottom: 5px;"
                         placeholder="请输入1-65535整数,默认30s" class="col-xs-10 col-sm-6"
                         maxlength="5"
                         pattern="(^[6][0-4]\d{3}$)|(^[6][5][0-4]\d{2}$)|(^[6][5][5][0-2][0-9]$)|(^[6][5][5][3][0-5]$)|(^[1-5]\d{0,4}$)|10000|(^[1-9]\d{0,3}$)"
                         required>
                  <h4 class="text-warning" style="font-size: 15px;">
                    <i class="ace-icon fa fa-exclamation-circle">注: 每个对时周期检测一次是否发生对时异常</i></h4>
                  <div class="help-block with-errors"></div>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-3 control-label no-padding-right">
                  是否采用广播<span style="color:red">*</span>:
                </label>
                <div class="col-sm-9">
                  <label class="radio-inline">
                    <input type="radio" id="enable" name="is_broadcast" value="0">启用
                  </label>
                  <label class="radio-inline">
                    <input type="radio" id="p2p" name="is_broadcast" value="1">点对点
                  </label>
                  <div class="help-block with-errors"></div>
                </div>
              </div>
              <div class="col-md-offset-3 col-md-9">
                <button class="btn btn-sm btn-primary" id="btn_import" type="reset">导入</button>
                &nbsp;
                <button class="btn btn-sm btn-primary" id="btn_export" type="reset">导出</button>
                &nbsp;
                <button type="submit" class="btn btn-sm btn-primary" id="btn_ntp_save">
                  <i class="ace-icon fa fa-check bigger-110"></i>保存
                </button>
              </div>
            </form>
          </div><!-- /.col -->
        </div><!-- /.row -->
      </div><!-- /.page-content -->
    </div>
  </div><!-- /.main-content -->
  <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
    <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
  </a>
  <!-- 验证用户身份-->
  <div class="modal" id="validateUserDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <form class="form-horizontal" id="validateUserForm" method="post" action="" data-toggle="validator" role="form">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header" style="color: white;background: deepskyblue">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            <h4 class="modal-title">验证用户身份</h4>
          </div>
          <div class="modal-body" style="padding: 10px 50px;">
            <p>请输入登录密码</p>
            <input type="hidden" id="opraType"/>
            <input type="password" id="password" class="form-control" placeholder="密码" required/>
            <div class="help-block with-errors"></div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
            <button type="submit" class="btn btn-primary" id="validateuser_btn">确定
            </button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </form>
  </div><!-- /.modal -->
  <!-- 导入导出选择对话框 -->
  <div class="modal fade" id="confirmPortDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <form class="form-horizontal" id="confirmPortForm" method="post" action="" data-toggle="validator" role="form">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
              <span class="sr-only">Close</span>
            </button>
            <h4 class="modal-title" wa-name="import_tt">请选择</h4>
          </div>
          <div class="modal-body">
            <div class="form-group">
              <label class="col-sm-2 control-label text-right" for="select_file">请选择:</label>
              <div class="col-sm-9">
                <label class="ace-file-input">
                  <input type="file" id="select_file" required>
                </label>
                <!--<div class="help-block with-errors"></div>-->
              </div>
            </div>
            <div class="form-group hidden">
              <label class="col-sm-2 control-label text-right" for="select_folder">请选择:</label>
              <div class="col-sm-9">
                <label class="ace-file-input">
                  <input type="file" id="select_folder" webkitdirectory directory  required>
                </label>
                <!--<div class="help-block with-errors"></div>-->
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
            <button type="button" class="btn btn-primary" id="port_btn">确定</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.form-horizontal-->
    </form><!-- /.modal-dialog -->
  </div><!-- /.modal -->
  <script>
    // console.log(require.cache)
    delete require.cache[require.resolve('./script/view/device_mgr/NTP_timing.js')];
    require('./script/view/device_mgr/NTP_timing.js');
    $('#ntpForm').validator();
  </script>
</div>
