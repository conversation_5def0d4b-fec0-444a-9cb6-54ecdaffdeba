<div class="main-content">
  <div class="main-content-inner">
    <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs">
      <div class="page-content">
        <div class="page-header">
          <h1>设备参数
            <small>
              <i class="ace-icon fa fa-angle-double-right"></i>
              网卡配置
            </small>
          </h1>
        </div><!-- /.page-header -->
        <div class="row wa-mb10">
          <div class="col-xs-12 text-right">
            <button class="btn btn-sm btn-primary wa-mr5" id="btn_import">导入</button>
            <button class="btn btn-sm btn-primary wa-mr5" id="btn_export">导出</button>
            <button class="btn btn-sm btn-primary" id="btn_Newly"><i class="ace-icon fa fa-plus"></i>新增网卡</button>
          </div>
        </div>
        <div class="row">
          <div class="col-xs-12">
            <table id="net-config-tb" class="table table-bordered table-hover">
            </table>
            <div id="net-config-pager"></div>
            <!-----用来计算单元格内容实际长度的--------->
            <div class="ui-jqgrid ui-widget ui-widget-content ui-corner-all" style="position:absolute;top:-9999px">
              <div class="ui-jqgrid-view">
                <div class="ui-jqgrid-bdiv">
                  <div style="position: relative;">
                    <table cellspacing="0" cellpadding="0" border="0">
                      <tr class="ui-widget-content jqgrow ui-row-ltr" style="table-layout:table">
                        <td id="tdCompute" style="background:#eee;width:auto"></td>
                      </tr>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div><!-- /.col -->
        </div><!-- /.row -->
      </div><!-- /.page-content -->
    </div>
  </div><!-- /.main-content -->
  <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
    <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
  </a>
  <!-- 验证用户身份-->
  <div class="modal" id="validateUserDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <form class="form-horizontal" id="validateUserForm" method="post" action="" data-toggle="validator" role="form">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header" style="color: white;background: deepskyblue">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            <h4 class="modal-title">验证用户身份</h4>
          </div>
          <div class="modal-body" style="padding: 10px 50px;">
            <p>请输入登录密码</p>
            <input type="hidden" id="opraType"/>
            <input type="hidden" id="opraNetId"/>
            <input type="hidden" id="opraNameStr"/>
            <input type="hidden" id="opraOldIp"/>
            <input type="hidden" id="opraIp"/>
            <input type="hidden" id="opraMask"/>
            <input type="password" id="password" class="form-control" placeholder="密码" required/>
            <div class="help-block with-errors"></div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
            <button type="submit" class="btn btn-primary" id="validateuser_btn">确定
            </button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </form>
  </div><!-- /.modal -->
  <!-- 导入导出选择对话框 -->
  <div class="modal fade" id="confirmPortDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <form class="form-horizontal" id="confirmPortForm" method="post" action="" data-toggle="validator" role="form">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
              <span class="sr-only">Close</span>
            </button>
            <h4 class="modal-title" wa-name="import_tt">请选择</h4>
          </div>
          <div class="modal-body">
            <div class="form-group">
              <label class="col-sm-2 control-label text-right" for="select_file">请选择:</label>
              <div class="col-sm-9">
                <label class="ace-file-input">
                  <input type="file" id="select_file" required>
                </label>
                <!--<div class="help-block with-errors"></div>-->
              </div>
            </div>
            <div class="form-group hidden">
              <label class="col-sm-2 control-label text-right" for="select_folder">请选择:</label>
              <div class="col-sm-9">
                <label class="ace-file-input">
                  <input type="file" id="select_folder" webkitdirectory directory  required>
                </label>
                <!--<div class="help-block with-errors"></div>-->
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
            <button type="button" class="btn btn-primary" id="port_btn">确定</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.form-horizontal-->
    </form><!-- /.modal-dialog -->
  </div><!-- /.modal -->
  <!-- 修改网卡信息-->
  <div class="modal" id="editNetDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <form class="form-horizontal" id="editNetForm" method="post" action="" data-toggle="validator" role="form">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header" style="color: white;background: deepskyblue">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
            <h4 class="modal-title" id="editNetTitle">修改网卡</h4>
          </div>
          <div class="modal-body">
            <input type="hidden" id="app_net_id"/>

            <div class="form-group">
              <label class="col-md-offset-1 col-sm-2 control-label text-right">网卡名称：</label>
              <div class="col-sm-7">
                <input type="text" class="form-control required" id="eth_nameStr" name="eth_nameStr"
                       disabled="disabled"/>
              </div>
            </div>
            <input type="hidden" class="form-control" id="old_ip"/>
            <div class="form-group">
              <label class="col-md-offset-1 col-sm-2 control-label text-right">ip地址：</label>
              <div class="col-sm-7">
                <input type="text" class="form-control required" id="ip" name="ip"
                       placeholder="请输入正确ip地址,格式为0.0.0.0~***************" maxlength="15"
                       pattern="(?=(\b|\D))(((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))\.){3}((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))(?=(\b|\D))"
                       required/>
                <div class="help-block with-errors"></div>
              </div>
            </div>
            <div class="form-group">
              <label class="col-md-offset-1 col-sm-2 control-label text-right">子网掩码：</label>
              <div class="col-sm-7">
                <input type="text" class="form-control required" id="mask" name="mask"
                       placeholder="请输入正确子网掩码,格式为0.0.0.0~***************" maxlength="15"
                       pattern="(?=(\b|\D))(((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))\.){3}((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))(?=(\b|\D))"
                       required/>
                <div class="help-block with-errors"></div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="submit" id="btn_net_save" class="btn btn-primary">保存</button>
            <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </form>
  </div><!-- /.modal -->
<!--    新增网卡-->
    <div class="modal" id="add_editNetDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <form class="form-horizontal" id="add_editNetForm" method="post" action="" data-toggle="validator" role="form">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header" style="color: white;background: deepskyblue">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                        <h4 class="modal-title" id="add_editNetTitle">新增网卡</h4>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" class="form-control" id="add_app_net_id"/>
                        <div class="form-group">
                            <label class="col-md-offset-1 col-sm-2 control-label text-right">网卡名称：</label>
                            <div class="col-sm-7">
                                <input type="text" class="form-control required" id="add_eth_nameStr" name="add_eth_nameStr"
                                       placeholder="请输入正确网卡名称,格式为eth1-eth8" maxlength="4"
                                       pattern="(^eth[1-8]$)" required/>
                                <div class="help-block with-errors"></div>
                            </div>
                        </div>
                        <input type="hidden" class="form-control" id="add_old_ip"/>
                        <div class="form-group">
                            <label class="col-md-offset-1 col-sm-2 control-label text-right">ip地址：</label>
                            <div class="col-sm-7">
                                <input type="text" class="form-control required" id="add_ip" name="add_ip"
                                       placeholder="请输入正确ip地址,格式为0.0.0.0~***************" maxlength="15"
                                       pattern="(?=(\b|\D))(((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))\.){3}((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))(?=(\b|\D))"
                                       required/>
                                <div class="help-block with-errors"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-offset-1 col-sm-2 control-label text-right">子网掩码：</label>
                            <div class="col-sm-7">
                                <input type="text" class="form-control required" id="add_mask" name="add_mask"
                                       placeholder="请输入正确子网掩码,格式为0.0.0.0~***************" maxlength="15"
                                       pattern="(?=(\b|\D))(((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))\.){3}((\d{1,2})|(1\d{1,2})|(2[0-4]\d)|(25[0-5]))(?=(\b|\D))"
                                       required/>
                                <div class="help-block with-errors"></div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" id="add_btn_net_save" class="btn btn-primary">保存</button>
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    </div>
                </div><!-- /.modal-content -->
            </div><!-- /.modal-dialog -->
        </form>
    </div><!-- /.modal -->
    <!-- 确认对话框 -->
    <div class="modal fade" id="confirmDlg">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                        <span class="sr-only">Close</span>
                    </button>
                    <h4 class="modal-title">删除</h4>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="remove_user_id"/>
                    确定删除网卡？
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" data-dismiss="modal" id="removeUserBtn">确定</button>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
  <script>
    delete require.cache[require.resolve('./script/view/device_mgr/network_config.js')];
    require('./script/view/device_mgr/network_config.js');
    $('#editNetForm').validator();
    $('#add_editNetForm').validator();
  </script>
</div>
