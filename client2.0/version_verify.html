<div class="main-content">
  <div class="main-content-inner">
    <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs">
      <div class="page-content">
        <div class="page-header">
          <h1>版本管理
            <small>
              <i class="ace-icon fa fa-angle-double-right"></i>
              版本校验
            </small>
          </h1>
        </div><!-- /.page-header -->
        <div class="row wa-mb10">
          <div class="col-xs-12 text-right wa-mb10">
            <a href="javascript:;" type="button" class="btn btn-sm btn-primary" id="add_verify">
              <i class="ace-icon fa fa-plus"></i>新增版本校验</a>
          </div>
        </div>
        <div class="row">
          <div class="col-xs-12">
            <table id="version-verify-tb" class="table table-bordered table-hover"></table>
            <div id="version-verify-pager"></div>
            <!-----用来计算单元格内容实际长度的--------->
            <div class="ui-jqgrid ui-widget ui-widget-content ui-corner-all" style="position:absolute;top:-9999px">
              <div class="ui-jqgrid-view">
                <div class="ui-jqgrid-bdiv">
                  <div style="position: relative;">
                    <table cellspacing="0" cellpadding="0" border="0">
                      <tr class="ui-widget-content jqgrow ui-row-ltr" style="table-layout:table">
                        <td id="tdCompute" style="background:#eee;width:auto"></td>
                      </tr>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div><!-- /.page-content -->
    </div>
  </div><!-- /.main-content -->
  <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
    <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
  </a>
  <!-- 验证用户身份-->
  <div class="modal" id="validateUserDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <form class="form-horizontal" id="validateUserForm" method="post" action="" data-toggle="validator" role="form">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header" style="color: white;background: deepskyblue">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            <h4 class="modal-title">验证用户身份</h4>
          </div>
          <div class="modal-body" style="padding: 10px 50px;">
            <p>请输入登录密码</p>
            <input type="hidden" id="opraType"/>
            <input type="hidden" id="opraId"/>
            <input type="hidden" id="opraIp"/>
            <input type="password" id="password" class="form-control" placeholder="密码" required/>
            <div class="help-block with-errors"></div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
            <button type="submit" class="btn btn-primary" id="validateuser_btn">确定
            </button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </form>
  </div><!-- /.modal -->
  <!-- 新建版本校验-->
  <div class="modal" id="addVerifyDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header" style="color: white;background: deepskyblue">
          <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
          <h4 class="modal-title">新建版本校验</h4>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-xs-12">
              <table id="device-tb" class="table table-bordered table-hover"></table>
              <div id="device-pager"></div>
            </div>
          </div>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
  </div><!-- /.modal -->
  <!-- 确认启动对话框 -->
  <div class="modal fade" id="confirmStartDlg">
    <div class="modal-dialog" role="document" style="width: 39%;margin: 10% auto;">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
            <span class="sr-only">Close</span>
          </button>
          <h4 class="modal-title">确认启动</h4>
        </div>
        <div class="modal-body">
          <input type="hidden" id="deviceId"/>
          <input type="hidden" id="deviceIp"/>
          确定启动该设备版本校验？
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
          <button type="button" class="btn btn-primary"
                  data-dismiss="modal" id="start_device_btn">确定
          </button>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
  </div><!-- /.modal -->
  <!-- 查看状态&结果对话框 -->
  <div class="modal fade" id="seeStateDlg">
    <div class="modal-dialog" role="document" style="width: 39%;margin: 10% auto;">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
            <span class="sr-only">Close</span>
          </button>
          <h4 class="modal-title" id="seeStateTitle" wa-name="see_state">查看该设备版本校验状态</h4>
        </div>
        <div class="modal-body">
          <input type="hidden" id="versionVerifyId"/>
          <input type="hidden" id="versionVerifyIp"/>
          <p id="versionStateCont"></p>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-primary" data-dismiss="modal">确定
          </button>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
  </div><!-- /.modal -->
  <!-- 确认停止&删除对话框 -->
  <div class="modal fade" id="confirmStopDlg">
    <div class="modal-dialog" role="document" style="width: 39%;margin: 10% auto;">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
            <span class="sr-only">Close</span>
          </button>
          <h4 class="modal-title" id="stopTitle" wa-name="_stop">确认取消</h4>
        </div>
        <div class="modal-body">
          <input type="hidden" id="versionVerifyId2"/>
          <input type="hidden" id="versionVerifyIp2"/>
          <p>确定取消该设备版本校验？</p>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
          <button type="button" class="btn btn-primary" data-dismiss="modal" id="stop_btn">确定
          </button>
        </div>
      </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
  </div><!-- /.modal -->
  <script>
    delete require.cache[require.resolve('./script/view/version_mgr/version_verify.js')];
    require('./script/view/version_mgr/version_verify.js');
  </script>
</div>
