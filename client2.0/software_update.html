<!--&lt;!&ndash; element-plus &ndash;&gt;-->

<!--<script src="vue/admin/controller/port_param.js"></script>-->

<div class="main-content">
    <div class="main-content-inner">
        <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs" style="height: calc(100% - 100px);">
            <div class="page-content">
                <div class="page-header">
                    <h1>配置交互
                        <small>
                            <i class="ace-icon fa fa-angle-double-right"></i>软件升级
                        </small>
                    </h1>
                </div>
                <!-- /.page-header -->
                <div class="row">
                    <div id="app">
                        <div ref="box" class="backup-container">
                            <div class="backup-panel">
                                <!-- 左侧 -->
                                <div class="panel-left">
                                    <div class="panel-title" style="justify-content: space-between">
                                        <div>升级包上传</div>
                                        <div style="display: flex;flex-wrap: nowrap;width: 40%;">
                                            <el-button type="success" class="panel-button" @click="() => validateToNext('UNDONE')">待办列表</el-button>
                                        </div>
                                    </div>
                                    <div class="custom-select-wrapper">
                                        <div style="margin: 10px 0 10px 0">*选择任务子类型</div>
                                        <el-select v-model="subType" :disabled="filePath && uploadStatus === 1" placeholder="请选择任务子类型" class="custom-select" :suffix-icon="null">
                                            <el-option label="监测装置软件" value="1"></el-option>
                                            <el-option label="监测装置插件" value="2"></el-option>
                                            <el-option label="AGENT升级" value="3"></el-option>
                                            <el-option label="AGENT插件升级" value="4"></el-option>
                                        </el-select>
                                        <span class="fake-arrow"><svg width="14" height="14" viewBox="0 0 24 24" fill="#2699d4"><path d="M7 10l5 5 5-5z" /></svg></span>
                                    </div>
                                    <div v-if="['2', '4'].includes(subType)" class="custom-select-wrapper">
                                        <div style="margin: 10px 0 10px 0">*选择插件</div>
                                        <el-select v-model="subType" :disabled="filePath && uploadStatus === 1" placeholder="请选择任务子类型" class="custom-select" :suffix-icon="null">
                                            <el-option label="监测装置软件" value="1"></el-option>
                                            <el-option label="监测装置插件" value="2"></el-option>
                                            <el-option label="AGENT升级" value="3"></el-option>
                                            <el-option label="AGENT插件升级" value="4"></el-option>
                                        </el-select>
                                        <span class="fake-arrow"><svg width="14" height="14" viewBox="0 0 24 24" fill="#2699d4"><path d="M7 10l5 5 5-5z" /></svg></span>
                                    </div>
                                    <div class="restore-box" @click="uploadHandleClick" v-if="!fileName">
                                        <p class="restore-tip">请选择 .jy 后缀文件</p>
                                        <svg class="restore-icon" viewBox="0 0 24 24" fill="#2699d4"><path d="M5 20h14v-2H5v2zM12 2l-5.5 5.5h4v7h3v-7h4L12 2z" /></svg>
                                        <input ref="fileInput" type="file" accept=".jy" class="file-input" @change="onFileChange" tabindex="-1" />
                                    </div>
                                    <div class="file-info" v-else>
                                        <span class="file-name" :title="fileName">{{ fileName }}</span>
                                        <span class="file-size">{{ formatFileSize(fileSize) }}</span>
                                        <button v-if="uploadStatus === 0 && fileName" class="delete-btn" @click.stop="removeFile">删除</button>
                                    </div>
                                    <div style="display: flex;flex-wrap: nowrap">
                                        <el-button v-if="fileName && [0, 3].includes(uploadStatus) && fileName" type="success" class="panel-button" @click="() => validateToNext('START')">开始上传</el-button>
                                        <el-button v-if="fileName && [2].includes(uploadStatus) && fileName" type="success" class="panel-button" @click="startFn">续传</el-button>
                                        <el-button v-if="fileName && [1, 2, 3].includes(uploadStatus) && fileName" type="success" class="panel-button" @click="shutdownFn">终止</el-button>
                                    </div>
                                </div>
                                <!-- 右侧 -->
                                <div class="panel-right">
                                    <div class="panel-title" style="justify-content: space-between">
                                        <div>软件升级</div>
                                    </div>
                                    <div class="custom-select-wrapper">
                                        <div style="margin: 10px 0 10px 0">*选择任务子类型</div>
                                        <el-select v-model="subType2" placeholder="请选择任务子类型" class="custom-select" :suffix-icon="null" @change="getSoftPacketListFn">
                                            <el-option label="监测装置软件" value="1"></el-option>
                                            <el-option label="监测装置插件" value="2"></el-option>
                                            <el-option label="AGENT升级" value="3"></el-option>
                                            <el-option label="AGENT插件升级" value="4"></el-option>
                                        </el-select>
                                        <span class="fake-arrow"><svg width="14" height="14" viewBox="0 0 24 24" fill="#2699d4"><path d="M7 10l5 5 5-5z" /></svg></span>
                                    </div>
                                    <div>
                                        <el-table stripe :data="softPacketList" :height="updateTableHeight" style="width: 100%">
                                            <el-table-column label="升级包序号" prop="id"></el-table-column>
                                            <el-table-column label="状态" prop="statusName"></el-table-column>
                                            <el-table-column label="升级包名" prop="pkgname"></el-table-column>
                                            <el-table-column label="升级包大小" prop="pkgsizeName"></el-table-column>
                                            <el-table-column label="下载时间" prop="loadtimeName"></el-table-column>
                                            <el-table-column label="操作" width="200">
                                                <template #default="scope">
                                                    <el-button v-if="scope.row.status !== 1" type="primary" size="small" @click="() => validateToNext('UPDATE', scope.row)">升级</el-button>
                                                    <el-button v-if="scope.row.status === 1" type="primary" size="small" @click="() => validateToNext('RESULT', scope.row)">查询结果</el-button>
                                                    <el-button  v-if="scope.row.status !== 1" type="danger" size="small" @click="() => validateToNext('DELETE', scope.row)">删除</el-button>
                                                </template>
                                            </el-table-column>
                                        </el-table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 待上传 -->
                        <el-dialog title="待上传" v-model="unDoneVisible" width="70%" @close="closeUnDoneVisibleFn">
                            <div :style="{ height: `${queryModelHeight}px` }">
                                <div class="info_box">
                                    <el-table stripe :data="unDoneTableList" :height="queryModelTableHeight" style="width: 100%">
                                        <el-table-column type="index" label="序号" width="80"></el-table-column>
                                        <el-table-column label="任务子类型" prop="subTypeName"></el-table-column>
                                        <el-table-column label="文件名" prop="file_name"></el-table-column>
                                        <el-table-column label="文件大小" width="150" prop="totalSizeTransfer"></el-table-column>
                                        <el-table-column label="文件路径" prop="file_path" width="350"></el-table-column>
                                        <el-table-column label="上传进度" prop="process"></el-table-column>
                                        <el-table-column label="最后上传时间" prop="lastTransferTimeName"></el-table-column>
                                        <el-table-column label="操作" width="200">
                                            <template #default="scope">
                                                <el-button type="primary" size="small" @click="() => { continueUnDoneFn(scope.row) }">继续</el-button>
                                                <el-button type="danger" size="small" @click="() => { deleteUnDoneFn(scope.row) }">删除</el-button>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                    <!-- 分页 -->
                                    <div style="display: flex;align-items: center;justify-content: center">
                                        <el-pagination
                                                background
                                                layout="prev, jumper, next"
                                                :current-page="unDonePageNum"
                                                :page-size="unDonePageSize"
                                                :total="unDoneTotalCount"
                                                @current-change="unDoneCurrentChangeFn"
                                        ></el-pagination>
                                    </div>
                                </div>
                            </div>
                        </el-dialog>
                        <!-- 上传提示框 -->
                        <el-dialog title="升级包上传" v-model="uploadDoneVisible" width="40%" @close="closeUploadVisibleModelFn">
<!--                            <div v-if="uploadStatus === 1" style="padding: 20px 0; text-align: center; font-size: 16px; height: 50px">-->
<!--                                <span v-for="(char, index) in uploadProcessChars" :key="index" class="jump-char" :style="{ animationDelay: `${index * 0.5}s` }">{{ char }}</span>-->
<!--                            </div>-->
                            <div v-if="uploadStatus === 1" style="padding: 20px 0; text-align: center; font-size: 16px;height: 50px">{{ uploadProcessStr }}</div>
                            <!-- 上传特殊情况 -->
                            <div v-else style="padding: 20px 0; text-align: center; font-size: 16px;height: 50px">{{ showUploadStatusTips }}</div>
                            <span slot="footer" class="dialog-footer">
                              <el-button v-if="uploadStatus === 1" type="danger" @click="shutdownFn">终止</el-button>
                              <el-button v-if="[0, 2, 3].includes(uploadStatus)" type="primary" @click="closeUploadVisibleModelFn">确定</el-button>
                            </span>
                        </el-dialog>
                        <el-dialog title="执行升级" v-model="updateVisible" width="40%" @close="closeUpdateVisibleFn">
                            <div style="text-align: center; margin: 20px 0;">
                                <el-form>
                                    <el-form-item label="资产选择" label-width="100px">
                                        <el-select v-model="assetValue" placeholder="请选择资产" multiple filterable style="width: 60%;" @change="changeAssetValue">
                                            <el-option v-for="item in assetList" :key="item.GID" :label="item.DNAME" :value="item.GID"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-form>
                            </div>
                            <span slot="footer" class="dialog-footer">
                              <el-button type="primary" @click="closeUpdateVisibleFn">取消</el-button>
                              <el-button type="primary" @click="updateFn">确定</el-button>
                            </span>
                        </el-dialog>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /.main-content -->
    <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
        <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
    </a>
    <!-- 验证用户身份-->
    <div class="modal" style="z-index: 4000;" id="validateUserDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         aria-hidden="true">
        <form class="form-horizontal" id="validateUserForm" method="post" action="" data-toggle="validator" role="form">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header" style="color: white;background: deepskyblue">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                        <h4 class="modal-title">验证用户身份</h4>
                    </div>
                    <div class="modal-body" style="padding: 10px 50px;">
                        <p>请输入登录密码</p>
                        <input type="hidden" id="opraType"/>
                        <input type="password" id="password" class="form-control" placeholder="密码" required/>
                        <div class="help-block with-errors"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary" id="validateuser_btn">确定
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>

</div>
<style>
    .jump-char {
        display: inline-block;
        animation: jump 0.6s infinite;
    }

    @keyframes jump {
        0%, 100% {
            transform: translateY(0);
        }
        50% {
            transform: translateY(-6px);
        }
    }
</style>
<style>
    .dialog-footer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }
    html, body, #app {
        height: 100%;
        margin: 0;
    }

    .backup-container {
        height: 100%;
        padding: 20px;
        box-sizing: border-box;
        background-color: #f9f9f9;
    }

    .backup-panel {
        display: flex;
        height: 100%;
        border: 2px solid #2699d4;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 0 12px rgba(38, 153, 212, 0.2);
    }

    .panel-left,
    .panel-right {
        width: 50%;
        display: flex;
        flex-direction: column;
        padding: 20px;
        box-sizing: border-box;
        background-color: white;
    }

    .panel-left {
        border-right: 1px solid #ccc;
    }

    .panel-title {
        height: 30px;
        font-size: 20px;
        color: #2699d4;
        margin-bottom: 20px;
        display: flex;
        align-content: center;
        flex-wrap: nowrap;
    }

    .custom-select-wrapper {
        position: relative;
        width: 100%;
        margin-bottom: 16px;
    }

    .custom-select :deep(.el-input__inner) {
        padding-right: 32px !important;
    }

    .fake-arrow {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
    }

    .warning-text {
        color: red;
        font-size: 14px;
        margin-bottom: 16px;
    }

    .spacer {
        flex-grow: 1;
    }

    .panel-button {
        width: 100%;
        font-size: 14px;
    }


</style>
<style scoped>
    .restore-box {
        position: relative;
        height: 200px;
        background-color: #eef7fb;
        border: 1px dashed #2699d4;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition: background-color 0.3s;
        user-select: none;
        margin-bottom: 10px;
    }

    .restore-box:hover {
        background-color: #e0f1f9;
    }

    .restore-tip {
        color: #2699d4;
        font-size: 14px;
        margin-bottom: 12px;
        pointer-events: none;
    }

    .restore-icon {
        width: 48px;
        height: 48px;
        transition: transform 0.2s;
        pointer-events: none;
    }

    .restore-box:hover .restore-icon {
        transform: scale(1.1);
    }

    .file-input {
        position: absolute !important;
        width: 1px !important;
        height: 1px !important;
        opacity: 0 !important;
        pointer-events: none !important;
        overflow: hidden !important;
        top: 0;
        left: 0;
        z-index: -1;
    }

    .file-info {
        margin-bottom: 10px;
        height: 200px;
        background-color: #eef7fb;
        border: 1px dashed #2699d4;
        border-radius: 8px;

        display: flex;
        justify-content: center;
        align-items: center;
        gap: 16px;
        padding: 0 20px;
        box-sizing: border-box;
        user-select: none;
    }

    .file-name {
        max-width: 40%;
        white-space: nowrap;
        margin-right: 5px;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: 600;
        color: #2699d4;
        font-size: 16px;
    }

    .file-size {
        color: #666;
        font-size: 14px;
        white-space: nowrap;
    }

    .delete-btn {
        background-color: #ff4d4f;
        border: none;
        color: white;
        font-weight: 600;
        border-radius: 4px;
        padding: 6px 12px;
        cursor: pointer;
        user-select: none;
        transition: background-color 0.3s;
        flex-shrink: 0;
    }

    .delete-btn:hover {
        background-color: #d9363e;
    }
</style>

<style>
    .info_box {
        width: 100%;
        height: 100%;
    }
    /* 去掉el-input内置的边框 */
    .el-input__inner {
        border: none !important;
    }
    .el-dialog {
        padding: 0 !important;
    }
    .el-dialog__header {
        background-color: #00bfff !important;
        color: white !important;
        padding-top: 10px !important;
        padding-left: 10px !important;
    }
    .el-dialog__body {
    }
    .el-dialog__title {
        color: white !important;
    }
    .dialog_content {
        height: 100px;
        padding: 20px !important;
    }
    .dialog-footer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 20px !important;
        background-color: #eff3f8;
    }
    /* ------------------------------------------------------------------------------------------------- */
    /* 让 #app 占满剩余空间 */
    .main-content {
        height: 100%;

    }
    .main-content-inner, .page-content, .row {
        height: 100%;
    }

    html, body {
        height: 100%;
        margin: 0;
        padding: 0;
    }

    #app {
        display: flex;
        flex-direction: column;
        height: 100%;
        background-color: #f9f9f9;
    }
</style>

<link rel="stylesheet" href="lib/elementPlus/index.css"/>
<script src="lib/third/vue3/vue.global.js"></script>
<script src="lib/elementPlus/index.full.min.js"></script>
<script src="lib/elementPlus/zh-cn.js"></script>

<script>
    console.log(require.cache)
    delete require.cache[require.resolve('./vue/systemSetting/CONFIGMANAGEPACKETTYPE_UPGRADE')];
    require('./vue/systemSetting/CONFIGMANAGEPACKETTYPE_UPGRADE');
    // $('#handlerForm').validator();
</script>
