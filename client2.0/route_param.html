<!--&lt;!&ndash; element-plus &ndash;&gt;-->

<!--<script src="vue/admin/controller/port_param.js"></script>-->

<div class="main-content">
    <div class="main-content-inner">
        <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs" style="height: calc(100% - 100px);">
            <div class="page-content">
                <div class="page-header">
                    <h1>参数配置
                        <small>
                            <i class="ace-icon fa fa-angle-double-right"></i>
                            路由参数
                        </small>
                    </h1>
                </div>
                <!-- /.page-header -->
                <div class="row" style="height: 100%">
                    <div id="app" style="height: 100%">
                        <div ref="box" style="height: 100%">
                            <div style="display: flex;align-items: center;justify-content: flex-end;margin-top: 30px;padding-right: 5%">
                                <!--                            <el-button type="primary" @click="() => importExportStep1Handler('IMPORT')">导入</el-button>-->
                                <el-button type="success" @click="() => operationFn('ADD')">新增</el-button>
                                <!--                            <el-button type="primary" @click="() => importExportStep1Handler('EXPORT')">导出</el-button>-->
                            </div>
                            <el-row>
                                <el-table stripe :height="tableHeight" :data="routeList">
                                    <el-table-column type="index" label="序号" width="60"></el-table-column>
                                    <el-table-column label="目的网段" prop="dstNetSeg"></el-table-column>
                                    <el-table-column label="网口掩码" prop="dstNetMask"></el-table-column>
                                    <el-table-column label="网关地址" prop="netGateway"></el-table-column>
                                    <el-table-column label="操作" width="200">
                                        <template #default="scope">
                                            <el-button type="primary" size="small" @click="() => { operationFn('EDIT', scope.row, scope.$index) }">修改</el-button>
                                            <el-button type="danger" size="small" @click="() => { operationFn('DELETE', scope.row) }">删除</el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </el-row>
                            <div style="display: flex; justify-content: center; align-items: center; flex-direction: column;margin-top: 30px;">
                                <el-pagination background layout="prev, jumper, next" :total="4" style="margin-bottom: 10px;" class="custom-pagination" />
                            </div>
                            <el-dialog v-model="updateDialogVisible" :title="updateDialogType === 'ADD' ? '新增' : '编辑'"  width="50%">
                                <div class="dialog_content">
                                    <el-form ref="formRef" :model="rowData" :rules="formRules" label-width="80px">
                                        <el-row>
                                            <el-col>
                                                <el-form-item label="目的网段" prop="dstNetSeg">
                                                    <el-input v-model="rowData.dstNetSeg" placeholder="请输入目的网段"></el-input>
                                                </el-form-item>
                                            </el-col>
                                        </el-row>
                                        <el-row>
                                            <el-col>
                                                <el-form-item label="网口掩码" prop="dstNetMask">
                                                    <el-input v-model="rowData.dstNetMask" placeholder="请输入网口掩码"></el-input>
                                                </el-form-item>
                                            </el-col>
                                        </el-row>
                                        <el-row>
                                            <el-col>
                                                <el-form-item label="网关地址" prop="netGateway">
                                                    <el-input v-model="rowData.netGateway" placeholder="请输入网关地址"></el-input>
                                                </el-form-item>
                                            </el-col>
                                        </el-row>
                                    </el-form>
                                </div>
                                <div slot="footer" class="dialog-footer">
                                    <el-button type="primary" @click="() => { operationFn('SAVE') }">提交</el-button>
                                    <el-button @click="updateDialogVisible = false">关闭</el-button>
                                </div>
                            </el-dialog>
                        </div>
                    </div>
                    <!-- /.col -->
                </div>
                <!-- /.row -->
            </div>
            <!-- /.page-content -->
        </div>
    </div>
    <!-- /.main-content -->
    <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
        <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
    </a>
    <!-- 验证用户身份-->
   <div class="modal" style="z-index: 4000;" id="validateUserDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
        aria-hidden="true">
       <form class="form-horizontal" id="validateUserForm" method="post" action="" data-toggle="validator" role="form">
           <div class="modal-dialog">
               <div class="modal-content">
                   <div class="modal-header" style="color: white;background: deepskyblue">
                       <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                       <h4 class="modal-title">验证用户身份</h4>
                   </div>
                   <div class="modal-body" style="padding: 10px 50px;">
                       <p>请输入登录密码</p>
                       <input type="hidden" id="opraType"/>
                       <input type="password" id="password" class="form-control" placeholder="密码" required/>
                       <div class="help-block with-errors"></div>
                   </div>
                   <div class="modal-footer">
                       <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                       <button type="submit" class="btn btn-primary" id="validateuser_btn">确定
                       </button>
                   </div>
               </div>
           </div>
       </form>
   </div>
    <!-- /.modal -->
    <!-- 导入导出选择对话框 -->
   <div class="modal fade" id="confirmPortDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
        aria-hidden="true">
       <form class="form-horizontal" id="confirmPortForm" method="post" action="" data-toggle="validator" role="form">
           <div class="modal-dialog" role="document">
               <div class="modal-content">
                   <div class="modal-header">
                       <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                           <span aria-hidden="true">&times;</span>
                           <span class="sr-only">Close</span>
                       </button>
                       <h4 class="modal-title" wa-name="import_tt">请选择</h4>
                   </div>
                   <div class="modal-body">
                       <div class="form-group">
                           <label class="col-sm-2 control-label text-right" for="select_file">请选择:</label>
                           <div class="col-sm-9">
                               <label class="ace-file-input">
                                   <input type="file" id="select_file" required/>
                               </label>
                           </div>
                       </div>
                       <div class="form-group hidden">
                           <label class="col-sm-2 control-label text-right" for="select_folder">请选择:</label>
                           <div class="col-sm-9">
                               <label class="ace-file-input">
                                   <input type="file" id="select_folder" webkitdirectory directory required/>
                               </label>
                           </div>
                       </div>
                   </div>
                   <div class="modal-footer">
                       <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                       <button type="button" class="btn btn-primary" id="port_btn">确定</button>
                   </div>
               </div>
           </div>
       </form>
   </div>
    <!-- /.modal -->

</div>
<!-- 统一高度用 -->
<style>
    #app {
        display: flex;
        flex-direction: column;
        height: 100%;
        background-color: #f9f9f9;
    }
    .main-content {
        height: 100%;

    }
    .main-content-inner, .page-content, .row {
        height: 100%;
    }

    html, body {
        height: 100%;
        margin: 0;
        padding: 0;
    }

    #app {
        display: flex;
        flex-direction: column;
        height: 100%;
        background-color: #f9f9f9;
    }
</style>
<style>
    /* 去掉el-input内置的边框 */
    .el-input__inner {
        border: none !important;
    }
    .el-dialog {
        padding: 0 !important;
    }
    .el-dialog__header {
        background-color: #00bfff !important;
        color: white !important;
        padding-top: 10px !important;
        padding-left: 10px !important;
    }
    .el-dialog__title {
        color: white !important;
    }
    .dialog_content {
        padding: 20px !important;
    }
    .dialog-footer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 20px !important;
        background-color: #eff3f8;
    }
            /* 隐藏跳转框前后的文字 */
        .custom-pagination .el-pagination__jump {
            margin-left: 0;
            font-size: 0;
        }
        .custom-pagination .el-pagination__jump:before,
        .custom-pagination .el-pagination__jump:after {
            content: none;
        }
    .el-table__header th {
        border-bottom: 3px solid #e1e1e1 !important;
    }
    /* 表格最底部的线（蓝色，加粗） */
    .el-table::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        border-bottom: 2px solid #e1e1e1 !important;
    }
    .cell-input {
        height: 26px;
        margin-left: -10px;
    }

        .cell-select .el-select__wrapper {
            height: 26px;
            min-height: 26px;
            margin-left: -11px;
        }

.el-table .el-table__row {
  height: 50px;
}
.btn-prev {
    border-radius: 50% !important;
}
.btn-next {
    border-radius: 50% !important;
}
</style>
<link rel="stylesheet" href="lib/elementPlus/index.css"/>
<script src="lib/third/vue3/vue.global.js"></script>
<script src="lib/elementPlus/index.full.min.js"></script>
<script src="lib/elementPlus/zh-cn.js"></script>

<script>
    console.log(require.cache)
    delete require.cache[require.resolve('./vue/paramConf/MSG_RouteConfig')];
    require('./vue/paramConf/MSG_RouteConfig');
    // $('#handlerForm').validator();
</script>
