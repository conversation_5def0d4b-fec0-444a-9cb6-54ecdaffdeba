<!--&lt;!&ndash; element-plus &ndash;&gt;-->

<!--<script src="vue/admin/controller/port_param.js"></script>-->
<div class="main-content">
    <div class="main-content-inner">
        <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs" style="height: calc(100% - 100px);">
            <div class="page-content">
                <div class="page-header">
                    <h1>配置交互
                        <small>
                            <i class="ace-icon fa fa-angle-double-right"></i>插件管理
                        </small>
                    </h1>
                </div>
                <div class="row">
                    <div id="app">
                        <div ref="box" class="main-container">
                            <el-table stripe :data="tableData" :height="tableHeight" style="width: 100%">
                                <el-table-column label="插件ID" prop="PLUG_ID"></el-table-column>
                                <el-table-column label="应用ID" prop="APPID"></el-table-column>
                                <el-table-column label="升级包名" prop="pkgname"></el-table-column>
                                <el-table-column label="升级包大小" prop="pkgsizeName"></el-table-column>
                                <el-table-column label="下载时间" prop="loadtimeName"></el-table-column>
                                <el-table-column label="操作" width="200">
                                    <template #default="scope">
                                        <el-button v-if="scope.row.status !== 1" type="primary" size="small" @click="() => validateToNext('UPDATE', scope.row)">升级</el-button>
                                        <el-button v-if="scope.row.status === 1" type="primary" size="small" @click="() => validateToNext('RESULT', scope.row)">查询结果</el-button>
                                        <el-button  v-if="scope.row.status !== 1" type="danger" size="small" @click="() => validateToNext('DELETE', scope.row)">删除</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                        <el-dialog title="查看" v-model="viewVisible" width="70%" @close="closeViewVisibleFn">
                            <div :style="{ height: `${queryModelHeight}px` }">
                                <div class="info_box">
                                    <div class="panel-title" style="justify-content: space-between">插件</div>
                                    <el-row :gutter="20">
                                        <el-col :span="8">
                                            <div class="form-item">
                                                <span class="view_label">字段1：</span>
                                                <span class="value">值1</span>
                                            </div>
                                        </el-col>
                                        <el-col :span="8">
                                            <div class="form-item">
                                                <span class="view_label">字段1：</span>
                                                <span class="value">值1</span>
                                            </div>
                                        </el-col>
                                        <el-col :span="8">
                                            <div class="form-item">
                                                <span class="view_label">字段1：</span>
                                                <span class="value">值ddddddddddddddddwdwdwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwdddddddddddddddddddddddddddddddddddddddddddddddddd1</span>
                                            </div>
                                        </el-col>
                                    </el-row>
                                    <div class="panel-title" style="justify-content: space-between">插件状态</div>
                                    <el-row :gutter="20">
                                        <el-col :span="8">
                                            <div class="form-item">
                                                <span class="view_label">字段1：</span>
                                                <span class="value">值1</span>
                                            </div>
                                        </el-col>
                                        <el-col :span="8">
                                            <div class="form-item">
                                                <span class="view_label">字段1：</span>
                                                <span class="value">值1</span>
                                            </div>
                                        </el-col>
                                        <el-col :span="8">
                                            <div class="form-item">
                                                <span class="view_label">字段1：</span>
                                                <span class="value">值ddddddddddddddddwdwdwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwdddddddddddddddddddddddddddddddddddddddddddddddddd1</span>
                                            </div>
                                        </el-col>
                                    </el-row>
                                </div>
                            </div>
                        </el-dialog>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /.main-content -->
    <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
        <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
    </a>
    <!-- 验证用户身份-->
    <div class="modal" style="z-index: 4000;" id="validateUserDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         aria-hidden="true">
        <form class="form-horizontal" id="validateUserForm" method="post" action="" data-toggle="validator" role="form">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header" style="color: white;background: deepskyblue">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                        <h4 class="modal-title">验证用户身份</h4>
                    </div>
                    <div class="modal-body" style="padding: 10px 50px;">
                        <p>请输入登录密码</p>
                        <input type="hidden" id="opraType"/>
                        <input type="password" id="password" class="form-control" placeholder="密码" required/>
                        <div class="help-block with-errors"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary" id="validateuser_btn">确定
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
<style scoped>
    .panel-title {
        height: 30px;
        font-size: 20px;
        color: #2699d4;
        margin-bottom: 20px;
        display: flex;
        align-content: center;
        flex-wrap: nowrap;
    }
    .form-item {
        width: 100%;
        display: flex;
        align-items: flex-start; /* 顶对齐，避免高度不同 */
    }
    .view_label {
        font-weight: bold;
        margin-right: 6px;
        flex-shrink: 0; /* 不被压缩 */
        width: 70px; /* 给个固定宽度，看情况调 */
    }
    .value {
        flex: 1; /* 占满剩余空间 */
        word-break: break-all; /* 遇到长字母/数字自动换行 */
        white-space: normal;   /* 允许换行 */
    }
</style>
<!-- 基础页面样式 -->
<style>

    .main-container {
        height: 100%;
        /*padding: 20px;*/
        box-sizing: border-box;
        background-color: #f9f9f9;
    }
    .info_box {
        padding: 0px 10px 10px 10px;
        width: 100%;
        height: 100%;
    }
    html, body, #app {
        height: 100%;
        margin: 0;
    }
    /* 让 #app 占满剩余空间 */
    .main-content {
        height: 100%;

    }
    .main-content-inner, .page-content, .row {
        height: 100%;
    }

    html, body {
        height: 100%;
        margin: 0;
        padding: 0;
    }

    #app {
        display: flex;
        flex-direction: column;
        height: 100%;
        background-color: #f9f9f9;
    }
    /* ------------------------------------------------------------------------------------------------- */
</style>
<link rel="stylesheet" href="lib/elementPlus/index.css"/>
<script src="lib/third/vue3/vue.global.js"></script>
<script src="lib/elementPlus/index.full.min.js"></script>
<script src="lib/elementPlus/zh-cn.js"></script>
<!--dialog 相关-->
<style>
    /* 去掉el-input内置的边框 */
    .el-input__inner {
        border: none !important;
    }
    .el-dialog {
        padding: 0 !important;
    }
    .el-dialog__header {
        background-color: #00bfff !important;
        color: white !important;
        padding-top: 10px !important;
        padding-left: 10px !important;
    }
    .el-dialog__body {
    }
    .el-dialog__title {
        color: white !important;
    }
    .dialog_content {
        height: 100px;
        padding: 20px !important;
    }
    .dialog-footer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 20px !important;
        background-color: #eff3f8;
    }
</style>
<script>
    console.log(require.cache)
    delete require.cache[require.resolve('./vue/configBackupTrigger/MSG_PLUGIN')];
    require('./vue/configBackupTrigger/MSG_PLUGIN');
    // $('#handlerForm').validator();
</script>