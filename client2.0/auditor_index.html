<script type="text/javascript">
  try {
    ace.settings.loadState('sidebar')
  } catch (e) {
  }
</script>
<ul class="l-b-nav" wa-name="audit_list">
  <li class data-id data-code="log_audit" data-tt="日志审计" data-nav-id="log_audit" data-nav-name="日志审计">
    <div class="l-b-navbox-1">
      <img src="assets/images/l-b-images/selected_bar.png">
    </div>
    <div class="list l-b-navbox-2" align="center">
      <img src="assets/images/l-b-images/log_query.png"><br>日志审计
      <input name="menuId" type="hidden" value>
      <input name="menuUrl" type="hidden" value>
    </div>
  </li>
  <li class data-id data-code="log_analysis" data-tt="日志分析" data-nav-id="log_analysis" data-nav-name="日志分析">
    <div class="l-b-navbox-1">
      <img src="assets/images/l-b-images/selected_bar.png">
    </div>
    <div class="list l-b-navbox-2" align="center">
      <img src="assets/images/l-b-images/log_analyse_statistic.png"><br>日志分析
      <input name="menuId" type="hidden" value>
      <input name="menuUrl" type="hidden" value>
    </div>
  </li>
  <li class data-id data-code="log_backup" data-tt="日志备份" data-nav-id="log_backup" data-nav-name="日志备份">
    <div class="l-b-navbox-1">
      <img src="assets/images/l-b-images/selected_bar.png">
    </div>
    <div class="list l-b-navbox-2" align="center">
      <img src="assets/images/l-b-images/log_backup.png"><br>日志备份
      <input name="menuId" type="hidden" value>
      <input name="menuUrl" type="hidden" value>
    </div>
  </li>
</ul><!-- /.nav-list -->