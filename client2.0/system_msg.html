<div class="main-content">
  <div class="main-content-inner">
    <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs">
      <div class="page-content">
        <div class="page-header">
          <h1>系统设置
            <small>
              <i class="ace-icon fa fa-angle-double-right"></i>
              系统信息
            </small>
          </h1>
        </div><!-- /.page-header -->
        <div class="row">
          <div class="col-xs-12">
            <form class="form-horizontal" id="sysMsgForm" role="form">
              <!--<input type="hidden" id="app_policy_id"/>-->
              <div class="form-group">
                <label class="col-sm-3 control-label text-right">应用磁盘使用情况:</label>
                <div class="col-sm-6">
                  <input type="text" id="app_disk_stat" name="app_disk_stat" class="col-xs-12 col-sm-12" disabled="disabled"/>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-3 control-label text-right">root用户是否启用:</label>
                <div class="col-sm-6">
                  <input type="text" id="is_root_user_enabled" name="is_root_user_enabled" class="col-xs-12 col-sm-12"
                   disabled="disabled"/>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-3 control-label text-right">对外开放端口:</label>
                <div class="col-sm-6">
                  <textarea id="opened_ports" class="form-control sub_val" disabled="disabled" rows="16"></textarea>
                </div>
              </div>
              <div class="form-group" id="certCheckSwitch">
                <label class="col-sm-3 control-label text-right">是否启用证书链验证</label>
                <div class="col-sm-6">
                  <label>
                    <input name="switch-field-1" class="ace ace-switch" type="checkbox">
                    <span class="lbl" title="是否启用证书链验证"></span>
                  </label>
                </div>
              </div>
            </form>
          </div><!-- /.col -->
        </div><!-- /.row -->
      </div><!-- /.page-content -->
    </div>
  </div><!-- /.main-content -->
  <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
    <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
  </a>
  <script>
    // console.log(require.cache)
    delete require.cache[require.resolve('./script/view/system_setup/system_msg.js')];
    require('./script/view/system_setup/system_msg.js');
  </script>
</div>
