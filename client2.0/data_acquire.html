<link href="lib/third/bootstrap-table/bootstrap-table.min.css" />
<div class="main-content">
  <div class="main-content-inner">
    <div class="breadcrumbs ace-save-state wa-breadcrumbs" id="breadcrumbs">
      <div class="page-content">
        <div class="page-header">
          <h1>数据采集</h1>
        </div><!-- /.page-header -->
        <div class="row wa-mb10">
          <div class="col-xs-12">
            <form class="form-horizontal" role="form" id="data_acquire_frm">
              <div class="form-group wa-mb10">
                <label class="col-sm-1 control-label no-padding-right">
                  <b>设备类型:</b>
                </label>
                <div class="col-sm-11" id="deviceTypeSelect">
                  <label class="radio-inline">
                    <input name="all" type="checkbox" class="ace" value="0" checked><span class="lbl">全选</span>
                  </label>
                  <label class="radio-inline">
                    <input name="form-field-checkbox" type="checkbox" class="ace" value="0x01" checked><span class="lbl">服务器工作站类</span>
                  </label>
                  <label class="radio-inline">
                    <input name="form-field-checkbox" type="checkbox" class="ace" value="0x02" checked><span class="lbl">数据库</span>
                  </label>
                  <label class="radio-inline">
                    <input name="form-field-checkbox" type="checkbox" class="ace" value="0x04" checked><span class="lbl">网络设备</span>
                  </label>
                  <label class="radio-inline">
                    <input name="form-field-checkbox" type="checkbox" class="ace" value="0x08" checked><span class="lbl">横向正向隔离装置</span>
                  </label>
                  <label class="radio-inline">
                    <input name="form-field-checkbox" type="checkbox" class="ace" value="0x10" checked><span class="lbl">横向反向隔离装置</span>
                  </label>
                  <label class="radio-inline">
                    <input name="form-field-checkbox" type="checkbox" class="ace" value="0x20" checked><span class="lbl">纵向加密装置</span>
                  </label>
                  <label class="radio-inline">
                    <input name="form-field-checkbox" type="checkbox" class="ace" value="0x40" checked><span class="lbl">防火墙</span>
                  </label>
                  <label class="radio-inline">
                    <input name="form-field-checkbox" type="checkbox" class="ace" value="0x80" checked><span class="lbl">入侵检测系统</span>
                  </label>
                  <label class="radio-inline">
                    <input name="form-field-checkbox" type="checkbox" class="ace" value="0x100" checked><span class="lbl">防病毒系统</span>
                  </label>
                  <label class="radio-inline">
                    <input name="form-field-checkbox" type="checkbox" class="ace" value="0x200" checked><span class="lbl">网络安全监测装置</span>
                  </label>
                </div>
              </div>
              <div class="form-group wa-mb10">
                <label class="col-sm-1 control-label no-padding-right">
                  <b>事件等级:</b>
                </label>
                <div class="col-sm-11" id="eventLevelSlt">
                  <label class="radio-inline">
                    <input name="all" type="checkbox" class="ace" value="0" checked><span class="lbl">全选</span>
                  </label>
                  <label class="radio-inline">
                    <input name="form-field-checkbox" type="checkbox" class="ace" value="0x01" checked><span class="lbl">紧急</span>
                  </label>
                  <label class="radio-inline">
                    <input name="form-field-checkbox" type="checkbox" class="ace" value="0x02" checked><span class="lbl">重要</span>
                  </label>
                  <label class="radio-inline">
                    <input name="form-field-checkbox" type="checkbox" class="ace" value="0x04" checked><span class="lbl">次要</span>
                  </label>
                  <label class="radio-inline">
                    <input  name="form-field-checkbox" type="checkbox" class="ace" value="0x08" checked><span class="lbl">一般</span>
                  </label>
                  <label class="radio-inline">
                    <input name="form-field-checkbox" type="checkbox" class="ace" value="0x10" checked><span class="lbl">告知</span>
                  </label>
                </div>
              </div>
              <!--<div class="form-group">-->
                <!--<label class="col-sm-1 control-label no-padding-right">-->
                  <!--<b>查询条数:</b>-->
                <!--</label>-->
                <!--<div class="col-sm-11">-->
                  <!--<input type="number" id="recordCount" name="recordCount"-->
                         <!--placeholder="请输入查询条数,不超过1000万条" class="col-sm-3" min="0" max="10000000" value="100">-->
                  <!--<div class="help-block with-errors"></div>-->
                <!--</div>-->
              <!--</div>-->
              <div class="form-group">
                <label class="col-sm-1 control-label no-padding-right">
                  <b>开始&结束时间:</b>
                </label>
                <div class="col-sm-5">
                  <input type="text" id="newTimeRange" class="form-control">
                  <i class="glyphicon glyphicon-calendar fa fa-calendar" style="position: absolute;right: 20px;top: 10px;"></i>
                </div>
                  <div class="form-group">
                      <label class="col-sm-1 control-label no-padding-right">
                          <b>查询条数:</b>
                      </label>
                      <div class="col-sm-5">
                          <input type="text" id="selectNum" class="form-control">
                      </div>
                  </div>
              </div>
                <div class="col-sm-12">
                    <div class="col-sm-12 text-left" style="width: 50%">
                        <a href="javascript:;" type="submit" class="btn-default" style="border-radius: 8px;padding: 6px 12px;color: #FFF;display: inline-block;" id="queryAll"><i
                            class="ace-icon fa fa-refresh" ></i> 查询全部</a>
                    </div>
                    <div class="col-sm-12 text-right"style="width: 50%">
                        <a href="javascript:;" type="submit" class="btn btn-sm btn-success" id="query"><i
                            class="ace-icon fa fa-refresh"></i> 查询</a>
                        <a href="javascript:;" type="button" class="btn btn-sm btn-success" id="statis_chart"><i
                            class="ace-icon fa fa-bar-chart-o"></i> 统计图</a>
                  </div>
                </div>
            </form>
          </div>
        </div>
        <div class="row">
          <div class="col-xs-12">
            <div class="panel panel-default table-responsive" id="collect-evt-tb">

            </div>
            <div class="pagination-wrap" id="collect-evt-pager">
            </div>
            <div class="hidden" id="collect-evt-tb-nodata">
              <p>当前没有数据采集记录</p>
            </div>
            <!--<table id="collect-evt-tb" class="table table-bordered table-hover"></table>-->
            <!--<div id="collect-evt-pager"></div>-->
            <!--&lt;!&ndash;-&#45;&#45;用来计算单元格内容实际长度的-&#45;&#45;&#45;&#45;&#45;&#45;&ndash;&gt;-->
            <!--<div class="ui-jqgrid ui-widget ui-widget-content ui-corner-all" style="position:absolute;top:-9999px">-->
              <!--<div class="ui-jqgrid-view">-->
                <!--<div class="ui-jqgrid-bdiv">-->
                  <!--<div style="position: relative;">-->
                    <!--<table cellspacing="0" cellpadding="0" border="0">-->
                      <!--<tr class="ui-widget-content jqgrow ui-row-ltr" style="table-layout:table">-->
                        <!--<td id="tdCompute" style="background:#eee;width:auto"></td>-->
                      <!--</tr>-->
                    <!--</table>-->
                  <!--</div>-->
                <!--</div>-->
              <!--</div>-->
            <!--</div>-->
            <!--&lt;!&ndash;-&#45;&#45;用来计算单元格内容实际长度的-&#45;&#45;&#45;&#45;&#45;&#45;&ndash;&gt;-->
          </div>
          <!--<div class="col-xs-12">-->
            <!--<a href="javascript:;" type="submit" class="btn btn-sm btn-default" id="queryAll"><i class="ace-icon fa fa-refresh"></i> 查询全部</a>-->
          <!--</div>-->
        </div>
      </div><!-- /.page-content -->
    </div>
  </div><!-- /.main-content -->
  <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-sm btn-inverse">
    <i class="ace-icon fa fa-angle-double-up icon-only bigger-110"></i>
  </a>
  <!-- 统计图信息-->
  <div class="modal" id="seeChartDlg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <form class="form-horizontal" id="seeChartForm" method="post" action="" data-toggle="validator" role="form">
      <div class="modal-dialog" style="width: 800px">
        <div class="modal-content">
          <div class="modal-header" style="color: white;background: deepskyblue">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
            <h4 class="modal-title" id="seeChartTitle" wa-data>统计图</h4>
          </div>
          <div class="modal-body" style="height: 366px">
            <div class="row wa-mb10">
              <div class="col-xs-12" id="auditRecordsEcharts">
                <div class="col-xs-6">
                  <div id="level_percent" style="height: 355px"></div>
                </div><!-- /.col -->
                <div class="col-xs-6">
                  <div id="evt_classific_statis" style="height: 355px"></div>
                </div><!-- /.col -->
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-primary" data-dismiss="modal">确定
            </button>
          </div>
        </div>
      </div>
    </form>
  </div>
  <script src="lib/third/bootstrap-table/bootstrap-table.js"></script>
  <script src="lib/third/bootstrap-table/bootstrap-table-pagejump.js"></script>
  <script src="lib/third/bootstrap-table/locale/bootstrap-table-zh-CN.min.js"></script>
  <script>
    delete require.cache[require.resolve('./script/view/data_acquire.js')];
    require('./script/view/data_acquire.js');
    $('#data_acquire_frm').validator();
  </script>
</div>
